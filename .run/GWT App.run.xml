<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="GWT App" type="Application" factoryName="Application">
    <option name="ALTERNATIVE_JRE_PATH" value="21" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="INCLUDE_PROVIDED_SCOPE" value="true" />
    <option name="MAIN_CLASS_NAME" value="com.google.gwt.dev.codeserver.CodeServer" />
    <module name="sdng-war" />
    <option name="PROGRAM_PARAMETERS" value="-logLevel INFO -bindAddress 0.0.0.0 -port 9876 -generateJsInteropExports -XmethodNameDisplayMode FULL -launcherDir $ProjectFileDir$/sdng-war/target/sdng-war -src $ProjectFileDir$/sdng-war/src/main/java -src $ProjectFileDir$/sdng-war/src/main/resources -src $ProjectFileDir$/admin-lite/src/main/java -src $ProjectFileDir$/admin-lite/src/main/resources -src $ProjectFileDir$/cti/src/main/java -src $ProjectFileDir$/cti/src/main/resources -src $ProjectFileDir$/sdng-ndap/src/main/java -src $ProjectFileDir$/sdng-ndap/src/main/resources -src $ProjectFileDir$/cmdb/src/main/java -src $ProjectFileDir$/cmdb/src/main/resources -src $ProjectFileDir$/sdng-mobile/src/main/java -src $ProjectFileDir$/sdng-mobile/src/main/resources -src $ProjectFileDir$/sdng-ndap/src/main/java -src $ProjectFileDir$/sdng-ndap/src/main/resources -src $ProjectFileDir$/workload/src/main/java -src $ProjectFileDir$/workload/src/main/resources -src $ProjectFileDir$/sdng/src/main/java -src $ProjectFileDir$/sdng/src/main/resources -src $ProjectFileDir$/sdng/src/profiles/developer/resources -src $ProjectFileDir$/sdng/src/profiles/en_no/resources -src $ProjectFileDir$/sdng/src/profiles/multiLang_no/resources -src $ProjectFileDir$/sdng/src/profiles/uk_no/resources -src $ProjectFileDir$/sdng/src/profiles/pl_no/resources -src $ProjectFileDir$/sdng/src/profiles/de_no/resources -src $ProjectFileDir$/commons-util/src/main/java -src $ProjectFileDir$/commons-util/src/main/resources -src $ProjectFileDir$/sdng-generated/src/main/java -src $ProjectFileDir$/sdng-generated/src/main/resources -src $ProjectFileDir$/sdng-generated/target/generated-sources/antlr4 -src $ProjectFileDir$/sdng-generated/target/generated-sources/jaxb/fileStorageSettings -src $ProjectFileDir$/sdng-generated/target/generated-sources/jaxb/jmsSettings -src $ProjectFileDir$/sdng-generated/target/generated-sources/jaxb/ldapSettings -src $ProjectFileDir$/sdng-generated/target/generated-sources/jaxb/licensingPolicy -src $ProjectFileDir$/sdng-generated/target/generated-sources/jaxb/test-object -src $ProjectFileDir$/sdng-generated/target/generated-sources/transferable -src $ProjectFileDir$/script-api/src/main/java -src $ProjectFileDir$/commons-util-test/src/main/java -src $ProjectFileDir$/sdng-generated/src/test/java -src $ProjectFileDir$/sdng-generated/src/test/resources -src $ProjectFileDir$/gwt-jaxb-support/src/main/java -src $ProjectFileDir$/gwt-jaxb-support/src/main/resources -src $ProjectFileDir$/sdng-smia/src/main/java -src $ProjectFileDir$/sdng-smia/src/main/resources -src $ProjectFileDir$/sdng-planned-version/src/main/java -src $ProjectFileDir$/sdng-planned-version/src/main/resources -src $ProjectFileDir$/sdng-omnichannel/src/main/java -src $ProjectFileDir$/sdng-omnichannel/src/main/resources -src $ProjectFileDir$/sdng-dynamic-field/src/main/java -src $ProjectFileDir$/sdng-dynamic-field/src/main/resources -src $ProjectFileDir$/core/src/main/java -src $ProjectFileDir$/ui-common/src/main/java -src $ProjectFileDir$/report-spec/src/main/java -src $ProjectFileDir$/report-spec/src/main/resources ru.naumen.war.admin.Admin ru.naumen.war.operator.Operator ru.naumen.war.password.Password -sourceLevel 17" />
    <option name="VM_PARAMETERS" value="-Xmx6G --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.text=ALL-UNNAMED --add-opens java.base/sun.nio.ch=ALL-UNNAMED --add-opens java.base/jdk.internal.loader=ALL-UNNAMED --add-opens java.base/sun.util.calendar=ALL-UNNAMED --add-opens jdk.management/com.sun.management.internal=ALL-UNNAMED --add-opens java.desktop/java.awt.font=ALL-UNNAMED --add-exports java.base/jdk.internal.misc=ALL-UNNAMED --add-exports java.base/sun.nio.ch=ALL-UNNAMED --add-exports java.management/com.sun.jmx.mbeanserver=ALL-UNNAMED --add-exports jdk.internal.jvmstat/sun.jvmstat.monitor=ALL-UNNAMED --add-exports java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED --add-exports java.naming/com.sun.jndi.ldap=ALL-UNNAMED" />
    <method v="2" />
  </configuration>
</component>