package ru.naumen;

import static java.util.regex.Pattern.compile;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.Assert;
import org.junit.Test;

/**
 * Тесты проверки пакетов и их содержимого
 * <AUTHOR>
 */
public class BasePackageCheckJdkTest
{
    private static final String PKG_PATH = "src/main/java/ru/naumen";

    private static final String END_REGEX = "[^;{]*[}]";
    private static final String BEGIN_REGEX = "catch?[\\s\\S][(][^)]*[)][^{]*[{][^;{}]*";

    private static final Pattern RETURN_NULL_REGEX = compile(BEGIN_REGEX + "return[\\s\\S]null[;]" + END_REGEX);
    private static final Pattern PRINT_TRACE_REGEX = compile(BEGIN_REGEX + ".printStackTrace[(][^;*][;]" + END_REGEX);
    private static final Pattern EMPTY_RETURN_REGEX = compile(BEGIN_REGEX + "return[;]" + END_REGEX);
    private static final Pattern EMPTY_REGEX = compile(BEGIN_REGEX + END_REGEX);
    private static final Predicate<Path> EMPTY_CATCH_FILTER = new Predicate<Path>()
    {
        @Override
        public boolean test(Path path)
        {
            return containsEmptyCatch(path);
        }

        private boolean containsEmptyCatch(Path path)
        {
            try
            {
                String s = new String(Files.readAllBytes(path));
                return hasUncorrectCatchBlock(EMPTY_REGEX, s) || hasUncorrectCatchBlock(EMPTY_RETURN_REGEX, s)
                       || hasUncorrectCatchBlock(RETURN_NULL_REGEX, s) || hasUncorrectCatchBlock(PRINT_TRACE_REGEX, s);
            }
            catch (IOException e)
            {
                throw new RuntimeException(e);
            }
        }

        private boolean hasUncorrectCatchBlock(Pattern pattern, String text)
        {
            return pattern.matcher(text).find();
        }
    };
    private static final Predicate<Path> DIRECTORY_FILTER = new Predicate<Path>()
    {
        private static final String TARGET = "target";
        private static final String TRANSLATABLE = "translatable";
        private static final String ANNOTATION_STRING = "ParametersAreNonnullByDefault";
        private static final String PACKAGE_INFO_FILE_NAME = "package-info.java";

        @Override
        public boolean test(Path path)
        {
            try
            {
                return !generatedSource(path) && !containsOnlyPackages(path) && !packageInfoIsCorrect(path);
            }
            catch (IOException e)
            {
                throw new RuntimeException(e);
            }
        }

        private boolean containsOnlyPackages(Path dir) throws IOException
        {
            try (Stream<Path> list = Files.list(dir))
            {
                return list.count() == 0;
            }
        }

        private boolean generatedSource(Path dir)
        {
            String path = dir.toAbsolutePath().toString();
            return path.contains(TRANSLATABLE) || path.contains(TARGET);
        }

        private boolean hasAnnotation(Path path) throws IOException
        {
            try (Stream<String> lines = Files.lines(path, Charset.defaultCharset()))
            {
                return lines.anyMatch(s -> s.contains(ANNOTATION_STRING));
            }
        }

        private boolean packageInfoIsCorrect(Path dir) throws IOException
        {
            Path info = dir.resolve(PACKAGE_INFO_FILE_NAME);
            if (!info.toFile().exists())
            {
                return false;
            }
            return hasAnnotation(info);
        }
    };

    /**
     * Проверяет наличие классов с наличием следующих типов catch блоков:
     * - пустой или содержащий только комментарий
     * - содержащий только return; 
     * - содержащий только return null;
     * - содержащий только e.printStackTrace();
     */
    @Test
    public void findEmptyCatchBlockTest() throws IOException
    {
        try (Stream<Path> walk = Files.walk(Paths.get(PKG_PATH)))
        {
            List<Path> result = walk
                    .filter(p -> p.toString().endsWith(".java"))
                    .filter(EMPTY_CATCH_FILTER)
                    .collect(Collectors.toList());

            Assert.assertTrue("Check catch blocks in this files: " + result.toString().replace(',', '\n'), result
                    .isEmpty());
        }
    }

    /**
     * Проверяет наличие в каждом source пакете файла package-info.java
     * В package-info.java должна быть аннотация {@code ParametersAreNonnullByDefault}
     * Исключает пакеты translatable и target
     */
    @Test
    public void packageInfoExistsAnnotationTest() throws IOException
    {
        try (Stream<Path> walk = Files.walk(Paths.get(PKG_PATH)))
        {
            List<Path> result = walk
                    .filter(p -> p.toString().endsWith(".java"))
                    .map(Path::getParent)
                    .filter(DIRECTORY_FILTER)
                    .distinct()
                    .collect(Collectors.toList());

            Assert.assertTrue("Check package-info files in these packages: " + result.toString().replace(',', '\n'),
                    result.isEmpty());
        }
    }
}
