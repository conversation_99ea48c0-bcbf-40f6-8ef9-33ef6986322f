package ru.naumen.commons.logging;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.zip.DeflaterOutputStream;
import java.util.zip.GZIPOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.status.StatusLogger;

/**
 * Менеджер сжатия файлов
 * (реализация из log4j-extras)
 *
 * <AUTHOR>
 * @since 08.12.2016
 */
public class CompressingManager
{
    private static final Logger LOGGER = StatusLogger.getLogger();

    static class GZIPOutputStreamWithLevel extends GZIPOutputStream
    {
        public GZIPOutputStreamWithLevel(OutputStream out) throws IOException
        {
            super(out);
        }

        public void setLevel(int level)
        {
            def.setLevel(level);
        }
    }

    private static final String THREAD_NAME = "CompressingLogThread";
    private static final ExecutorService EXECUTOR_SERVICE =
            Executors.newSingleThreadExecutor((runnable) -> new Thread(runnable, THREAD_NAME));

    /**
     * Сжать файл в формате zip
     *
     * @param source путь до исходного файла
     * @param destination путь до архива
     * @param compressionLevel уровень сжатия
     */
    private static void zipFile(final Path source, final Path destination, int compressionLevel)
    {
        try (final FileOutputStream fileOutputStream = new FileOutputStream(destination.toString());
             final ZipOutputStream outputStream = new ZipOutputStream(fileOutputStream))
        {
            outputStream.setLevel(compressionLevel);
            final ZipEntry zipEntry = new ZipEntry(source.getFileName().toString());
            outputStream.putNextEntry(zipEntry);

            compressFile(source, outputStream);
        }
        catch (Exception e)
        {
            LOGGER.error("Unable to compress the log file " + source.toString(), e);
        }
    }

    /**
     * Сжать файл в формате gz
     *
     * @param source путь до исходного файла
     * @param destination путь до архива
     * @param compressionLevel уровень сжатия
     */
    private static void gzFile(final Path source, final Path destination, int compressionLevel)
    {
        try (final FileOutputStream fileOutputStream = new FileOutputStream(destination.toString());
             final GZIPOutputStreamWithLevel outputStream = new GZIPOutputStreamWithLevel(fileOutputStream))
        {
            outputStream.setLevel(compressionLevel);
            compressFile(source, outputStream);
        }
        catch (Exception e)
        {
            LOGGER.error("Unable to compress the log file " + source.toString(), e);
        }
    }

    /**
     * Сжать файл и после удалить. Операция будет происходить асинхронно через SingleThreadExecutor
     *
     * @param source путь до исходного файла
     * @param destination путь до архива
     * @param compressType тип сжатия
     * @param compressionLevel уровень сжатия
     */
    public static void compressAndDeleteFileAsync(final Path source,
            final Path destination,
            final CompressType compressType,
            final int compressionLevel)
    {
        if (Files.notExists(source))
        {
            return;
        }

        EXECUTOR_SERVICE.submit(() ->
        {
            if (Files.notExists(source))
            {
                return;
            }

            switch (compressType)
            {
                case GZ:
                    gzFile(source, destination, compressionLevel);
                    break;
                case ZIP:
                    zipFile(source, destination, compressionLevel);
                    break;
                default:
                    LOGGER.error("Compression method for [" + compressType.getId() + "] is not implemented yet!");
                    return;
            }

            try
            {
                if (!Files.deleteIfExists(source))
                {
                    LOGGER.warn("Unable to delete " + source.toString() + ".");
                }
            }
            catch (final IOException e)
            {
                LOGGER.error(e.getMessage(), e);
            }
        });
    }

    /**
     * Сжатие файла с последующим его удалением
     *
     * @param source исходный файл для сжатия
     * @param outputStream поток, куда будем сохранять файл
     * @throws IOException ошибка сжатия для последующего логирования
     */
    private static void compressFile(final Path source, final DeflaterOutputStream outputStream) throws IOException
    {
        try (FileInputStream fis = new FileInputStream(source.toString()))
        {
            byte[] data = new byte[8102];
            int n;

            while ((n = fis.read(data)) != -1)
            {
                outputStream.write(data, 0, n);
            }
        }
    }
}
