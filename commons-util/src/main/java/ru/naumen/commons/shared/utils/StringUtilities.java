package ru.naumen.commons.shared.utils;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.primitives.Chars;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import jakarta.annotation.Nullable;

/**
 * Утилитарные методы для работы со строками.
 * <p>
 * В отличие от {@link ru.naumen.commons.server.utils.StringUtilities} может использоваться в коде на GWT
 *
 * <AUTHOR>
 *
 */
public class StringUtilities
{
    public static class DoubleQuoted implements Function<CharSequence, String>
    {
        @Override
        public String apply(CharSequence input)
        {
            return "\"" + input + "\"";
        }
    }

    public static class Quoted implements Function<CharSequence, String>
    {
        @Override
        public String apply(CharSequence input)
        {
            return "'" + input + "'";
        }
    }

    /**
     * Предназначен для пропуска пустых строк при конкатенации элементов списка в строку
     * <AUTHOR>
     * @since 27.04.2011
     *
     */
    public static class SkipEmpty implements Function<CharSequence, String>
    {
        @Override
        public String apply(CharSequence input)
        {
            if (input == null)
            {
                return null;
            }
            if (input instanceof String)
            {
                input = ((String)input).trim();
            }
            return input.length() == 0 ? null : input.toString();
        }
    }

    public static class ToLowerCase implements Function<String, String>
    {
        @Override
        public String apply(String input)
        {
            return null == input ? null : input.toLowerCase();
        }
    }

    public static final String ALL_SPACES = "\\u0000-\\u0020\\u0080-\\u00A0\\u1680\\u180E\\u2000-\\u200F\\u2028"
                                            + "-\\u202F\\u205F\\u3000\\uFEFF";
    public static final SkipEmpty SKIP_EMPTY_INSTANCE = new SkipEmpty();
    public static final String EMPTY = "";
    public static final String SPACE = " ";
    public static final String UNDERSCORE = "_";
    public static final String COLON = ":";
    public static final String SEMICOLON = ";";
    public static final String COMMA = ",";
    public static final String COMMA_ENC = "%2C";
    public static final String EQUAL = "=";
    public static final String UUID_SYMBOLS = "[^A-Za-z$0-9_]";
    public final static String ELLIPSIS = "…";
    public final static String LINE_DELIMITER = "\n";
    private static final String CR_OR_LF = "[\\r\\n]";
    private final static char[] C_NBSP = "&nbsp;".toCharArray();
    private final static char[] C_SPACE = " ".toCharArray();
    private final static char[] C_LT = "&lt;".toCharArray();
    private final static char[] C_QUOT = "&quot;".toCharArray();
    private final static char[] C_AMP = "&amp;".toCharArray();
    private final static char[] C_QT = "&gt;".toCharArray();
    private static final DoubleQuoted DOUBLE_QUOTED_INSTANCE = new DoubleQuoted();
    private static final Quoted QUOTED_INSTANCE = new Quoted();
    private static final int INDEX_NOT_FOUND = -1;
    public static final int MAX_ERROR_SIZE_IN_MASSOPERATION = 3;

    /**
     * Вставляет неразрывный пробел &nbsp; между первым и вторым словом, если первое слово строго меньше 5 символов.
     * Перед проверкой количества символов заменяет все экранированные символы на неэкранированные,
     * например: (;quot;nbsp;quot -> " ").
     * @param isHtmlEscaped возвращать ли строку в формате {@link SafeHtmlUtils#htmlEscape(String)}. Может
     *                      пригодиться при использовании {@link SafeHtmlUtils#fromTrustedString(String)}.
     *                      Если параметр false, тогда строка вернётся в исходном виде с неэкранированными символами
     *                      и неразрывным пробелом.
     * @return возвращает исходную строку со встроенным неразрывным пробелом
     */
    public static String addNbspAfterFirstShortWord(@Nullable String s, boolean isHtmlEscaped)
    {
        if (s == null)
        {
            return null;
        }
        String sn = s.replaceAll("&nbsp;", " ").replaceAll("&amp;", "&").replaceAll("&lt;", "<").replaceAll("&gt;", ">")
                .replaceAll("&quot;", "\"").replaceAll("&#39;", "'");
        int spaceIndex = sn.indexOf(' ');
        if (spaceIndex < 6 && spaceIndex >= 0)
        {
            return isHtmlEscaped
                    ? SafeHtmlUtils.htmlEscape(sn.substring(0, spaceIndex)) + "&nbsp;"
                      + SafeHtmlUtils.htmlEscape(sn.substring(spaceIndex + 1))
                    : sn.substring(0, spaceIndex) + "&nbsp;" + sn.substring(spaceIndex + 1);
        }
        return isHtmlEscaped ? SafeHtmlUtils.htmlEscape(sn) : sn;
    }

    /**
     * Сделать текст жирным
     */
    public static String bold(String html)
    {
        return "<b>" + html + "</b>";
    }

    /**
     * Возвращает строку с заглавной первой буквой
     */
    public static String capitalizeFirstLetter(String str)
    {
        if (isEmpty(str))
        {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    /**
     * Возвращает строку с маленькой первой буквой
     */
    public static String uncapitalizeFirstLetter(String str)
    {
        if (isEmpty(str))
        {
            return str;
        }
        return str.substring(0, 1).toLowerCase() + str.substring(1);
    }

    /**
     * Разбивает строку на подстроки указанной длины и соединяет их, вставляя
     * между соседними частями указанную строку.
     * @param str исходная строка
     * @param chunkSize максимальная длина подстрок
     * @param joiner подстрока-соединитель
     * @return строка, в которой каждые {@code chunkSize} символов вставлена строка {@code joiner}
     */
    public static String chunkSplitAndJoin(String str, int chunkSize, String joiner)
    {
        if (isEmpty(str))
        {
            return str;
        }
        StringBuilder result = new StringBuilder();
        int pos = 0;
        for (; pos + chunkSize < str.length(); pos += chunkSize)
        {
            result.append(str, pos, pos + chunkSize).append(joiner);
        }
        result.append(str.substring(pos));
        return result.toString();
    }

    /**
     * @param a
     * @param b
     * @return первый не null параметр
     */
    public static <T> T coalesce(T a, T b)
    {
        return a == null ? b : a;
    }

    /**
     * Performs a lexicographic comparison of two Strings, where null is considered to be lexicographically lower than
     * any non-null String.
     * <p/>
     * Otherwise equivalent to operand.compareTo( target ).
     */
    public static int compareTo(@Nullable String first, @Nullable String second)
    {
        if (null == first)
        {
            if (null == second)
            {
                return 0;
            }
            return -1;
        }
        else if (null == second)
        {
            return 1;
        }
        else
        {
            return first.compareTo(second);
        }
    }

    /**
     * Performs a case-insensitive lexicographic comparison of two Strings, where null is considered to be
     * lexicographically lower than any non-null String.
     * <p/>
     * Otherwise equivalent to operand.compareTo( target ).
     */
    public static int compareToIgnoreCase(@Nullable String first, @Nullable String second)
    {
        if (null == first)
        {
            if (null == second)
            {
                return 0;
            }
            return -1;
        }
        else if (null == second)
        {
            return 1;
        }
        else
        {
            return first.compareToIgnoreCase(second);
        }
    }

    /**
     * Содержится ли str2 в str1, с учетом регистра.
     * @param str1 исходная строка;
     * @param str2 искомая подстрока;
     * @return результат поиска;
     */
    public static boolean contains(String str1, String str2)
    {
        if (str1 == null || str2 == null)
        {
            return false;
        }
        return str1.contains(str2);
    }

    /**
     * Проверяет, содержит ли строка все подстроки
     * Если @param string равен null - результат false
     * Если @param strs пустые, либо null - результат true
     * Иначе проверяем @param string
     * на наличие всех подстрок @strs в ней
     *
     * @param string - искомая строка
     * @param strs - подстроки, которые нужно проверить
     * @return true - в строке присустствуют все подстроки
     */
    public static boolean containsAll(String string, String... strs)
    {
        if (string == null)
        {
            return false;
        }
        if (strs == null)
        {
            return true;
        }
        for (String str : strs)
        {
            if (!string.contains(str))
            {
                return false;
            }
        }
        return true;
    }

    /**
     * Содержится ли str2 в str1, без учета регистра
     */
    public static boolean containsIgnoreCaseSafe(@Nullable String str1, @Nullable String str2)
    {
        if (str1 == null || str2 == null)
        {
            return false;
        }
        int len = str2.length();
        int max = str1.length() - len;
        for (int i = 0; i <= max; i++)
        {
            if (str1.regionMatches(true, i, str2, 0, len))
            {
                return true;
            }
        }
        return false;
    }

    /**
     * Содержит ли str1 строку str2, или является ли str1 пустой строкой или null
     * @param str1
     * @param str2
     * @return
     */
    public static boolean containsOrEmpty(String str1, String str2)
    {
        return isEmptyTrim(str1) || containsIgnoreCaseSafe(str1, str2);
    }

    public static String convertTagWithHrefToMarkup(String str)
    {
        if (isEmptyTrim(str))
        {
            return str;
        }
        return str.replace("&#61;", EQUAL).replace("&#34;", "\"").replace(String.valueOf(C_QUOT), "\"")
                .replace(String.valueOf(C_LT), "<").replace(String.valueOf(C_QT), ">");
    }

    public static String convertTagWithHrefToNonMarkup(String str)
    {
        if (isEmptyTrim(str))
        {
            return str;
        }
        return toNonMarkupString(str).replace(EQUAL, "&#61;").replace(String.valueOf(C_QUOT), "&#34;");
    }

    public static DoubleQuoted doubleQuoted()
    {
        return DOUBLE_QUOTED_INSTANCE;
    }

    /**
     * Метод проверяет равенство строк с учетом регистра
     */
    public static boolean equals(@Nullable String str1, @Nullable String str2)
    {
        return Objects.equals(str1, str2);
    }

    /**
     * Экранирует символы в строке с помощью обратного слэша.
     * @param str исходная строка
     * @param escapedChars строка из символов, которые подлежат экранированию
     * @return экранированная строка
     */
    public static String escapeWithBackslash(String str, String escapedChars)
    {
        StringBuilder resultBuilder = new StringBuilder();
        for (char sourceChar : str.toCharArray())
        {
            if (sourceChar == '\\' || escapedChars.indexOf(sourceChar) >= 0)
            {
                resultBuilder.append('\\');
            }
            resultBuilder.append(sourceChar);
        }
        return resultBuilder.toString();
    }

    /**
     * Проверяет, равны ли строки, без учета регистра
     * @param str1
     * @param str2
     * @return
     */
    public static boolean equalsIgnoreCase(String str1, String str2)
    {
        return str1 == null ? str2 == null : str1.equalsIgnoreCase(str2);
    }

    /**
     * Метод для обрезания длинной строки
     * @param str - строка
     * @param maxSymbols - максимальное количество символов
     * @return обрезанную до maxSymbols символов строку с троеточием в конце, если она
     * длиннее maxSymbols, или изначальную строку в противном случае
     */
    @Nullable
    public static String formatLongString(@Nullable String str, int maxSymbols)
    {
        if (!isEmpty(str) && str.length() > maxSymbols)
        {
            str = str.substring(0, maxSymbols) + "...";
        }
        return str;
    }

    /**
     * Метод для обрезания длинной строки
     * @param str - строка
     * @param maxSymbols - максимальное количество символов
     * @return обрезанную до maxSymbols символов строку с троеточием в конце, если она
     * длиннее maxSymbols, или изначальную строку в противном случае. Если изначальная
     * строка была null, возвращает пустую строку.
     */
    public static String formatLongStringSafe(@Nullable String str, int maxSymbols)
    {
        String result = formatLongString(str, maxSymbols);
        return result == null ? EMPTY : result;
    }

    /**
     * Формирует ссылку на основании token из Place
     * @param token
     * @return
     */
    @Nullable
    public static String getHrefByToken(String token)
    {
        return isEmpty(token) ? null : "#" + token;
    }

    /**
     * Формирует token (у Place) по href
     * @param href
     * @return
     */
    @Nullable
    public static String getTokenByHref(String href)
    {
        if (isEmpty(href))
        {
            return null;
        }
        return href.charAt(0) == '#' ? href.substring(1) : href;
    }

    /**
     * Находит позицию первого вхождения в строку
     * Иначе возвращает -1
     * @param str
     * @param searchStr
     * @return
     */
    public static int indexOf(String str, String searchStr)
    {
        if (str == null || searchStr == null)
        {
            return INDEX_NOT_FOUND;
        }
        return str.indexOf(searchStr);
    }

    /**
     * Проверяет строку на пустоту. Строка считается пустой если она null, не содержит ни одного символа или состоит из
     * одних непечатаемых символов (включая неразрывные пробелы).
     *
     * @param s - проверяемая строка
     * @return true если строка пустая, false иначе
     */
    public static boolean isBlank(CharSequence s)
    {
        if (s != null)
        {
            String replaced = s.toString().replaceAll("^[" + ALL_SPACES + "]*|[" + ALL_SPACES + "]*$", " ");
            return isEmptyTrim(replaced);
        }
        return true;
    }

    /**
     * @
     */
    public static boolean isDigit(char ch)
    {
        return ch >= '0' && ch <= '9';
    }

    /**
     * Проверяет строку на пустоту. Строка считается пустое если она null или если ее длина равно 0.
     *
     * @param s
     *            проверяемая строка
     * @return true если строка пустая, false иначе
     */
    public static boolean isEmpty(@Nullable CharSequence s)
    {
        return null == s || 0 == s.length();
    }

    /**
     * Валидатор проверяющий текст RTF на пустоту
     * Кроме тегов текст RTF должен содержать хотя бы один печатный символ
     */
    public static boolean isEmptyRichText(@Nullable String value)
    {
        if (null == value)
        {
            return true;
        }
        //Если в текст RTF вставлена картинка
        if (value.replaceAll("<\\s*img[^<>]*>", "").length() != value.length())
        {
            return false;
        }
        // ... или видео
        if (value.replaceAll("<\\s*video[^<>]*>", "").length() != value.length())
        {
            return false;
        }
        if (value.replaceAll("(?:<iframe.+src[^>]*)(?:(?:\\/>)|(?:>.*?<\\/iframe>))", "").length() != value.length())
        {
            return false;
        }
        value = value.replaceAll("<[^<>]*>", "").replaceAll("&nbsp;", "").replaceAll("&nbsp", "")
                .replaceAll("<div><br></div>", "").replaceAll("<div>&nbsp;</div>", "");
        return StringUtilities.isBlank(value);
    }

    /**
     * Проверяет строку на пустоту. Строка считается пустой если она null, не содержит ни одного символа или состоит из
     * одних непечатаемых символов.
     *
     * @param s
     *            проверяемая строка
     * @return true если строка пустая, false иначе
     */
    public static boolean isEmptyTrim(@Nullable CharSequence s)
    {
        if (s != null)
        {
            // This is more efficient than using s.trim().length() == 0
            // (but only when using the server hotspot)
            int len = s.length();
            for (int i = 0; i < len; ++i)
            {
                if (s.charAt(i) > ' ')
                {
                    return false;
                }
            }
        }
        return true;
    }

    public static boolean isLatinLetter(char ch)
    {
        return ch >= 'a' && ch <= 'z' || ch >= 'A' && ch <= 'Z';
    }

    /**
     * Проверяет строку на не пустоту. Строка считается непустой если она не null и ее длина больше 0.
     * @param s   проверяемая строка
     * @return true если строка пустая, false иначе
     */
    public static boolean isNotEmpty(@Nullable CharSequence s)
    {
        return !isEmpty(s);
    }

    /**
     * метод предназначен для соединения содержимого коллекции символьных
     * последовательностей в строку через запятую в порядке итерирования
     * @param items коллекция строк
     * @return
     */
    public static String join(Collection<? extends CharSequence> items)
    {
        return join(items, SKIP_EMPTY_INSTANCE);
    }

    /**
     * метод предназначен для соединения содержимого коллекции символьных
     * последовательностей в строку через разделитель в порядке итерирования
     * @param items коллекция строк
     * @param delimiter разделитель
     * @return
     */
    public static String join(Collection<? extends CharSequence> items, String delimiter)
    {
        return join(items, delimiter, SKIP_EMPTY_INSTANCE);
    }

    /**
     * метод предназначен для соединения содержимого коллекции
     * в строку через запятую в порядке итерирования<p>
     * Если в коллекции есть null элемент, или результат преобразования элемента в CharSequence = null, то такой
     * элемент пропускается
     * @param <T> базовый тип элементов коллекции
     * @param items коллекция
     * @param itemPresenter трансформер, предназначенный для преобразования элемента коллекции в
     *                      последовательность символов
     * @return
     */
    public static <T> String join(Collection<? extends T> items, Function<T, ? extends CharSequence> itemPresenter)
    {
        return join(items, ", ", itemPresenter);
    }

    /**
     * метод предназначен для соединения содержимого коллекции
     * в строку через разделитель в порядке итерирования
     *
     * @param <T> базовый тип элементов коллекции
     * @param items коллекция
     * @param delimiter разделитель
     * @param itemPresenter трансформер, предназначенный для преобразования элемента коллекции в
     *                      последовательность символов
     * @return
     */
    public static <T> String join(Collection<? extends T> items, String delimiter,
            java.util.function.Function<T, ? extends CharSequence> itemPresenter)
    {
        return join(items, delimiter, itemPresenter, items.size());
    }

    /**
     * метод предназначен для соединения содержимого коллекции
     * в строку через разделитель в порядке итерирования
     *
     * @param <T> базовый тип элементов коллекции
     * @param items коллекция
     * @param delimiter разделитель
     * @param itemPresenter трансформер, предназначенный для преобразования элемента коллекции в
     *                      последовательность символов
     * @param maxLength максимальное количество объектов от начала коллекции которые будут соединены. В случае если
     *                  количество элементов
     *          коллекции превышает это число оставшиеся элементы будут проигнорированы.
     * @return
     */
    public static <T> String join(Collection<? extends T> items, String delimiter,
            java.util.function.Function<T, ? extends CharSequence> itemPresenter, int maxLength)
    {
        if (items == null || items.isEmpty())
        {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        boolean hasData = false;
        int length = 0;
        for (Iterator<? extends T> it = items.iterator(); it.hasNext() && length < maxLength; )
        {
            T next = it.next();
            if (next == null)
            {
                continue;
            }
            CharSequence value = itemPresenter.apply(next);
            if (value == null)
            {
                continue;
            }
            if (hasData)
            {
                sb.append(delimiter);
            }
            sb.append(value);
            hasData = true;
            ++length;
        }
        return sb.toString();
    }

    /**
     * метод предназначен для соединения содержимого коллекции символьных
     * последовательностей в строку через запятую в порядке итерирования.
     * Содержимое каждой символьной последовательности заключается в двойные кавычки.
     *
     * @param items коллекция строк
     */
    public static String joinDoubleQuoted(Collection<? extends CharSequence> items)
    {
        return join(items, doubleQuoted());
    }

    /**
     * метод предназначен для соединения содержимого коллекции локализованных строк
     * в одну строку через запятую в порядке итерирования
     * @param items коллекция строк
     * @return
     */
    public static Map<String, String> joinLocale(Collection<Map<String, String>> items)
    {
        Map<String, String> localizedMessages = Maps.newLinkedHashMap();
        if (items.iterator().hasNext())
        {
            Set<String> locales = items.iterator().next().keySet();
            for (String locale : locales)
            {
                List<String> strings = Lists.newLinkedList();
                for (Map<String, String> item : items)
                {
                    strings.add(item.get(locale));
                }
                localizedMessages.put(locale, join(strings, SKIP_EMPTY_INSTANCE));
            }
        }
        return localizedMessages;
    }

    /**
     * метод предназначен для соединения содержимого коллекции символьных
     * последовательностей в строку через запятую в порядке итерирования.
     * Содержимое каждой символьной последовательности заключается в одинарные кавычки.
     *
     * @param items коллекция строк
     */
    public static String joinQuoted(Collection<? extends CharSequence> items)
    {
        return join(items, quoted());
    }

    /**
     * Метод предназначен для соединения содержимого коллекции символьных
     * последовательностей в строку через переданный разделитель в порядке итерирования.
     * Содержимое каждой символьной последовательности заключается в одинарные кавычки.
     */
    public static String joinQuoted(Collection<? extends CharSequence> items, String delimiter)
    {
        return join(items, delimiter, quoted());
    }

    /**
     *
     * @param s
     * @return длина строки
     */
    public static int length(@Nullable String s)
    {
        return s == null ? 0 : s.length();
    }

    /**
     * Ограничить строку limit символами и добавить в конец "..."
     * @param value
     * @param limit
     * @return
     */
    @Nullable
    public static String limitStringValue(String value, int limit)
    {
        if (value != null && value.length() > limit)
        {
            return value.substring(0, limit) + ELLIPSIS;
        }
        return value;
    }

    /**
     * Проверяет, является ли строка натуральным числом
     * @param s строка для проверки
     * @return true если является, иначе false
     */
    public static boolean isNaturalNumber(@Nullable String s)
    {
        if (ru.naumen.commons.shared.utils.StringUtilities.isEmpty(s))
        {
            return false;
        }
        for (char ch : s.toCharArray())
        {
            if (!isDigit(ch))
            {
                return false;
            }
        }
        return true;
    }

    /**
     * Заменяет значимые символы генерируемого UUID на символ звездочки. Позволяет оставить некоторую часть символов
     * в начале и в конце строки открытыми.
     * @param uuid строковый UUID
     * @param openLeadingChars количество символов, которые не нужно заменять, в начале строки
     * @param openTrailingChars количество символов, которые не нужно заменять, в конце строки
     * @return преобразованный UUID с замаскированными значимыми символами
     */
    @Nullable
    public static String maskUuid(@Nullable String uuid, int openLeadingChars, int openTrailingChars)
    {
        if (StringUtilities.isEmpty(uuid) || uuid.length() <= (openLeadingChars + openTrailingChars))
        {
            return uuid;
        }

        StringBuilder resultBuilder = new StringBuilder(uuid.length());
        resultBuilder.append(uuid, 0, openLeadingChars);
        for (int i = openLeadingChars; i < (uuid.length() - openTrailingChars); ++i)
        {
            char currentChar = uuid.charAt(i);
            resultBuilder.append(currentChar == '-' ? currentChar : '*');
        }
        resultBuilder.append(uuid.substring(uuid.length() - openTrailingChars));
        return resultBuilder.toString();
    }

    public static Quoted quoted()
    {
        return QUOTED_INSTANCE;
    }

    /**
     * Удаляет все символы CR (\r) и LF(\n) из строки str
     * @param str строка
     * @return строка str без \r и \n
     */
    public static String removeCRLF(String str)
    {
        return str.replaceAll(CR_OR_LF, EMPTY);
    }

    /**
     * Удаляет один символ переноса строки, если строка заканчивается двумя
     * @param str строка
     * @return строка str без двойного переноса
     */
    public static String removeDoubleLineBreak(String str)
    {
        if (str.length() < 2)
        {
            return str;
        }
        if (str.charAt(str.length() - 1) == '\n' && str.charAt(str.length() - 2) == '\n')
        {
            str = str.substring(0, str.length() - 2);
        }
        return str;
    }

    /**
     * Удаляет подстроку из строки только в том случае, если подстрока находится
     * в конце исходной строки. В противном случае возвращает исходную строку.
     *
     * @param str исходная строка
     * @param remove строка, которую необходимо локализовать в конце исходной
     * строки и удалить. Может быть null.
     *
     * @return строка str с удаленным окончанием remove
     */
    public static String removeEnd(String str, String remove)
    {
        if (isEmpty(str) || isEmpty(remove))
        {
            return str;
        }
        if (str.endsWith(remove))
        {
            return str.substring(0, str.length() - remove.length());
        }
        return str;
    }

    /**
     * Удаляет подстроку из строки только в том случае, если подстрока находится
     * в конце исходной строки. В противном случае возвращает исходную строку.
     * Повторяет действие, пока подстрока находится в конце исходной строки.
     *
     * @param str исходная строка
     * @param remove строка, которую необходимо локализовать в конце исходной
     * строки и удалить. Может быть null.
     *
     * @return строка str с удаленными окончаниями remove
     */
    public static String removeEndRecursively(String str, String remove)
    {
        if (isEmpty(str) || isEmpty(remove))
        {
            return str;
        }
        while (str.endsWith(remove))
        {
            str = str.substring(0, str.length() - remove.length());
        }
        return str;
    }

    /**
     * Заменяет несколько подряд идущих пробелов одним.
     *
     * @param html строка, в которой необходимо произвести замены.
     * @return строка с произведёнными заменами.
     */
    public static String removeHtmlWhitespaceCharacters(String html)
    {
        String text = html.replaceAll("\r\n", " ");
        return removePlainTextLineBreaks(text);
    }

    /**
     * Заменить переносы строк "\n" на пробелы
     */
    public static String removePlainTextLineBreaks(String text)
    {
        return text.replaceAll("\n", " ").replaceAll("\\s+", " ");
    }

    /**
     * Удаляет последний символ из строки.
     *
     * @param str
     * @return
     */
    @Nullable
    public static String removeLastCharacter(@Nullable String str)
    {
        if (!isEmpty(str))
        {
            str = str.substring(0, str.length() - 1);
        }
        return str;
    }

    /**
     * Удалить все символы, которые не могут использоваться в UUID
     *
     * @param str
     * @return
     */
    public static String removeNotUUIDSymbols(@Nullable String str)
    {
        if (!isEmpty(str))
        {
            str = str.replaceAll(UUID_SYMBOLS, EMPTY);
        }
        return str;
    }

    /**
     * Создание строки состоящей из повторений указанной строки,
     * указанного кол-ва раз.
     * @param str исходная строка
     * @param count кол-во повторений
     * @return
     */
    public static String repeat(String str, int count)
    {
        if (count <= 0)
        {
            return EMPTY;
        }
        StringBuilder sb = new StringBuilder(str.length() * count);
        while (count > 0)
        {
            sb.append(str);
            --count;
        }
        return sb.toString();
    }

    /**
     * Для посимвольной замены с заданым соответсвием
     * @param str изменяемая строка
     * @param map соответсвия строк символу
     */
    public static String replace(String str, Map<Character, String> map)
    {
        StringBuilder result = new StringBuilder();
        Set<Character> characters = map.keySet();
        for (Character ch : str.toCharArray())
        {
            result.append(characters.contains(ch) ? map.get(ch) : ch);
        }
        return result.toString();
    }

    /**
     * Заменяет непечатные пробелы в строке
     *
     * @param str изменяемая строка
     * @return измененная строка
     */
    public static String replaceNonPrintableSpaces(String str)
    {
        return str.replaceAll("^[" + StringUtilities.ALL_SPACES + "]*|[" + StringUtilities.ALL_SPACES + "]*$", " ");
    }

    /**
     * Возвращает суффикс для соответствующего числа
     * @param n число
     * @return суффикс [one] или [few] или [many]
     */
    public static String selectPlural(int n)
    {
        if (n % 10 == 1 && n % 100 != 11)
        {
            return "[one]";
        }
        if (n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20))
        {
            return "[few]";
        }
        return "[many]";
    }

    /**
     * Проверяет, начинается ли строка с подстроки с учетом регистра букв
     * @param str
     * @param prefix
     * @return
     */
    public static boolean startsWith(String str, String prefix)
    {
        return startsWith(str, prefix, false);
    }

    /**
     * Проверяет, начинается ли строка с подстроки без учета регистра букв
     * @param str
     * @param prefix
     * @return
     */
    public static boolean startsWithIgnoreCase(String str, String prefix)
    {
        return startsWith(str, prefix, true);
    }

    /**
     * @param input исходная строка
     * @param target искомая подстрока
     * @return Возвращает строку от начала строки до указанной подстроки
     */
    public static String substringBefore(String input, String target)
    {
        return contains(input, target) ? input.substring(0, input.indexOf(target)) : input;
    }

    /**
     * <PRE>
     *
     * Quotes the following characters with their HTML entity equivalents: < -
     * &lt; " - &quot; & - &amp; Returns the empty string if the argument is
     * null.
     *
     * </PRE>
     */
    public static String toNonMarkupString(String in)
    {
        return toNonMarkupString(in, false);
    }

    /**
     * <PRE>
     *
     * Quotes the following characters with their HTML entity equivalents: < -
     * &lt; " - &quot; & - &amp; Returns the empty string if the argument is
     * null.
     *
     * </PRE>
     */
    public static String toNonMarkupString(String in, boolean escapeSpace)
    {
        if (null == in)
        {
            return ""; //$NON-NLS-1$
        }
        int l = in.length();
        if (0 == l)
        {
            return ""; //$NON-NLS-1$
        }
        char[] charArr = in.toCharArray();

        final StringBuilder buffer = new StringBuilder(2 * l);
        for (int i = 0; i < l; i++)
        {
            char c = charArr[i];
            switch (c)
            {
                case ' ':
                    buffer.append(escapeSpace ? C_NBSP : C_SPACE);
                    break;
                case '<':
                    buffer.append(C_LT);
                    break;
                case '\"':
                    buffer.append(C_QUOT);
                    break;
                case '&':
                    buffer.append(C_AMP);
                    break;
                case '>':
                    buffer.append(C_QT);
                    break;
                default:
                    buffer.append(c);
                    break;
            }
        }

        return buffer.toString();
    }

    public static String toNonNullString(@Nullable String str)
    {
        return isEmpty(str) ? EMPTY : str;
    }

    @Nullable
    public static String toString(@Nullable Object object)
    {
        if (null != object)
        {
            return object.toString();
        }
        return null;
    }

    public static String toStringNullSafe(@Nullable Object object)
    {
        if (null != object)
        {
            return object.toString();
        }
        return EMPTY;
    }

    public static String trim(String strValue)
    {
        strValue = strValue.replaceAll("^[" + ALL_SPACES + "]*|[" + ALL_SPACES + "]*$", " ");
        return strValue.trim();
    }

    /**
     * Рекурсивно удаляет указанные символы из начала и окончания строки.
     * @param str исходная строка
     * @param characters удаляемые символы
     * @return строка, не начинающаяся и не оканчивающаяся указанными символами
     */
    @Nullable
    public static String trimChars(@Nullable String str, char[] characters)
    {
        if (isEmpty(str))
        {
            return str;
        }
        while (!str.isEmpty() && Chars.contains(characters, str.charAt(0)))
        {
            str = str.substring(1);
        }
        while (!str.isEmpty() && Chars.contains(characters, str.charAt(str.length() - 1)))
        {
            str = str.substring(0, str.length() - 1);
        }
        return str;
    }

    /**
     * Рекурсивно удаляет указанные символы, если они находятся одновременно и в начале, и в конце строки.
     * @param str исходная строка
     * @param characters удаляемые символы
     * @return строка с удаленными окружающими символами
     */
    @Nullable
    public static String trimSurroundingChars(@Nullable String str, char[] characters)
    {
        if (isEmpty(str))
        {
            return str;
        }
        while (str.length() > 1 && str.charAt(0) == str.charAt(str.length() - 1)
               && Chars.contains(characters, str.charAt(0)))
        {
            str = str.substring(1, str.length() - 1);
        }
        return str;
    }

    public static String trimToEmpty(@Nullable String str)
    {
        return str == null ? EMPTY : str.trim();
    }

    /** Проверяет, начинается ли строка с подстроки
     * @param str
     * @param prefix
     * @param ignoreCase
     * @return
     */
    private static boolean startsWith(@Nullable String str, @Nullable String prefix, boolean ignoreCase)
    {
        if (str == null || prefix == null)
        {
            return str == null && prefix == null;
        }
        if (prefix.length() > str.length())
        {
            return false;
        }
        return str.regionMatches(ignoreCase, 0, prefix, 0, prefix.length());
    }

    /**
     * Возвращает число вхождений символа ch в строке str
     *
     * @param str строка, в которой будут посчитаны символы
     * @param ch символ, количество которого необходимо посчитать
     */
    public static int countOccurrencesOf(String str, char ch)
    {
        int count = 0;
        for (char symbol : str.toCharArray())
        {
            if (symbol == ch)
            {
                count++;
            }
        }
        return count;
    }

    /**
     * Удаление из строки всех вхождений пустых тэгов <tr></tr>
     * @param htmlText текст
     * @return
     */
    public static String cutEmptyTRTag(String htmlText)
    {
        return htmlText.replaceAll("<tr[^>]*>\\W*</tr>", "");
    }
}
