package ru.naumen.core.shared;

import ru.naumen.metainfo.shared.CoreTitled;

/**
 * Интерфейс объекта значения агрегирующего атрибута
 *
 * <AUTHOR>
 * @since 22.09.2023
 */
public interface CoreAggregateContainer extends CoreTitled, IUUIDIdentifiable
{
    /**
     * @return команда
     */
    IUUIDIdentifiable getTeam();

    /**
     * @return сотрудник
     */
    IUUIDIdentifiable getEmployee();

    /**
     * @return отдел
     */
    IUUIDIdentifiable getOu();

    /**
     * @return основной объект агрегирующего атрибута
     */
    IUUIDIdentifiable getMain();
}
