package ru.naumen.core.shared;

/**
 * Интерфейс объекта "Гиперссылка"
 *
 * <AUTHOR>
 * @since 22.09.2023
 */
public interface CoreHyperLink
{
    /**
     * @return текст, к которому привязана гиперссылка
     */
    String getText();

    /**
     * Установить текст, к которому привязана гиперссылка
     * @param text текст
     */
    void setText(String text);

    /**
     * @return ссылка
     */
    String getURL();
}
