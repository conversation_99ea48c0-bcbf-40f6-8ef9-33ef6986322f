package ru.naumen.cti.server;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.inject.Inject;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.sec.server.encryption.EncryptionService;
import ru.naumen.core.server.metastorage.JaxbStorageSerializer;
import ru.naumen.core.server.metastorage.StorageSerializer;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.cti.shared.elements.CtiConfig;

/**
 * <AUTHOR>
 * @since 18.07.2013
 */
@Configuration
public class CtiSerializersConfig
{
    @Inject
    private XmlUtils xmlUtils;
    @Inject
    private EncryptionService encryptionService;
    @Inject
    private MessageFacade messages;
    @Inject
    private ConfigurationProperties configurationProperties;

    @Bean
    public StorageSerializer<CtiConfig> getCtiConfigSerializer()
    {
        JaxbStorageSerializer<CtiConfig> serializer = new JaxbStorageSerializer<>();
        serializer.setMetaStorageType("CtiConfig");
        serializer.setJaxbPackage(CtiConfig.class.getPackage().getName());
        serializer.setXmlUtils(xmlUtils);
        serializer.setEncryptionService(encryptionService);
        serializer.setMessageFacade(messages);
        serializer.setConfigurationProperties(configurationProperties);
        return serializer;
    }
}
