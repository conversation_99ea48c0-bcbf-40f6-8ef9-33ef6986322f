package ru.naumen.cti.server;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.metastorage.MetaStorageException;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.cti.shared.elements.CtiConfig;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.metainfo.server.spi.IServiceInitializer;

/**
 * <AUTHOR>
 * @since 12.08.2013
 */
@Component
public class DbCtiSettingsInitializer implements IServiceInitializer<CtiSettingsServiceBean>
{
    private final MetaStorageService metaStorage;

    @Inject
    public DbCtiSettingsInitializer(MetaStorageService metaStorage)
    {
        this.metaStorage = metaStorage;
    }

    @Override
    public void initialize(CtiSettingsServiceBean service)
    {
        CtiConfig ctiConfig;
        try
        {
            ctiConfig = metaStorage.get(Constants.CTI_CONFIG, Constants.SINGLE_KEY);
        }
        catch (MetaStorageException e)
        {
            ctiConfig = new CtiConfig();
        }
        service.saveCtiServerConfig(ctiConfig);
    }
}
