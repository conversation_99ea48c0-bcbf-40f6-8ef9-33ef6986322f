package ru.naumen.cti.server.ncc.messages;

import jakarta.annotation.Nullable;

import org.apache.commons.lang3.builder.ToStringBuilder;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Событие, оповещающее о создании (поступлении или осуществлении) в программном телефоне телефонного вызова 
 * <AUTHOR>
 * @since 14 февр. 2017 г.	
 */
public class NewCall extends BaseNCCMessage implements IHasCallInfoNCCMessage
{

    private final static long serialVersionUID = 6295551815915490035L;
    @SerializedName("cid")
    @Expose
    private int cid;
    @SerializedName("creationTime")
    @Expose
    private int creationTime;
    @SerializedName("direction")
    @Expose
    private Direction direction;
    @SerializedName("displayName")
    @Expose
    private String displayName;
    @SerializedName("hold")
    @Expose
    private boolean hold;
    @SerializedName("login")
    @Expose
    private String login;
    @SerializedName("muted")
    @Expose
    private boolean muted;
    @SerializedName("params")
    @Expose
    private Params params;
    @SerializedName("state")
    @Expose
    private State state;
    @SerializedName("type")
    @Expose
    private String type;

    public int getCid()
    {
        return cid;
    }

    public int getCreationTime()
    {
        return creationTime;
    }

    @Override
    public Direction getDirection()
    {
        return direction;
    }

    public String getDisplayName()
    {
        return displayName;
    }

    @Override
    public String getLogin()
    {
        return login;
    }

    @Override
    @Nullable
    public Params getParams()
    {
        return params;
    }

    @Override
    public State getState()
    {
        return state;
    }

    public String getType()
    {
        return type;
    }

    @Override
    public boolean isAnswered()
    {
        return false;
    }

    @Override
    public boolean isEnded()
    {
        return false;
    }

    public boolean isHold()
    {
        return hold;
    }

    public boolean isMuted()
    {
        return muted;
    }

    public NewCall setCid(int cid)
    {
        this.cid = cid;
        return this;
    }

    public NewCall setCreationTime(int creationTime)
    {
        this.creationTime = creationTime;
        return this;
    }

    public NewCall setDirection(Direction direction)
    {
        this.direction = direction;
        return this;
    }

    public NewCall setDisplayName(String displayName)
    {
        this.displayName = displayName;
        return this;
    }

    public NewCall setHold(boolean hold)
    {
        this.hold = hold;
        return this;
    }

    public NewCall setLogin(String login)
    {
        this.login = login;
        return this;
    }

    public NewCall setMuted(boolean muted)
    {
        this.muted = muted;
        return this;
    }

    public NewCall setParams(@Nullable Params params)
    {
        this.params = params;
        return this;
    }

    public NewCall setState(State state)
    {
        this.state = state;
        return this;
    }

    public NewCall setType(String type)
    {
        this.type = type;
        return this;
    }

    @Override
    public String toString()
    {
        return ToStringBuilder.reflectionToString(this);
    }

}
