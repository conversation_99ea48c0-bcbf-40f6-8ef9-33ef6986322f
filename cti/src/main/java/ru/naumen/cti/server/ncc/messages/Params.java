package ru.naumen.cti.server.ncc.messages;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ToStringBuilder;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import ru.naumen.cti.server.ncc.messages.IHasCallInfoNCCMessage.Direction;

/**
 * Параметры обращений
 * <AUTHOR>
 * @since 14 февр. 2017 г.	
 */
public class Params implements Serializable
{

    private final static long serialVersionUID = -662311015730512506L;
    @SerializedName("agentId")
    @Expose
    private String agentId;
    @SerializedName("id")
    @Expose
    private String id;
    @SerializedName("MusicOnHold")
    @Expose
    private String musicOnHold;
    @SerializedName("baseId")
    @Expose
    private String baseId;
    @SerializedName("called")
    @Expose
    private String called;
    @SerializedName("caller")
    @Expose
    private String caller;
    @SerializedName("direction")
    @Expose
    private Direction direction;
    @SerializedName("seanceId")
    @Expose
    private String seanceId;
    @SerializedName("sessionId")
    @Expose
    private String sessionId;
    @SerializedName("url")
    @Expose
    private String url;

    public String getAgentId()
    {
        return agentId;
    }

    public String getBaseId()
    {
        return baseId;
    }

    public String getCalled()
    {
        return called;
    }

    public String getCaller()
    {
        return caller;
    }

    public Direction getDirection()
    {
        return direction;
    }

    public String getId()
    {
        return id;
    }

    public String getMusicOnHold()
    {
        return musicOnHold;
    }

    public String getSeanceId()
    {
        return seanceId;
    }

    public String getSessionId()
    {
        return sessionId;
    }

    public String getUrl()
    {
        return url;
    }

    public Params setAgentId(String agentId)
    {
        this.agentId = agentId;
        return this;
    }

    public Params setBaseId(String baseId)
    {
        this.baseId = baseId;
        return this;
    }

    public Params setCalled(String called)
    {
        this.called = called;
        return this;
    }

    public Params setCaller(String caller)
    {
        this.caller = caller;
        return this;
    }

    public Params setDirection(Direction direction)
    {
        this.direction = direction;
        return this;
    }

    public Params setId(String id)
    {
        this.id = id;
        return this;
    }

    public Params setMusicOnHold(String musicOnHold)
    {
        this.musicOnHold = musicOnHold;
        return this;
    }

    public Params setSeanceId(String seanceId)
    {
        this.seanceId = seanceId;
        return this;
    }

    public Params setSessionId(String sessionId)
    {
        this.sessionId = sessionId;
        return this;
    }

    public Params setUrl(String url)
    {
        this.url = url;
        return this;
    }

    @Override
    public String toString()
    {
        return ToStringBuilder.reflectionToString(this);
    }

}
