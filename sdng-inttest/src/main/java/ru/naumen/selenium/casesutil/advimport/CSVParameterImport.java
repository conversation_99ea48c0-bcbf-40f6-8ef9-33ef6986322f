package ru.naumen.selenium.casesutil.advimport;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import ru.naumen.selenium.casesutil.file.GUIFileAdmin;
import ru.naumen.selenium.casesutil.model.CleanerGroup;
import ru.naumen.selenium.casesutil.model.IForRemoveModel;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.element.SEAdvimport;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.util.FileUtils;
import ru.naumen.selenium.util.RandomUtils;
import ru.naumen.selenium.util.StringUtils;

/**
 * Класс описывает структуру импортируемых данных из csv-файла
 * <AUTHOR>
 * @since 02.08.2012
 */
public class CSVParameterImport extends CleanerGroup implements IForRemoveModel, IParameterImport
{
    /**Список кодов импортируемых атрибутов (используются в "шапке" csv-файла).*/
    private List<String> attrs;
    /**Список соответствий: код атрибута - значение (используется для хранения данных csv-файла).*/
    private List<ModelMap> data;
    /**Список кодов атрибутов, по которым осуществляется поиск при импорте.*/
    private List<String> attrsForSearch;
    /**Метакласс импортируемых объектов.*/
    private MetaClass metaclassForSearch;
    /**Разделитель, используемый в csv-файле.*/
    private String delimiter = ";";
    /**Параметр, используемый в конфигурации импорта для создания поля выбора файла на форме запуска импорта
     * (используется для составления xpath'а до поля на форме запуска импорта).*/
    private String name;
    /**Название поля выбора файла на форме запуска импорта.*/
    private String title;
    /**Полное путь до csv-файла.*/
    private String filename;
    /**Кодировка по умолчанию (можно передать в качестве параметра в конструктор)*/
    private String encoding = "UTF-8";

    /**
     * Конструктор структуры csv-файла с данными,заполняет основные поля (name="file")
     * (Если создается csv-файл без заголовка, то нужно заполнить импортируемые атрибуты методом setAttrs
     * (List<String> attrs))
     * @param metaclassForSearch метакласс импортируемых объектов
     * @param attrsForSearch Список кодов атрибутов, по которым осуществляется поиск при импорте
     */
    public CSVParameterImport(MetaClass metaclassForSearch, String... attrsForSearch)
    {
        this("file", metaclassForSearch, attrsForSearch);
    }

    /**
     * Конструктор структуры csv-файла с данными,заполняет основные поля
     * (Если создается csv-файл без заголовка, то нужно заполнить импортируемые атрибуты методом setAttrs
     * (List<String> attrs))
     * @param name код поля на форме запуска импорта
     * @param metaclassForSearch метакласс импортируемых объектов
     * @param attrsForSearch Список кодов атрибутов, по которым осуществляется поиск при импорте
     */
    public CSVParameterImport(String name, MetaClass metaclassForSearch, String... attrsForSearch)
    {
        this();
        setData(new ArrayList<>());
        setMetaclassForSearch(metaclassForSearch);
        setAttrsForSearch(Arrays.asList(attrsForSearch));
        setName(name);
        setTitle(ModelUtils.createTitle());
    }

    /**
     * Конструктор структуры csv-файла с данными,заполняет основные поля
     * (Если создается csv-файл без заголовка, то нужно заполнить импортируемые атрибуты методом setAttrs
     * (List<String> attrs))
     * @param name код поля на форме запуска импорта
     * @param encoding кодировка csv-файла
     * @param metaclassForSearch метакласс импортируемых объектов
     * @param attrsForSearch Список кодов атрибутов, по которым осуществляется поиск при импорте
     */
    public CSVParameterImport(String name, String encoding, MetaClass metaclassForSearch, String... attrsForSearch)
    {
        this();
        setData(new ArrayList<>());
        setMetaclassForSearch(metaclassForSearch);
        setAttrsForSearch(Arrays.asList(attrsForSearch));
        setName(name);
        setTitle(ModelUtils.createTitle());
        this.encoding = encoding;
    }

    /**
     * Конструктор по умолчанию
     */
    private CSVParameterImport()
    {
    }

    /**
     * Добавление строки с данными в csv-файл
     * @param strings список значений импортируемых атрибутов
     * (количество должно совпадать с количеством кодов атрибутов, указанных в заголовке csv-файла)
     * @return модель импортируемых данных из csv-файла
     */
    public CSVParameterImport add(String... strings)
    {
        //Проверяем, что указаны коды атрибутов, которые импортируем
        if (getAttrs() == null || getAttrs().isEmpty())
        {
            throw new ErrorInCodeException(
                    "Перед добавлением строк csv-файла необходимо указать атрибуты, которые импортируются.");
        }
        //Проверяем, что в строке с данными укзано столько же атрибутов, сколько импортируется
        if (getAttrs().size() != strings.length)
        {
            throw new ErrorInCodeException(String.format(
                    "Количество передаваемых атрибутов(%d) в csv-файл не соответсвует количеству импортируемых "
                    + "атрибутов(%d).",
                    strings.length, attrs.size()));
        }
        ModelMap string = ModelMap.newMap();
        int i = 0;
        for (String str : strings)
        {
            string.put(getAttrs().get(i), str);
            i++;
        }
        getData().add(string);
        return this;
    }

    /**
     * Добавление заголовка с кодами импортируемых атрибутов в csv-файл
     * @param strings список кодов атрибутов
     * @return модель импортируемых данных из csv-файла
     */
    public CSVParameterImport addHeader(String... strings)
    {
        if (getAttrs() == null)
        {
            setAttrs(new ArrayList<>());
        }
        else
        {
            getAttrs().clear();
        }
        getAttrs().addAll(Arrays.asList(strings));
        return this;
    }

    @Override
    public List<ScriptElement> addToRemoveScript()
    {
        List<ScriptElement> elements = new ArrayList<>();
        //Через скриптовый элемент ищем по атрибутам импортируемые объекты и добавляем их в очередь на удаление
        for (ModelMap map : getData())
        {
            Attribute[] attributes = new Attribute[getAttrsForSearch().size()];
            int i = 0;
            for (String str : getAttrsForSearch())
            {
                Attribute attr = new Attribute();
                attr.setCode(str);
                attr.setValue(map.get(str));
                attributes[i++] = attr;
            }
            elements.add(SEAdvimport.deleteObjectByAttrs(getMetaclassForSearch().getFqn(), attributes));
        }
        return elements;
    }

    @Override
    public void clear()
    {
        if (filename != null)
        {
            FileUtils.forceDelete(filename);
        }
        Cleaner.push(this);
    }

    @Override
    public void fillAdvimportForm(String value)
    {
        Object[] args = { name };
        GUIFileAdmin.uploadFile("//*[contains(@id, 'gwt-debug-%s')]", value, args);
    }

    public List<String> getAttrs()
    {
        return attrs;
    }

    public List<String> getAttrsForSearch()
    {
        return attrsForSearch;
    }

    public List<ModelMap> getData()
    {
        return data;
    }

    public String getDelimiter()
    {
        return delimiter;
    }

    public MetaClass getMetaclassForSearch()
    {
        return metaclassForSearch;
    }

    @Override
    public String getName()
    {
        return name;
    }

    @Override
    public String getTitle()
    {
        return title;
    }

    @Override
    public String getValue()
    {
        filename = FileUtils.getAbsolutePath("ATestData" + RandomUtils.randomNumber(1000000) + ".csv");
        FileUtils.saveDataInFile(filename, buildCSVFile(), encoding);
        return filename;
    }

    public void setAttrs(List<String> attrs)
    {
        this.attrs = attrs;
    }

    public void setAttrsForSearch(List<String> attrsForSearch)
    {
        this.attrsForSearch = attrsForSearch;
    }

    public void setData(List<ModelMap> data)
    {
        this.data = data;
    }

    public void setDelimiter(String delimiter)
    {
        this.delimiter = delimiter;
    }

    public void setMetaclassForSearch(MetaClass metaclassForSearch)
    {
        this.metaclassForSearch = metaclassForSearch;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    /**
     * Построение содержимого csv-файла
     * @return содержимое csv-файла
     */
    private String buildCSVFile()
    {
        StringBuilder result = new StringBuilder();
        if (getAttrs() != null && !getAttrs().isEmpty())
        {
            result.append(StringUtils.join(getAttrs(), getDelimiter()));
            result.append('\n');
        }
        for (ModelMap string : getData())
        {
            List<String> list = new ArrayList<>();
            for (String attr : getAttrs())
            {
                list.add(string.get(attr));
            }
            result.append(StringUtils.join(list, getDelimiter()));
            result.append('\n');

        }
        return result.toString();
    }
}
