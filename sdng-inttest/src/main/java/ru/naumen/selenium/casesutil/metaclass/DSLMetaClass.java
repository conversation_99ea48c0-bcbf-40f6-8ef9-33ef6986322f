package ru.naumen.selenium.casesutil.metaclass;

import static ru.naumen.selenium.core.WaitTool.WAIT_TIME;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.Assert;

import com.google.common.base.Function;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.gdata.util.common.base.StringUtil;

import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.IRemoveOperation;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.RemoveOperation;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOMetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.element.SEMetaClass;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.modules.IModuleMetaClass;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.RandomUtils;

/**
 * Утилитарные методы для всех метаклассов
 * <AUTHOR>
 * @since 30.09.2011
 */
public class DSLMetaClass
{
    /**
     * Коды групп атрибутов для контента списка файлов
     */
    public enum FileListAttrGroup
    {
        addForm, search, system
    }

    /**
     * Вкладка карточки метакласса
     * <AUTHOR>
     * @since 7.12.2011
     */
    public enum MetaclassCardTab
    {
        //@formatter:off
        /**Атрибуты.*/
        ATTRIBUTES("Attributes"),
        /**Группы атрибутов.*/
        ATTRIBUTGROUPS("Groups"),
        /**Карточка объекта.*/
        OBJECTCARD("__window__"),
        /**Форма добавления.*/
        NEWENTRYFORM("newEntryForm"),
        /**Форма редактирования.*/
        EDITFORM("editForm"),
        /**Другие формы.*/
        CUSTOMFORM("customForm"),
        /**Форма смены типа.*/
        CHANGECASEFORM("changeCaseForm"),
        /**Жизненный цикл.*/
        LIFECYCLE("wf"),
        /**Права доступа.*/
        PERMISSIONSETTINGS("PermissionSettings"),
        /**Передача ответственности */
        RESP_TRANSFER("ResponsibilityTransfer"),
        /**Поиска настройка */
        SEARCHSETTINGS("fts");
        //@formatter:on

        /**
         * Получаем элемент {@link MetaclassCardTab} по строке
         * @param value строка по которой получаем MetaclassCardTab
         * @return элемент MetaclassCardTab
         */
        public static MetaclassCardTab getMetaclassCardTab(String value)
        {
            for (MetaclassCardTab tab : MetaclassCardTab.values())
            {
                if (tab.get().equals(value))
                {
                    return tab;
                }
            }
            throw new ErrorInCodeException(
                    String.format("Не найден элемент MetaclassCardTab сопоставимый с переданным значением: %s", value));
        }

        private String xpathTab;

        MetaclassCardTab(String xpathTab)
        {
            this.xpathTab = xpathTab;
        }

        public String get()
        {
            return xpathTab;
        }
    }

    /**
     * Получить модуль для работы с метаклассами
     */
    public static IModuleMetaClass getMetaclassModule()
    {
        return ScriptModules.getModuleMetaclass();
    }

    public static final String MATRIX_HINT = "Из команды %s в команду %s";

    /**
     * Добавить в систему метаклассы определенные их моделями
     * @param models модели метаклассов для добавления
     */
    public static void add(MetaClass... models)
    {
        List<Map<String, String>> metaclasses = new ArrayList<>();
        String cleaningUuid = RandomUtils.randomUUID();
        for (MetaClass model : models)
        {
            Map<String, String> properties = getAddMetaClassProperties(model);
            metaclasses.add(properties);
            model.setExists(true);
            model.setCleaningUuid(cleaningUuid);
        }

        getMetaclassModule().addMetaclass(metaclasses);
    }

    /**
     * Добавить всевозможные переходы в матрицу передачи ответственности между командами
     * @param metaClass метакласс, в котором редактируется матрица
     * @param teams набор моделей команд
     */
    public static void addAllResponsibleTransfer(MetaClass metaClass, Bo... teams)
    {
        List<String> uuids = (Lists.newArrayList(teams)).stream().map(i ->
        {
            return i.getUuid();
        }).collect(Collectors.toList());

        addAllResponsibleTransfer(metaClass, uuids.toArray(new String[uuids.size()]));
    }

    /**
     * Добавить всевозможные переходы в матрицу передачи ответственности между командами
     * @param metaClass метакласс, в котором редактируется матрица
     * @param uuids набор uuid-ов команд
     */
    public static void addAllResponsibleTransfer(MetaClass metaClass, String... uuids)
    {
        for (String from : uuids)
        {
            for (String to : uuids)
            {
                if (!from.equals(to))
                {
                    getMetaclassModule().addResponsibleTransfer(metaClass.getFqn(), from, to);
                }
            }
        }
    }

    /**
     * Добавить всевозможные переходы в матрицу передачи ответственности между всеми командами во всех типах запросов
     */
    public static void addAllResponsibleTransferForAllScCase()
    {
        getMetaclassModule().addAllResponsibleTransferForAllScCase();
    }

    /**
     * Добавить всевозможные переходы в матрицу передачи ответственности между командами во всех типах запросов
     * @param uuids набор uuid-ов команд
     */
    public static void addAllResponsibleTransferForAllScCase(String... uuids)
    {
        for (MetaClass metaClass : getChildMetaClasses(DAOScCase.createClass()))
        {
            addAllResponsibleTransfer(metaClass, uuids);
        }
    }

    /**
     * Добавить переходы в матрицу передачи ответственности между командами
     * @param metaClass метакласс, в котором редактируется матрица
     * @param fromTeam модель команды, из которой добавляется переход
     * @param toTeams набор моделей команд, в которые добавляется переход
     */
    public static void addResponsibleTransfer(MetaClass metaClass, Bo fromTeam, Bo... toTeams)
    {
        for (Bo to : toTeams)
        {
            getMetaclassModule().addResponsibleTransfer(metaClass.getFqn(), fromTeam.getUuid(), to.getUuid());
        }
    }

    /**
     * Установить значения в матрице передачи ответственного
     * @param metaClass метакласс, в котором редактируется матрица 
     * @param teams массив команд
     * В массиве должно быть четное число элементов:<br>
     * <ol>
     * <li>2i - из какой команды можно передавать</li>
     * <li>2i + 1 - в какую команду из команды 2i передаем</li> 
     */
    public static void addResponsibleTransferPairs(MetaClass metaClass, Bo... teams)
    {
        Preconditions.checkArgument(teams.length % 2 == 0);
        for (int index = 0; index < teams.length; index += 2)
        {
            Bo team1 = teams[index];
            Bo team2 = teams[index + 1];
            addResponsibleTransfer(metaClass, team1, team2);
        }
    }

    /**
     * Поместить в архив метаклассы определенные их моделями
     * @param models модели метаклассов для архивирования
     */
    public static IRemoveOperation archive(MetaClass... models)
    {
        List<String> metaclasses = new ArrayList<>();
        for (MetaClass model : models)
        {
            metaclasses.add(model.getFqn());
            model.setArchived(true);
        }

        getMetaclassModule().archiveMetaclasses(metaclasses);

        List<ScriptElement> elements = Stream.of(models).map(SEMetaClass::restore).collect(Collectors.toList());
        Runnable afterTest = () ->
        {
            Stream.of(models).forEach(MetaClass::setActive);
        };
        return new RemoveOperation(elements, afterTest, true);
    }

    /**
     * Проверка отсутствия метакласса в системе
     * @param model модель метакласса
     */
    public static void assertAbsence(MetaClass model)
    {
        boolean isPresent = DSLMetaClass.isPresent(model.getFqn());
        long timeEdge = System.currentTimeMillis() + WaitTool.WAIT_TIME * 1000;
        while (System.currentTimeMillis() < timeEdge && isPresent)
        {
            WaitTool.waitMills(500);
            isPresent = DSLMetaClass.isPresent(model.getFqn());
        }
        model.setExists(isPresent);
        Assert.assertFalse("Метакласс с уникальным идентификатором присутствует в системе FQN: " + model.getFqn(),
                isPresent);
    }

    /**
     * Проверка метакласс в архиве
     * @param model модель метакласса
     */
    public static void assertArchived(MetaClass model)
    {
        Boolean isArchived = DSLMetaClass.isArchived(model);
        Assert.assertTrue("Метакласс не в архиве", isArchived);
    }

    /**
     * Проверка метакласс не в архиве
     * @param model модель метакласса
     */
    public static void assertNotArchived(MetaClass model)
    {
        long currentTime = System.currentTimeMillis();
        boolean isArchived = false;
        String message = String.format("Метакласс в архиве %s", model.getFqn());
        while (!isArchived && System.currentTimeMillis() - currentTime < WAIT_TIME * 1000)
        {
            WaitTool.waitMills(100);
            isArchived = DSLMetaClass.isPresent(model.getFqn());
        }
        Assert.assertTrue(message, isArchived);
        model.setArchived(isArchived);
    }

    /**
     * Проверка существования метакласса в системе
     * @param model модель метакласса
     */
    public static void assertPresent(MetaClass model)
    {
        Assert.assertTrue("Метакласс с уникальным идентификатором, не найден в системе FQN: " + model.getFqn(),
                DSLMetaClass.isPresent(model.getFqn()));
    }

    /**
     * Создать копию метакласс в системе
     * @param metaClass модель метакласса, копия которого создается
     * @return созданную модель метакласса
     */
    public static MetaClass copy(MetaClass metaClass)
    {
        MetaClass copy;
        if (metaClass.getFqn().contains("$"))
        {
            copy = DAOMetaClass.createCase(metaClass);
        }
        else
        {
            copy = DAOUserClass.create();
        }
        copy.setExists(true);

        getMetaclassModule().copyMetaclass(metaClass.getFqn(), copy.getFqn(), copy.getTitle(), copy.getDescription());

        return copy;
    }

    /**
     * Удаляем из системы метаклассы определенные их моделями
     * @param models модели метаклассов для удаления
     */
    public static void delete(MetaClass... models)
    {
        List<String> fqns = new ArrayList<>();
        for (MetaClass model : models)
        {
            fqns.add(model.getFqn());
            model.setExists(false);
        }

        getMetaclassModule().deleteMetaclasses(fqns);
    }

    /**
     * Редактировать метаклассы определенные их моделями
     * @param models модели метаклассов для редактирования
     */
    public static void edit(MetaClass... models)
    {
        List<Map<String, String>> metaclasses = new ArrayList<>();
        for (MetaClass model : models)
        {
            Map<String, String> properties = getEditMetaClassProperties(model);
            metaclasses.add(properties);
        }

        getMetaclassModule().editMetaclasses(metaclasses);
    }

    /**
     * Редактировать description метакласса
     * @param fqn fqn метакласса
     * @param description метакласса
     */
    public static void editDescription(String fqn, String description)
    {
        Map<String, String> metaclassProperties = new HashMap<>();
        metaclassProperties.put("fqn", fqn);
        metaclassProperties.put("description", description);
        editWithParameters(metaclassProperties);
    }

    /**
     * Редактировать title метакласса
     * @param fqn fqn метакласса
     * @param title метакласса
     * @param description метакласса
     */
    public static void editParameters(String fqn, String title, String description)
    {
        Map<String, String> metaclassProperties = new HashMap<>();
        metaclassProperties.put("fqn", fqn);
        metaclassProperties.put("title", title);
        metaclassProperties.put("description", description);
        editWithParameters(metaclassProperties);
    }

    /**
     * Редактировать вложенность метакласса
     * @param model модель метакласса для редактирования
     * @param parentRelFqn значение вложенности
     */
    public static void editParentRelFqn(MetaClass model, String parentRelFqn)
    {
        MetaClass oldMC = getMetaClass(model.getFqn());

        Map<String, String> properties = getEditParentRelFqnProperties(model, parentRelFqn);
        editWithParameters(properties);

        if (!StringUtil.equals(oldMC.getParentRelFqn(), parentRelFqn))
        {
            Cleaner.afterTest(() ->
            {
                Map<String, String> oldProperties = getEditParentRelFqnProperties(model, oldMC.getParentRelFqn());
                editWithParameters(oldProperties);

                DSLMetainfo.reloadSessionFactory();
            });
        }
    }

    /**
     * Редактировать вложенность метакласса для пользователского класса
     * @param model модель метакласса для редактирования
     * @param parentRelFqn значение вложенности
     */
    public static void editParentRelFqnForUserClass(MetaClass model, String parentRelFqn)
    {
        Map<String, String> properties = getEditParentRelFqnProperties(model, parentRelFqn);
        editWithParameters(properties);
    }

    /**
     * Устанавливает свойство "Разрешить создание плановых версий"<br>
     * После теста, свойство откатывается к старому значению!<br>
     * <b>Внимание! Использовать этот метод не рекомендуется, т.к. включение возможности
     * создания плановых версий - длительная операция.<br>
     * Лучше использовать {@link DAOUserClass#createWithPlannedVersions()}</b>
     */
    public static void editPlannedVersionsAllowed(MetaClass metaClass, boolean isPlannedVersionsAllowed)
    {
        String plannedVersionsAllowedOld = metaClass.isPlannedVersionsAllowed();
        metaClass.setPlannedVersionsAllowed(isPlannedVersionsAllowed);

        // Выбираем только те свойства, которые нужны
        Map<String, String> properties = new HashMap<>();
        properties.put("fqn", metaClass.getFqn());
        properties.put(MetaClass.IS_PLANNED_VERS_ALLOWED,
                Boolean.toString(isPlannedVersionsAllowed));
        getMetaclassModule().editMetaclasses(Collections.singletonList(properties));

        Cleaner.afterTest(() ->
        {
            metaClass.setPlannedVersionsAllowed(Boolean.parseBoolean(plannedVersionsAllowedOld));
            properties.put(MetaClass.IS_PLANNED_VERS_ALLOWED, plannedVersionsAllowedOld);
            getMetaclassModule().editMetaclasses(Collections.singletonList(properties));
        });
    }

    /**
     * Редактировать title метакласса
     * @param fqn fqn метакласса
     * @param title метакласса
     */
    public static void editTitle(String fqn, String title)
    {
        Map<String, String> metaclassProperties = new HashMap<>();
        metaclassProperties.put("fqn", fqn);
        metaclassProperties.put("title", title);
        editWithParameters(metaclassProperties);
    }

    /**
     * Получить свойства добавляемого метакласса в виде карты
     * @param model модель метакласса
     */
    public static Map<String, String> getAddMetaClassProperties(MetaClass model)
    {
        Map<String, String> properties = new HashMap<>();
        properties.put("classFqn", model.getParentFqn());
        properties.put("caseFqn", model.getFqn());
        properties.put("title", model.getTitle());
        properties.put("description", model.getDescription());
        properties.put("parentRelFqn", model.getParentRelFqn());
        properties.put("hasWorkflow", model.getHasWorkflow());
        properties.put("hasResponsible", model.getHasResponsible());
        properties.put("scCaseFqn", model.getDefaultScCase());
        properties.put("agreementUUID", model.getDefaultScAgreement());
        properties.put("serviceUUID", model.getDefaultScService());
        List<String> tagCodes = model.getTagCodes();
        properties.put("tags", Json.listToString(tagCodes.toArray(new String[tagCodes.size()])));
        properties.put(MetaClass.IS_PLANNED_VERS_ALLOWED, model.isPlannedVersionsAllowed());
        properties.put(MetaClass.DEPTH_ENVIRONMENT, model.getDepthEnvironment());
        properties.put(MetaClass.TAB_TITLE_ATTRIBUTE, model.getTabTitleAttribute());
        properties.put(MetaClass.SETTINGS_SET, model.getSettingsSet());
        return properties;
    }

    /**
     * Получить список вложенных метаклассов первого уровня
     * @param metaClass модель метакласса
     * @return список моделей вложенных метаклассов
     */
    public static List<MetaClass> getChildFirstLevel(MetaClass metaClass)
    {
        List<MetaClass> result = new ArrayList<>();
        for (Map<String, String> map : getMetaclassModule().getChildMetaClasses(metaClass.getFqn()))
        {
            MetaClass child = new MetaClass();
            child.setTitle(map.get("title"));
            child.setCode(map.get("code"));
            child.setDescription(map.get("description"));
            child.setFqn(map.get("fqn"));
            child.setParentFqn(map.get("parentFqn"));
            result.add(child);
        }
        return result;
    }

    /**
     * Получить список вложенных метаклассов всех уровней вложенности
     * @param metaClass модель метакласса
     * @return список моделей вложенных метаклассов
     */
    public static List<MetaClass> getChildMetaClasses(MetaClass metaClass)
    {
        List<MetaClass> result = new ArrayList<>();
        for (MetaClass child : getChildFirstLevel(metaClass))
        {
            result.add(child);
            result.addAll(getChildMetaClasses(child));
        }
        return result;
    }

    /**
     * Получить параметры запроса по умолчанию. 
     * @param metaClass модель метакласса, из которого получаем параметры
     * @return карту с ключами: defaultClientAgreement, defaultClientService, defaultServiceCallClassFqn
     */
    public static ModelMap getDefaultScParams(MetaClass metaClass)
    {
        return getMetaclassModule().getDefaultScParams(metaClass.getFqn());
    }

    /**
     * Получить свойства редактируемого метакласса в виде карты
     * @param model модель метакласса
     */
    public static Map<String, String> getEditMetaClassProperties(MetaClass model)
    {
        Map<String, String> properties = new HashMap<>();
        properties.put("fqn", model.getFqn());
        properties.put("title", model.getTitle());
        properties.put("description", model.getDescription());
        properties.put("defaultServiceCallClassFqn", model.getDefaultScCase());
        properties.put("defaultClientAgreement", model.getDefaultScAgreement());
        properties.put("defaultClientService", model.getDefaultScService());
        properties.put("responsibilityTransferTableEnabled", model.isResponsibilityTransferTableEnabled());
        properties.put(MetaClass.IS_PLANNED_VERS_ALLOWED, model.isPlannedVersionsAllowed());
        List<String> tagCodes = model.getTagCodes();
        properties.put("tags", Json.listToString(tagCodes.toArray(new String[tagCodes.size()])));
        properties.put(MetaClass.TAB_TITLE_ATTRIBUTE, model.getTabTitleAttribute());
        properties.put(MetaClass.SETTINGS_SET, model.getSettingsSet());
        return properties;
    }

    /**
     * Получить  метакласс по его FQN
     * @param fqn  метакласса
     * @return модель метакласса
     */
    public static MetaClass getMetaClass(String fqn)
    {
        Map<String, String> map = getMetaclassModule().getMetaClassByFqn(fqn);
        MetaClass metaClass = new MetaClass();
        metaClass.setTitle(map.get("title"));
        metaClass.setCode(map.get("code"));
        metaClass.setDescription(map.get("description"));
        metaClass.setFqn(map.get("fqn"));
        metaClass.setParentFqn(map.get("parentFqn"));
        metaClass.setParentRelFqn(map.get("parentRel"));
        metaClass.setHasWorkflow(map.get("wf"));
        metaClass.setHasResponsible(map.get("responsible"));
        return metaClass;
    }

    /**
     * Получить список родительских метаклассов
     * @param fqn идентификатор метакласса, у которого получаем родителей
     * @return список идентификаторов родительских метаклассов (чем левее, тем старше предок)
     */
    public static List<String> getParents(String fqn)
    {
        List<String> parentFqns = getMetaclassModule().getParents(fqn);
        Collections.reverse(parentFqns);
        return parentFqns;
    }

    /**
     * Проверяет помещен ли класс или тип в архив
     * @param metaClass Метакласс для проверки
     * @return true - в архиве, false - не в архиве
     */
    public static boolean isArchived(MetaClass metaClass)
    {
        WaitTool.waitMills(200);
        return getMetaclassModule().getArchived(metaClass.getFqn());
    }

    /**
     * Проверяет существование метакласса
     * @param fqn
     * @return существует ли метакласс
     */
    public static boolean isPresent(String fqn)
    {
        return getMetaclassModule().isPresent(fqn);
    }

    /**
     * Сбросить настройки ЖЦ для метакласса. 
     * @param model модель метакласса
     */
    public static void resetLifeCycle(MetaClass model)
    {
        getMetaclassModule().reset(model.getFqn());
    }

    /**
     * Восстановить из архива метаклассы определенные их моделями
     * @param models модели метаклассов для восстановления из архива
     */
    public static void restore(MetaClass... models)
    {
        List<String> metaclasses = new ArrayList<>();
        for (MetaClass model : models)
        {
            metaclasses.add(model.getFqn());
            model.setArchived(false);
        }
        getMetaclassModule().restoreMetaclasses(metaclasses);
    }

    /**
     * Изменить значение "Соглашение/Услуга" для "Параметры запроса по умолчанию"
     * @param metaClass метакласc для установки значения
     * @param agreement устанавливаемое значение соглашения (может быть null)
     * @param service устанавливаемое значение услуги (может быть null)
     * @param scCase тип запроса по умолчанию (может быть null)
     * @return операция отмены действия
     */
    public static void setScParams(MetaClass metaClass, @Nullable Bo agreement, @Nullable Bo service,
            @Nullable MetaClass scCase)
    {
        String oldDefaultScAgreement = metaClass.getDefaultScAgreement();
        String oldDefaultScService = metaClass.getDefaultScService();
        String oldDefaultScCase = metaClass.getDefaultScCase();
        metaClass.setDefaultScAgreement(agreement == null ? null : agreement.getUuid());
        metaClass.setDefaultScService(service == null ? null : service.getUuid());
        metaClass.setDefaultScCase(scCase == null ? null : scCase.getFqn());
        edit(metaClass);
        Cleaner.afterTest(true, () ->
        {
            metaClass.setDefaultScAgreement(oldDefaultScAgreement);
            metaClass.setDefaultScService(oldDefaultScService);
            metaClass.setDefaultScCase(oldDefaultScCase);
            edit(metaClass);
        });
    }

    /**
     * Устанавливает порядок статусов в ЖЦ для заданного метакласса
     * @param model модель метакласса, в котором настраиваем порядок статусов
     * @param order набор моделей метаклассов в нужном порядке
     */
    public static void setStatesOrder(MetaClass model, BoStatus... order)
    {
        List<String> codes = Lists.transform(Lists.newArrayList(order), new Function<BoStatus, String>()
        {
            @Override
            public String apply(BoStatus input)
            {
                if (input == null)
                {
                    throw new ErrorInCodeException("Статус не может быть null.");
                }
                return input.getCode();
            }

            ;
        });

        getMetaclassModule().orderStates(model.getFqn(), codes);
    }

    /**
     * Устанавливает порядок статусов в ЖЦ для заданного метакласса
     */
    public static void setStatesOrder(MetaClass model, List<String> order)
    {
        getMetaclassModule().orderStates(model.getFqn(), order);
    }

    public static void updateMetaClassMaxSearchResults(MetaClass metaClass, Integer value)
    {
        getMetaclassModule().updateMetaClassMaxSearchResults(metaClass.getFqn(), value.toString());
    }

    public static void updateMetaClassSearchResultOrder(Map<MetaClass, Integer> order)
    {
        getMetaclassModule().updateMetaClassSearchOrder(order.entrySet().stream()
                .collect(Collectors.toMap(e -> e.getKey().getFqn(), e -> e.getValue().toString())));
    }

    /**
     * Разорвать наследование настроек карточки в типе
     * @param metaClass тип, в котором нужно разорвать наследование
     */
    public static void editMetaClassCardUI(MetaClass metaClass)
    {
        getMetaclassModule().editMetaClassUI(metaClass.getFqn(), MetaclassCardTab.OBJECTCARD.get());
        Cleaner.afterTest(true, () ->
        {
            if (metaClass.isExists())
            {
                resetMetaClassCardUI(metaClass);
            }
        });
    }

    /**
     * Сбросить настройки карточки в типе
     * @param metaClass тип, в котором нужно сбросить наследование
     */
    public static void resetMetaClassCardUI(MetaClass metaClass)
    {
        getMetaclassModule().resetMetaClassUI(metaClass.getFqn(), MetaclassCardTab.OBJECTCARD.get());
    }

    private static void editWithParameters(Map<String, String> metaclassParameters)
    {
        List<Map<String, String>> metaclasses = new ArrayList<>();
        metaclasses.add(metaclassParameters);
        getMetaclassModule().editMetaclasses(metaclasses);
    }

    private static Map<String, String> getEditParentRelFqnProperties(MetaClass model, String parentRelFqn)
    {
        Map<String, String> properties = new HashMap<>();
        properties.put("fqn", model.getFqn());
        properties.put("parentRelFqn", parentRelFqn);
        return properties;
    }

    /**
     * Проверить, разорвано ли наследование в карточке метакласса, на формах добавления/редактирования
     * @param metaClassFqn идентификатор (fqn) метакласса, для которого осуществляется проверка
     * @return true - если наследование разорвано хотя бы на одной из форм или  вкарточке метакласса, false - если
     * наследование не разорвано
     */
    public static Boolean hasAnyUiFormOverride(String metaClassFqn)
    {
        return getMetaclassModule().hasMetaClassAnyUiFormOverride(metaClassFqn);
    }
}
