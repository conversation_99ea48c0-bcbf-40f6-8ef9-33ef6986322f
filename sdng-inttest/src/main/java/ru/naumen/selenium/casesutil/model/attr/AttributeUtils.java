package ru.naumen.selenium.casesutil.model.attr;

import static java.util.stream.Collectors.toList;

import java.util.Arrays;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;

import java.util.HashMap;

import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.SourceCodeLang;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.Strategy;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.util.DateTimeUtils;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитрные методы для работы с атрибутами
 * <AUTHOR>
 * @since 11.07.2012
 */
public class AttributeUtils
{
    /**
     * Подготовить агрегирующий атрибут Ответственный для добавления строки в таблицу соответствий, у которой в
     * качестве
     * определяемого или определяющего выступает агрегирующий атрибут
     *
     * Формат строки для значения должен выглядеть так: 
     * СтратегияНазначенияОтветственного:ОтветственнаяКоманда:ОтветственныйСотрудник
     *
     * @param objects набор БО - состовляющий агрегирующего атрибута Ответственный  
     * @return готовая строка для передачи в скрипт добавления строки в таблицу соответствий
     * @see VMapValueConverter
     */
    public static String prepareAggregateForResponsibleSettings(Bo team, Bo employee)
    {
        if (team == null && employee == null)
        {
            return null;
        }
        List<String> uuids = Lists.newArrayList(Strategy.EMPLOYEE.getId());
        for (Bo bo : Arrays.asList(team, employee))
        {
            String uuid = bo != null ? bo.getUuid() : "null";
            uuids.add(uuid);
        }
        return StringUtils.join(uuids, ":");
    }

    /**
     * Подготовить агрегирующий атрибут для добавления строки в таблицу соответствий, у которой в качестве 
     * определяемого или определяющего выступает агрегирующий атрибут
     * @param objects набор БО - состовляющий агрегирующего атрибута
     * @return готовая строка для передачи в скрипт добавления строки в таблицу соответствий 
     */
    public static String prepareAggregateForRuleSettings(Bo... objects)
    {
        return prepareObjectsForRuleSettings(ModelUtils.extractUuids(objects));
    }

    /**
     * Подготовить значение поля VALUE у модели атрибута типа Агрегирующий
     * @param uuids массив uuid-ов, из которых состоит агрегирующий атрибут, должен состоять
     * из uuid-а отдела или команды, при этом может содержать uuid сотрудника
     * @return возвращает строку подготовленную для установки в поле value
     */
    public static String prepareAggregateValue(String... uuids)
    {
        String ouUuid = null;
        String teamUuid = null;
        String employeeUuid = null;
        for (String uuid : uuids)
        {
            switch (StringUtils.substringBefore(uuid, "$"))
            {
                case "ou":
                    ouUuid = uuid;
                    break;
                case "team":
                    teamUuid = uuid;
                    break;
                case "employee":
                    employeeUuid = uuid;
                    break;
                default:
                    String msg = String.format(
                            "Неизвестный тип идентификатора (uuid-а) для атрибута типа агрегирующий " + "(uuid='%s')",
                            uuid);
                    throw new ErrorInCodeException(msg);
            }
        }
        return prepareAggregateValue(ouUuid, teamUuid, employeeUuid);
    }

    /**
     * Подготовить значение поля VALUE у модели атрибута типа Агрегирующий
     * <pre>
     * Возможны следующие сочетания аргументов, которые не приведут к ошибке:
     * (ouUuid == null, teamUuid == null, employeeUuid == null)
     * (ouUuid == null, teamUuid != null, employeeUuid == null || employeeUuid != null)
     * (ouUuid != null, teamUuid == null, employeeUuid == null || employeeUuid != null)
     * </pre>
     * @param ouUuid идентификатор отдела
     * @param teamUuid идентификатор команды
     * @param employeeUuid идентификатор сотрудника
     * @return возвращает строку подготовленную для установки в поле value
     */
    public static String prepareAggregateValue(String ouUuid, String teamUuid, String employeeUuid)
    {
        if (ouUuid == null && teamUuid == null && employeeUuid == null || ouUuid != null && teamUuid == null
            || ouUuid == null && teamUuid != null)
        {
            List<String> value = new ArrayList<>();
            if (employeeUuid != null)
            {
                value.add(employeeUuid);
            }
            if (teamUuid != null)
            {
                value.add(teamUuid);
            }
            if (ouUuid != null)
            {
                value.add(ouUuid);
            }

            return Json.GSON.toJson(value);
        }

        String msg = String.format("Данное сочетание параметров недопустимо для данного метода. "
                                   + "(ouUuid='%s'; teamUuid='%s'; employeeUuid='%s')", ouUuid, teamUuid, employeeUuid);
        throw new ErrorInCodeException(msg);
    }

    /**
     * Подготовить значение для добавления строки в таблицу соответствий, у которой в качестве 
     * определяемого или определяющего выступают атрибуты типов: Дата, Дата/время
     * @param dateTime дата в строковом представлении
     * @param formatterDateTime формат даты
     * @return готовая строка для передачи в скрипт добавления строки в таблицу соответствий 
     */
    public static String prepareDateForRuleSettings(String dateTime, String formatterDateTime)
    {
        Date date = DateTimeUtils.getDateFromString(dateTime, formatterDateTime, null);
        return !Config.isMssql()
                ? Json.GSON.toJson(date)
                : Json.GSON_MSSQL.toJson(date);
    }

    /**
     * Подготовить значение для заполнения атрибута DateTime
     * @param date дата в строковом представлении
     * @param time время в строковом представлении
     * @return JSON объект с датой и временем
     */
    public static String prepareDateTime(String date, String time)
    {
        return Json.mapToStringJson(ModelMap.newMap("date", date, "time", time));
    }

    /**
     * Подготовить значение поля VALUE у модели атрибута типа Гиперссылка
     * @param text текст гиперссылки
     * @param url адрес гиперссылки
     * @return возвращает строку подготовленную для установки в поле value
     */
    public static String prepareHyperlinkValue(String text, String url)
    {
        Map<String, String> value = new HashMap<>();
        value.put("url", url);
        value.put("text", text);
        return Json.GSON.toJson(value);
    }

    /**
     * Подготовить значение поля VALUE у модели атрибута типа Лицензия
     * @param codes коды лицензий
     * @return возвращает строку подготовленную для установки в поле value
     */
    @SuppressWarnings("unchecked")
    public static String prepareLicensesValue(String codes)
    {
        return StringUtils.join(Json.GSON.fromJson(codes, Set.class), ",");
    }

    /**
     * Подготовить значение для добавления строки в таблицу соответствий, у которой в качестве 
     * определяемого или определяющего выступают атрибуты типов: набор ссылок на БО, набор элементов справочников,
     * обратная ссылка
     * @param uuids набор uuid-ов объектов
     * @return готовая строка для передачи в скрипт добавления строки в таблицу соответствий 
     */
    public static String prepareObjectsForRuleSettings(String... uuids)
    {
        return Json.listToString(uuids);
    }

    /**
     * Сформировать значение атрибута текст с подсветкой синтаксиса для передачи в скриптовые модули.
     * @param text текст
     * @param lang язык из {@link SourceCodeLang}
     * @return строка для передачи в скрипт значения атрибута
     */
    public static String prepareSourceCode(String text, String lang)
    {
        return Json.GSON.toJson(ImmutableMap.of("text", text, "lang", lang));
    }

    /**
     * Подготовить значение для существующих элементов системного справочника
     * @param catalog системный справочник
     * @param codes коды существующих элементов справочника
     * @return JSON с uuid'ами справочников
     */
    public static String prepareSystemCatalogItems(SystemCatalog catalog, Set<String> codes)
    {
        //@formatter:off
        return Json.GSON.toJson(codes
                .stream()
                .map(code -> DSLCatalogItem.getExistingCatalogItem(catalog, code))
                .map(CatalogItem::getUuid)
                .collect(toList()));
        //@formatter:on
    }

    /**
     * Подготовить значение поля VALUE у модели атрибута типа Временной интервал
     * @param length длина промежутка времени
     * @param interval интервал ("SECOND", "MINUTE", "HOUR", "DAY", "WEEK")
     * @return возвращает строку подготовленную для установки в поле value
     */
    public static String prepareTimeInterval(String length, String interval)
    {
        Map<String, String> value = new HashMap<>();
        value.put("length", length);
        value.put("interval", interval);
        return Json.GSON.toJson(value);
    }
}
