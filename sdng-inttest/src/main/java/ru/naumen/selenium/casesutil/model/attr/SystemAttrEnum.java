package ru.naumen.selenium.casesutil.model.attr;

/**
 * Перечисление системных атрибутов
 * <AUTHOR>
 * @since Oct 13, 2015
 */
public enum SystemAttrEnum
{
    //@formatter:off
    ADDRESS("Адрес", "address"),
    AGREEMENT("Соглашение", "agreement"),
    AGREEMENTS("Связанные соглашения", "agreements"),
    ALERT_RULE("Правило определения тревоги", "alertRule"),
    ALL_GROUP("Группы пользователей сотрудника, отдела, команд", "all_Group"),
    ANOMALY_SEVERITY("Критичность аномалий", "severity"),
    AUDIT("Аудит", "audit"),
    AUDIT_STATE("Статус", "auditState"),
    AUTHOR("Автор", "author"),
    AUTHOR_LOGIN("Автор действия", "authorLogin"),
    AVAILABLE("Доступен", "available"),
    AVAILABLE_METRICS("Доступные метрики", "availableMetrics"),
    ALLOWED_START_HOUR("Час начала очистки", "allowedStartHour"),
    ALLOWED_END_HOUR("Час окончания очистки", "allowedEndHour"),
    BATCH_SIZE("Размер пачки", "batchSize"),
    BLOCK_DOWNLOADING("Запретить скачивание", "blockDownloading"),
    CALC_RULE("Правило вычисления", "calcRule"),
    CALC_RULE_TEMPLATE("Шаблон правило вычисления", "calcRuleTemplate"),
    CALL_CASES("Типы запросов", "callCases"),
    CATEGORIES("Категории запроса", "categories"),
    CHANNEL_AVAILABLE_DATE("Дата/время последней успешной проверки канала событий", "channelAvailableDate"),
    CITY_PHONE_NUMBER("Номер городского телефона", "cityPhoneNumber"),
    CLIENT("Контрагент", "client"),
    CLIENT_EMAIL("Контактный e-mail", "clientEmail"),
    CLIENT_EMPLOYEE("Контрагент (сотрудник)", "clientEmployee"),
    CLIENT_LINK_NAME("Информация о клиенте для отображения в интерфейсе","clientLinkName"),
    CLIENT_NAME("Контактное лицо", "clientName"),
    CLIENT_OU("Контрагент (отдел)", "clientOU"),
    CLIENT_PHONE("Контактный телефон", "clientPhone"),
    CLIENT_TEAM("Контрагент (команда)", "clientTeam"),
    CLOSED_BY("Кем закрыт", "closedBy"),
    CLOSED_BY_EMPLOYEE("Кем закрыт (сотрудник)", "closedByEmployee"),
    CLOSED_BY_TEAM("Кем закрыт (команда)", "closedByTeam"),
    CODE("Код", "code"),
    CODE_OF_CLOSING("Код закрытия", "codeOfClosing"),
    COMMENT("Комментарий", "@comment"),
    COMMENT_AUTHOR_ALIAS("Псевдоним", "commentAuthorAlias"),
    CONNECTION("Подключение", "connection"),
    CONNECTION_STRING("Строка подключения", "connectionString"),
    CONNECTION_TYPES("Типы подключений", "connectionTypes"),
    COPIES("Копии", "copies"),
    CREATION_DATE("Дата создания", "creationDate"),
    DATE_OF_BIRTH("Дата рождения", "dateOfBirth"),
    DAYS_OF_WEEK_NDAP("Повторять каждую неделю в следующие дни", "days"),
    DEAD_LINE_TIME("Регламентное время закрытия запроса", "deadLineTime"),
    DEPENDS_ON("Зависит от", "dependsOn"),
    DESCRIPTION("Описание", "description"),
    DESCRIPTION_RTF("Описание", "descriptionInRTF"),
    DESYNC_TYPE("Тип рассинхронизации", "desyncType"),
    DEVIATION("Отклонение", "deviation"),
    CORRIDOR_WIDTH("Ширина коридора", "corridorWidth"),
    DIRECTOR_HEAD("Директор отдела", "head"),
    DOMAIN("Домен пользователя", "domain"),
    DURATION("Продолжительность", "duration"),
    EDITED_OBJECTS("Отредактированы объекты", "editedObjects"),
    EMAIL("Адрес электронной почты", "email"),
    EMPLOYEE_SEC_GROUPS("Группы пользователей сотрудника","employeeSecGroups"),
    ENABLED("Включена", "enabled"),
    ERROR("Ошибка", "error"),
    RULE("Правило определения тревоги", "rule"),
    ENCRYPTION_PROTOCOL("Протокол шифрования", "encryptProto"),
    EVENTS("Виды событий", "events"),
    EVENT_TYPE("Событие", "eventType"),
    EVENT_TYPES("Типы событий", "eventTypes"),
    EVENT_CATEGORY("Код категории события", "eventCategory"),
    EVENT_CATEGORY_NAME("Событие", "eventCategoryName"),
    EVENT_DATE("Дата и время происхождения события", "eventDate"),
    EVENT_FILTER("Фильтр событий", "eventFilter"),
    EVENT_MESSAGES("Сообщение события", "eventMessages"),
    EVENT_SCRIPT("Скрипт", "script"),
    EXECUTION_ERROR("Ошибка", "executionError"),
    EXECUTION_RESULT("Результат выполнения", "executionResult"),
    FIRST_NAME("Имя", "firstName"),
    FOLDERS("Папки", "folders"),
    HAS_COPYES("Наличие копий", "hasCopyes"),
    HAS_MASTER_PROBLEM("Наличие проблемы c массовым запросом", "hasMasterProblem"),
    HAS_SLAVE_PROBLEMS("Наличие проблем с подчиненными запросами", "hasSlaveProblems"),
    HEAD("Курирующий", "head"),
    HEAD_OU("Курирующий отдел", "headOU"),
    HISTORY_DEPTH("Глубина истории", "historyDepth"),
    STEP_SIZE("Длина шага", "stepSize"),
    HOME_PHONE_NUMBER("Номер домашнего телефона", "homePhoneNumber"),
    HTTP_PORT("HTTP-порт", "httpPort"),
    BASE_URL("URL", "baseUrl"),
    IMAGE("Картинка пользователя","image"),
    IMMEDIATE_SUPERVISOR("Непосредственный руководитель","immediateSupervisor"),
    IMPACT("Влияние", "impact"),
    INTERNAL_PHONE_NUMBER("Номер внутреннего телефона", "internalPhoneNumber"),
    IS_COPY("Копируемый", "isCopy"),
    IS_EMPLOYEE_ACTIVE("Активный", "isEmployeeActive"),
    IS_EMPLOYEE_LOCKED("Заблокирован", "isEmployeeLocked"),
    EMPLOYEE_FOR_INTEGRATION("Служебный для интеграции", "employeeForIntegration"),
    LAST_MODIFIED_DATE("Дата изменения", "lastModifiedDate"),
    LAST_NAME("Фамилия", "lastName"),
    LEADER("Лидер команды", "leader"),
    LICENSE("Лицензия", "license"),
    LOGIN("Логин","login"),
    MANAGER_HEAD("Руководитель отдела", "head"),
    MASS_PROBLEM("Массовый", "massProblem"),
    MASS_PROBLEM_SLAVES("Подчиненные запросы", "massProblemSlaves"),
    MASTER_MASS_PROBLEM("Массовый запрос", "masterMassProblem"),
    MEMBERS("Участники команды", "members"),
    METACLASS("Тип объекта", "metaClass"),
    MESSAGE_RTF("Текст оповещения", "message"),
    METRICS("Метрики", "metrics"),
    TASK_PLANNERS("Планировщики задач", "taskPlanners"),
    MODELS("Модели", "models"),
    METRIC("Метрика", "metric"),
    ALERTING_RULE("Правило определения тревоги", "alertingRule"),
    MIDDLE_NAME("Отчество", "middleName"),
    MOBILE_PHONE_NUMBER("Номер мобильного телефона", "mobilePhoneNumber"),
    MODEL_STATE("Статус", "modelState"),
    MONITORING_SERVER("Сервер мониторинга", "monitoringServer"),
    NAME("Имя", "name"),
    NAMESPACE("Пространство имен", "namespace"),
    NEED_AUTHENTICATION("Требуется аутентификация", "needAuthentication"),
    NUMBER("Номер", "number"),
    OU_SEC_GROUPS("Группы пользователей отдела", "ouSecGroups"),
    OBJECTS("Объекты", "metaClasses"),
    OBJECTS_STATE("Статус объектов", "states"),
    OBJECT_FQN("Имя объекта", "objectName"),
    PARAMETER("Параметр", "parameter"),
    PARENT("Родитель", "parent"),
    OU_PARENT("Отдел", "parent"),
    PARENT_UUID("UUID РОдителя", "parentUUID"),
    PASSWORD("Пароль", "password"),
    PERFORMER("Исполнитель","performer"),
    PHONES_INDEX("Список телефонов","phonesIndex"),
    POLLPERIOD("Период опроса", "pollPeriod"),
    PORT("Порт", "port"),
    POST("Должность", "post"),
    PRIORITY("Приоритет", "priority"),
    PRIVATE("Приватный", "private"),
    PRIVATE_CODE("Личный код", "privateCode"),
    PROCESSING_RULE("Правило обработки событий", "processingRule"),
    PROCESSING_TIME_TIMER("Время обработки запроса", "processingTimeTimer"),
    PROTOCOL("Протокол", "protocol"),
    PROXY_ADDRESS("Адрес прокси-сервера", "proxyAddress"),
    PROXY_PORT("Порт прокси-сервера", "proxyPort"),
    PROXY_USE_SSL("Использовать SSL для прокси-сервера", "proxyUseSSL"),
    READCOMMUNITY("Community", "readCommunity"),
    RECIPIENT_AGREEMENTS("Соглашения", "recipientAgreements"),
    RECIPIENTS("Получатели (сотрудники)", "recipients"),
    RECIPIENTS_OU("Получатели (отделы)", "recipientsOU"),
    REGISTRATION_DATE("Дата регистрации", "registrationDate"),
    REMOVAL_DATE("Дата архивирования", "removalDate"),
    REMOVED("Признак архивирования", "removed"),
    REQUEST_DATE("Дата обращения", "requestDate"),
    REQUEST_DESCRIPTION("Описание запроса", "description"),
    RESOLUTION_TIME("Нормативное время обработки", "resolutionTime"),
    RESPONSIBLE("Ответственный", "responsible"),
    RESPONSIBLE_EMPLOYEE("Ответственный (сотрудник)", "responsibleEmployee"),
    RESPONSIBLE_EMPLOYEE_USER("Ответственный (Сотрудник)", "responsibleEmployee"),
    RESPONSIBLE_START_TIME("Время последнего изменения ответственных", "responsibleStartTime"),
    RESPONSIBLE_START_TIME_USERCLASS(
            "Время последнего изменения ответственного", "responsibleStartTime"),
    RESPONSIBLE_TEAM("Ответственный (команда)", "responsibleTeam"),
    RESPONSIBLE_TEAM_USER("Ответственный (Команда)", "responsibleTeam"),
    RETENTION_POLICY("Период хранения необработанных данных", "retentionPolicy"),
    SERVICE("Услуга", "service"),
    SERVICE_HOURS("Время предоставления услуги", "serviceHours"),
    SERVICE_TIME("Класс обслуживания", "serviceTime"),
    SERVICES("Услуги", "services"),
    SEVERITY("Критичность", "severity"),
    SHEDULER_TASK("Задача планировщика", "schedulerTask"),
    SNMPVERSION("Версия SNMP", "snmpVersion"),
    SOLVED_BY("Кем решен", "solvedBy"),
    SOLVED_BY_EMPLOYEE("Кем решен (сотрудник)", "solvedByEmployee"),
    SOLVED_BY_TEAM("Кем решен (команда)", "solvedByTeam"),
    SOURCE("Источник","source"),
    SOURCES("Источники","sources"),
    SOURCE_FQN("Тип связанного объекта", "sourceFqn"),
    SOURCE_UUID("UUID связанного объекта", "source"),
    START_AT("Дата/время начала действия правила", "startAt"),
    START_TIME("Регламентное время начала работ", "startTime"),
    STATE("Статус", "state"),
    STATE_CODE("Предыдущий статус", "stateCode"),
    STATE_SCRIPT_MODULES("Статус синхронизации скриптовых модулей", "sys_stateScriptModules"),
    STORAGE_TIME("Срок хранения, дней", "storageTime"),
    SUPPLIER("Поставщик", "supplier"),
    SUPPLIER_TEAM("Поставщик (команда)", "supplierTeam"),
    SUPPLIER_EMPLOYEE("Поставщик (сотрудник)", "supplierEmpoyee"),
    ERROR_SCRIPT_MODULES("Ошибка синхронизации скриптовых модулей", "sys_errorScriptModules"),
    MODULE_STATE("Статус", "moduleState"),
    NEW_STATE_CODE("Новый статус", "newStateCode"),
    RECORD_STATE("Статус", "recordState"),
    STATE_START_TIME("Время входа в статус", "stateStartTime"),
    SUBJECT_UUID("UUID объекта, с которым происходит событие", "subjectUUID"),
    SUPPORT_HOURS("Время поддержки", "supportHours"),
    SYNC_ERROR("Ошибка синхронизации", "syncError"),
    SYNC_LOG("Лог", "syncLog"),
    SYNC_STATE("Статус", "syncState"),
    SYSTEM_ALIVE("Последняя проверка успешна", "sys_alive"),
    SYSTEM_ERROR("Ошибка", "system_error"),
    SYSTEM_ICON("Иконка", "system_icon"),
    SYSTEM_TIME("Время значения", "system_time"),
    SYSTEM_RECORD_TIME("Время записи", "system_recordTime"),
    SYSTEM_VALUE("Значение", "system_value"),
    TASK_PLANNER("Планировщик задач", "taskPlanner"),
    TEAMS("Команды", "teams"),
    TEAM_USER_GROUPS("Группы пользователей команды", "teamUserGroups"),
    TEXT("Текст", "text"),
    TEMPORARY("Признак временного файла", "temporary"),
    TEMPLATE_TEXT("Текст шаблона", "templateText"),
    TIME_ALLOWANCE_TIMER("Запас нормативного времени обслуживания", "timeAllowanceTimer"),
    TIMEOUT("Таймаут", "timeout"),
    SYSTEM_TIMEOUT("Таймаут", "sys_timeout"),
    IGNORE_EXCEPTIONS("Игнорировать ошибки метрик", "ignoreExceptions"),
    ACTIVATION_COUNT("Повторов для активации", "activationCount"),
    ACTIVATION_DELAY("Задержка для активации", "activationDelay"),
    TIMEZONE("Часовой пояс запроса", "timeZone"),
    TITLE("Название", "title"),
    TITLE_EMPLOYEE("Наименование", "title"),
    TOTAL_TIME_TIMER("Счетчик суммарного времени обработки запросов", "totalTimeTimer"),
    TRIGGERS("Триггеры", "triggers"),
    URGENCY("Срочность", "urgency"),
    URL("URL", "url"),
    SSO_URL("SSO URL", "ssoUrl"),
    USE_PROXY("Использовать прокси-сервер", "useProxy"),
    USE_SSL("Использовать SSL", "useSSL"),
    SYSTEM_USE_BASIC_AUTH("Использовать базовую аутентификацию","sys_useBasicAuth"),
    SYSTEM_USERNAME("Имя пользователя","sys_username"),
    SYSTEM_PASSWORD("Пароль","sys_password"),
    SYSTEM_USED("Используется","sys_used"),
    USER_IMAGE("Картинка пользователя", "image"),
    USERNAME("Имя пользователя", "username"),
    UUID("Уникальный идентификатор", "UUID"),
    VALUE("Значение", "defaultValue"),
    WF_PROFILE("Код профиля связанных жизненных циклов", "wfProfile"),
    LABELS("Набор ключевых признаков", "sys_labels"),
    SYSTEM_SEVERITY("Критичность", "system_severity"),
    USAGE_PLACES("Места использования", "usagePlaces"),
    SYSTEM_STATUS("Статус тревоги", "alertStatus"),
    MESSAGE_DATA("Содержимое письма", "messageDataFile"),
    SEASON_LENGTH_ONE("Длина сезона 1", "slen1"),
    SEASON_LENGTH_TWO("Длина сезона 2", "slen2"),
    ALPHA("Коэффициент Альфа", "alpha"),
    BETA("Коэффициент Бета", "beta"),
    GAMMA_ONE("Коэффициент Гамма 1", "gamma1"),
    GAMMA_TWO("Коэффициент Гамма 2", "gamma2"),
    F_HORIZON("Горизонт прогнозирования", "fhorizon"),
    BRANCHES("Ветки", "sys_branches"),
    GATEWAY_ID("Внешний идентификатор канала", "gatewayId"),
    TOKEN("Токен", "token"),
    GROUP_ID("Id группы в соц. сети", "groupId"),
    APP_TOKEN("Ключ доступа пользователя", "appToken"),
    GROUP_TOKEN("Ключ доступа группы", "groupToken"),
    IM_MESSAGES("Получать личные сообщения группы", "imMessages"),
    MENTION_MESSAGES("Получать посты и комментарии к ним", "mentionMessages"),
    MESSENGER_TOKEN("Ключ доступа к мессенджеру", "messengerToken"),
    WAIT_PERIOD("Период ожидания", "waitPeriod"),
    CHAT("Чат", "chat"),
    DIALOG_SESSION("Сессия диалога", "dialogSession"),
    MESSAGE_STATE("Статус", "messageState"),
    DIALOG("Диалог", "dialog"),
    SERIES("Внешний номер", "series"),
    EXTERNAL_ID("Внешний идентификатор", "externalId"),
    CHANNEL("Канал", "channel"),
    SESSION_IS_OPEN("Сессия открыта", "sessionIsOpen"),
    USER_ID("Идентификатор  пользователя", "userId"),
    USER_FULL_NAME("ФИО пользователя в канале", "userFullName"),
    EMPLOYEE("Пользователь в систем", "employee"),
    KEYBOARD("Клавиатура", "keyboard"),
    SENT_DATE("Дата отправки сообщения", "sentDate"),
    FIELD_TYPE("Тип поля", "fieldType"),
    FIELD_OBJECTS("Объекты", "objects"),
    FIELD_VIEW_PRESENTATION("Представление для отображения", "viewPresentation"),
    FIELD_EDIT_PRESENTATION("Представление для редактирования", "editPresentation"),
    FIELD_EDITABLE("Редактируемый", "editable"),
    FIELD_EDITABLE_IN_LISTS("Редактируемый в списках", "editableInLists"),
    FIELD_REQUIRED("Обязательный", "required"),
    FIELD_REQUIRED_IN_INTERFACE("Обязательный для заполнения в интерфейсе", "requiredInInterface"),
    FIELD_HIDDEN_WHEN_NO_POSSIBLE_VALUES("Скрывать, если нет значений для выбора", "hiddenWhenNoPossibleValues"),
    GATE_TYPE("Тип шлюза", "gateType"),
    NDAP_METRIC_VALUES("Значения метрик", "metricValues"),
    NDAP_SWITCH_DATE("Дата, время", "switchDate"),
    SYSTEM_URL("URL", "sys_url"),
    SYSTEM_AUTHENTICATION("Тип аутентификации", "sys_authentication"),
    SYSTEM_PRINCIPAL("URL", "sys_principal"),
    SYSTEM_SECURITY_PROTOCOL("URL", "sys_securityProtocol"),
    INVENTORY_NUMBER("Уникальный номер", "inventoryNumber");
    //@formatter:on

    private final String code;
    private final String title;

    SystemAttrEnum(String title, String code)
    {
        this.title = title;
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public String getTitle()
    {
        return title;
    }
}