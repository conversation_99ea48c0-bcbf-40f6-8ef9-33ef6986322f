package ru.naumen.selenium.casesutil.model.mail;

import ru.naumen.selenium.casesutil.model.ModelFactory;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.mail.MailServer.SecurityProtocol;
import ru.naumen.selenium.util.RandomUtils;

/**
 * Утилитарные методы для работы с моделями данных относящихся к SMTP серверу
 * <AUTHOR>
 * <AUTHOR>
 * @since 09.06.2014
 */
public class DAOSmtpServer
{
    /**
     * Создать модель SMTP сервера с настройками по-умолчанию
     * (Модель регистрируется в очередь на удаление)
     * @return возвращает модель SMTP сервера
     */
    public static SmtpServer create()
    {
        return create(RandomUtils.nextInt(65535));
    }

    /**
     * Создать модель SMTP сервера с настройками по-умолчанию
     * (Модель регистрируется в очередь на удаление)
     * @param port порт
     * @return возвращает модель SMTP сервера
     */
    public static SmtpServer create(int port)
    {
        return create(port, fillSendMailParams(new SendMailParam()));
    }

    public static SmtpServer create(int port, SendMailParam params)
    {
        return create(
                ModelUtils.createText(10),
                port,
                SecurityProtocol.UNSECURED,
                Boolean.FALSE,
                ModelUtils.createLogin(),
                ModelUtils.createPassword(),
                Boolean.FALSE,
                Boolean.FALSE,
                Boolean.FALSE,
                fillSendMailParams(params));
    }

    /**
     * Создать модель SMTP сервера
     * (Модель регистрируется в очередь на удаление)
     * @param server адрес сервера
     * @param port порт
     * @param protocol протокол
     * @param auth нужна ли авторизация?
     * @param login логин
     * @param password пароль
     * @param enable включен/выключен
     * @param defaultConnection является ли соединением по-умолчанию
     * @param skipCertVerification игнорировать ли проверку сертификата
     * @param params параметры отправки писем
     * @return возвращает модель SMTP сервера
     */
    public static SmtpServer create(String server, int port, SecurityProtocol protocol, boolean auth, String login,
            String password, boolean enable, boolean defaultConnection, boolean skipCertVerification,
            SendMailParam params)
    {
        SmtpServer model = ModelFactory.create(SmtpServer.class);
        model.setServer(server);
        model.setPort(String.valueOf(port));
        model.setProtocol(protocol.getCode());
        model.setAuth(String.valueOf(auth));
        model.setLogin(login);
        model.setPassword(password);
        model.setDefault(defaultConnection);
        model.setEnable(String.valueOf(enable));
        model.setSkipCertVerification(String.valueOf(skipCertVerification));
        model.setParams(params);
        return model;
    }

    /**
     * Заполнить модель для настроек исходящей почты параметрами по-умолчанию
     * @return возвращает модель настроек исходящей почты
     */
    public static SendMailParam fillSendMailParams(SendMailParam params)
    {
        params.setFeedbackAddress(ModelUtils.createEmail());
        params.setSystemMail(ModelUtils.createEmail());
        params.setSenderName(ModelUtils.createTitle());
        return params;
    }
}
