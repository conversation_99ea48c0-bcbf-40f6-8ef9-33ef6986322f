package ru.naumen.selenium.casesutil.model.smia;

import java.util.ArrayList;
import java.util.List;

import ru.naumen.selenium.casesutil.model.IForRemoveModel;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.element.SESmiaLearningTask;

/**
 * Модель "задач обучения" SMIA
 * <AUTHOR>
 * @since 05.07.2021
 */
public class LearningTask extends ModelUuid implements IForRemoveModel
{
    /**
     * Статусы процесса обучения в задаче обучения
     */
    public enum LearningProcessState
    {
        /**
         * Ожидает обучения
         */
        AWAITING_LEARNING(),
        /**
         * Обучается
         */
        LEARNING,
        /**
         * Завершен
         */
        COMPLETED,
        /**
         * Остановлен с ошибкой
         */
        STOPPED_WITH_ERROR,
        /**
         * Остановлен
         */
        STOPPED
    }

    // id метакласса
    public static final String CLASS_ID = "sys_learnTask";

    // Атрибуты
    private static final String ADD_TO_QUEUE_DATE = "addToQueueDate";
    public static final Attribute ADD_TO_QUEUE_DATE_ATTRIBUTE = DAOAttribute.createPseudo("Дата помещения задачи "
                                                                                          + "обучения в очередь",
            ADD_TO_QUEUE_DATE, null);
    private static final String START_DATE = "startDate";
    public static final Attribute START_DATE_ATTRIBUTE = DAOAttribute.createPseudo("Дата запуска задачи обучения",
            START_DATE, null);
    private static final String STOP_DATE = "stopDate";
    public static final Attribute STOP_DATE_ATTRIBUTE = DAOAttribute.createPseudo("Дата остановки задачи обучения",
            STOP_DATE, null);
    private static final String STOPPED = "stopped";
    public static final Attribute STOPPED_ATTRIBUTE = DAOAttribute.createPseudo("Признак остановки",
            STOPPED, null);
    private static final String LP_CODE = "lpCode";
    public static final Attribute LP_CODE_ATTRIBUTE = DAOAttribute.createPseudo("Код процесса обучения",
            LP_CODE, null);
    private static final String STATUS = "status";
    public static final Attribute STATUS_ATTRIBUTE = DAOAttribute.createPseudo("Статус", STATUS, null);
    private static final String UUID = "uuid";

    /**
     * Конструктор по умолчанию:
     * ADD_TO_QUEUE_DATE = null;
     * STOP_DATE = null;
     * START_DATE = null;
     * STOPPED = false;
     * LP_CODE = null;
     * STATUS = AWAITING_LEARNING;
     */
    public LearningTask()
    {
        model.put(ADD_TO_QUEUE_DATE, null);
        model.put(STOP_DATE, null);
        model.put(START_DATE, null);
        model.put(STOPPED, Boolean.FALSE.toString());
        model.put(LP_CODE, null);
        model.put(STATUS, LearningProcessState.AWAITING_LEARNING.name());
    }

    @Override
    public List<ScriptElement> addToRemoveScript()
    {
        List<ScriptElement> scripts = new ArrayList<>();
        if (isExists())
        {
            scripts.add(SESmiaLearningTask.deleteLearningTask(getUuid()));
        }
        return scripts;
    }

    public String getAddToQueueDate()
    {
        return get(ADD_TO_QUEUE_DATE);
    }

    public String getLpCode()
    {
        return get(LP_CODE);
    }

    public String getStartDate()
    {
        return get(START_DATE);
    }

    public LearningProcessState getStatus()
    {
        return LearningProcessState.valueOf(get(STATUS));
    }

    public String getStopDate()
    {
        return get(STOP_DATE);
    }

    public String getUuid()
    {
        return get(UUID);
    }

    public boolean isStopped()
    {
        return Boolean.parseBoolean(get(STOPPED));
    }

    public void setAddToQueueDate(String date)
    {
        set(ADD_TO_QUEUE_DATE, date);
    }

    public void setLpCode(String lpCode)
    {
        set(LP_CODE, lpCode);
    }

    public void setStartDate(String date)
    {
        set(START_DATE, date);
    }

    public void setStatus(LearningProcessState status)
    {
        set(STATUS, status.name());
    }

    public void setStopDate(String date)
    {
        set(STOP_DATE, date);
    }

    public void setStopped(boolean stopped)
    {
        set(STOPPED, Boolean.valueOf(stopped).toString());
    }

    public void setUuid(String uuid)
    {
        set(UUID, uuid);
    }

}
