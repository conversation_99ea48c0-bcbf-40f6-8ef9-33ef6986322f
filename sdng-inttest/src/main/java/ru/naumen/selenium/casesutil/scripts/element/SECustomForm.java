package ru.naumen.selenium.casesutil.scripts.element;

import java.util.Map;

import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

import java.util.HashMap;

/**
 * Создание скриптовых элементов для действий над специальными формами
 * <AUTHOR>
 * @since 15.06.2016
 */
public class SECustomForm
{
    private static final String FORM_TYPE = "formType";
    private static final String TRANSITION_CLASSES = "transitionClasses";
    private static final String FORM_CODE = "formCode";
    private static final String QUICK_FORM = "quickAddAndEditForm";
    private static final String MASS_EDIT_FORM = "massEditForm";
    private static final String UUID = "uuid";

    /**
     * Удалить специальную форму
     * @param model модель формы
     * @return возвращает скриптовый элемент
     */
    public static ScriptElement delete(CustomForm model)
    {
        Map<String, Object> dataForScript = new HashMap<>();
        if (QUICK_FORM.equals(model.getFormType().getCode()) || MASS_EDIT_FORM.equals(model.getFormType().getCode()))
        {
            dataForScript.put(UUID, model.getUuid());
        }
        else
        {
            dataForScript.put(UUID, model.getFormType().getCode());
        }
        dataForScript.put(FORM_TYPE, model.getFormType().name());
        dataForScript.put(FORM_CODE, model.getFormType().getCode());

        switch (model.getFormType())
        {
            case QuickForm:
                dataForScript.put(FORM_CODE, model.getUuid());
                dataForScript.put(TRANSITION_CLASSES, model.getTransitionClasses());
                break;
            case ChangeCaseForm:
            case ChangeResponsibleForm:
            case MassEditForm:
                dataForScript.put(TRANSITION_CLASSES, model.getTransitionClasses());
                break;
            default:
                throw new RuntimeException("Unknown content: " + model.getFormType());
        }

        ScriptElement element = new ScriptElement();
        element.getParams().add(StringUtils.escapingCharacters(Json.GSON.toJson(dataForScript)));

        // Добавляем путь к шаблону функции
        element.setPathToFunctionFile("scripts/groovy/customform/deleteCustomForm.groovy");
        return element;
    }
}
