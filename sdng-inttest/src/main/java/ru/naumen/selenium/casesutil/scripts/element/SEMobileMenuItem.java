package ru.naumen.selenium.casesutil.scripts.element;

import java.util.Map;

import java.util.HashMap;

import ru.naumen.selenium.casesutil.model.admin.MobileMenuItem;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

/**
 * Создание скриптовых элементов для элементов навигационного меню мобильного приложения
 * <AUTHOR>
 * @since 28.03.2018
 */
public class SEMobileMenuItem
{
    /**
     * Удалить элемент навигационного меню
     * @param item модель элемента навигационного меню
     * @return скриптовый элемент для удаления элемента навигационного меню
     */
    public static ScriptElement deleteMobileMenuItem(MobileMenuItem item)
    {
        Map<String, Object> dataForScript = new HashMap<>();
        dataForScript.put("code", item.getCode());
        dataForScript.put("title", item.getTitle());

        ScriptElement element = new ScriptElement();
        element.getParams().add(StringUtils.escapingCharacters(Json.GSON.toJson(dataForScript)));
        // Добавляем путь к шаблону функции;
        element.setPathToFunctionFile("scripts/groovy/mobile/deleteMobileMenuItem.groovy");
        return element;
    }
}
