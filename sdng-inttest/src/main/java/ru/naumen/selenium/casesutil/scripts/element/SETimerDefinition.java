package ru.naumen.selenium.casesutil.scripts.element;

import java.util.List;

import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.modules.ScriptModules;

/**
 * Создание скриптовых элементов для действий над счетчиками времени
 * <AUTHOR>
 * @since 15.06.2012
 */
public class SETimerDefinition
{
    /**
     * Удалить счетчик времени
     * @param model модель счетчика времени
     * @return возвращает скриптовый элемент
     */
    public static ScriptElement delete(TimerDefinition model)
    {
        ScriptModules.getModuleTimerDefinition();
        ScriptElement element = new ScriptElement();
        //Добавляем параметры
        List<String> params = element.getParams();
        params.add(model.getCode());
        // Добавляем путь к шаблону функции
        element.setPathToFunctionFile("scripts/groovy/timer/deleteTimerDefinition.groovy");
        return element;
    }
}
