package ru.naumen.selenium.casesutil.secgroup;

import java.util.List;

import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.scripts.element.SESecurityMarker;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.util.Json;

/**
 * Утилитарные методы для работы с маркерами прав
 *
 * <AUTHOR>
 * @since Oct 3, 2013
 */
public class DSLSecurityMarker
{
    /**
     * Изменить маркер прав для метакласса
     */
    public static void edit(MetaClass metaClass, SecurityMarker marker)
    {
        ScriptElement element = SESecurityMarker.edit(metaClass, marker);
        ScriptRunner script = new ScriptRunner(true, element);
        script.runScript();
    }

    /**
     * Возвращает маркер прав для метакласса с соответствующим кодом
     * @param metaClass для которого мы хоти получить маркер прав
     * @param code маркера прав
     * @return модель маркера прав
     */
    public static SecurityMarker getSecurityMarker(MetaClass metaClass, String code)
    {
        ScriptElement element = SESecurityMarker.getSecurityMarker(metaClass, code);
        ScriptRunner script = new ScriptRunner(true, element);
        ModelMap map = Json.stringToMapJson(script.runScript().get(0).trim());
        if (!map.isEmpty())
        {
            SecurityMarker marker = SecurityMarker.createWithType(metaClass, map.get("group"));
            marker.setCode(code).setTitle(map.get("title"));
            List<String> listAttrs = Json.stringToList(map.get("attrs"));
            marker.addCodes(listAttrs.toArray(new String[listAttrs.size()]));
            return marker;
        }
        return null;
    }

    /**
     * Возвращает коды маркеров прав метакласса
     */
    public static List<String> getSecurityMarkerCodes(MetaClass metaClass)
    {
        ScriptElement element = SESecurityMarker.getSecurityMarkerCodes(metaClass);
        ScriptRunner script = new ScriptRunner(true, element);
        String codes = script.runScript().get(0);
        return Json.stringToList(codes);
    }
}
