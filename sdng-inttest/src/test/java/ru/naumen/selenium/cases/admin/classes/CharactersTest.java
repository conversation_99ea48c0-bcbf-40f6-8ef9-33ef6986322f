package ru.naumen.selenium.cases.admin.classes;

import java.io.File;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.GUIScriptField;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScript;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.config.DbType;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;

/**
 * Тестирование возможности использования или запрета использования различных символов
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00040
 *
 * <AUTHOR>
 * @since 21.10.19
 */
public class CharactersTest extends AbstractTestCase
{
    private static final String EMOJI_1 = new String(Character.toChars(
            128512)); // 0x1F600
    private static final String EMOJI_2 = new String(Character.toChars(
            128591)); // 0x1F64F
    private static final String EMOJI_3 = new String(Character.toChars(
            Integer.parseUnsignedInt("1F525", 16)));
    private static final String EMOJI_4 = new String(Character.toChars(
            Integer.parseUnsignedInt("263A", 16)));
    private static final String EMOJI_5 = new String(Character.toChars(
            Integer.parseUnsignedInt("1F5FF", 16)));

    /**
     * Тестирование отсутствия возможности сохранить невалидный xml символ в метаифномрации
     * для классов, типов и атрибутов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00040
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$80346274
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс {@code userclass}</li>
     * <li>Создать тип пользовательский класса {@code userCase}</li>
     * <li>Создать строковый атрибут {@code attribute}</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Открыть форму редактирования метакласса {@code userclass}</li>
     * <li>Ввести название метакласса: "Название со спецсимволом (\u0005)"</li>
     * <li>Нажать "Сохранить" и проверить появилась ошибка:
     *  "Произошла ошибка. Тэг <title> содержит невалидный символ '0x05', который не может быть сохранён в XML"</li>
     * <li>Нажать "Отмена"</li>
     * <li>Открыть форму редактирования метакласса {@code userCase}</li>
     * <li>Ввести название метакласса: "Название со спецсимволом (\u001F)"</li>
     * <li>Нажать "Сохранить" и проверить появилась ошибка:
     *  "Произошла ошибка. Тэг <title> содержит невалидный символ '0x1F', который не может быть сохранён в XML"</li>
     * <li>Нажать "Отмена"</li>
     * <li>Перейти на карточку метакласса с атрибутом {@code attribute}</li>
     * <li>Открыть форму редактирования атрибута {@code attribute}</li>
     * <li>Ввести название атрибута: "Название со спецсимволом (\u001F)"</li>
     * <li>Нажать "Сохранить" и проверить появилась ошибка:
     *  "Произошла ошибка. Тэг <title> содержит невалидный символ '0x1F', который не может быть сохранён в XML"</li>
     * <li>Нажать "Отмена"</li>
     * <li>Открыть форму редактирования атрибута {@code attribute}</li>
     * <li>Ввести описание атрибута: "(\u000B)"</li>
     * <li>Нажать "Сохранить" и проверить появилась ошибка:
     *  "Произошла ошибка. Тэг <description> содержит невалидный символ '0x0B', который не может быть сохранён в
     *  XML"</li>
     * <li>Нажать "Отмена"</li>
     * </ol>
     */
    @Test
    public void testCantSaveInvalidXmlChars()
    {
        // Подготовка
        String invalidTitle1 = "Название со спецсимволом (\u0005)";
        String invalidTitle2 = "Название со спецсимволом (\u001F)";
        String invalidTitle3 = "Название со спецсимволом (\u0019)";
        String invalid4 = "(\uFFFF)";
        String errorMsgTemplate = "Произошла ошибка. Тэг <%s> содержит невалидный символ '%s', "
                                  + "который не может быть сохранён в XML.";

        MetaClass userclass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userclass);
        DSLMetaClass.add(userclass, userCase);

        Attribute attribute = DAOAttribute.createString(userclass);
        DSLAttribute.add(attribute);

        // Действия и проверки
        GUILogon.asSuper();

        GUIMetaClass.openEditForm(userclass);
        /*
         Метод GUIMetaClass.setTitle(invalidTitle1); мы использовать не можем из-за особого символа.
         В Chrome это приводит к бесконечному циклу стирания и ввода текста в поле.
        */
        GUIForm.fillAttributeByUseClipboard(GUIXpath.Input.TITLE_VALUE, invalidTitle1);
        GUIForm.applyFormAssertError(String.format(errorMsgTemplate, "title", "0x05"));
        GUIForm.cancelForm();

        GUIMetaClass.openEditForm(userCase);
        /*
         Метод GUIMetaClass.setTitle(invalidTitle2); мы использовать не можем из-за особого символа.
         В Chrome это приводит к бесконечному циклу стирания и ввода текста в поле.
        */
        GUIForm.fillAttributeByUseClipboard(GUIXpath.Input.TITLE_VALUE, invalidTitle2);
        GUIForm.applyFormAssertError(String.format(errorMsgTemplate, "title", "0x1F"));
        GUIForm.cancelForm();

        GUIAttribute.goToAttribute(attribute);
        GUIAttribute.clickEdit(attribute);
        /*
         Метод GUIAttribute.fillAttrTitle(invalidTitle3); мы использовать не можем из-за особого символа.
         В Chrome это приводит к бесконечному циклу стирания и ввода текста в поле.
        */
        GUIForm.fillAttributeByUseClipboard(
                GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, invalidTitle3);
        GUIForm.applyFormAssertError(String.format(errorMsgTemplate, "title", "0x19"));
        GUIForm.cancelForm();

        GUIAttribute.clickEdit(attribute);
        String descriptionXPath = GUIAttribute.X_DESCRIPTION_VALUE;
        GUIRichText.clickRichTextEditorByXPath(descriptionXPath);
        GUIForm.fillAttributeByUseClipboard(descriptionXPath, invalid4);
        GUIForm.applyFormAssertError(String.format(errorMsgTemplate, "description", "0xFFFF"));
        GUIForm.cancelForm();
    }

    /**
     * Тестирование возможности использования символов emoji в названиях классов, типов и атрибутов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00040
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$80346274
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс {@code userclass}</li>
     * <li>Создать тип пользовательский класса {@code userCase}</li>
     * <li>Создать строковый атрибут {@code attribute}</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Открыть форму редактирования метакласса {@code userclass}</li>
     * <li>Ввести название метакласса: символ с кодом 0x1F525</li>
     * <li>Нажать "Сохранить" и проверить что форма закрылась</li>
     * <li>Проверить что название установилось правильно</li>
     * <li>Открыть форму редактирования метакласса {@code userCase}</li>
     * <li>Ввести название метакласса: символ с кодом 0x263A</li>
     * <li>Нажать "Сохранить" и проверить что форма закрылась</li>
     * <li>Проверить что название установилось правильно</li>
     * <li>Перейти на карточку метакласса с атрибутом {@code attribute}</li>
     * <li>Открыть форму редактирования атрибута {@code attribute}</li>
     * <li>Ввести название атрибута: символ с кодом 0x1F5FF</li>
     * <li>Нажать "Сохранить" и проверить что форма закрылась</li>
     * <li>Проверить что название установилось правильно</li>
     * </ol>
     */
    @Test
    public void testUseEmoji()
    {
        // Подготовка
        MetaClass userclass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userclass);
        DSLMetaClass.add(userclass, userCase);

        Attribute attribute = DAOAttribute.createString(userclass);
        DSLAttribute.add(attribute);

        // Действия и проверки
        GUILogon.asSuper();

        setMetaClassTitle(userclass, EMOJI_3);
        GUIMetaClass.assertMetaClassTitle(EMOJI_3);

        setMetaClassTitle(userCase, EMOJI_4);
        GUIMetaClass.assertMetaClassTitle(EMOJI_4);

        GUIAttribute.goToAttribute(attribute);
        GUIAttribute.clickEdit(attribute);
        /*
         Метод GUIAttribute.fillAttrTitle(EMOJI_5); мы использовать не можем из-за особого символа.
         В Chrome это приводит к бесконечному циклу стирания и ввода текста в поле.
        */
        GUIForm.fillAttributeByUseClipboard(
                GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, EMOJI_5);
        GUIForm.applyForm();
        GUIAttribute.assertAttributeTitle(EMOJI_5, attribute.getCode());
    }

    /**
     * Тестирования возможности использования символов emoji в названии и теле скрипта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00488
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$80346274
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать скрипт {@code script} с наименованием: символ с кодом 0x1F600</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Перейти на карточку скрипта {@code script}</li>
     * <li>Проверить что название создано правильно (символ 0x1F600)</li>
     * <li>Открыть форму редактирования скрипта</li>
     * <li>Ввести название скрипта: символ с кодом 0x1F64F</li>
     * <li>Добавить в тело скрипта символ с кодом 0x1F600</li>
     * <li>Нажать "Сохранить" и проверить что форма закрылась</li>
     * <li>Проверить что название установилось правильно</li>
     * <li>Проверить что тело скрипта установилось правильно</li>
     * </ol>
     */
    @Test
    public void testCanUseEmojiInScript()
    {
        // Подготовка
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        script.setTitle(EMOJI_1);

        // Действия и проверки
        // Тестирование создание скрипта с emoji через скрипт
        DSLScriptInfo.addScript(script);

        GUILogon.asSuper();
        GUIScript.goToCard(script);
        GUIScript.assertTitle(EMOJI_1);
        GUIScript.clickEdit();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        // Можно ли использовать emoji в названии скрипта
        GUIForm.fillAttributeByUseClipboard(GUIXpath.Any.TITLE_VALUE, EMOJI_2);
        // Можно ли использовать emoji в теле скрипта
        String scriptBody = "// " + EMOJI_1 + "\n" + script.getBody();
        GUIScriptField.sendKeysUsingJS(scriptBody);
        GUIForm.applyForm();
        GUIScript.assertTitle(EMOJI_2);
        GUIScript.assertText(scriptBody);
    }

    /**
     * Тестирование выгрузки метаинформации, в которой содержатся emoji (в названиях классов, типов и атрибутов), с
     * ее последующей
     * повторной загрузкой (при выгрузке emoji не должны преобразовываться в суррогатные пары UTF-16)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00040
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90270870
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс {@code userclass}</li>
     * <li>Создать тип пользовательский класса {@code userCase}</li>
     * <li>Создать строковый атрибут {@code attribute}</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Открыть форму редактирования метакласса {@code userclass}</li>
     * <li>Ввести название метакласса: символ с кодом 0x1F525</li>
     * <li>Нажать "Сохранить" и проверить что форма закрылась</li>
     * <li>Проверить, что название установилось правильно</li>
     * <li>Открыть форму редактирования метакласса {@code userCase}</li>
     * <li>Ввести название метакласса: символ с кодом 0x263A</li>
     * <li>Нажать "Сохранить" и проверить что форма закрылась</li>
     * <li>Проверить, что название установилось правильно</li>
     * <li>Перейти на карточку метакласса с атрибутом {@code attribute}</li>
     * <li>Открыть форму редактирования атрибута {@code attribute}</li>
     * <li>Ввести название атрибута: символ с кодом 0x1F5FF</li>
     * <li>Нажать "Сохранить" и проверить что форма закрылась</li>
     * <li>Проверить, что название установилось правильно</li>
     * <li>Выгрузить метаинформацию</li>
     * <li>Загрузить метаинформацию</li>
     * <li>Проверить, что метаинформация загрузилась без ошибок</li>
     * <li>Проверить, что название метакласса отображается корректно</li>
     * <li>Проверить, что название типа отображается корректно</li>
     * <li>Проверить, что название атрибута отображается корректно</li>
     * </ol>
     */
    @IgnoreConfig(cause = "На MSSQL некорректно сохраняются emoji. Подробности в ASK-33154",
            ignoreDb = { DbType.MSSQL })
    @Test
    public void testEmojiInMetaInfo()
    {
        // Подготовка
        MetaClass userclass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userclass);
        DSLMetaClass.add(userclass, userCase);

        Attribute attribute = DAOAttribute.createString(userclass);
        DSLAttribute.add(attribute);

        // Действия и проверки
        GUILogon.asSuper();

        setMetaClassTitle(userclass, EMOJI_1);
        GUIMetaClass.assertMetaClassTitle(EMOJI_1);

        setMetaClassTitle(userCase, EMOJI_2);
        GUIMetaClass.assertMetaClassTitle(EMOJI_2);

        GUIAttribute.goToAttribute(attribute);
        GUIAttribute.clickEdit(attribute);

        GUIForm.fillAttributeByUseClipboard(
                GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, EMOJI_3);
        GUIForm.applyForm();
        GUIAttribute.assertAttributeTitle(EMOJI_3, attribute.getCode());

        File metaInfo = DSLMetainfoTransfer.exportMetainfo();
        DSLMetainfoTransfer.importMetainfo(metaInfo);

        GUIMetaClass.goToCard(userclass);
        GUIMetaClass.assertMetaClassTitle(EMOJI_1);

        GUIMetaClass.goToCard(userCase);
        GUIMetaClass.assertMetaClassTitle(EMOJI_2);

        GUIAttribute.assertAttributeTitle(EMOJI_3, attribute.getCode());
    }

    private static void setMetaClassTitle(MetaClass metaClass, String title)
    {
        GUIMetaClass.openEditForm(metaClass);
        /*
         Метод GUIMetaClass.setTitle(title); мы использовать не можем из-за особого символа.
         В Chrome это приводит к бесконечному циклу стирания и ввода текста в поле.
        */
        GUIForm.fillAttributeByUseClipboard(GUIXpath.Input.TITLE_VALUE, title);
        GUIForm.applyForm();
    }
}
