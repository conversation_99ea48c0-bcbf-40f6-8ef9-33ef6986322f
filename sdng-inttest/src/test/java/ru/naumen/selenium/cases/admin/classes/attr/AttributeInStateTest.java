package ru.naumen.selenium.cases.admin.classes.attr;

import static ru.naumen.selenium.casesutil.model.attr.DAOAttribute.createPseudo;

import java.io.File;
import java.util.Arrays;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIWorkflow;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.ResponsibleType;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.Strategy;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoElementIdBuilder;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Настройка атрибутов для состояний
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00340
 *
 * <AUTHOR>
 *
 */

public class AttributeInStateTest extends AbstractTestCase
{
    /**
     * Тестирование отсутствия перечисленных атрибутов в разрезе статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00340
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе Запрос создать пользовательский статус state</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Войти под суперпользователем на карточку редактирования статуса state</li>
     * <li>Настройка осуществляется для всех системных и пользовательских атрибутов и комментариев соответствующего
     * метакласса, кроме атрибутов: "Название", "Статус", "Тип объекта", "Признак архивирования", "Родитель",
     * "Уникальный идентификатор"</li>
     * </ol>
     */
    @Test
    public void testAbsenceOfAttrInState()
    {
        // Подготовка
        BoStatus state = DAOBoStatus.createUserStatus(DAOScCase.createClass().getFqn());
        DSLBoStatus.add(state);

        String scClassFqn = DAOScCase.createClass().getFqn();
        Attribute titleAttribute = createPseudo("Название", "title", scClassFqn);
        Attribute stateAttribute = createPseudo("Статус", "state", scClassFqn);
        Attribute metaClassAttribute = createPseudo("Тип объекта", "metaClass", scClassFqn);
        Attribute removedAttribute = createPseudo("Признак архивирования", "removed", scClassFqn);
        Attribute parentAttribute = createPseudo("Родитель", "parent", scClassFqn);
        Attribute uuidAttribute = createPseudo("Уникальный идентификатор", "UUID", scClassFqn);
        List<Attribute> attribute = Arrays.asList(titleAttribute, stateAttribute, metaClassAttribute, removedAttribute,
                parentAttribute, uuidAttribute);

        // Выполнение действий и проверка
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(state);
        GUITester.assertPresent(String.format(Div.ANY, "stateSettings"), "Отсутсвует контент "
                                                                         + "'Управление параметрами объекта'");
        GUITester.assertAbsent(String.format(GUIBoStatus.ATTR_FOR_CONFIGURE_STATE_VIEW, titleAttribute
                .getCode()), "Элемент присутствует - " + String.format(GUIBoStatus.ATTR_FOR_CONFIGURE_STATE_VIEW,
                titleAttribute.getCode()));
        GUIBoStatus.assertAttributesForConfigure(attribute, false);
    }

    /**
     * Тестирование сброса настройки атрибута для статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00340
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать в scCase атрибут типа строка attrString</li>
     * <li>Создать в scCase статус state (между зарегистрирован и разрешен)</li>
     * <li>Для attrString установить: отображать, не редактировать, заполнять
     * при входе, не заполнять при выходе</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти карточку state</li>
     * <li>Нажать Сбросить настройки</li>
     * <li>Нажать сохранить.</li>
     * <br>
     * <b>Проверки</b>
     * <li>Зайти в карточку state, проверить, что для атрибута attrString:
     * отображать, редактировать, не заполнять на входе и выходе</li>
     * </ol>
     */
    @Test
    public void testResetAttrInStateSettings()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();

        Attribute attrString = DAOAttribute.createString(scCase.getFqn());
        DSLMetainfo.add(scCase, attrString);

        BoStatus status = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.EMPLOYEE, Strategy.EMPTY);
        DSLBoStatus.add(status);
        //Тестовые действия
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(status);
        //Проверяем текущие настройки атрибута

        //Собственно меняем
        tester.click(String.format(GUIXpath.Div.ANY_IN_STATE, attrString.getCode()) + GUIXpath.Span.SPAN_CODE);
        tester.click(GUIXpath.Div.POPUP_LIST_SELECT + GUIXpath.Span.ASTATE_DOT);
        tester.click(String.format(GUIXpath.Div.ANY_PRE_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_DOT);
        tester.click(GUIXpath.Div.POPUP_LIST_SELECT + GUIXpath.Span.ASTATE_PRE_FILL_NORMAL);
        tester.click(GUIXpath.Div.SAVE);

        //Делаем ресет
        GUIBoStatus.goToStatusCardWithRefresh(status);
        tester.click(GUIXpath.Div.REFRESH);
        GUIForm.assertQuestionAppear("Подтверждение удаления не появилось");
        GUIForm.confirmByYes();
        //Проверки
        GUIBoStatus.goToStatusCardWithRefresh(status);
        GUIBoStatus.assertAttributeInState(attrString, 0);
        Assert.assertTrue("Атрибут не должен заполняться при входе в статус",
                tester.find(String.format(GUIXpath.Div.ANY_PRE_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_DOT)
                        .isDisplayed());
        Assert.assertTrue("Атрибут не должен заполняться при выходе из статуса",
                tester.find(String.format(GUIXpath.Div.ANY_POST_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_DOT)
                        .isDisplayed());
    }

    /**
     * Тестирование того, что удаленные атрибуты, использовавшиеся для настроек ЖЦ,
     * не выгружаются в метаинформацию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * http://sd-jira.naumen.ru/browse/NSDPRD-5237
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать в классе Запрос атрибут типа строка attrString</li>
     * <li>Для статуса "Разрешен" в типе scCase установить attrString заполнять при входе</li>
     * <li>Удалить атрибут attrString</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Выгрузить метаинформацию для класса "Запрос"</li>
     * <li>Загрузить метаинформацию обратно в систему</li>
     * <li>Зайти под администратором</li>
     * <li>Проверить отсутствие атрибута attrString на карточке типа scCase</li>
     * </ol>
     */
    @Test
    public void testResetAttrInStateSettingsAfterAttrDelete()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();

        Attribute attrString = DAOAttribute.createString(scClass.getFqn());
        DSLMetainfo.add(scCase, attrString);

        BoStatus status = DAOBoStatus.createResolved(scCase.getFqn());
        DSLBoStatus.setAttrInState(attrString, status, true, true, 1, 0);

        DSLAttribute.delete(attrString);

        //Выполнение действий и проверки
        File metainfo = DSLMetainfoTransfer.exportMetainfo(DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { MetainfoElementIdBuilder.ALL_SETTINGS, MetainfoElementIdBuilder.CLASSES,
                        MetainfoElementIdBuilder.MAIN_CLASS_WITHOUT_PREFIX,
                        MetainfoElementIdBuilder.MAIN_CLASS_WITHOUT_PREFIX
                        + MetainfoElementIdBuilder.DESCENDANT_CLASS_SETTINGS, scClass.getCode() }));

        DSLMetainfoTransfer.importMetainfo(metainfo);

        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase);
        GUIAttribute.assertAbsence(attrString);
    }

    /**
     * Тестирование базовой настройки атрибута для статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00340
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать в scCase атрибут типа строка attrString</li>
     * <li>Создать в scCase статус state (между зарегистрирован и разрешен)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти карточку state</li>
     * <li>Для attrString установить: не отображать, (редактировать исчезла), не
     * заполнять при входе, обязательно заполнять при выходе</li>
     * <li>Нажать сохранить.</li>
     * <br>
     * <b>Проверки</b>
     * <li>Зайти в карточку state, проверить, что настройки сохранились</li>
     * </ol>
     */
    @Test
    public void testSetAttrNotVisibleInState()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();

        Attribute attrString = DAOAttribute.createString(scCase.getFqn());
        DSLMetainfo.add(scCase, attrString);

        BoStatus status = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.EMPLOYEE, Strategy.EMPTY);
        DSLBoStatus.add(status);
        //Тестовые действия
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(status);
        //Собственно меняем
        tester.click(String.format(GUIXpath.Div.ANY_IN_STATE, attrString.getCode()) + GUIXpath.Span.SPAN_CODE);
        tester.click(GUIXpath.Div.POPUP_LIST_SELECT + GUIXpath.Span.ASTATE_IN_HIDE);
        tester.click(String.format(GUIXpath.Div.ANY_POST_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_DOT);
        tester.click(GUIXpath.Div.POPUP_LIST_SELECT + GUIXpath.Span.ASTATE_POST_FILL_STAR);
        tester.click(GUIXpath.Div.SAVE);

        GUIBoStatus.goToStatusCardWithRefresh(status);
        GUIBoStatus.assertAttributeInState(attrString, 2);
        Assert.assertTrue("Атрибут не должен заполняться при входе в статус",
                tester.find(String.format(GUIXpath.Div.ANY_PRE_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_DOT)
                        .isDisplayed());
        Assert.assertTrue("Атрибут должен обязательно заполняться при выходе из статуса", tester.find(
                        String.format(GUIXpath.Div.ANY_POST_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_POST_FILL_STAR)
                .isDisplayed());
    }

    /**
     * Тестирование базовой настройки атрибута для статуса (обязательно на входе, заполнять на выходе)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00340
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать в scCase атрибут типа строка attrString</li>
     * <li>Создать в scCase статус state (между зарегистрирован и разрешен)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти карточку state</li>
     * <li>Для attrString установить: отображать, редактировать, обязательно
     * заполнять при входе, заполнять при выходе</li>
     * <li>Нажать сохранить.</li>
     * <br>
     * <b>Проверки</b>
     * <li>Зайти в карточку state, проверить, что настройки сохранились</li>
     * </ol>
     */
    @Test
    public void testSetAttrRequiredInState()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();

        Attribute attrString = DAOAttribute.createString(scCase.getFqn());
        DSLMetainfo.add(scCase, attrString);

        BoStatus status = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.EMPLOYEE, Strategy.EMPTY);
        DSLBoStatus.add(status);
        //Тестовые действия
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(status);
        //Проверяем текущие настройки атрибута
        GUIBoStatus.assertAttributeInState(attrString, 0);
        Assert.assertTrue("Атрибут не должен заполняться при входе в статус", tester.find(String.format(
                GUIXpath.Div.ANY_PRE_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_DOT).isDisplayed());
        Assert.assertTrue("Атрибут не должен заполняться при выходе из статуса", tester.find(String.format(
                GUIXpath.Div.ANY_POST_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_DOT).isDisplayed());
        //Собственно меняем
        tester.click(String.format(GUIXpath.Div.ANY_PRE_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_DOT);
        tester.click(GUIXpath.Div.POPUP_LIST_SELECT + GUIXpath.Span.ASTATE_PRE_FILL_STAR_NORMAL);

        tester.click(String.format(GUIXpath.Div.ANY_POST_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_DOT);
        tester.click(GUIXpath.Div.POPUP_LIST_SELECT + GUIXpath.Span.ASTATE_POST_FILL_NORMAL);
        tester.click("//div[@id='gwt-debug-save']");
        GUIBoStatus.goToStatusCardWithRefresh(status);
        GUIBoStatus.assertAttributeInState(attrString, 0);
        Assert.assertTrue("Атрибут  должен обязательно заполняться при входе в статус", tester.find(String.format(
                        GUIXpath.Div.ANY_PRE_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_PRE_FILL_STAR_NORMAL)
                .isDisplayed());
        Assert.assertTrue("Атрибут должен заполняться при выходе из статуса", tester.find(String.format(
                        GUIXpath.Div.ANY_POST_FILL, attrString.getCode()) + GUIXpath.Span.ASTATE_POST_FILL_NORMAL)
                .isDisplayed());
    }

    /**
     * Тестирование наследования настроек комментария и атрибутов в разрезе статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00340
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе Запрос создать пользовательский статус state</li>
     * <li>В классе Запрос создать атрибут stringAttribute типа "Строка"</li>
     * <li>В блоке "Управление параметрами объекта" настраить параметры для атрибута stringAttribute: Отображать - да;
     * Редактировать - да; Заполнять при входе в статус - Обязательно заполнять при входе в статус;
     * Заполнять при выходе из статуса - Не заполнять</li>
     * <li>В блоке "Управление параметрами объекта" настраить параметры для параметра "Комментарии" comment:
     * Редактировать - нет; Заполнять при входе в статус - нет; Заполнять при выходе из статуса - нет</li>
     * <li>Создать тип Запроса scCase</li>
     * <br>
     * <b>Действие</b>
     * <li>Войти под суперпользователем на карточку редактирования статуса state</li>
     * <br>
     * <b>Проверка</b>
     * <li>В типе запроса scCase в статусе state, в блоке "Управление параметрами объекта", для атрибута
     * stringAttribute и
     * параметра comment настройки пронаследовались из класса Запрос</li>
     * </ol>
     */
    @Test
    public void testSettingsInheritanceOfAttrAndCommentInState()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();

        BoStatus state = DAOBoStatus.createUserStatus(scClass.getFqn());
        DSLBoStatus.add(state);

        Attribute comment = createPseudo("", "@comment", "");
        Attribute stringAttribute = DAOAttribute.createString(scClass.getFqn());
        DSLAttribute.add(stringAttribute);

        DSLBoStatus.setAttrInState(stringAttribute, state, true, true, 2, 0);
        DSLBoStatus.setAttrInState(comment, state, true, false, 0, 0);

        MetaClass scCase = DAOScCase.create(scClass);
        DSLMetaClass.add(scCase);

        // Действие
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(scCase, state);

        // Проверка
        GUIBoStatus.assertAttributeInState(stringAttribute, 0);
        GUIBoStatus.assertAttributePreFillState(stringAttribute, 2);
        GUIBoStatus.assertAttributePostFillState(stringAttribute, 0);

        GUIBoStatus.assertAttributeInState(comment, 1);
        GUIBoStatus.assertAttributePreFillState(comment, 0);
        GUIBoStatus.assertAttributePostFillState(comment, 0);
    }

    /**
     * Тестирование отображения параметров не редактируемого атрибута в блоке Управление параметрами объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00340
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип Запроса scCase</li>
     * <li>В типе запроса scCase создать не редактируемый атрибут stringAttribute типа "Строка"</li>
     * <br>
     * <b>Действия</b>
     * <li>Войти под суперпользователем на карточку любого статуса (Разрешен resolved) в типе запроса scCase</li>
     * <br>
     * <b>Проверка</b>
     * <li>Следующие параметры статуса типа запроса для атрибута stringAttribute не редактируемые: "Редактировать",
     * "Заполнять при входе в статус", "Заполнять при выходе из статуса"</li>
     * </ol>
     */
    @Test
    public void testViewSettingsOfNonEditableStringAttrInState()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create(DAOScCase.createClass());

        Attribute stringAttribute = DAOAttribute.createString(scCase);
        stringAttribute.setEditable(Boolean.FALSE.toString());
        DSLMetainfo.add(scCase, stringAttribute);

        // Действия
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(DAOBoStatus.createResolved(scCase.getFqn()));

        // Проверка
        GUITester.assertExists(String.format(GUIXpath.Div.ANY_PRE_FILL, stringAttribute.getCode()), false);
        GUITester.assertExists(String.format(GUIXpath.Div.ANY_POST_FILL, stringAttribute.getCode()), false);
        tester.click(GUIXpath.Div.ANY_IN_STATE + GUIXpath.Span.SPAN_CODE, stringAttribute.getCode());
        GUITester.assertExists(GUIXpath.Div.POPUP_LIST_SELECT + GUIXpath.Span.ASTATE_IN_EDIT, false);
    }

    /**
     * Тестирование сохранения настроек атрибута в статусе типа, если настройки отличны от настроек в классе
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$148201615
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский userClass класс с жизненным циклом и вложенный в него тип userCase</li>
     * <li>Создать атрибут strAttr типа "Строка" в классе userClass</li>
     * <li>Создать пользовательский статус status в userClass</li>
     * <li>В блоке "Управление параметрами объекта" для класса userClass
     * настроить параметры для атрибута strAttr в статусе state:
     * Отображать - да; Редактировать - да; Заполнять при входе в статус -Не заполнять;
     * Заполнять при выходе из статуса - Не заполнять</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти в ИА под суперпользователем</li>
     * <li>Перейти на карточку статуса status в типе userCase</li>
     * <li>Изменить параметр для атрибута strAttr "Заполнять при входе в статус" на "Заполнять при входе в статус"</li>
     * <li>Нажать кнопку "Сохранить" в блоке "Управление параметрами объекта"</li>
     * <li>Изменить параметр для атрибута strAttr "Заполнять при входе в статус" на "Не заполнять"</li>
     * <li>Нажать кнопку "Сохранить" в блоке "Управление параметрами объекта"</li>
     * <li>Проверить, что сообщение об ошибке отсутствует</li>
     * </ol>
     */
    @Test
    public void testSaveSettingsAttrOnState()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Attribute strAttr = DAOAttribute.createString(userClass);
        DSLAttribute.add(strAttr);
        BoStatus status = DAOBoStatus.createUserStatus(userClass.getFqn());
        DSLBoStatus.add(status);

        DSLBoStatus.setAttrInState(strAttr, status, true, true, 0, 0);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(userCase, status);

        tester.click(String.format(GUIXpath.Div.ANY_PRE_FILL, strAttr.getCode()) + GUIXpath.Span.ASTATE_DOT);
        tester.click(GUIXpath.Div.POPUP_LIST_SELECT + GUIXpath.Span.ASTATE_PRE_FILL_NORMAL);
        tester.click(GUIXpath.Div.SAVE);

        tester.click(String.format(GUIXpath.Div.ANY_PRE_FILL, strAttr.getCode()) +
                     GUIXpath.Span.ASTATE_PRE_FILL_NORMAL);
        tester.click(GUIXpath.Div.POPUP_LIST_SELECT + GUIXpath.Span.ASTATE_DOT);
        tester.click(GUIXpath.Div.SAVE);

        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование отсутствия атрибута на списке связанных объектов, если атрибут отключен для отобраджения в статусе
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$164443934
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00340
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательские классы: userScClass, userTaskClass и в них типы userScCase, userTaskCase</li>
     * <li>В классе userScClass создать атрибут типа "Ссылка на бизнес объект" sboAttrToTask</li>
     * <li>В классе userScClass создать группу атрибутов grpInUserScClass и добавить в нее атрибуты: sboAttrToTask,
     * системный атрибут "Наименование"</li>
     * <li>В классе userTaskClass создать атрибут типа "Строка" strAttrInTask и задать ему значение по-умолчанию</li>
     * <li>В классе userTaskClass создать группу атрибуто grpInTaskClass и добавить в нее атрибуты: strAttrInTask,
     * системный атрибут "Наименование"</li>
     * <li>В классе userScClass создать контент типа "Параметры связаного объекта" relObjPropListInScClass, на
     * класс userTaskClass, по атрибуту sboAttrToTask</li>
     * <li>Зайти под суперпользователем</li>
     * <li>Перейти на подвкладку "Управление параметрами в статусах" вкладки "Жизненный цикл" в классе
     * userTaskClass</li>
     * <li>Нажать кнопку "Не отображать" для атрибута strAttrInTask в сатусах "Зарегистрирован" и "Закрыт"</li>
     * <li>Создать объекты scBo и taskBo</li>
     * <li>Атрибуту sboAttrToTask для объекта scBo присвоить значение taskBo</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Перейти карточку объекта scBo</li>
     * <li>Проверить, что атрибут strAttrInTask не виден на контенте relObjPropListInScClass</li>
     * </ol>
     */
    @Test
    public void testSetAttrNotVisibleOnRelPropertyList()
    {
        //Подготовка
        MetaClass userScClass = DAOUserClass.create();
        MetaClass userScCase = DAOUserCase.create(userScClass);

        MetaClass userTaskClass = DAOUserClass.createWithWF();
        MetaClass userTaskCase = DAOUserCase.create(userTaskClass);

        DSLMetainfo.add(userScClass, userScCase, userTaskClass, userTaskCase);

        Attribute sboAttrToTask = DAOAttribute.createObjectLink(userScClass, userTaskClass, null);
        DSLAttribute.add(sboAttrToTask);

        GroupAttr grpInUserScClass = DAOGroupAttr.create(userScClass);
        DSLGroupAttr.add(grpInUserScClass, sboAttrToTask, SysAttribute.title(userScClass));

        Attribute strAttrInTask = DAOAttribute.createString(userTaskClass);
        DSLAttribute.add(strAttrInTask);
        strAttrInTask.setDefaultValue("testDefault");
        DSLAttribute.edit(strAttrInTask);

        GroupAttr grpInTaskClass = DAOGroupAttr.create(userTaskClass);
        DSLGroupAttr.add(grpInTaskClass, strAttrInTask, SysAttribute.title(userTaskClass));

        ContentForm relObjPropListInScClass = DAOContentCard.createRelObjPropList(userScClass, sboAttrToTask,
                grpInTaskClass);

        DSLContent.add(relObjPropListInScClass);
        BoStatus registered = DAOBoStatus.createRegistered(userTaskClass.getFqn());
        BoStatus closed = DAOBoStatus.createClosed(userTaskClass.getFqn());

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userTaskClass, MetaclassCardTab.LIFECYCLE);
        GUIWorkflow.goToStatesAttrsSettingsTab();

        GUIBoStatus.clickInStatesAttrsSettings(strAttrInTask, registered.getCode());
        tester.click(GUIXpath.Div.POPUP_LIST_SELECT + GUIXpath.Span.ASTATE_IN_HIDE);

        GUIBoStatus.clickInStatesAttrsSettings(strAttrInTask, closed.getCode());
        tester.click(GUIXpath.Div.POPUP_LIST_SELECT + GUIXpath.Span.ASTATE_IN_HIDE);

        tester.click(GUIXpath.Div.SAVE);

        Bo scBo = DAOUserBo.create(userScCase);
        Bo taskBo = DAOUserBo.create(userTaskCase);
        DSLBo.add(scBo, taskBo);
        sboAttrToTask.setValue(taskBo.getUuid());
        DSLBo.editAttributeValue(scBo, sboAttrToTask);

        //Действия и проверки
        GUIBo.goToCard(scBo);
        GUIPropertyList.assertAbsenceAttribute(relObjPropListInScClass, strAttrInTask);
    }
}