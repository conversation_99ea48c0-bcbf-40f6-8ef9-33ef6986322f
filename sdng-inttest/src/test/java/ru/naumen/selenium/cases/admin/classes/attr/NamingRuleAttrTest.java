package ru.naumen.selenium.cases.admin.classes.attr;

import static org.junit.Assert.assertTrue;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOCommentClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOFileClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тестирование правил нумерации и именования в атрибутах
 * <AUTHOR>
 * @since 20.12.2013
 */
public class NamingRuleAttrTest extends AbstractTestCase
{
    private static final String NAME_ACTUAL_VALUE = "З/а_{N}.2013";
    private static final String NUMBER_ACTUAL_VALUE = "{ND}";

    /**
     * Тестирование отсутствия чекбокса "Определяемый по правилу формирования номера" для атрибута
     * "Номер" в классах Сотрудник, Компания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Заходим последовательно в карточку классов: Компания, Комментарий, Событие, Файл
     * <br>
     * <b>Проверки</b>
     * <li>В блоке "Системные атрибуты" отсутствует атрибут "Номер"
     * </ol>
     */
    @Test
    public void testAssertUseGenRuleNumberField()
    {
        MetaClass[] classes = { DAORootClass.create(), DAOCommentClass.create(), DAOEventClass.create(),
                DAOFileClass.create() };

        GUILogon.asSuper();
        for (MetaClass mc : classes)
        {
            //Выполнение действия
            GUIMetaClass.goToCard(mc);

            //Проверки
            Attribute numberAttr = SysAttribute.number(mc);
            assertTrue("Атрибут 'Номер' присутствует в метаклассе: " + mc.getTitle(),
                    tester.waitDisappear(GUIAttribute.ATTRIBUTE_TABLE_ELEMENT, numberAttr.getCode(), "code"));
        }
    }

    /**
     * Тестирование отсутствия чекбокса "Определяемый по правилу именования" для атрибута
     * "Название" в классах Сотрудник, Компания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Заходим последовательно в карточку классов: Сотрудник, Файл
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Название"
     * <br>
     * <b>Проверки</b>
     * <li>На форме отсутствует поле "Определяемый по правилу именования"
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим последовательно в карточку классов: Комментарий, Событие
     * <br>
     * <b>Проверки</b>
     * <li>В блоке "Системные атрибуты" отсутствует атрибут "Название"
     * </ol>
     */
    @Test
    public void testAssertUseGenRuleTitleField()
    {
        //Подготовка
        MetaClass[] classes = { DAOEmployeeCase.createClass(), DAOFileClass.create() };

        GUILogon.asSuper();
        for (MetaClass mc : classes)
        {
            //Выполнение действия
            GUIMetaClass.goToCard(mc);
            Attribute titleAttr = SysAttribute.title(mc);
            GUIAttribute.clickEdit(titleAttr);

            //Проверки
            assertTrue("На форме присутствует поле 'Определяемый по правилу именования'",
                    tester.waitDisappear(GUIAttribute.X_USE_GEN_RULE_CHECKBOX));
            GUIForm.cancelForm();
        }

        MetaClass[] classesWithoutTitle = { DAOCommentClass.create(), DAOEventClass.create() };
        for (MetaClass mc : classesWithoutTitle)
        {
            //Выполнение действия
            GUIMetaClass.goToCard(mc);

            //Проверки
            Attribute titleAttr = SysAttribute.title(mc);
            assertTrue("Атрибут 'Название' присутствует в метаклассе: " + mc.getTitle(),
                    tester.waitDisappear(GUIAttribute.ATTRIBUTE_TABLE_ELEMENT, titleAttr.getCode(), "code"));
        }
    }

    /**
     * Тестирование копирования типа запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase
     * <li>В scCase для атрибутов Номер и Название указать правило {N}1
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Войти под суперпользователем
     * <li>Заходим в карточку scCase
     * <li>Нажать кнопку "Копировать", заполнить основные поля
     * <li>Проверить, что на форме присутствует поле "Правило формирования атрибута "Название" (title):"
     * со значением "{N}1"
     * <li>Проверить, что на форме присутствует поле "Правило формирования атрибута "Номер" (number):"
     * со значением "{N}1"
     * <li>Сохранить
     * <li>Проверяем поле "Правило формирования атрибута "Название" (title):" на карточке скопированного типа - "{N}1"
     * <li>Проверяем поле "Правило формирования номера(атрибут "Правило формирования атрибута "Номер" (number):"
     * </ol>
     */
    @Test
    public void testCopyScCase()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        String generationRule = "{N}1";

        Attribute numberAttr = SysAttribute.number(scCase);
        numberAttr.setGenerationRule(generationRule);

        Attribute titleAttr = SysAttribute.title(scCase);
        titleAttr.setGenerationRule(generationRule);

        DSLAttribute.edit(numberAttr, titleAttr);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase);

        MetaClass copyScCase = DAOScCase.create();
        GUIMetaClass.clickCopy();
        GUIMetaClass.fillAddForm(copyScCase);

        GUITester.assertTextPresent(GUIMetaClass.TITLE_RULE_CAPTION + "/label",
                "Правило формирования атрибута \"Название\" (title):");
        GUITester.assertTextPresent(GUIMetaClass.TITLE_RULE_CAPTION + "/span", generationRule);

        GUITester.assertTextPresent(GUIMetaClass.NUMBER_RULE_CAPTION + "/label",
                "Правило формирования атрибута \"Номер\" (number):");
        GUITester.assertTextPresent(GUIMetaClass.NUMBER_RULE_CAPTION + "/span", generationRule);

        GUIForm.applyModalForm();
        copyScCase.setExists(true);
        GUIMetaClass.goToCard(copyScCase);

        GUITester.assertTextPresent(GUIMetaClass.TITLE_RULE_VALUE, generationRule);
        GUITester.assertTextPresent(GUIMetaClass.NUMBER_RULE_VALUE, generationRule);
    }

    /**
     * Тестирование правила "Определяемый по правилу формирования номера" по умолчанию на форме редактирования
     * атрибута "Номер" в классе Запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Заходим в карточку класса Запрос, вкладка атрибуты
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Номер"
     * <br>
     * <b>Проверки</b>
     * <li>На форме присутствует установленный чекбокс "Определяемый по правилу формирования номера"
     * <li>В поле "Правило формирования номера" значение {N}
     * </ol>
     */
    @Test
    public void testDefaultValueUseGenNumberRuleInScClass()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        Attribute numberAttr = SysAttribute.number(scClass);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scClass);

        GUIAttribute.clickEdit(numberAttr);

        //Проверки
        assertTrue("На форме присутствует поле ввода 'Определяемый по правилу формирования номера'",
                tester.waitAppear(GUIAttribute.X_USE_GEN_RULE_CHECKBOX));

        GUITester.assertCheckboxState(GUIAttribute.X_USE_GEN_RULE_CHECKBOX_VALUE, true);

        GUITester.assertValue(GUIAttribute.X_GEN_RULE_VALUE, "{N}");

        GUIForm.cancelForm();
    }

    /**
     * Тестирование правила "Определяемый по правилу формирования номера" по умолчанию на форме редактирования
     * атрибута "Название" в классе Запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Заходим в карточку класса Запрос, вкладка атрибуты
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Название"
     * <br>
     * <b>Проверки</b>
     * <li>На форме присутствует установленный чекбокс "Определяемый по правилу именования"
     * <li>В поле "Правило именования" значение {N}
     * </ol>
     */
    @Test
    public void testDefaultValueUseGenTitleRuleInScClass()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        Attribute titleAttr = SysAttribute.title(scClass);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scClass);

        GUIAttribute.clickEdit(titleAttr);

        //Проверки
        assertTrue("На форме присутствует поле ввода 'Определяемый по правилу именования'",
                tester.waitAppear(GUIAttribute.X_USE_GEN_RULE_CHECKBOX));

        GUITester.assertCheckboxState(GUIAttribute.X_USE_GEN_RULE_CHECKBOX_VALUE, true);

        GUITester.assertValue(GUIAttribute.X_GEN_RULE_VALUE, "{N}");

        GUIForm.cancelForm();
    }

    /**
     * Тестирование изменений на форме редактирования атрибута "Номер" при установке чекбокса
     * "Определяемый по правилу формирования номера"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Заходим в карточку класса userClass, вкладка атрибуты
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Номер"
     * <li>Ставим галочку в чекбоксе "Определяемый по правилу формирования номера"
     * <br>
     * <b>Проверки</b>
     * <li>На форме отсутствуют поля "Вычислимый", "Определяемый по таблице соответствий", "Редактируемый", "Уникальный"
     * <li>Появилось поле "Правило именования", не заполнено
     * <li>Заполняем поле "Правило именования", нажимаем "Сохранить"
     * <li>Проверяем, что атрибут "Номер" не "Редактируемый" и не "Уникальный"
     * <li>Проверяем поле "Правило формирования номера(атрибут "Номер" (number))" на карточке userClass
     * </ol>
     */
    @Test
    public void testEditUseGenNumberRule()
    {
        MetaClass userClass = DAOUserClass.create();
        Attribute numberAttr = SysAttribute.number(userClass);
        numberAttr.setRequired(Boolean.FALSE.toString());
        editUseGenRule(numberAttr, userClass);
        GUITester.assertTextPresent(GUIMetaClass.NUMBER_RULE_VALUE, NUMBER_ACTUAL_VALUE);
    }

    /**
     * Тестирование изменений на форме редактирования атрибута "Название" при установке чекбокса
     * "Определяемый по правилу именования"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Заходим в карточку класса userClass, вкладка атрибуты
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Название"
     * <li>Ставим галочку в чекбоксе "Определяемый по правилу именования"
     * <br>
     * <b>Проверки</b>
     * <li>На форме отсутствуют поля "Вычислимый", "Определяемый по таблице соответствий", "Редактируемый", "Уникальный"
     * <li>Появилось поле "Правило именования", не заполнено
     * <li>Заполняем поле "Правило именования", нажимаем "Сохранить"
     * <li>Проверяем, что атрибут "Название" не "Редактируемый" и не "Уникальный"
     * <li>Проверяем поле "Правило именования(атрибут "Название" (title))" на карточке userClass
     * </ol>
     */
    @Test
    public void testEditUseGenTitleRule()
    {
        MetaClass userClass = DAOUserClass.create();
        Attribute titleAttr = SysAttribute.title(userClass);
        editUseGenRule(titleAttr, userClass);
        GUITester.assertTextPresent(GUIMetaClass.TITLE_RULE_VALUE, NAME_ACTUAL_VALUE);
    }

    /**
     * Тестирование наследования правил именования и формирования номера из класса
     * Запрос при добавлении типа в этот класс
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под суперпользователем
     * <li>Перейти на форму добавления типа запроса, заполнить основные поля
     * <li>Проверить, что на форме присутствует поле "Правило формирования атрибута "Название" (title):"
     * со значением "{N}"
     * <li>Проверить, что на форме присутствует поле "Правило формирования атрибута "Номер" (number):"
     * со значением "{N}"
     * <li>Нажимаем "Сохранить", переходим на карточку созданного типа запроса
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Название"
     * <li>Проверить, что установлен чекбокс "Наследовать параметры"
     * <li>Проверить, что на форме присутствует установленный чекбокс "Определяемый по правилу формирования номера"
     * <li>Проверить, что в поле "Правило именования" значение {N}
     * <li>Нажать "Отмена"
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Номер"
     * <li>Проверить, что установлен чекбокс "Наследовать параметры"
     * <li>Проверить, что на форме присутствует установленный чекбокс "Определяемый по правилу формирования номера"
     * <li>Проверить, что в поле "Правило формирования номера" значение {N}
     * </ol>
     */
    @Test
    public void testInheredGenRulesInScCase()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        Attribute titleAttr = SysAttribute.title(scCase);
        Attribute numberAttr = SysAttribute.number(scCase);

        //Выполнение действий и проверки
        GUILogon.asSuper();

        GUIMetaClass.callAddForm(scCase);
        GUIMetaClass.fillAddForm(scCase);

        GUITester.assertTextPresent(GUIMetaClass.TITLE_RULE_CAPTION + "/label",
                "Правило формирования атрибута \"Название\" (title):");
        GUITester.assertTextPresent(GUIMetaClass.TITLE_RULE_CAPTION + "/span", "{N}");

        GUITester.assertTextPresent(GUIMetaClass.NUMBER_RULE_CAPTION + "/label",
                "Правило формирования атрибута \"Номер\" (number):");
        GUITester.assertTextPresent(GUIMetaClass.NUMBER_RULE_CAPTION + "/span", "{N}");

        GUIForm.applyModalForm();
        scCase.setExists(true);
        GUIMetaClass.goToCard(scCase);

        //атрибут "Название"
        GUIAttribute.clickEdit(titleAttr);
        GUITester.assertCheckboxState(GUIXpath.Complex.INHERIT_VALUE_INPUT, true);

        assertTrue("На форме присутствует поле ввода 'Определяемый по правилу именования'",
                tester.waitAppear(GUIAttribute.X_USE_GEN_RULE_CHECKBOX));

        GUITester.assertValue(GUIAttribute.X_GEN_RULE_VALUE, "{N}");

        GUIForm.cancelForm();

        //атрибут "Номер"
        GUIAttribute.clickEdit(numberAttr);
        GUITester.assertCheckboxState(GUIXpath.Complex.INHERIT_VALUE_INPUT, true);

        assertTrue("На форме присутствует поле ввода 'Определяемый по правилу формирования номера'",
                tester.waitAppear(GUIAttribute.X_USE_GEN_RULE_CHECKBOX));

        GUITester.assertValue(GUIAttribute.X_GEN_RULE_VALUE, "{N}");

        GUIForm.cancelForm();
    }

    /**
     * Тестирование наследования правил именования и формирования номера из пользовательского
     * класса при добавлении типа в этот класс
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass
     * <li>В userClass для атрибута Номер указать правило {ND}
     * <li>В userClass для атрибута Название указать правило З/а_{N}.2013
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Перейти на форму добавления типа в userClass
     * <b>Проверки</b>
     * <li>На форме добавления типа в поле "Правило формирования атрибута "Название" (title):" стоит значение "З/а_{N
     * }.2013"
     * <li>На форме добавления типа в поле "Правило формирования атрибута "Номер" (number):" стоит значение "{ND}"
     * </ol>
     */
    @Test
    public void testInheredGenRulesInUserCase()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);

        Attribute numberAttr = SysAttribute.number(userClass);
        numberAttr.setGenerationRule(NUMBER_ACTUAL_VALUE);

        Attribute titleAttr = SysAttribute.title(userClass);
        titleAttr.setGenerationRule(NAME_ACTUAL_VALUE);

        DSLAttribute.edit(numberAttr, titleAttr);

        //Выполнение действия
        GUILogon.asSuper();
        MetaClass userCase = DAOUserCase.create(userClass);
        GUIMetaClass.callAddForm(userCase);

        //Проверки
        GUITester.assertTextPresent(GUIMetaClass.TITLE_RULE_CAPTION + "/label",
                "Правило формирования атрибута \"Название\" (title):");
        GUITester.assertTextPresent(GUIMetaClass.TITLE_RULE_CAPTION + "/span", NAME_ACTUAL_VALUE);

        GUITester.assertTextPresent(GUIMetaClass.NUMBER_RULE_CAPTION + "/label",
                "Правило формирования атрибута \"Номер\" (number):");
        GUITester.assertTextPresent(GUIMetaClass.NUMBER_RULE_CAPTION + "/span", NUMBER_ACTUAL_VALUE);
    }

    /**
     * Тестирование ограничения по количеству символов в поле "Правило формирования номера"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Заходим в карточку scCase, вкладка атрибуты
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Номер"
     * <li>Убираем галку в "Наследовать параметры"
     * <li>В поле "Правило формирования номера" вводим строку длиной 257 символов, нажимаем "Сохранить"
     * <li>Проверяем, что форма не закрылась, появилось корректное предупреждение
     * <li>В поле "Правило формирования номера" вводим строку длиной 256 символов, нажимаем "Сохранить"
     * <li>Проверяем поле "Правило формирования номера(атрибут "Номер" (number))" на карточке scCase
     * </ol>
     */
    @Test
    public void testLengthGenNumberRule()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        Attribute numberAttr = SysAttribute.number(scCase);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase);

        GUIAttribute.clickEdit(numberAttr);
        tester.setCheckbox(GUIXpath.Complex.INHERIT_VALUE_INPUT, false);

        String len_255 = org.apache.commons.lang3.StringUtils.repeat("{N}", 85);

        GUIAttribute.setGenRule(len_255 + "11");
        GUIForm.applyFormAssertValidation(GUIXpath.Div.PROPERTY_DIALOG_BOX, ErrorMessages.LENGTH_257);

        GUIAttribute.setGenRule(len_255 + "1");
        GUIForm.applyModalForm();

        GUITester.assertTextPresent(GUIMetaClass.NUMBER_RULE_VALUE, len_255 + "1");
    }

    /**
     * Тестирование ограничения по количеству символов в поле "Правило именования"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Заходим в карточку scCase, вкладка атрибуты
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Название"
     * <li>Убираем галку в "Наследовать параметры"
     * <li>В поле "Правило именования" вводим строку длиной 257 символов, нажимаем "Сохранить"
     * <li>Проверяем, что форма не закрылась, появилось корректное предупреждение
     * <li>В поле "Правило именования" вводим строку длиной 256 символов, нажимаем "Сохранить"
     * <li>Проверяем поле "Правило именования(атрибут "Название" (title))" на карточке scCase
     * </ol>
     */
    @Test
    public void testLengthGenTitleRule()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        Attribute titleAttr = SysAttribute.title(scCase);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase);

        GUIAttribute.clickEdit(titleAttr);
        tester.setCheckbox(GUIXpath.Complex.INHERIT_VALUE_INPUT, false);

        String len_255 = org.apache.commons.lang3.StringUtils.repeat("{N}", 85);

        GUIAttribute.setGenRule(len_255 + "11");
        GUIForm.applyFormAssertValidation(GUIXpath.Div.PROPERTY_DIALOG_BOX, ErrorMessages.LENGTH_257);

        GUIAttribute.setGenRule(len_255 + "1");
        GUIForm.applyModalForm();

        GUITester.assertTextPresent(GUIMetaClass.TITLE_RULE_VALUE, len_255 + "1");
    }

    /**
     * Тестирование сохранения значения в поле "Значение" (по умолчанию) на форме редактирования атрибута "Номер"
     * при установке чекбокса "Определяемый по правилу формирования номера"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Заходим в карточку класса userClass, вкладка атрибуты
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Номер"
     * <li>Ставим галочку в чекбоксе "Определяемый по правилу формирования номера", заполняем поле "Правило именования"
     * <li>Заполняем поле "Значение" (по умолчанию) значением 1, сохраняем
     * <br>
     * <b>Проверки</b>
     * <li>У атрибута "Номер" значение по умолчанию - 1
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Номер"
     * <li>В поле "Значение" (по умолчанию) значение 1
     * </ol>
     */
    @Test
    public void testSaveDefaultValueWithUseGenNumberRule()
    {
        MetaClass userClass = DAOUserClass.create();
        Attribute numberAttr = SysAttribute.number(userClass);
        numberAttr.setRequired(Boolean.FALSE.toString());
        saveDefaultValueWithUseGenRule(numberAttr, userClass);
    }

    /**
     * Тестирование сохранения значения в поле "Значение" (по умолчанию) на форме редактирования атрибута "Название"
     * при установке чекбокса "Определяемый по правилу именования"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Заходим в карточку класса userClass, вкладка атрибуты
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Название"
     * <li>Ставим галочку в чекбоксе "Определяемый по правилу именования", заполняем поле "Правило именования"
     * <li>Заполняем поле "Значение" (по умолчанию) значением 1, сохраняем
     * <br>
     * <b>Проверки</b>
     * <li>У атрибута "Название" значение по умолчанию - 1
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Название"
     * <li>В поле "Значение" (по умолчанию) значение 1
     * </ol>
     */
    @Test
    public void testSaveDefaultValueWithUseGenTitleRule()
    {
        MetaClass userClass = DAOUserClass.create();
        Attribute titleAttr = SysAttribute.title(userClass);
        saveDefaultValueWithUseGenRule(titleAttr, userClass);
    }

    /**
     * Тестирование валидации введенного правила для атрибута "Номер"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00275
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип соглашения agreementCase
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем
     * <li>Заходим в карточку agreementCase
     * <li>В блоке "Системные атрибуты" нажимаем "Редактировать" напротив атрибута "Номер"
     * <li>Убираем галку "Наследовать параметры"
     * <li>Ставим галку в чекбоксе "Определяемый по правилу формирования номера",
     * заполняем поле "Правило именования" значением "abc"
     * <li>Нажимаем "Сохранить"
     * <br>
     * <b>Проверки</b>
     * <li>Появилось корректное сообщение об ошибке
     * </ol>
     */
    @Test
    public void testValidationGenNumberRule()
    {
        //Подготовка
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(agreementCase);
        Attribute numberAttr = SysAttribute.number(agreementCase);

        //Выполнение действия
        GUILogon.asSuper();
        GUIMetaClass.goToCard(agreementCase);

        GUIAttribute.clickEdit(numberAttr);
        tester.setCheckbox(GUIXpath.Complex.INHERIT_VALUE_INPUT, false);
        GUIAttribute.setUseGenRule(true);
        GUIAttribute.setGenRule("abc");

        //Проверки
        GUIForm.applyFormAssertError(ErrorMessages.GEN_RULE_HELP);
    }

    /**
     * Шаблон для тестирования изменений на форме редактирования атрибута "Название"/"Номер" при установке чекбокса
     * "Определяемый по правилу именования"/"Определяемый по правилу формирования номера"
     * @param attr модель атрибута "Название"/"Номер"
     * @param userClass модель пользовательского класса
     */
    private void editUseGenRule(Attribute attr, MetaClass userClass)
    {
        //Подготовка
        DSLMetaClass.add(userClass);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToCard(userClass);

        GUIAttribute.clickEdit(attr);
        GUIAttribute.setUseGenRule(true);

        //Проверки
        assertTrue("На форме присутствует поле 'Вычислимый'", tester.waitDisappear(GUIAttribute.X_COMPUTABLE_CAPTION));

        assertTrue("На форме присутствует поле 'Определяемый по таблице соответствий'",
                tester.waitDisappear(GUIAttribute.X_DETERMINABLE_CHECKBOX));

        assertTrue("На форме присутствует поле 'Редактируемый'",
                tester.waitDisappear(GUIXpath.InputComplex.EDITABLE_VALUE));

        assertTrue("На форме присутствует поле 'Уникальный'", tester.waitDisappear(GUIXpath.Any.UNIQUE_VALUE));

        assertTrue("На форме отсутствует поле 'Правило именования'", tester.waitAppear(GUIAttribute.X_GEN_RULE_VALUE));
        assertTrue("На форме отсутствует [Справка] для поля 'Правило именования'",
                tester.waitAppear(GUIAttribute.X_GEN_RULE_HELP));

        GUITester.assertValue(GUIAttribute.X_GEN_RULE_VALUE, "");
        GUIAttribute.setGenRule(attr.getCode().equals("number") ? NUMBER_ACTUAL_VALUE : NAME_ACTUAL_VALUE);
        GUIForm.applyModalForm();

        attr.setEditable(Boolean.FALSE.toString());
        attr.setUnique(Boolean.FALSE.toString());
        GUIAttribute.assertPresent(attr);
    }

    /**
     * Шаблон для тестирования сохранения значения в поле "Значение" (по умолчанию) на 
     * форме редактирования атрибута "Название"/"Номер" при установке чекбокса 
     * "Определяемый по правилу именования"/"Определяемый по правилу формирования номера"
     * @param attr модель атрибута "Название"/"Номер"
     * @param userClass модель пользовательского класса
     */
    private void saveDefaultValueWithUseGenRule(Attribute attr, MetaClass userClass)
    {
        //Подготовка
        DSLMetaClass.add(userClass);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToCard(userClass);

        GUIAttribute.clickEdit(attr);
        GUIAttribute.setUseGenRule(true);
        GUIAttribute.setGenRule(NUMBER_ACTUAL_VALUE);
        attr.setDefaultValue("1");
        GUIAttribute.fillGeneralDefaultValue(attr);

        GUIForm.applyModalForm();

        //Проверки
        GUIAttribute.assertGeneralDefaultValue(attr);
        GUIAttribute.clickEdit(attr);
        GUIAttribute.assertDefaultValueEditForm(attr);
    }

}
