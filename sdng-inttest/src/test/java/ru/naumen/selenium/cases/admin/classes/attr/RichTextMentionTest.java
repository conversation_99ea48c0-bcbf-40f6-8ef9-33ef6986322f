package ru.naumen.selenium.cases.admin.classes.attr;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.attr.GUIGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.fastlinks.DAOObjectMentions;
import ru.naumen.selenium.casesutil.model.fastlinks.DSLFastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.FastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.GUIFastLinkSetting;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тесты на вставку ссылок на объекты в текст RTF
 *
 * <AUTHOR>
 * @since 08.05.18
 */
public class RichTextMentionTest extends AbstractTestCase
{
    private static final String UUID = "UUID";

    /**
     * Тестирование отсутствия выбороа неабсолютных профилей на форме добавления упоминания
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$63816528
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить группу пользователей emplGroup</li>
     * <li>Добавить абсолютный профиль absoluteProfile с абсолютной ролью absRole ("Сотрудник")</li>
     * <li>Добавить неабсолютный профиль nonAbsoluteProfile с абсолютной ролью absRole ("Сотрудник") и неабсолютной
     * ролью nonAbsRole ("Сотрудник отдела")</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Авторизоваться под суперпользователем</li>
     * <li>Перейти на страницу Настройка системы / Упоминание объектов</li>
     * <li>нажать кнопку "Добавить"</li>
     * <br>
     * <b>Проверки</b>
     * <li>В выпадающем списке выбора профилья присутствует absoluteProfile</li>
     * <li>В выпадающем списке выбора профилья отсутствует nonAbsoluteProfile</li>
     */
    @Test
    public void testAbsentNonAbsoluteProfilesOnEditMentionForm()
    {
        //Подготовка
        DSLSearchSettings.setUseRightsInLists(false);
        SecurityRole absRole = SysRole.employee();
        SecurityRole nonAbsRole = SysRole.ouMember();
        SecurityGroup emplGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(emplGroup);
        SecurityProfile absoluteProfile = DAOSecurityProfile.create(true, emplGroup, absRole);
        SecurityProfile nonAbsoluteProfile = DAOSecurityProfile.create(true, emplGroup, nonAbsRole, absRole);
        DSLSecurityProfile.add(absoluteProfile, nonAbsoluteProfile);
        //Действия
        GUILogon.asNaumen();
        GUINavigational.goToFastLinkSettings();
        GUIFastLinkSetting.clickAdd();
        //Проверки
        GUIFastLinkSetting.assertPresentProfilesOnForm(absoluteProfile);
        GUIFastLinkSetting.assertAbsentProfilesOnForm(nonAbsoluteProfile);
    }

    /**
     * Тестирование добавления подтипа в объекты упоминания при добавлении нового типа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req0075
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$60779749
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с типом userCase</li>
     * <li>Создать упоминание fastLinkSettingUserClass (объекты: userClass, префикс: test1, код атрибута: UUID)</li>
     * <li>Создать упоминание fastLinkSettingUserCase (объекты: userCase, префикс: test2, код атрибута: UUID)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>В типе userCase добавить подтип subUserCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Подтип subUserCase добавлен в объекты упоминания fastLinkSettingUserCase</li>
     * <li>Подтип subUserCase не добавлен в объекты упоминания fastLinkSettingUserClass</li>
     */
    @Test
    public void testAddSubcaseInMentionWithAddingNewSubcase()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        FastLinkSetting fastLinkSettingUserClass = DAOObjectMentions.createFastLinkSetting("test1", UUID, userClass);
        FastLinkSetting fastLinkSettingUserCase = DAOObjectMentions.createFastLinkSetting("test2", UUID, userCase);
        DSLFastLinkSetting.add(fastLinkSettingUserClass, fastLinkSettingUserCase);

        MetaClass subUserCase = DAOUserCase.create(userCase);
        DSLMetaClass.add(subUserCase);

        GUILogon.asNaumen();
        GUINavigational.goToFastLinkSettings();
        GUIFastLinkSetting.assertMentionTypes(fastLinkSettingUserClass, userClass.getTitle());
        GUIFastLinkSetting.assertMentionTypesAbsence(fastLinkSettingUserClass, subUserCase.getTitle());
        GUIFastLinkSetting.assertMentionTypes(fastLinkSettingUserCase,
                String.format("%s, %s", userCase.getTitle(), subUserCase.getTitle()));
    }

    /**
     * Тестирование удаления группы атрибутов, если она используется в настройке упоминания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req0075
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$60779749
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с типом userCase</li>
     * <li>Создать две группы атрибутов (groupAttr1 и groupAttr2) в userClass</li>
     * <li>Создать упоминание fastLinkSetting (объекты: userClass, префикс: @#$, код атрибута: UUID, 
     * Группа атрибутов для сложной формы упоминания: groupAttr2)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Удалить группу groupAttr1</li>
     * <li>Проверка. Группа groupAttr1 удалена. В упоминании поле группа атрибутов для 
     * сложной формы упоминания очистилось</li>
     * <li>Удалить группу groupAttr2.</li>
     * <li>Проверка. Группа атрибутов groupAttr2 удалена</li>
     */
    @Test
    public void testDeleteGroupAttributeWithMention()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        GroupAttr groupAttr1 = DAOGroupAttr.create(userClass);
        GroupAttr groupAttr2 = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(groupAttr1);
        DSLGroupAttr.add(groupAttr2);

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@#$", UUID, userClass);
        fastLinkSetting.setAttributeGroup(groupAttr1.getCode());
        DSLFastLinkSetting.add(fastLinkSetting);

        // Выполнение действий
        GUILogon.asNaumen();
        GUIGroupAttr.goToGroup(groupAttr1);
        GUIGroupAttr.delete(groupAttr1);
        groupAttr1.setExists(false);
        GUINavigational.goToFastLinkSettings();
        GUIFastLinkSetting.assertAttributeGroup(fastLinkSetting, groupAttr1.getTitle(), false);
        GUIGroupAttr.goToGroup(groupAttr2);
        GUIGroupAttr.delete(groupAttr2);
        groupAttr2.setExists(false);
        GUIGroupAttr.assertAbsence(groupAttr2);
    }

    /**
     * Тестирование невозможности добавить упоминание с неуникальным префиксом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req0075
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$60779749
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать упоминание fastLinkSettingSc (объекты: Запрос, префикс: ~, код атрибута: UUID)</li>
     * <li>Создать модель упоминания fastLinkSettingOu (объекты: Отдел, префикс: ~, код атрибута: UUID)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти под naumen</li>
     * <li>Перейти на страницу с упоминаниями</li>
     * <li>Нажать на кнопку добавить упоминание</li>
     * <li>Заполнить поля на форме добавления по модели fastLinkSettingOu</li>
     * <li>Нажать сохранить</li>
     * <li>Проверка. появилось сообщение об ошибке с текстом: "Значение префикса должно быть уникальным."</li>
     * <li>Изменить префикс для упоминания объекта на %. Сохранить.</li>
     * <li>Проверка. Упоминание fastLinkSettingOu добавилось</li>
     */
    @Test
    public void testImpossibilityAddMentionWithNotUniqueAlias()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass ouClass = DAOOuCase.createClass();
        FastLinkSetting fastLinkSettingSc = DAOObjectMentions.createFastLinkSetting("~", UUID, scClass);
        FastLinkSetting fastLinkSettingOu = DAOObjectMentions.createFastLinkSetting("~", UUID, ouClass);
        DSLFastLinkSetting.add(fastLinkSettingSc);

        // Выполнение действий
        GUILogon.asNaumen();
        GUINavigational.goToFastLinkSettings();
        GUIFastLinkSetting.clickAdd();
        GUIFastLinkSetting.fillTitle(fastLinkSettingOu.getTitle());
        GUIFastLinkSetting.fillCode(fastLinkSettingOu.getCode());
        GUIFastLinkSetting.selectMentionType(ouClass);
        GUIFastLinkSetting.fillAlias(fastLinkSettingOu.getAlias());
        GUIFastLinkSetting.selectMentionAttribute("Уникальный идентификатор");

        GUIForm.applyFormAssertError("Значение префикса должно быть уникальным.");
        GUIFastLinkSetting.fillAlias("%");
        GUIForm.applyForm();
        fastLinkSettingOu.setExists(true);
    }

    /**
     * Тестирование невозможности удалить атрибут, если он используется в настройках упоминания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req0075
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$60779749
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе Сотрудник добавить уникальный атрибут strAttr типа Строка.</li>
     * <li>Создать упоминание fastLinkSettingEmp (объекты: Запрос, префикс: ~, код атрибута: UUID)</li>
     * <li>Создать модель упоминания fastLinkSettingEmp (объекты: Сотрудник, префикс: qwerty, код атрибута: strAttr)
     * </li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти под naumen</li>
     * <li>Попытаться удалить атрибут strAttr</li>
     * <li>Проверка. Возникла ошибка: Атрибут '"strAttr" (класс 'Сотрудник')' не может быть удален. Атрибут 
     * используется в настройках упоминаний объектов: fastLinkSettingEmp</li>
     * <li>Отредактировать упоминание fastLinkSettingEmp: Атрибут для формирования ссылки: UUID</li>
     * <li>Удалить атрибут strAttr</li>
     * <li>Проверка. Атрибут strAttr удален </li>
     */
    @Test
    public void testImpossibilityDeleteAttributeWithMention()
    {
        // Подготовка
        MetaClass empClass = DAOEmployeeCase.createClass();
        Attribute strAttr = DAOAttribute.createString(empClass);
        strAttr.setUnique(Boolean.TRUE.toString());
        DSLAttribute.add(strAttr);
        FastLinkSetting fastLinkSettingEmp = DAOObjectMentions.createFastLinkSetting("qwerty", strAttr.getCode(),
                empClass);
        DSLFastLinkSetting.add(fastLinkSettingEmp);

        // Выполнение действий
        GUILogon.asNaumen();
        GUIMetaClass.goToCard(empClass);
        GUIAttribute.tryDelete(strAttr,
                String.format(
                        "Атрибут \"%s\" (класс '%s') не может быть удален.\n"
                        + "Атрибут используется в настройках упоминаний объектов: %s",
                        strAttr.getTitle(), empClass.getTitle(), fastLinkSettingEmp.getTitle()));
        fastLinkSettingEmp.setMentionAttribute(UUID);
        DSLFastLinkSetting.edit(fastLinkSettingEmp);
        GUIAttribute.delete(strAttr);
        GUIAttribute.assertAbsence(strAttr);
    }

    /**
     * Тестирование невозможности удалить класс на который настроено упоминание
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req0075
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$60779749
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с типом userCase</li>
     * <li>Создать упоминание fastLinkSetting (объекты: userClass, префикс: абвг, код атрибута: UUID)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти под naumen</li>
     * <li>Попытаться удалить метакласс userClass</li>
     * <li>Проверка. Возникла ошибка: Класс 'UserClass' не может быть удален. На класс 'userClass' 
     * ссылаются настройки упоминаний объектов: 'fastLinkSetting'</li>
     * <li>Удалить упоминание fastLinkSetting</li>
     * <li>Удалить метакласс userClass </li>
     */
    @Test
    public void testImpossibilityDeleteMetaclassWithMention()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("абвг", UUID, userClass);
        DSLFastLinkSetting.add(fastLinkSetting);

        // Выполнение действий
        GUILogon.asNaumen();
        GUIMetaClass.goToCard(userClass);
        GUIMetaClass.tryDelete(String.format(
                "Класс '%s' не может быть удален.\n" + "На класс '%s' ссылаются настройки упоминаний объектов: '%s'",
                userClass.getTitle(), userClass.getTitle(), fastLinkSetting.getTitle()));

        DSLFastLinkSetting.delete(fastLinkSetting);
        GUIMetaClass.deleteMetaclass(userClass);
    }

    /**
     * Тестирование невозможности сделать атрибут неуникальным, если настроено упоминание на этот атрибут
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req0075
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$60779749
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе Сотрудник добавить уникальный атрибут intAttr типа Целое число.</li>
     * <li>Создать упоминание fastLinkSettingEmp (объекты: Сотрудник, префикс: ~, код атрибута: intAttr)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти под naumen</li>
     * <li>Попытаться изменить атрибут intAttr на неуникальный</li>
     * <li>Проверка. Возникла ошибка:  “Атрибут должен быть уникальным. Атрибут используется в 
     * настройках упоминаний объектов: fastLinkSettingEmp'”</li>
     * <li>Отредактировать упоминание fastLinkSettingEmp: Атрибут для формирования ссылки: UUID</li>
     * <li>ОТредактировать атрибут intAttr, сделать его неуникальным</li>
     * <li>Проверка. Атрибут intAttr стал неуникальным</li>
     */
    @Test
    public void testImpossibilityToChangeAttrToNotUniqueWithMention()
    {
        // Подготовка
        MetaClass empClass = DAOEmployeeCase.createClass();
        Attribute intAttr = DAOAttribute.createInteger(empClass.getFqn());
        intAttr.setUnique(Boolean.TRUE.toString());
        DSLAttribute.add(intAttr);
        FastLinkSetting fastLinkSettingEmp = DAOObjectMentions.createFastLinkSetting("~", intAttr.getCode(), empClass);
        DSLFastLinkSetting.add(fastLinkSettingEmp);

        // Выполнение действий
        GUILogon.asNaumen();
        GUIAttribute.goToAttribute(intAttr);
        GUIAttribute.clickEdit(intAttr);
        GUIAttribute.setUniqueValueAttribute(false);
        GUIForm.applyFormAssertError(String.format(
                "Атрибут должен быть уникальным. Атрибут используется в настройках упоминаний объектов: '%s'",
                fastLinkSettingEmp.getTitle()));
        fastLinkSettingEmp.setMentionAttribute(UUID);
        DSLFastLinkSetting.edit(fastLinkSettingEmp);
        GUIForm.cancelForm();
        GUIAttribute.clickEdit(intAttr);
        GUIAttribute.setUniqueValueAttribute(false);
        GUIForm.applyForm();
        GUIAttribute.assertAttributeUnique(intAttr, false);
    }

    /**
     * Тестирование наличия выбора профилей на форме добавления настройки упоминания объекта при снятом флаге 
     * "Использовать механизм контроля прав пользователей на объекты, выводимые в список результатов поиска"
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$63816528
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Снять флаг "Использовать механизм контроля прав пользователей на объекты, выводимые в список результатов
     * поиска"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти под суперпользователем</li>
     * <li>Перейти на страницу Настройка системы / Упоминание объектов</li>
     * <li>Открыть форму "Добавление настройки упоминания объекта"</li>
     * <b>Проверки</b>
     * <li>На форме присутствует поле для выбора профилей</li>
     */
    @Test
    public void testPresentProfilesOnEditMentionForm()
    {
        //Подготовка
        DSLSearchSettings.setUseRightsInLists(false);
        //Действия
        GUILogon.asNaumen();
        GUINavigational.goToFastLinkSettings();
        GUIFastLinkSetting.clickAdd();
        //Проверки
        GUIFastLinkSetting.assertPresentProfilesColumnOnForm();
    }

    /**
     * Тестирование наличия поля профили в списке настроек упоминаний и карточке при снятом флаге 
     * "Использовать механизм контроля прав пользователей на объекты, выводимые в список результатов поиска"
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$63816528
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Снять флаг "Использовать механизм контроля прав пользователей на объекты, выводимые в список результатов
     * поиска"</li>
     * <li>Добавить пользовательский класс и тип: userClass и userCase</li>
     * <li>Добавить настройку упоминания fastLinkSetting</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти под суперпользователем</li>
     * <li>Перейти на страницу Настройка системы / Упоминание объектов</li>
     * <b>Проверки</b>
     * <li>В списке присутствует колонка "профили"</li>
     * <li>Перейти на карточкку fastLinkSetting</li>
     * <li>На карточке присутствует поле "профили"</li>
     */
    @Test
    public void testPresentProfilesOnFastLinkListAndCard()
    {
        //Подготовка
        String mentionPrefix = ModelUtils.createCode();
        DSLSearchSettings.setUseRightsInLists(false);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting(mentionPrefix, UUID, userClass);
        DSLFastLinkSetting.add(fastLinkSetting);
        //Действия
        GUILogon.asNaumen();
        GUINavigational.goToFastLinkSettings();
        //Проверки
        GUIFastLinkSetting.assertPresentProfilesColumnInList();
        GUIFastLinkSetting.clickFastLinkSettings(fastLinkSetting);
        GUIFastLinkSetting.assertPresentProfilesColumnOnCard();
    }

}
