package ru.naumen.selenium.cases.admin.classes.cases;

import org.junit.Test;

import ru.naumen.selenium.casesutil.admin.DSLWfProfile;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.escalation.DSLEscalation;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.admin.DAOWfProfile;
import ru.naumen.selenium.casesutil.model.admin.WfProfile;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationSheme;
import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOMetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition.SystemTimer;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование действий над классами и типами объектов со ссылочной целостностью
 * <AUTHOR>
 * @since 18.03.2014
 */
public class IntegrityRelations1Test extends AbstractTestCase
{
    /**
     * Тестирование добавления Типа объекта в Архивный класс Пользовательского БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить класс Пользовательского БО userClass</li>
     * <li>Заархивировать добавленный класс userClass</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем</li>
     * <li>Добавить тип Пользовательского БО userType в класс userClass</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип userType добавился без ошибок, состояние – в архиве</li>
     * </ol>
     */
    @Test
    public void testAddCaseToArchivedClass()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userType = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass);

        DSLMetaClass.archive(userClass);

        // Действие
        GUILogon.asSuper();
        userType.setArchived(true);
        GUIMetaClass.addMetaclass(userType);

        // Проверка
        DSLMetaClass.assertArchived(userType);
    }

    /**
     * Тестирование архивирования типа объекта, используемого в схеме эскалации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00452
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе Запрос добавить тип scCase</li>
     * <li>Добавить схему эскалации (Объекты – scCase, название/Счетчик времени - любые)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку метакласса типа класса Запрос scCase</li>
     * <li>Заархивировать тип запроса scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип запроса scCase в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseUsedInEscalation()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalation = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        DSLEscalation.add(escalation);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(scCase);
    }

    /**
     * Тестирование архивирования типа объекта, который используется в Таблице соответствий в эскалации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00453
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В класс Запрос добавить тип scCase</li>
     * <li>В Эскалации добавляем тс (Объекты – scCase, Название/Определяющие атрибуты - любые (Тип объекта
     * metaClassAttr))</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку метакласса типа класса Запрос scCase</li>
     * <li>Заархивировать тип запроса scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип запроса scCase в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseUsedInRSOfEscalation()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        String metaClassAttrCode = SysAttribute.metaClass(scCase).getCode();
        CatalogItem item = DAOCatalogItem.createEscalationRule(scCase, metaClassAttrCode);
        DSLCatalogItem.add(item);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(scCase);
    }

    /**
     * Тестирование архивирования типа объекта, который используется в счетчиках времени
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00357
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В класс Запрос добавить тип scCase</li>
     * <li>Добавить счетчик времени counter (Объекты – scCase, Метрика – астрономическое время, Часовой пояс -
     * timeZoneAttr)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку метакласса типа класса Запрос scCase</li>
     * <li>Заархивировать тип запроса scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип запроса scCase в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseUsedInTimerDefinition()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();

        Attribute timeZoneAttr = DAOAttribute.createCatalogItem(scCase.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.TIMEZONE), null);
        DSLMetainfo.add(scCase, timeZoneAttr);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("true");
        DSLScriptInfo.addScript(script);
        TimerDefinition counter = DAOTimerDefinition.createAstroTimerByScript(scCase.getFqn(), timeZoneAttr.getCode());
        counter.setStartCondition(script.getCode());
        DSLTimerDefinition.add(counter);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(scCase);
    }

    /**
     * Тестирование архивирования типа объекта, используемого в профилях связанных жизненных циклов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00500
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе Запрос добавить тип scCase</li>
     * <li>В профилях связанных жизненных циклов добавить профиль (Ведущий/Ведомы – scCase)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку метакласса типа класса Запрос scCase</li>
     * <li>Заархивировать тип запроса scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип запроса scCase в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseUsedInWfProfile()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        WfProfile userPr = DAOWfProfile.create(scCase, scCase);
        DSLWfProfile.add(userPr);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(scCase);
    }

    /**
     * Тестирование архивирования типа объекта, настройке действия в эскалации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00450
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе Запрос добавить тип scCase</li>
     * <li>В Эскалации добавить Действие по событию (Объекты – scCase, название/Счетчик времени - любые)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку метакласса типа класса Запрос scCase</li>
     * <li>Заархивировать тип запроса scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип запроса scCase в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseUsedToConfigureActionInEscalation()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);
        EventAction event = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true, scCase);
        DSLEventAction.add(event);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(scCase);
    }

    /**
     * Тестирование архивирования типа объекта, используемого в настройке таблицы соответствий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00226
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить Пользовательский класс classBo</li>
     * <li>В пользовательский класс classBo добавить тип typeBo</li>
     * <li>В классе Запрос добавить контент типа Список объектов – Класс объектов - classBo, Тип объектов – typeBo</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку метакласса типа класса Пользовательский typeBo</li>
     * <li>Заархивировать тип typeBo пользовательского класса classBo</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип пользовательского БО typeBo в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseUsedToConfigureContent()
    {
        // Подготовка
        MetaClass classBo = DAOUserClass.create();
        MetaClass typeBo = DAOUserCase.create(classBo);
        DSLMetaClass.add(classBo, typeBo);

        ContentForm content = DAOContentCard.createObjectList(DAOScCase.createClass().getFqn(), classBo, typeBo);
        DSLContent.add(content);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(typeBo.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(typeBo);
    }

    /**
     * Тестирование архивирования типа объекта, используемого в Параметрах запроса по умолчанию у сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00141
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе Запрос добавить тип scCase</li>
     * <li>В классе Соглашение добавить тип agrCase</li>
     * <li>Добавить соглашение agreement типа agrCase</li>
     * <li>В классе Сотрудник добавить тип emplCase</li>
     * <li>В типе сотрудника настроить Параметры запроса по умолчанию: Соглашение/Услуга – Agreement, Тип объекта –
     * scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку метакласса типа класса Запрос scCase</li>
     * <li>Заархивировать тип запроса scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип запроса scCase в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseUsedToConfigureDefaultScForEmployee()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass agrCase = DAOAgreementCase.create();
        DSLMetaClass.add(scCase, agrCase);

        Bo agreement = DAOAgreement.create(agrCase);
        DSLBo.add(agreement);

        MetaClass emplCase = DAOMetaClass.createCaseWithDefaultScParams(DAOEmployeeCase.createClass(), scCase.getFqn(),
                agreement.getUuid(), null);
        DSLMetaClass.add(emplCase);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(scCase);
    }

    /**
     * Тетсирование архивирования типа объекта, используемого в настройке действия по событию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе Запрос добавить тип scCase</li>
     * <li>Добавить действие по событию eventAction (Объекты – scCase, Название/Событие/Действие - любые (Событие -
     * удаление))</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку метакласса типа класса Запрос scCase</li>
     * <li>Заархивировать тип запроса scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип запроса scCase в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseUsedToConfigureEventAction()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        EventAction eventAction = DAOEventAction.createNotificationByMetaClass(scCase);
        DSLEventAction.add(eventAction);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(scCase);
    }

    /**
     * Тестирование архивирования типа объекта, используемого в настройке таблицы соответствий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Добавить Таблицу cоответствий (Объекты – ouCase,
     * Определяемые/Определяющие атрибуты - любые (Название title / Автор author))</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку метакласса типа класса Отдел ouCase</li>
     * <li>Заархивировать тип отдела ouCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип отдела ouCase в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseUsedToConfigureRS()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute title = SysAttribute.title(ouCase);
        Attribute author = SysAttribute.author(ouCase);
        CatalogItem rs = DAOCatalogItem.createRulesSettings(ouCase, title.getCode(), author.getCode());
        DSLCatalogItem.add(rs);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(ouCase.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(ouCase);
    }

    /**
     * Тестирование архивирования типа объекта, у которого есть созданные объекты
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Добавить отдел, в нём создать сотрудника employee типа employeeCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку типа сотрудника employeeCase</li>
     * <li>Заархивировать тип сотрудника employeeCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип сотрудника employeeCase в архиве</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseWithCreatedCase()
    {
        // Подготовка
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);

        Bo employee = DAOEmployee.create(employeeCase, ou, false);
        DSLBo.add(employee);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(employeeCase.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(employeeCase);
    }

    /**
     * Тестирование архивирования типа объекта со вложенным типом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип класса Отдел ouCase1</li>
     * <li>В созданном типе добавить вложенный тип ouCase2</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку типа отдела ouCase</li>
     * <li>Заархивировать тип класса Отдел ouCase1</li>
     * <br>
     * <b>Проверки</b>
     * <li>Типы класса Отдел ouCase1 и ouCase2 в архиве</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseWithNestedCase()
    {
        // Подготовка
        MetaClass ouCase1 = DAOOuCase.create();
        MetaClass ouCase2 = DAOOuCase.create(ouCase1);
        DSLMetaClass.add(ouCase1, ouCase2);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(ouCase1.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(ouCase1);
        DSLMetaClass.assertArchived(ouCase2);
    }

    /**
     * Тестирование архивирования типа объекта со вложенным типом и объектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип класса Отдел ouCase1</li>
     * <li>В созданном типе добавить вложенный тип ouCase2</li>
     * <li>Добавить отдел ou типа ouCase2</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку типа отдела ouCase1</li>
     * <li>Заархивировать тип класса Отдел ouCase1</li>
     * <br>
     * <b>Проверки</b>
     * <li>Типы класса Отдел ouCase1 и ouCase2 в архиве</li>
     * </ol>
     */
    @Test
    public void testArchiveCaseWithNestedCaseAndObject()
    {
        // Подготовка
        MetaClass ouCase1 = DAOOuCase.create();
        MetaClass ouCase2 = DAOOuCase.create(ouCase1);
        DSLMetaClass.add(ouCase1, ouCase2);

        Bo ou = DAOOu.create(ouCase2);
        DSLBo.add(ou);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(ouCase1.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(ouCase1);
        DSLMetaClass.assertArchived(ouCase2);
    }

    /**
     * Тестирование архивирования типа объекта, на который ссылаются строки из таблицы соответствий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В класс Запрос добавить тип scCase</li>
     * <li>Добавить Таблицу cоответствий (Объекты – scCase,
     * Определяемые атрибуты – Описание запроса, Определяющие – Тип объекта)</li>
     * <li>Добавить строку: Тип объекта – scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку метакласса типа класса Запрос scCase</li>
     * <li>Заархивировать тип запроса scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип запроса scCase в архиве, ошибок нет/li>
     * </ol>
     */
    @Test
    public void testArchiveCaseWithReferencedRowsOfRS()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        Attribute description = SysAttribute.description(scCase);
        Attribute metaClass = SysAttribute.metaClass(scCase);
        CatalogItem rs = DAOCatalogItem.createRulesSettings(scCase, description.getCode(), metaClass.getCode());
        DSLCatalogItem.add(rs);

        RsRow row = DAORsRow.create(rs, description, description.getCode(), metaClass, scCase.getFqn());
        DSLRsRows.addRowToRSItem(row);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(scCase);
    }

    /**
     * Тестирование архивирования типа объекта (только что созданного)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00068
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать Тип запроса scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем на карточку типа запроса scCase</li>
     * <li>Заархивировать созданный scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Тип запроса в архиве</li>
     * </ol>
     */
    @Test
    public void testArchiveNewCase()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase.getFqn());
        GUIMetaClass.archive();

        // Проверка
        DSLMetaClass.assertArchived(scCase);
    }
}
