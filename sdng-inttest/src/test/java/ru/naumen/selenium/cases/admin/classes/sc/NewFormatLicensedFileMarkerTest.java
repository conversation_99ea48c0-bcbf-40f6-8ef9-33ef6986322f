package ru.naumen.selenium.cases.admin.classes.sc;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIAccessMatrix;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIMarkerForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScriptCatalogList;
import ru.naumen.selenium.casesutil.script.GUIScriptComponentEdit;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.security.SecurityMarkerUserEvent;

/**
 * Тестирование на новый формат лицензионного файла и матрицу прав
 * <AUTHOR>
 * @since 03.08.2021
 */

public class NewFormatLicensedFileMarkerTest extends AbstractTestCase
{
    private static MetaClass userClass, userCase;
    private static SecurityMarkerUserEvent marker1, marker2;
    private static EventAction eventAction;
    private static SecurityProfile unlicProfile1, unlicProfile2;
    private static ScriptInfo script;

    /**
     * Общая подготовка
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать профили для нелицензированного пользователя Profile_1 и Profile_2, роль Сотрудник</li>
     * <li>Загрузить в систему лицензионный файл, в котором прописаны действия, доступные нелицензированным
     * пользователям
     * (пример приложен)</li>
     * <li>Создать действие по событию userEvent со следующими параметрами: </li>
     * <ul>
     *     <li>Объекты: userClass</li>
     *     <li>Событие: Пользовательское событие</li>
     *     <li>Действие: скрипт</li>
     *     <li>Скрипт: любой</li>
     * </ul>
     * <li>В userClass создать маркеры прав marker_1 и  marker_2 на Пользовательские события userEvent</li>
     * <li>Выдать профилям Profile_1 , Profile_2 права  marker_1, marker_2</li>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        script = DAOScriptInfo.createNewScriptInfo();
        script.setBody("return true");
        script.setCode("MKVGugl");
        DSLScriptInfo.addScript(script);

        eventAction = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), false,
                userClass);
        eventAction.setUuid("eventAction$" + "MKVGugl");
        eventAction.setUserEventUuid("userEvent$" + "MKVGugl");
        eventAction.setTitle("MKVGugl" + "Title");
        DSLEventAction.add(eventAction);

        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);

        unlicProfile1 = DAOSecurityProfile.create(false, group, SysRole.employee());
        DSLSecurityProfile.add(unlicProfile1);
        DSLSecurityProfile.grantAllPermissions(unlicProfile1);

        unlicProfile2 = DAOSecurityProfile.create(false, group, SysRole.employee());
        DSLSecurityProfile.add(unlicProfile2);
        DSLSecurityProfile.grantAllPermissions(unlicProfile2);

        DSLAdmin.installLicense(DSLAdmin.LICENSE_WITH_UNLICENSED_ACTION_USER_CLASS);

        marker1 = new SecurityMarkerUserEvent(userClass);
        marker1.addEventActions(eventAction);
        marker1.apply();

        marker2 = new SecurityMarkerUserEvent(userClass);
        marker2.addEventActions(eventAction);
        marker2.apply();

        DSLSecurityProfile.setRights(userClass, unlicProfile1, marker1);
        DSLSecurityProfile.setRights(userClass, unlicProfile2, marker2);
    }

    /**
     * Тестирование отображения всплывающих подсказок для маркера прав пользовательских событий для нелицензированных
     * пользователей.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00270
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$99695141
     * <b>Выполнение действий и проверки</b>
     * <li>Загрузить в систему лицензионный файл, в котором для действий, доступных нелицензированным пользователям,
     * истек срок</li>
     * <li>Проверить, что маркеры  в состоянии "disabled" , отображаются всплывающие подсказки возле ячеек для обоих
     * профилей</li>
     */
    @Test
    public void testAppearanceOfAttrsMarkerNotifications()
    {
        DSLAdmin.installLicense(DSLAdmin.LICENSE_WITH_UNLICENSED_ACTION_BLOCK_WITH_EXPIRED_DATE_PATH);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.PERMISSIONSETTINGS);

        String markerCode1 = GUIAccessMatrix.getMarkerCodeByTitle(marker1.getTitle());
        GUIAccessMatrix.assertCheckBoxEnabled(markerCode1, unlicProfile1.getCode(), false);

        String markerCode2 = GUIAccessMatrix.getMarkerCodeByTitle(marker2.getTitle());
        GUIAccessMatrix.assertCheckBoxEnabled(markerCode2, unlicProfile1.getCode(), false);
    }

    /**
     * Тестирование сохранения состояния disabled маркера прав пользовательских событий для нелицензированных
     * пользователей, если маркер пустой.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00270
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$99695141
     * <b>Выполнение действий и проверки</b>
     * <li>Загрузить в систему лицензионный файл, в котором для действий, доступных нелицензированным пользователям,
     * истек срок</li>
     * <li>Отредактировать маркер marker_1, убрав действие userEvent , оставив маркер пустым</li>
     * <li>Сохранить</li>
     * <li>Проверить, что маркер остался в  состоянии "disabled", галка снята</li>
     */
    @Test
    public void testSaveDisableConditionWithEmptyMarker()
    {
        DSLAdmin.installLicense(DSLAdmin.LICENSE_WITH_UNLICENSED_ACTION_BLOCK_WITH_EXPIRED_DATE_PATH);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.PERMISSIONSETTINGS);

        GUIAccessMatrix.clickEditMarker(marker1.getRightCode());
        GUIMarkerForm.deleteEventActionOnEditForm(eventAction);
        GUIForm.applyModalForm();

        String markerCode1 = GUIAccessMatrix.getMarkerCodeByTitle(marker1.getTitle());
        GUIAccessMatrix.assertCheckBoxEnabled(markerCode1, unlicProfile1.getCode(), false);
        GUITester.assertPresent(GUIAccessMatrix.CELL_CHECKBOX_SELECTED, "чекбокс отмечен",
                markerCode1, unlicProfile1.getCode(), "false");
    }

    /**
     * Тестирование редактирования маркера прав пользовательских событий для нелицензированных пользователей и отмены
     * изменений с сохранением состояния для другого маркера.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00270
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$99695141
     * <b>Выполнение действий и проверки</b>
     * <li>Загрузить в систему лицензионный файл, в котором для действий, доступных нелицензированным пользователям,
     * истек срок</li>
     * <li>Отредактировать маркер marker_1, убрав действие userEvent , оставив маркер пустым</li>
     * <li>Сохранить</li>
     * <li>В матрице прав нажать "Отменить изменения"</li>
     * <li>Проверить, что marker_2 остался в состоянии "disabled" , галка установлена</li>
     */
    @Test
    public void testEditMarkerWithSaveConditionForAnotherMarker()
    {
        DSLAdmin.installLicense(DSLAdmin.LICENSE_WITH_UNLICENSED_ACTION_BLOCK_WITH_EXPIRED_DATE_PATH);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.PERMISSIONSETTINGS);

        GUIAccessMatrix.clickEditMarker(marker1.getRightCode());
        GUIMarkerForm.deleteEventActionOnEditForm(eventAction);
        GUIForm.applyModalForm();

        tester.click(GUIAccessMatrix.ERASE);

        String markerCode2 = GUIAccessMatrix.getMarkerCodeByTitle(marker2.getTitle());
        GUIAccessMatrix.assertCheckBoxEnabled(markerCode2, unlicProfile2.getCode(), false);
        GUITester.assertPresent(GUIAccessMatrix.CELL_CHECKBOX_SELECTED, "чекбокс не отмечен",
                markerCode2, unlicProfile2.getCode(), "true");
    }

    /**
     * Тестирование сохранения состояния disabled маркера прав пользовательских событий для нелицензированных
     * пользователей, если маркер пустой.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00270
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$99695141
     * <b>Выполнение действий и проверки</b>
     * <li>Добавить скрипт уточнения прав для маркера marker_1: return true. Для marker_2: return false</li>
     * <li>Сохранить</li>
     * <li>Загрузить в систему лицензионный файл, в котором для действий, доступных нелицензированным пользователям,
     * истек срок</li>
     * <li>Отредактировать маркер marker_1, убрав действие userEvent , оставив маркер пустым</li>
     * <li>Сохранить</li>
     * <li>Проверить, что в Каталог скриптов/Скрипт/Настройки, где используется скрипт/Категория "Уточнение прав
     * доступа"
     * скрипта return true нет</li>
     * <li>Удалить в матрице прав маркер marker_2</li>
     * <li>Проверить, что в Каталог скриптов/Скрипт/Настройки, где используется скрипт/Категория "Уточнение прав
     * доступа"
     * скрипта return false нет</li>
     */
    @IgnoreConfig(cause = "NSDPRD-27779")
    @Test
    public void testComOfAttrsMarkerInheritance()
    {
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.PERMISSIONSETTINGS);

        String markerCode1 = GUIAccessMatrix.getMarkerCodeByTitle(marker1.getTitle());
        GUIAccessMatrix.clickMatrixCell(markerCode1, unlicProfile1.getCode());

        ScriptInfo scriptInfo1 = DAOScriptInfo.createNewScriptInfo("return true");
        GUIScriptComponentEdit scriptComponentEdit = new GUIScriptComponentEdit(
                GUIXpath.Any.SCRIPT_PERMISSION_VALUE);
        scriptComponentEdit.fillNewScript(scriptInfo1);
        GUIForm.applyForm();

        String markerCode2 = GUIAccessMatrix.getMarkerCodeByTitle(marker2.getTitle());
        GUIAccessMatrix.clickMatrixCell(markerCode2, unlicProfile2.getCode());
        ScriptInfo scriptInfo2 = DAOScriptInfo.createNewScriptInfo("return false");
        scriptComponentEdit.fillNewScript(scriptInfo2);
        GUIForm.applyForm();

        tester.click(GUIAccessMatrix.SAVE);

        DSLAdmin.installLicense(DSLAdmin.LICENSE_WITH_UNLICENSED_ACTION_BLOCK_WITH_EXPIRED_DATE_PATH);

        GUIAccessMatrix.clickEditMarker(marker1.getRightCode());
        GUIMarkerForm.deleteEventActionOnEditForm(eventAction);
        GUIForm.applyModalForm();

        tester.click(GUIAccessMatrix.SAVE);

        GUIScriptCatalogList.goToAllScripts();
        GUIScriptCatalogList.advlist().content().asserts().uuidsAbsence(scriptInfo1.getCode());

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickDeleteMarker(markerCode2);
        GUIForm.clickYes();

        tester.click(GUIAccessMatrix.SAVE);

        GUIScriptCatalogList.goToAllScripts();
        GUIScriptCatalogList.advlist().content().asserts().uuidsAbsence(scriptInfo2.getCode());
    }
}
