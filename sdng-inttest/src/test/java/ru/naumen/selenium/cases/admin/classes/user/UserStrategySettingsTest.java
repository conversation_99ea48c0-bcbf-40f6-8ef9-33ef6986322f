package ru.naumen.selenium.cases.admin.classes.user;

import java.util.List;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.ResponsibleType;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.Strategy;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование настройки стратегии в статусе пользовательских метаклассов
 * <AUTHOR>
 * @since 27.11.2014
 */
public class UserStrategySettingsTest extends AbstractTestCase
{
    /**
     * Тестирование прерывания наследования стратегии назначения ответственного и класса ответственного в
     * пользовательском
     * статусе пользовательского типа с ЖЦ и назначением ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00352
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass с ЖЦ и Передачей ответственности</li>
     * <li>Создать тип userCase в классе userClass</li>
     * <li>Создать команду team</li>
     * <li>Создать сотрудника-исполнителя employee</li>
     * <li>Связать команду team и сотрудника employee</li>
     * <li>В классе userClass добавить пользовательский статус state (Класс ответственного в статусе - сотрудники, 
     * Ответственный - employee/team)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на вкладку Жизненный цикл типа userCase</li>
     * <li>В строке статуса stateUserCase нажать иконку редактировать</li>
     * <li>Выбрать Класс ответственного в статусе — команды</li>
     * <li>В поле Ответственный выбрать team</li>
     * <li>Сохранить</li>
     * <li>Перейти на карточку статуса state класса userClass</li>
     * <li>В строке статуса state нажать иконку редактировать</li>
     * <li>Выбрать Класс ответственного в статусе - команды и сотрудники</li>
     * <li>Сохранить</li>
     * <li>Перейти на карточку статуса stateUserCase в типе userCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>В строке Ответственный отображается team</li>
     * <li>В строке Класс ответственного в статусе — команды</li>
     * </ol>
     */
    @Test
    public void testBreakInheritanceRespStrategyAndTypeInUserStateOfUserCaseWithWfAndResp()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(team, employee);

        DSLTeam.addEmployees(team, employee);

        BoStatus state = DAOBoStatus.createUserStatus(userClass.getFqn());
        state.setResponsibleType(BoStatus.ResponsibleType.EMPLOYEE.getType());
        state.setResponsibleStrategy(Strategy.EMPLOYEE.getId());
        state.setEmployeeUuid(employee.getUuid());
        state.setTeamUuid(team.getUuid());
        DSLBoStatus.add(state);

        BoStatus stateUserCase = DAOBoStatus.copy(state, userCase);

        // Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userCase, MetaclassCardTab.LIFECYCLE);
        GUIBoStatus.clickEditIcon(stateUserCase);
        GUISelect.select(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_TYPE_INPUT,
                BoStatus.ResponsibleType.TEAM.name());
        GUISelect.select(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_INPUT,
                "employeeResponsibleStrategy:" + team.getUuid() + ":null");
        GUIForm.applyForm();

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.LIFECYCLE);
        GUIBoStatus.clickEditIcon(state);
        GUISelect.select(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_TYPE_INPUT,
                BoStatus.ResponsibleType.EMPLOYEE_AND_TEAM.name());
        GUIForm.applyForm();

        GUIBoStatus.goToStatusCardWithRefresh(stateUserCase);

        // Проверки
        GUIBoStatus.assertResponsible(team.getTitle());
        GUIBoStatus.assertResponsibleType(BoStatus.ResponsibleType.TEAM.getTitle());
    }

    /**
     * Тестирование наследования стратегии назначения ответственного и класса ответственного в пользовательском статусе 
     * пользовательского типа с ЖЦ и назначением ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00352
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass с ЖЦ и Передачей ответственности</li>
     * <li>Создать тип userCase в классе userClass</li>
     * <li>Создать команду team</li>
     * <li>Создать сотрудника-исполнителя employee</li>
     * <li>Связать команду team и сотрудника employee</li>
     * <li>В классе userClass добавить пользовательский статус state (Класс ответственного в статусе - команды и
     * сотрудники,
     * Ответственный - текущий ответственный)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на вкладку Жизненный цикл класса userClass</li>
     * <li>В строке статуса state нажать иконку редактировать</li>
     * <li>Выбрать Класс ответственного в статусе — сотрудники</li>
     * <li>В поле Ответственный выбрать employee/team</li>
     * <li>Сохранить</li>
     * <li>Перейти на карточку статуса state типа userCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>В строке Ответственный отображается employee/team</li>
     * <li>В строке Класс ответственного в статусе — сотрудники</li>
     * </ol>
     */
    @Test
    public void testInheritanceRespStrategyAndTypeInUserStateOfUserCaseWithWfAndResp()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(team, employee);

        DSLTeam.addEmployees(team, employee);

        BoStatus state = DAOBoStatus.createUserStatus(userClass.getFqn());
        DSLBoStatus.add(state);

        BoStatus stateUserCase = DAOBoStatus.copy(state, userCase);

        // Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.LIFECYCLE);
        GUIBoStatus.clickEditIcon(state);
        GUISelect.select(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_TYPE_INPUT,
                BoStatus.ResponsibleType.EMPLOYEE.name());
        GUISelect.select(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_INPUT,
                "employeeResponsibleStrategy:" + team.getUuid() + ":" + employee.getUuid());
        GUIForm.applyForm();

        GUIBoStatus.goToStatusCardWithRefresh(stateUserCase);

        // Проверки
        GUIBoStatus.assertResponsible(DSLEmployee.getFullName(employee) + " / " + team.getTitle());
        GUIBoStatus.assertResponsibleType(BoStatus.ResponsibleType.EMPLOYEE.getTitle());
    }

    /**
     * Тестирование восстановления наследования стратегии назначения ответственного и класса ответственного в 
     * пользовательском статусе пользовательского типа с ЖЦ и назначением ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00352
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass с ЖЦ и Передачей ответственности</li>
     * <li>Создать тип userCase в классе userClass</li>
     * <li>Создать команду team</li>
     * <li>Создать сотрудника-исполнителя employee</li>
     * <li>Связать команду team и сотрудника employee</li>
     * <li>В классе userClass добавить пользовательский статус stateUserClass (Класс ответственного в статусе -
     * сотрудники,
     * Ответственный - employee/team)</li>
     * <li>В типе userCase отредактировать статус stateUserCase (Класс ответственного в статусе - команды,
     * Ответственный - team)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на вкладку Жизненный цикл в типе userCase</li>
     * <li>Нажать кнопку Сбросить настройки</li>
     * <li>Перейти на карточку статуса stateUserCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>В строке Ответственный отображается employee/team</li>
     * <li>В строке Класс ответственного в статусе — сотрудники</li>
     * </ol>
     */
    @Test
    public void testRestoreInheritanceRespStrategyAndTypeInUserStateOfUserCaseWithWfAndResp()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(team, employee);

        DSLTeam.addEmployees(team, employee);

        BoStatus stateUserClass = DAOBoStatus.createUserStatus(userClass.getFqn());
        stateUserClass.setResponsibleType(BoStatus.ResponsibleType.EMPLOYEE.getType());
        stateUserClass.setResponsibleStrategy(Strategy.EMPLOYEE.getId());
        stateUserClass.setEmployeeUuid(employee.getUuid());
        stateUserClass.setTeamUuid(team.getUuid());
        DSLBoStatus.add(stateUserClass);

        BoStatus stateUserCase = DAOBoStatus.copy(stateUserClass, userCase);
        stateUserCase.setResponsibleType(BoStatus.ResponsibleType.TEAM.getType());
        stateUserCase.setResponsibleStrategy(Strategy.EMPLOYEE.getId());
        stateUserCase.setTeamUuid(team.getUuid());
        DSLBoStatus.edit(stateUserCase);

        // Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userCase, MetaclassCardTab.LIFECYCLE);
        GUIBoStatus.refresh(userCase);
        GUIBoStatus.goToStatusCardWithRefresh(stateUserCase);

        // Проверки
        GUIBoStatus.assertResponsible(DSLEmployee.getFullName(employee) + " / " + team.getTitle());
        GUIBoStatus.assertResponsibleType(BoStatus.ResponsibleType.EMPLOYEE.getTitle());
    }

    /**
     * Тестирование сохранения стратегии назначения ответственного при копировании типа пользовательского класса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00352
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с ЖЦ и передачей ответственного</li>
     * <li>Создать пользовательский тип userCase в классе userClass</li>
     * <li>Добавить статус state в тип userCase со стратегией назначения Ответственного — Без ответственного</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Копировать тип userCase (новый тип - userCaseCopy)</li>
     * <li>Перейти на вкладку Жизненный цикл типа userCaseCopy</li>
     * <br>
     * <b>Проверки</b>
     * <li>В строке статуса state в колонке Ответственный отображается Без ответственного</li>
     * </ol>
     */
    @Test
    public void testSaveRespStrategyWhenCopyUserCase()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        BoStatus state = DAOBoStatus.createUserStatus(userCase.getFqn(), ResponsibleType.EMPLOYEE_AND_TEAM,
                Strategy.EMPTY);
        DSLBoStatus.add(state);

        MetaClass userCaseCopy = DAOUserCase.create(userClass);

        // Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.copyMetaclass(userCase, userCaseCopy);
        GUIMetaClass.goToTab(userCaseCopy, MetaclassCardTab.LIFECYCLE);

        // Проверки
        GUIBoStatus.assertResponsibleInStateList(state, Strategy.EMPTY);
    }

    /**
     * Тестирование сохранения стратегии назначения ответственного при копировании пользовательского класса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00352
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с ЖЦ и передачей ответственного</li>
     * <li>Добавить статус state со стратегией назначения Ответственного — Автор объекта</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Копировать класс userClass (новый класс - userClassCopy)</li>
     * <li>Перейти на вкладку Жизненный цикл класса userClassCopy</li>
     * <br>
     * <b>Проверки</b>
     * <li>В строке статуса state в колонке Ответственный отображается Автор объекта</li>
     * </ol>
     */
    @Test
    public void testSaveRespStrategyWhenCopyUserClass()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        DSLMetaClass.add(userClass);

        BoStatus state = DAOBoStatus.createUserStatus(userClass.getFqn(), ResponsibleType.EMPLOYEE_AND_TEAM,
                Strategy.AUTHOR);
        DSLBoStatus.add(state);

        MetaClass userClassCopy = DAOUserClass.createWithWFAndResp();

        // Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.copyMetaclass(userClass, userClassCopy);
        GUIMetaClass.goToTab(userClassCopy, MetaclassCardTab.LIFECYCLE);

        // Проверки
        GUIBoStatus.assertResponsibleInStateList(state, Strategy.AUTHOR);
    }

    /**
     * Тестирование выбора стратегии назначения ответственного и класса ответственного во включенном 
     * пользовательском статусе пользовательского класса с ЖЦ
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00352
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass с ЖЦ и Передачей ответственности</li>
     * <li>Создать команду team</li>
     * <li>Создать сотрудника-исполнителя employee</li>
     * <li>Связать команду team и сотрудника employee</li>
     * <li>Добавить пользовательский статус state в классе userClass</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на карточку статуса state</li>
     * <li>Нажать кнопку Редактировать</li>
     * <li>Выбрать Класс ответственного в статусе — сотрудники</li>
     * <li>В поле Ответственный выбрать employee/team</li>
     * <li>Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>В строке Ответственный отображается employee/team</li>
     * <li>В строке Класс ответственного в статусе — сотрудники</li>
     * </ol>
     */
    @Test
    public void testSetRespStrategyAndTypeInEnabledUserStateOfUserClass()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        DSLMetaClass.add(userClass);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(team, employee);

        DSLTeam.addEmployees(team, employee);

        BoStatus state = DAOBoStatus.createUserStatus(userClass.getFqn());
        DSLBoStatus.add(state);

        // Выполнение действий
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(state);
        GUIBoStatus.edit();
        GUISelect.select(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_TYPE_INPUT,
                BoStatus.ResponsibleType.EMPLOYEE.name());
        GUISelect.select(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_INPUT,
                "employeeResponsibleStrategy:" + team.getUuid() + ":" + employee.getUuid());
        GUIForm.applyForm();

        // Проверки
        GUIBoStatus.assertResponsible(DSLEmployee.getFullName(employee) + " / " + team.getTitle());
        GUIBoStatus.assertResponsibleType(BoStatus.ResponsibleType.EMPLOYEE.getTitle());
    }

    /**
     * Тестирование выбора стратегии назначения ответственного в выключенном пользовательском статусе типа 
     * пользовательского класса с ЖЦ
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00352
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с ЖЦ и Передачей ответственности</li>
     * <li>Создать пользовательский тип userCase класса userClass</li>
     * <li>В типе userCase добавить статус state (Выключен)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на вкладку Жизненный цикл типа userCase</li>
     * <li>В строке статуса state нажать иконку Редактировать</li>
     * <li>В поле Ответственный выбрать стратегию Предыдущий ответственный</li>
     * <li>Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>В блоке Статус в строке статуса state в колонке Ответственный отображается стратегия Предыдущий
     * ответственный</li>
     * </ol>
     */
    @Test
    public void testSetRespStrategyAndTypeInOffUserStateOfUserCase()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        BoStatus state = DAOBoStatus.createUserStatus(userClass.getFqn());
        state.setEnable("false");
        DSLBoStatus.add(state);

        // Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userCase, MetaclassCardTab.LIFECYCLE);
        GUIBoStatus.clickEditIcon(state);
        GUISelect.select(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_INPUT,
                Strategy.PREV.getId());
        GUIForm.applyForm();

        // Проверки
        GUIBoStatus.assertResponsibleInStateList(state, Strategy.PREV);
    }

    /**
     * Тестирование регистрозависимости поля "Ответственный" на форме добавления/редактирования, по умолчанию в поле
     * подставляется "Текущий ответственный".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00389
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00352
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$121800504
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с ЖЦ и Передачей ответственности</li>
     * <li>Создать пользовательский тип userCase класса userClass</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на вкладку Жизненный цикл типа userCase</li>
     * <li>Нажать на кнопку "Добавить статус"</li>
     * <li>Проверить, что в поле "Ответственный" заполнено значение: "Текущий ответственный"</li>
     * <li>В поле ответственный ввести с клавиатуры значение: "тек"</li>
     * <li>Проверить, что в выпадающем списке 3 значения: "Команда текущего ответственного", "Лидер команды текущего
     * ответственного", "Текущий ответственный"</li>
     * <li>В поле ответственный ввести с клавиатуры значение: "Тек"</li>
     * <li>Проверить, что в выпадающем списке 3 значения: "Команда текущего ответственного", "Лидер команды текущего
     * ответственного", "Текущий ответственный"</li>
     * </ol>
     */
    @Test
    public void testResponsibleIsCaseSensitiveOnStatusForm()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userCase, MetaclassCardTab.LIFECYCLE);
        tester.click(GUIXpath.Any.BUTTON_TOOLBAR + GUIXpath.Div.ADD);
        GUISelect.assertSelected(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_INPUT,
                Strategy.CURRENT.getTitle());

        tester.sendKeys(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_INPUT, "тек");
        List<String> statuses = List.of("содержащие...", "тек", Strategy.TEAM.getTitle(),
                Strategy.TEAM_LEADER.getTitle(), Strategy.CURRENT.getTitle(), "найдено элементов: 3");
        GUISelect.assertSelect(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_INPUT,
                statuses, false, true, false);

        tester.sendKeys(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_INPUT, "Тек");
        List<String> statuses2 = List.of("содержащие...", "Тек", Strategy.TEAM.getTitle(),
                Strategy.TEAM_LEADER.getTitle(), Strategy.CURRENT.getTitle(), "найдено элементов: 3");
        GUISelect.assertSelect(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_INPUT,
                statuses2, false, true, false);
    }
}