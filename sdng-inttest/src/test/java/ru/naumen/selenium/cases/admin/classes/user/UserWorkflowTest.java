package ru.naumen.selenium.cases.admin.classes.user;

import java.io.File;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Test;

import com.google.common.collect.Lists;
import com.google.gson.Gson;

import ru.naumen.selenium.casesutil.GUIAttention;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIWfDiagram;
import ru.naumen.selenium.casesutil.metaclass.GUIWorkflow;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.ResponsibleType;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.Strategy;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoExportModel;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тестирование жизненного цикла пользовательских объектов в ИА
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00446
 * <AUTHOR>
 * @since 30.07.2012
 *
 */
public class UserWorkflowTest extends AbstractTestCase
{
    /**
     * Добавление типа объекта с ЖЦ
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00446
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в интерфейсе администратора в Базовый пользовательский бизнес-объект<li>
     * <li>Нажать Добавить</li>
     * <li>Ввести название, код, описание, поставить чекбокс ЖЦ</li>
     * <li>Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Пользовательский класс создан</li>
     * <li>Вкладка атрибуты карточки класса: присутствуют атрибуты Время входа в статус(stateStartTime) и Статус
     * (state)</li>
     * <li>Вкладка атрибуты карточки класса: Вложенный класс Event for <название класса></li>
     * <li>Вкладка карточка объекта класса: Есть кнопка Изменить статус</li>
     * <li>Карточка класса: присутствует вкладка ЖЦ с наполнением: Статусы зарегистрирован, Закрыт</li>
     * <li>Статус зарегистрирован</li>
     * <li>Статус закрыт</li>
     * </ol>
     */
    @Test
    public void testAddLifeCycle()
    {
        MetaClass userClass = DAOUserClass.createWithWF();
        //Переходим в интерфейс администратора
        GUILogon.asSuper();
        GUIMetaClass.callAddForm(userClass);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, userClass.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, userClass.getCode());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.INPUT_FIELD_DESCRIPTION, userClass.getDescription());
        tester.setCheckbox(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.HAS_WORKFLOW_VALUE_INPUT, true);
        tester.setCheckbox(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.HAS_RESPONSIBLE_VALUE_INPUT,
                false);
        GUIForm.applyForm();
        //Проверяем, что данный метакласс есть в системе SD 4.0
        GUIMetaClass.assertPresent(userClass);
        GUIMetaClass.goToCard(userClass);
        GUIAttribute.assertAttributeTitle("Статус", "state");
        GUIAttribute.assertAttributeTitle("Время входа в статус", "stateStartTime");

        Assert.assertEquals("Нет вложенного класса Event", "Event for " + userClass.getTitle(),
                tester.getText(GUIXpath.Div.INFO_CLASS_STRATEGY_VALUE));

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        Assert.assertTrue("Отсутствует кнопка Изменить статус",
                tester.waitAppear("//div[contains(@id, 'gwt-debug-changeState.')]//*[text()='Изменить статус']"));
        Assert.assertTrue("Отсутствует вкладка ЖЦ", tester.find(Div.CLASS_WF_TAB).isDisplayed());
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.LIFECYCLE);
        Assert.assertTrue("Отсутствует статус Зарегистрирован",
                tester.waitAppear("//tr[@__did='registered']//*[text()='Зарегистрирован']"));
        Assert.assertTrue("Отсутствует статус Закрыт", tester.waitAppear("//tr[@__did='closed']//*[text()='Закрыт']"));
    }

    /**
     * Добавление типа объекта с назначением ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00446
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в интерфейсе администратора в Базовый пользовательский бизнес-объект<li>
     * <li>Нажать Добавить</li>
     * <li>Ввести название, код, описание, поставить чекбокс назначение ответственного</li>
     * <li>Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Пользовательский класс создан</li>
     * <li>Вкладка атрибуты карточки класса: присутствуют атрибуты
     *  Время последнего изменения ответственного(responsibleStartTime), агрегирующий атрибут Ответственный
     *  (responsible)</li>
     * <li>Вкладка атрибуты карточки класса: Вложенный класс Event for <название класса></li>
     * <li>Вкладка карточка объекта класса: Есть кнопка Изменить ответственного</li>
     * <li>Нет вкладки ЖЦ</li>
     * </ol>
     */
    @Test
    public void testAddResponsible()
    {
        MetaClass userClass = DAOUserClass.createWithResp();
        //Переходим в интерфейс администратора
        GUILogon.asSuper();
        GUIMetaClass.callAddForm(userClass);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, userClass.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, userClass.getCode());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.INPUT_FIELD_DESCRIPTION, userClass.getDescription());
        tester.setCheckbox(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.HAS_WORKFLOW_VALUE_INPUT, false);
        tester.setCheckbox(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.HAS_RESPONSIBLE_VALUE_INPUT, true);
        GUIForm.applyForm();
        //Проверяем, что данный метакласс есть в системе SD 4.0
        GUIMetaClass.assertPresent(userClass);
        GUIMetaClass.goToCard(userClass);

        GUIAttribute.assertAttributeTitle("Время последнего изменения ответственного", "responsibleStartTime");
        GUIAttribute.assertAttributeTitle("Ответственный", "responsible");
        GUIAttribute.assertAttributeTitle("Ответственный (Команда)", "responsibleTeam");
        GUIAttribute.assertAttributeTitle("Ответственный (Сотрудник)", "responsibleEmployee");

        Assert.assertEquals("Нет вложенного класса Event", "Event for " + userClass.getTitle(),
                tester.getText(GUIXpath.Div.INFO_CLASS_STRATEGY_VALUE));
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        Assert.assertTrue("Отсутствует кнопка Изменить ответственного", tester
                .waitAppear("//div[contains(@id, 'gwt-debug-editResponsible.')]//*[text()='Изменить ответственного']"));
        Assert.assertTrue("Присутствует вкладка ЖЦ", tester.waitDisappear(Div.CLASS_WF_TAB));
    }

    /**
     * Проверка корректного обновления таблицы tbl_sys_metainfo_states при добавление статуса к объекту с ЖЦ
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00446
     * http://sd-jira.naumen.ru/browse/NSDPRD-3396
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать userClass с ЖЦ</li>
     * <li>В классе userClass создаем тип userCase1 и в userCase1 тип userCase2</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Добавляем в userClass пользовательский статус userState</li>
     * <li>Выполняем запрос: select fqn, title from tbl_sys_metainfo_states ms where ms.code = 'userState' 
     * и проверяем что статус появился для класса и двух типов</li>
     * <li>Изменяем в userClass title статуса на userState1</li>
     * <li>Проверяем что в таблице изменились записи для всех потомков</li>
     * <li>Удаляем статус userState из класса userClass</li>
     * <li>Проверяем что в таблице удалились записи для всех потомков</li>
     * </ol>
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testAddStateWithTableUpdate()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        userClass.setHasWorkflow("true");
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userCase1);
        DSLMetaClass.add(userClass, userCase1, userCase2);

        //@formatter:off
        String scriptPattern = 
        "def q = beanFactory.getBean(\"sessionFactory\").getCurrentSession().createNativeQuery(\"select fqn, title from tbl_sys_metainfo_states ms where ms.code = '%s'\")%n" +
        "def result = [:];%n" +
        "q.list().each{%n" +
            "result[it[0]] = it;%n" +
        "}%n" +
        "return new com.google.gson.Gson().toJson(result);";
        //@formatter:on
        String message = "Данные в таблице статусов не соответствуют структуре мета классов";

        //Действия и проверки
        BoStatus userState = DAOBoStatus.createUserStatus(userClass.getFqn());
        DSLBoStatus.add(userState);
        ScriptRunner script = new ScriptRunner(String.format(scriptPattern, userState.getCode()));
        Map<String, List<String>> result = new Gson().fromJson(script.runScript().get(0), Map.class);
        Assert.assertTrue(message, result.containsKey(userClass.getFqn()));
        Assert.assertTrue(message, result.containsKey(userCase1.getFqn()));
        Assert.assertTrue(message, result.containsKey(userCase2.getFqn()));
        userState.setTitle("userState1");
        DSLBoStatus.edit(userState);
        script = new ScriptRunner(String.format(scriptPattern, userState.getCode()));
        result = new Gson().fromJson(script.runScript().get(0), Map.class);
        Assert.assertTrue(message, result.get(userClass.getFqn()).get(1).equals(userState.getTitle()));
        Assert.assertTrue(message, result.get(userCase1.getFqn()).get(1).equals(userState.getTitle()));
        Assert.assertTrue(message, result.get(userCase2.getFqn()).get(1).equals(userState.getTitle()));
        DSLBoStatus.delete(userState);
        result = new Gson().fromJson(script.runScript().get(0), Map.class);
        Assert.assertTrue(message, result.isEmpty());
    }

    /**
     * Добавление статуса к объекту с ЖЦ и без передачей ответсвенности
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00446
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать userObject с ЖЦ</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в карточку userObject, вкладка Жизненный цикл<li>
     * <li>Нажать Добавить статус</li>
     * <li>Ввести название, код, цвет, описание</li>
     * <li>Нажать кнопку Сохранить</li>
     * <li>Проверить в списке статусов: код, Показывать кнопку изменения ответственного - отсутствует</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка статуса присутствуют: название, код, описание, Состояние - включено</li>
     * <li>Карточка статуса отсутствуют: Класс ответственного в статусе, Ответственный, Показывать кнопку изменения
     * ответственного</li>
     * </ol>
     */
    @Test
    public void testAddStateWithWF()
    {
        MetaClass userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);

        //Выполнение тестовых действий
        BoStatus status = DAOBoStatus.createUserStatus(userClass.getFqn(), null, null);
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.LIFECYCLE);

        GUIBoStatus.addStatus(status, false);
        GUIForm.assertFormDisappear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        //Проверки
        Object[] args = { status.getCode(), status.getTitle() };
        Assert.assertTrue("На вкладке ЖЦ нет добавленного статуса", tester.waitAppear(GUIBoStatus.X_BO_STATUS, args));
        status.setExists(true);
        GUIBoStatus.assertChangeResponsibleButtonVisibleInStateList(status, null);

        //Переходим в карточку
        tester.click("//tr[@__did='" + status.getCode() + "']/td[@__did='title']/a");

        Assert.assertEquals("Название  отображается неверно в карточке статуса", status.getTitle(),
                tester.getText(GUIXpath.Complex.SCS_TITLE));
        Assert.assertEquals("Код  отображается неверно в карточке статуса", status.getCode(),
                tester.getText(GUIXpath.Complex.SCS_CODE));
        Assert.assertTrue("В карточке статуса присутствует Класс ответственного",
                tester.waitDisappear(GUIXpath.Complex.SCS_RESP_TYPE));
        Assert.assertTrue("В карточке статуса присутствует Показывать кнопку изменения ответственного",
                tester.waitDisappear(GUIBoStatus.STATE_CHANGE_RESPONSIBLE_BUTTON_VISIBLE_CARD));
        Assert.assertTrue("В карточке статуса присутствует Стратегия ответственности",
                tester.waitDisappear(GUIXpath.Complex.SCS_DESCR));
        Assert.assertEquals("Описание отображается неверно в карточке статуса", status.getDescription(),
                tester.getText(GUIXpath.Complex.SCS_SRT));
        String state = Boolean.valueOf(status.getState()) ? GUIXpath.Constant.YES : GUIXpath.Constant.NO;
        Assert.assertTrue("Состояние отображается неверно в карточке статуса",
                tester.find(GUIXpath.Complex.SCS_STATE).getAttribute("class").contains(state));
    }

    /**
     * Добавление статуса к объекту с ЖЦ и передачей ответсвенности
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00446
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать userObject с ЖЦ, с назначением ответственного</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в карточку userObject, вкладка Жизненный цикл<li>
     * <li>Нажать Добавить статус</li>
     * <li>Ввести название, код, цвет, описание, класс ответственного- сотрудники, ответственный - предыдущий
     * ответственный (проверить список стратегий)</li>
     * <li>Нажать кнопку Сохранить</li>
     * <li>Проверить в списке статусов: код, Показывать кнопку изменения ответственного - включено</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка userObject вкладка Жизненный цикл: название и код статуса</li>
     * <li>Карточка статуса: название, код,  Класс ответственного в статусе, Ответственный, описание, Состояние -
     * включено</li>
     * </ol>
     */
    @Test
    public void testAddStateWithWFandResp()
    {
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        DSLMetaClass.add(userClass);

        //Выполнение тестовых действий
        BoStatus status = DAOBoStatus.createUserStatus(userClass.getFqn(), ResponsibleType.EMPLOYEE, Strategy.PREV);
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.LIFECYCLE);

        GUIBoStatus.addStatus(status, true);

        GUIForm.assertFormDisappear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        //Проверки
        Assert.assertTrue("На вкладке ЖЦ нет добавленного статуса",
                tester.waitAppear("//tr[@__did='" + status.getCode() + "']"));
        Assert.assertTrue("Код статуса отображается неверно или не найден, должен быть " + status.getCode(),
                tester.waitAppear("//div[text()='" + status.getCode() + "']"));
        status.setExists(true);
        GUIBoStatus.assertChangeResponsibleButtonVisibleInStateList(status, true);

        //Переходим в карточку
        tester.click("//tr[@__did='" + status.getCode() + "']/td[@__did='title']/a");

        Assert.assertEquals("Название  отображается неверно в карточке статуса", status.getTitle(),
                tester.getText(GUIXpath.Complex.SCS_TITLE));
        Assert.assertEquals("Код  отображается неверно в карточке статуса", status.getCode(),
                tester.getText(GUIXpath.Complex.SCS_CODE));
        Assert.assertEquals("Класс ответственного отображается неверно в карточке статуса", "сотрудники",
                tester.getText(GUIXpath.Complex.SCS_RESP_TYPE));
        GUIBoStatus.assertChangeResponsibleButtonVisibleInStateCard(status);
        Assert.assertEquals("Стратегия ответственности отображается неверно в карточке статуса",
                "Предыдущий ответственный", tester.getText(GUIXpath.Complex.SCS_DESCR));
        Assert.assertEquals("Описание отображается неверно в карточке статуса", status.getDescription(),
                tester.getText(GUIXpath.Complex.SCS_SRT));
        String state = Boolean.valueOf(status.getState()) ? GUIXpath.Constant.YES : GUIXpath.Constant.NO;
        Assert.assertTrue("Состояние отображается неверно в карточке статуса",
                tester.find(GUIXpath.Complex.SCS_STATE).getAttribute("class").contains(state));
    }

    /**
     * Тестирование удаления состояния из ЖЦ одного типа, если состояние с тем же кодом объявлено в другом типе
     * данного класса и используется счетчиком времени
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * http://sd-jira.naumen.ru/browse/NSDPRD-4501
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс с жизненным циклом userClass</li>
     * <li>В классе userClass создать тип userCase1</li>
     * <li>В классе userClass создать тип userCase2</li>
     * <li>В классе userClass создать атрибут типа Элемент справочника
     * (Справочник - Часовые пояса, Значение по умолчанию - отсутствует)</li>
     * <li>В ЖЦ типа userCase1 добавить статус userState1 с произвольным кодом</li>
     * <li>В ЖЦ типа userCase2 добавить статус userState2 с тем же кодом, что и у userState1</li>
     * <li>Создать счетчик времени (Объекты - userClass, Метрика времени - Астрономическое время,
     * Тип условия - По смене статуса, Останавливать счетчик в статусах - Закрыт (userClass, userCase1, userCase2),
     * Учитывать время в статусах - userState1)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в интерфейс технолога под суперпользователем</li>
     * <li>Перейти на карточку статуса userState2</li>
     * <li>Нажать на кнопку "Удалить" и подтвердить действие</li>
     * <br>
     * <b>Проверки</b>
     * <li>Удаление прошло без ошибок</li>
     * <li>Перейти на карточку жизненного цикла типа userCase2</li>
     * <li>На карточке жизненного цикла типа userCase2 отсутствует статус userState2</li>
     * </ol>
     */
    @Test
    public void testDeleteStateWhileOtherWithSameCodeInUse()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        Attribute tzAttr = DAOAttribute.createCatalogItem(userClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.TIMEZONE), null);
        DSLMetainfo.add(userClass, userCase1, userCase2, tzAttr);

        BoStatus userState1 = DAOBoStatus.createUserStatus(userCase1.getFqn());
        BoStatus userState2 = DAOBoStatus.createUserStatus(userCase2.getFqn());
        userState2.setCode(userState1.getCode());
        DSLBoStatus.add(userState1, userState2);

        BoStatus[] stopStates = { DAOBoStatus.createClosed(userClass.getFqn()),
                DAOBoStatus.createClosed(userCase1.getFqn()), DAOBoStatus.createClosed(userCase2.getFqn()) };
        TimerDefinition timer = DAOTimerDefinition.createAstroTimerByStatus(userClass.getFqn(), tzAttr.getCode(),
                stopStates, userState1);
        DSLTimerDefinition.add(timer);
        // Выполнение действий
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(userState2);
        GUIBoStatus.delete();
        // Проверки
        GUIMetaClass.goToLifeCycleCard(userCase2);
        GUIWfDiagram.assertStatusAbsence(userState2);
    }

    /**
     * Тестирование наследования параметра «Показывать кнопку изменения ответственного» для пользовательского класса
     * и системного статуса<br>
     * http://sd-jira.naumen.ru/browse/NSDPRD-5659<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00356
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с жизненным циклом и возможностью назначения ответственного.</li>
     * <li>Создать типы userType1 и userType2 пользовательского класса userClass.</li>
     * <li>Создать подтип subUserType1 типа userType1</li>
     * <br><b>Действия и проверки</b>
     * <li>Войти под суперпользователем.</li>
     * <li>Перейти на вкладку "Жизненный цикл" типа subUserType1.</li>
     * <li>Проверить, что "Показывать кнопку изменения ответственного" находится в значении true</li>
     * <li>Перейти на вкладку "Жизненный цикл" типа userType1.</li>
     * <li>Проверить, что "Показывать кнопку изменения ответственного" находится в значении true</li>
     * <li>Нажать на кнопку "Редактировать" статуса "Разрешён" в списке статусов.</li>
     * <li>Установить чекбокс "Показывать кнопку изменения ответственного" в значение false.</li>
     * <li>Сохранить изменения.</li>
     * <li>Проверить, что "Показывать кнопку изменения ответственного" находится в значении false</li>
     * <li>Перейти на вкладку "Жизненный цикл" типа userType2.</li>
     * <li>Проверить, что "Показывать кнопку изменения ответственного" находится в значении true</li>
     * <li>Перейти на вкладку "Жизненный цикл" типа subUserType1.</li>
     * <li>Проверить, что "Показывать кнопку изменения ответственного" находится в значении false</li>
     * <li>Перейти на вкладку "Жизненный цикл" типа userType1.</li>
     * <li>Нажать на кнопку "Сбросить настройки".</li>
     * <li>Проверить, что "Показывать кнопку изменения ответственного" находится в значении true</li>
     * <li>Перейти на вкладку "Жизненный цикл" типа subUserType1.</li>
     * <li>Проверить, что "Показывать кнопку изменения ответственного" находится в значении true</li>
     * </ol>
     */
    @Test
    public void testShowChangeResponsibleParameterForSystemStatus()
    {
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userType1 = DAOUserCase.create(userClass);
        MetaClass userType2 = DAOUserCase.create(userClass);
        MetaClass subUserType1 = DAOUserCase.create(userType1);
        DSLMetaClass.add(userClass, userType1, userType2, subUserType1);
        BoStatus registered = DAOBoStatus.createRegistered();

        GUILogon.asSuper();
        GUIMetaClass.goToLifeCycleCard(subUserType1);
        GUIBoStatus.assertChangeResponsibleButtonVisibleInStateList(registered, true);

        GUIMetaClass.goToLifeCycleCard(userType1);
        GUIBoStatus.assertChangeResponsibleButtonVisibleInStateList(registered, true);
        GUIBoStatus.clickEditIcon(registered);
        GUIBoStatus.clickDisableChangeResponsibleButton();
        GUIForm.applyModalForm();
        GUIBoStatus.assertChangeResponsibleButtonVisibleInStateList(registered, false);

        GUIMetaClass.goToLifeCycleCard(userType2);
        GUIBoStatus.assertChangeResponsibleButtonVisibleInStateList(registered, true);
        GUIMetaClass.goToLifeCycleCard(subUserType1);
        GUIBoStatus.assertChangeResponsibleButtonVisibleInStateList(registered, false);

        GUIMetaClass.goToLifeCycleCard(userType1);
        GUIMetaClass.pushResetButton();
        GUIBoStatus.assertChangeResponsibleButtonVisibleInStateList(registered, true);

        GUIMetaClass.goToLifeCycleCard(userType2);
        GUIBoStatus.assertChangeResponsibleButtonVisibleInStateList(registered, true);

        GUIMetaClass.goToLifeCycleCard(subUserType1);
        GUIBoStatus.assertChangeResponsibleButtonVisibleInStateList(registered, true);
    }

    /**
     * При удалении статуса не удаляются переходы, связанные с этим статусом<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00343 <br>
     * Дефект http://sd-jira.naumen.ru/browse/NSDPRD-2354<br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип объекта scCase,</li>
     * <li>Добавить в тип scCase статус test</li>
     * <li>Настроить переходы: Зарегистрирован - test, test - Разрешен</li>
     * <li>Сохранить изменения</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Удалить статус</li>
     * <li>Добавить новый статус test</li>
     * <br>
     * <b>Проверки</b>
     * <li>В статус и из статуса test нет ни одного перехода</li>
     * </ol>
     */
    @Test
    public void testTransitionMatrixOnDeleteAddState()
    {
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        // Выполнение действия
        BoStatus status = DAOBoStatus.createUserStatus(scClass.getFqn());
        DSLBoStatus.add(status);

        String nameFastTransition = ModelUtils.createTitle();
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(scCase.getFqn()), status,
                DAOBoStatus.createResumed(scCase.getFqn()));
        DSLBoStatus.addNamedTransition(scCase, DAOBoStatus.createRegistered(scCase.getFqn()), status,
                nameFastTransition);
        DSLBoStatus.addNamedTransition(scCase, status, DAOBoStatus.createResumed(scCase.getFqn()), nameFastTransition);

        GUILogon.asSuper();
        GUIMetaClass.goToLifeCycleCard(scCase);
        // Проверка
        GUIBoStatus.goToStatusCardWithRefresh(status);
        GUIBoStatus.delete();
        GUIMetaClass.goToTab(scCase, MetaclassCardTab.LIFECYCLE);
        GUIBoStatus.addStatus(status, false);

        Assert.assertTrue("Быстрый переход не был сброшен", tester.waitDisappear(
                "//a[@id='gwt-debug-tm.registered." + status.getCode() + "' and text()='" + nameFastTransition + "']"));
        Assert.assertTrue("Быстрый переход не был сброшен", tester.waitDisappear(
                "//a[@id='gwt-debug-tm." + status.getCode() + "' and text()='" + nameFastTransition + "']"));
    }

    /**
     * Тестирование присутствия базовых системных стратегий назначения ответственного у пользовательского класса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00447
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass с ЖЦ и назначением ответственного</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Зайти в карточку класса userClass</li>
     * <li>Перейти на вкладку Жизненный цикл</li>
     * <li>Нажать кнопку добавить статус Добавить статус</li>
     * <br>
     * <b>Проверки</b>
     * <li>В выпадающем списке Ответственный присутствуют:</li>
     * <li>Автор объекта, Без ответственного, Команда предыдущего ответственного, Команда текущего ответственного,
     * Лидер команды текущего ответственного, Предыдущий ответственный, Текущий ответственный</li>
     * <li>В выпадающем списке Ответственный отсутствуют: Решивший запрос, Куратора услуги</li>
     * </ol>
     */
    @Test
    public void testWFListResponsibleNames()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        DSLMetaClass.add(userClass);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.LIFECYCLE);
        tester.click(GUIXpath.Any.BUTTON_TOOLBAR + GUIXpath.Div.ADD);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        //Проверки
        List<String> respList = Lists.newArrayList("Автор объекта", "Без ответственного",
                "Команда предыдущего ответственного", "Команда текущего ответственного",
                "Лидер команды текущего ответственного", "Предыдущий ответственный", "Текущий ответственный");
        GUISelect.assertSelectWithoutEmpty(
                GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_INPUT, respList, true,
                false);
        GUISelect.assertNotDisplayedByTitle(
                GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Complex.CODE_RESPONSIBLE_INPUT, "Решивший запрос",
                "Куратора услуги");
    }

    /**
     * Тестирование назначения пользовательского статуса конечным
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114905021
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать userObject с ЖЦ</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Зайти в карточку userObject, вкладка Жизненный цикл</li>
     * <li>Нажать "Добавить статус"</li>
     * <li>Ввести основные поля, а "Конечный статус" оставить = false</li>
     * <li>Нажать кнопку Сохранить</li>
     * <li>Проверить, что статус появился в списке статусов</li>
     * <li>Нажать "Редактировать статус"</li>
     * <li>Изменить "Конечный статус" = true</li>
     * <li>Проверить, что появилось предупреждение: "1. Операция может занимать длительное время.\n
     *                 2. В конечном статусе системные счетчики времени останавливаются. Во всех существующих
     *                 запросах в статусе \"%s\" счетчик будет остановлен.\n
     *                 3. Объекты в конечном статусе не блокируют архивирование ответственных за них
     *                 сотрудника/команду/отдел и удаление/удаление из команды сотрудника, ответственного за
     *                 объект в конечном статусе в рамках команды."</li>
     * <li>Нажать кнопку Сохранить</li>
     * <li>Перейти на карточку статуса и проверить, что "Конечный статус" - включен</li>
     * </ol>
     */
    @Test
    public void testAddAndEditEndState()
    {
        MetaClass userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);

        //Выполнение тестовых действий и проверки
        BoStatus status = DAOBoStatus.createUserStatus(userClass.getFqn(), false);
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.LIFECYCLE);

        GUIBoStatus.addStatus(status, false);
        GUIForm.assertFormDisappear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        Object[] args = { status.getCode(), status.getTitle() };
        Assert.assertTrue("На вкладке ЖЦ нет добавленного статуса", tester.waitAppear(GUIBoStatus.X_BO_STATUS, args));

        GUIBoStatus.clickEditIcon(status);
        GUIBoStatus.changeEndStatus();
        GUIAttention.assertAttentionMessage(String.format("1. Операция может занимать длительное время.\n"
                                                          + "2. В конечном статусе системные счетчики времени "
                                                          + "останавливаются. Во всех существующих запросах в "
                                                          + "статусе \"%s\" счетчик будет остановлен.\n"
                                                          + "3. Объекты в конечном статусе не блокируют архивирование"
                                                          + " ответственных за них "
                                                          + "сотрудника/команду/отдел и удаление/удаление из команды "
                                                          + "сотрудника, ответственного за объект в "
                                                          + "конечном статусе в рамках команды.", status.getTitle()));
        GUIForm.applyForm();
        GUIBoStatus.goToStatusCardWithRefresh(status);
        Assert.assertTrue("Состояние конечности статуса отображается неверно в карточке статуса",
                tester.find(GUIXpath.Complex.END_STATE).getAttribute("class").contains(GUIXpath.Constant.YES));
    }

    /**
     * Тестирование сохранения настроек атрибутов в статусах в выгружаемой метаинформации, если настройки были
     * переопределены в типе, но при этом совпадают с настройками по умолчанию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$188536470
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass (Жизненный цикл — да) и унаследованный от него тип userCase</li>
     * <li>В классе userClass создать статус status</li>
     * <li>Установить параметр «Отображать, но не редактировать» для комментариев в статусе status</li>
     * <li>Установить параметр «Редактировать» для комментариев в статусе status в типе userCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Выгрузить частичную метаинформацию с классом userClass и типом userCase</li>
     * <li>Загрузить выгруженную метаинформацию обратно</li>
     * <li>Перейти на вкладку «Жизненный цикл» — «Управление параметрами в статусах» карточки типа userCase</li>
     * <br>
     * <b>Проверка</b>
     * <li>В статусе status выбрана настройка «Редактировать» для комментариев</li>
     * </ol>
     */
    @Test
    public void testSaveStateSettingsOverrideToExportedMetainfo()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute commentAttribute = DAOAttribute.createPseudo("Комментарии", "@comment");
        BoStatus status = DAOBoStatus.createUserStatus(userClass);
        DSLBoStatus.add(status);
        DSLBoStatus.setAttrInState(commentAttribute, status, true, false, 0, 0);
        BoStatus statusCase = DAOBoStatus.copy(status, userCase);
        DSLBoStatus.setAttrInState(commentAttribute, statusCase, true, true, 0, 0);
        // Выполнение действий
        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectMetaClasses(exportModel, userClass, userCase);
        File metainfo = DSLMetainfoTransfer.exportMetainfo(exportModel);
        DSLMetainfoTransfer.importMetainfo(metainfo);
        GUILogon.asSuper();
        GUIMetaClass.goToLifeCycleCard(userCase);
        GUIWorkflow.goToStatesAttrsSettingsTab();
        // Проверка
        GUIBoStatus.assertAttributeInStatesAttrsSettings(commentAttribute, status.getCode(), 0);
    }
}
