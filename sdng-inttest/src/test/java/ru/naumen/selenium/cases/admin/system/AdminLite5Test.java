package ru.naumen.selenium.cases.admin.system;

import static ru.naumen.selenium.casesutil.admin.GUIAdminNavigationTree.ADMIN_PROFILES_ITEM_ID;
import static ru.naumen.selenium.casesutil.admin.GUIAdminNavigationTree.EVENT_ACTIONS_ITEM_ID;
import static ru.naumen.selenium.casesutil.admin.GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID;
import static ru.naumen.selenium.casesutil.admin.interfaze.GUILogoUi2.*;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.ADMINISTRATION_PROFILES;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.OPERATOR_INTERFACE;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.ALL;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.VIEW;

import java.io.File;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.A;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLAdminLite;
import ru.naumen.selenium.casesutil.admin.DSLAdminLog;
import ru.naumen.selenium.casesutil.admin.DSLInterface;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.DSLSession;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.admin.GUIAdminLite;
import ru.naumen.selenium.casesutil.admin.GUIAdminLogList;
import ru.naumen.selenium.casesutil.admin.GUIAdminNavigationTree;
import ru.naumen.selenium.casesutil.admin.GUIEventCleanerListUtil;
import ru.naumen.selenium.casesutil.admin.GUIExportUtils;
import ru.naumen.selenium.casesutil.admin.LogEntry;
import ru.naumen.selenium.casesutil.admin.eventcleaner.GUIEventCleaner;
import ru.naumen.selenium.casesutil.admin.interfaze.GUILogoUi2;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUICommentList;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.customforms.DSLFormParameter;
import ru.naumen.selenium.casesutil.escalation.GUIEscalation;
import ru.naumen.selenium.casesutil.eventcleaner.DSLEventStorageRule;
import ru.naumen.selenium.casesutil.file.GUIFileAdmin;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.interfaceelement.ThreeStateBoTree;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLActionCondition;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEventAction;
import ru.naumen.selenium.casesutil.metaclass.GUIEventActionList;
import ru.naumen.selenium.casesutil.model.admin.AdminLitePage;
import ru.naumen.selenium.casesutil.model.admin.AdminLiteSettings;
import ru.naumen.selenium.casesutil.model.admin.log.Constants;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.eventcleaner.DAOEventStorageRule;
import ru.naumen.selenium.casesutil.model.eventcleaner.EventStorageRule;
import ru.naumen.selenium.casesutil.model.metaclass.ActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoElementIdBuilder;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.styletemplate.DAOStyleTemplate;
import ru.naumen.selenium.casesutil.model.styletemplate.StyleTemplate;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.model.tag.DAOTag;
import ru.naumen.selenium.casesutil.model.tag.Tag;
import ru.naumen.selenium.casesutil.personalsettings.GUIPersonalSettings.AddCommentInlineFormPresentation;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.DSLApplication;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.styletemplate.DSLStyleTemplate;
import ru.naumen.selenium.casesutil.tag.DSLTag;
import ru.naumen.selenium.casesutil.tag.GUITag;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование облегченного интерфейса настройки
 * <AUTHOR>
 * @since 03.03.2023
 */
public class AdminLite5Test extends AbstractTestCase
{
    private static Bo employee;
    private static SecurityGroup securityGroup;
    private static MetaClass employeeCase;

    /**
     * Общая подготовка:
     * <ul>
     * <li>Загрузить на стенд лицензионный файл с модулем admin-lite.</li>
     * <li>Создать сотрудника со всеми правами</li>
     * <li>Создать группу пользователей и добавить туда сотрудника</li>
     * </ul>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.MOBILE_AND_ADMINLITE_LICENSE_PATH);
        MetaClass ouCase = DAOOuCase.create();
        employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, employeeCase);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        employee = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(employee);

        securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);
        DSLSecurityGroup.addUsers(securityGroup, employee);
    }

    /**
     * Тестирование недоступности карточки эскалации для пользователя admin-lite при добавленной настройке Карточки ДПС
     * в Облегченном интерфейсе настройки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$182702583
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В настройки Облегченного интерфейса настройки добавить:<ul>
     *     <li>страницы интерфейса: "Карточка Действия по событию" в Настройках системы и "Эскалация" в Настройках
     *     бизнес-процессов</li>
     *     <li>группу пользователей groupCase</li>
     * </ul></li>
     * <li>Включить Облегченный интерфейс настройки</li>
     * <li>Добавить действие по событию testDPS типа Эскалация:<ul>
     *     <li>Действие: Уведомление в интерфейсе</li>
     *     <li>Кому: Сотрудник</li>
     *     <li>Текст уведомления: 123</li>
     * </ul></li>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     * <li>Войти под сотрудником employee в ИА</li>
     * <li>Открыть раздел Эскалации в блоке Настройка бизнес-процессов</li>
     * <li><em>Проверить</em>, что название действия testDps не имеет свойства href (нет ссылки), при клике перехода на
     * другую страницу не происходит</li>
     * </ol>
     */
    @Test
    public void testUnavailabilityEscalationCardForAdminLiteUserWhenDPSCardIsAdded()
    {
        //Подготовка
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(Lists.newArrayList(AdminLitePage.EVENT_ACTION_CARD.getCode()));
        settings.setProcessSettings(Lists.newArrayList(AdminLitePage.ESCALATION.getCode()));
        settings.setGroups(Lists.newArrayList(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        EventAction push = DAOEventAction.createPush(employeeCase, EventType.escalation, employee);
        push.setMessage("123");
        DSLEventAction.add(push);

        //Действия и проверки
        GUILogon.login(employee);
        GUIEscalation.goToActions();
        GUIEventActionList.assertActionInList(push);
        GUITester.assertAbsent(String.format(GUIEventActionList.TABLE_ROW_LINK_TITLE, push.getUuid()),
                "Название действия по событию является ссылкой: " + push.getTitle());
        GUITester.clickOnText(push.getDescription());
        GUIError.assertDialogError(ErrorMessages.RESOURCE_CANNOT_BE_FOUND_MESSAGE);
    }

    /**
     * Тестирование включения/отключения действия по событию пользователем admin-lite с карточки ДПС, если у него есть
     * доступ к действиям по событию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$182702583
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В настройки Облегченного интерфейса настройки добавить:<ul>
     *     <li>страницу интерфейса: "Действия по событиям"</li>
     *     <li>группу пользователей groupCase</li>
     * </ul></li>
     * <li>Включить Облегченный интерфейс настройки</li>
     * <li>Добавить действие по событию testDPS типа Пользовательское событие::<ul>
     *     <li>Объекты: Компания</li>
     *     <li>Действие:Оповещение</li>
     *     <li>Тема: 123</li>
     *     <li>Текст оповещения: 123</li>
     * </ul></li>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     * <li>Войти под сотрудником employee в ИА</li>
     * <li>Открыть раздел Действия по событиям в блоке Настройка системы</li>
     * <li>Открыть карточку ДПС testDPS</li>
     * <li>Включить ДПС testDPS по кнопке Включить</li>
     * <li><em>Проверить</em>, что ДПС testDPS включилось</li>
     * <li>Отключить ДПС testDPS по кнопке Отключить</li>
     * <li><em>Проверить</em>, что ДПС testDPS отключилось</li>
     * </ol>
     */
    @Test
    public void testEnablingDisablingEventActionByAdminLiteFromDPSCardIfHasAccess()
    {
        //Подготовка
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(Lists.newArrayList(AdminLitePage.EVENT_ACTION_CARD.getCode()));
        settings.setGroups(Lists.newArrayList(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        EventAction notification = DAOEventAction.createNotification(employeeCase, EventType.userEvent,
                Lists.newArrayList(SharedFixture.root()));
        notification.setSubject("123");
        notification.setMessage("123");
        notification.setEnable(Boolean.FALSE.toString());
        DSLEventAction.add(notification);

        //Действия и проверки
        GUILogon.login(employee);
        GUIEventAction.goToCardWithRefresh(notification);
        GUIEventAction.clickSwitch();
        GUIEventAction.assertEnable(true);
        GUIEventAction.clickSwitch();
        GUIEventAction.assertEnable(false);
    }

    /**
     * Тестирование добавления записи в лог технолога при изменении настройки доступности карточки ДПС пользователю
     * admin-lite
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$182702583
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li> В настройки Облегченного интерфейса настройки добавить страницу интерфейса "Карточка действия по событию" в
     * Настройках системы.</li>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     * <li>Войти под naumen в ИА.</li>
     * <li>Зайти в Настройки системы - Администрирование - Лог действий технолога</li>
     * <li><em>Проверить</em>, что появилась запись об Изменении настроек облегченного интерфейса настройки, с
     * Описанием:</li>
     * <p>Изменены параметры облегченного интерфейса настройки:
     * Страницы интерфейса (настройка системы): '' -> 'Карточка действия по событию'.</p>
     * </ol>
     */
    @Test
    public void testAddingEntryToTechnologistLogWhenChangingCardAccessibilityForAdminLite()
    {
        //Подготовка
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(Lists.newArrayList(AdminLitePage.EVENT_ACTION_CARD.getCode()));
        DSLAdminLite.save(settings);

        //Действия и проверки
        GUILogon.asSuper();
        GUIAdminLogList.goToAdminLog();
        GUIAdminLogList logList = GUIAdminLogList.advlist();
        LogEntry reportLogUuid = DSLAdminLog.getLastByCategoryCode(Constants.CategoryCode.ADMIN_LITE_SETTINGS);
        logList.content().asserts().attrValue(reportLogUuid, GUIAdminLogList.ADMIN_LOG_DESCRIPTION_ATTR,
                "Изменены параметры облегченного интерфейса настройки:\n"
                + "Страницы интерфейса (настройка системы): '' -> 'Карточка Действия по событию'.");
    }

    /**
     * Тестирование отображения параметра Шаблон на карточке ДПС под пользователем admin-lite
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$182702583
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В настройки Облегченного интерфейса настройки добавить:<ul>
     *     <li>страницу интерфейса: "Действия по событиям"</li>
     *     <li>группу пользователей groupCase</li>
     * </ul></li>
     * <li>Включить Облегченный интерфейс настройки</li>
     * <li>Добавить шаблон стилей testTemplate, текст: testTemplate</li>
     * <li>Добавить действие по событию testDPS типа Пользовательское событие::<ul>
     *     <li>Объекты: Компания</li>
     *     <li>Действие:Оповещение</li>
     *     <li>Тема: 123</li>
     *     <li>Текст оповещения: 123</li>
     *     <li>Шаблон: testTemplate</li>
     * </ul></li>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     * <li>Войти под сотрудником employee в ИА</li>
     * <li>Открыть карточку действия по событию testDPS</li>
     * <li><em>Проверить</em>, что строка testTemplate  в параметре Шаблон не имеет свойства href (нет ссылки), при
     * клике на неё перехода на другую страницу не происходит</li>
     * </ol>
     */
    @Test
    public void testDisplayTemplateParameterOnDPSCurd()
    {
        //Подготовка
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(Lists.newArrayList(AdminLitePage.EVENT_ACTION_CARD.getCode()));
        settings.setGroups(Lists.newArrayList(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        StyleTemplate template = DAOStyleTemplate.createTemplate("testTemplate");
        DSLStyleTemplate.add(template);

        EventAction notification = DAOEventAction.createNotification(employeeCase, EventType.userEvent,
                Lists.newArrayList(SharedFixture.root()));
        notification.setSubject("123");
        notification.setMessage("123");
        notification.setMessageTemplate(template.getCode());
        DSLEventAction.add(notification);

        //Действия и проверки
        GUILogon.login(employee);
        GUIEventAction.goToCardWithRefresh(notification);
        GUITester.assertPresent(Div.TEXT_ANY, "Шаблон не отображается", template.getTitle());
        GUITester.assertAbsent(String.format(A.TEXT_PATTERN, template.getTitle()),
                "Шаблон действия по событию является ссылкой: " + template.getTitle());
        GUIEventAction.assertThatCard(notification);
    }

    /**
     * Тестирование отображения контента Параметры на карточке ДПС под пользователем admin-lite
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$182702583
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В настройки Облегченного интерфейса настройки добавить:<ul>
     *     <li>страницу интерфейса: "Действия по событиям"</li>
     *     <li>группу пользователей groupCase</li>
     * </ul></li>
     * <li>Включить Облегченный интерфейс настройки</li>
     * <li>Добавить действие по событию testDPS типа Пользовательское событие::<ul>
     *     <li>Объекты: Компания</li>
     *     <li>Действие:Оповещение</li>
     *     <li>Тема: 123</li>
     *     <li>Текст оповещения: 123</li>
     * </ul></li>
     * <li>Добавить два параметра в testDps: boolCase (Логический) и stringCase (Строка)</li>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     * <li>Войти под сотрудником employee в ИА</li>
     * <li>Открыть карточку действия по событию testDPS</li>
     * <li><em>Проверить</em>, что у контента Параметры нет кнопки Добавить параметр, у самих параметров нет
     * кнопок-иконок Редактировать и Удалить справа, нет стрелочек для переноса слева</li>
     * <li><em>Проверить</em>, что параметры нельзя переносить drug'n'drop'ом</li>
     * </ol>
     */
    @Test
    public void testDisplayContentParametersOnDPSCardInAdminLite()
    {
        //Подготовка
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(Lists.newArrayList(AdminLitePage.EVENT_ACTION_CARD.getCode()));
        settings.setGroups(Lists.newArrayList(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        EventAction notification = DAOEventAction.createNotification(employeeCase, EventType.userEvent,
                Lists.newArrayList(SharedFixture.root()));
        notification.setSubject("123");
        notification.setMessage("123");
        DSLEventAction.add(notification);

        FormParameter stringAttr = DAOFormParameter.createString();
        stringAttr.setEventAction(notification.getUuid());
        FormParameter boolAttr = DAOFormParameter.createBool();
        boolAttr.setEventAction(notification.getUuid());
        DSLFormParameter.save(stringAttr, boolAttr);

        //Действия и проверки
        GUILogon.login(employee);
        GUIEventAction.goToCardWithRefresh(notification);
        GUIEventAction.assertThatCard(notification);

        GUIEventAction.assertMoveDownParamBtnPresent(boolAttr.getCode(), false);
        GUIEventAction.assertMoveDownParamBtnPresent(stringAttr.getCode(), false);
        GUIEventAction.assertMoveUpParamBtnPresent(boolAttr.getCode(), false);
        GUIEventAction.assertMoveUpParamBtnPresent(stringAttr.getCode(), false);

        tester.dragAndDropOnObjectWithOffset(String.format(GUIEventAction.PARAMETER_TABLE_ELEMENT, stringAttr.getCode(),
                "title"), String.format(GUIEventAction.PARAMETER_TABLE_ELEMENT, boolAttr.getCode(), "title"), 0, 5);
        GUIEventAction.assertParamOrder(stringAttr.getCode(), boolAttr.getCode());
        tester.dragAndDropOnObjectWithOffset(String.format(GUIEventAction.PARAMETER_TABLE_ELEMENT, boolAttr.getCode(),
                "title"), String.format(GUIEventAction.PARAMETER_TABLE_ELEMENT, stringAttr.getCode(), "title"), 0, 5);
        GUIEventAction.assertParamOrder(stringAttr.getCode(), boolAttr.getCode());
    }

    /**
     * Тестирование отображения контента Условия выполнения действия на карточке ДПС под пользователем admin-lite
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$182702583
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В настройки Облегченного интерфейса настройки добавить:<ul>
     *     <li>страницу интерфейса: "Действия по событиям"</li>
     *     <li>группу пользователей groupCase</li>
     * </ul></li>
     * <li>Включить Облегченный интерфейс настройки</li>
     * <li>Добавить действие по событию testDPS типа Пользовательское событие::<ul>
     *     <li>Объекты: Компания</li>
     *     <li>Действие:Оповещение</li>
     *     <li>Тема: 123</li>
     *     <li>Текст оповещения: 123</li>
     * </ul></li>
     * <li>Добавить в ДПС testDPS условие выполнения действия testCondition со скриптом return ''</li>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     * <li>Войти под сотрудником employee в ИА</li>
     * <li>Открыть карточку действия по событию testDPS</li>
     * <li><em>Проверить</em>, что у контента Условия выполнения действий нет кнопки Добавить условие, у самого условия
     * нет кнопок-иконок Редактировать и Удалить справа. Строка testCondition не имеет свойства href (нет ссылки), при
     * клике на неё перехода на другую страницу не происходит</li>
     * </ol>
     */
    @Test
    public void testDisplayContentConditionsOnDPSCardInAdminLite()
    {
        //Подготовка
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(Lists.newArrayList(AdminLitePage.EVENT_ACTION_CARD.getCode()));
        settings.setGroups(Lists.newArrayList(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        EventAction notification = DAOEventAction.createNotification(employeeCase, EventType.userEvent,
                Lists.newArrayList(SharedFixture.root()));
        notification.setSubject("123");
        notification.setMessage("123");
        DSLEventAction.add(notification);

        ScriptInfo scriptActionCondition = DAOScriptInfo.createNewScriptInfo("return ''");
        DSLScriptInfo.addScript(scriptActionCondition);
        ActionCondition condition = DAOActionCondition.create(notification, scriptActionCondition.getCode());
        DSLActionCondition.add(condition);

        //Действия и проверки
        GUILogon.login(employee);
        GUIEventAction.goToCardWithRefresh(notification);
        GUIEventAction.assertThatCard(notification);
        GUITester.assertPresent(Div.TEXT_ANY, "Условие не отображается", condition.getTitle());
        GUITester.assertAbsent(GUIEventAction.ADD_CONDITION_BUTTON, "Присутствует кнопка добавить условие");
        GUITester.assertAbsent(GUIEventAction.EDIT_CONDITION_ICON, "У условия присутствует кнопка редактировать",
                condition.getTitle());
        GUITester.assertAbsent(GUIEventAction.DELETE_PARAMETER_ICON, "У условия присутствует кнопка удалить",
                condition.getTitle());
        GUITester.assertAbsent(String.format(A.TEXT_PATTERN,
                condition.getTitle()), "Условие является ссылкой: " + notification.getTitle());
        GUIEventAction.assertThatCard(notification);
    }

    /**
     * Тестирование выключения доступности всех Прочих настроек администрирования для пользователя с правами Admin Lite
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$182702582
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить настройку Облегченного интерфейса настройки:<ul>
     *     <li>Настройка системы: установить две настройки:<ul>
     *         <li>Администрирование -> Комментарии</li>
     *         <li>Администрирование -> Прочие настройки -> Название системы</li>
     *     </ul></li>
     *     <li>Доступен группам пользователей = Group</li>
     * </ul></li>
     * </ol>
     * <b>Действия:</b>
     * <ol>
     *     <li>Войти под naumen</li>
     *     <li>Перейти на страницу Администрирование</li>
     *     <li>Открыть форму редактирования Облегченного интерфейса настройки</li>
     *     <li>В дереве снять галку Настройка системы - Прочие настройки - Название системы</li>
     *     <li>Сохранить</li>
     *     <li>Перелогиниться под пользователем employee</li>
     *     <li>Перейти в ИА на страницу Администрирование</li>
     * </ol>
     * <b>Проверки:</b>
     * <ol>
     *     <li>На странице полностью отсутствует контент "Прочие настройки"</li>
     * </ol>
     */
    @Test
    public void testDisablingAvailabilityAllOtherAdminSettings()
    {
        //Подготовка
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(Lists.newArrayList(AdminLitePage.COMMENTS_SETTINGS.getCode(),
                AdminLitePage.OTHER_ADMIN_OPTIONS_SYSTEM_NAME.getCode()));
        settings.setGroups(Lists.newArrayList(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        //Действия и проверки
        GUILogon.asSuper();
        GUIAdmin.goToCard();
        GUIAdminLite.edit();
        ThreeStateBoTree tree = new ThreeStateBoTree(GUIAdminLite.X_SYSTEM_SETTINGS);
        tree.openTreeWithNode(AdminLitePage.OTHER_ADMIN_OPTIONS_SYSTEM_NAME.getPath());
        tree.setCheckbox(false, AdminLitePage.OTHER_ADMIN_OPTIONS_SYSTEM_NAME.getPath());
        tree.hideSelect();
        GUIForm.applyModalForm();

        GUILogon.login(employee);
        GUIAdmin.goToCard();
        GUIAdmin.assertPresentAdminLiteCommentsSettingsBlock(true);
        GUITester.assertAbsent(GUIAdmin.OTHER_OPTION_LITE_BLOCK, "Присутствует контент \"Прочие настройки\"");
    }

    /**
     * Тестирование редактирования Прочих настроек администрирования под пользователем с правами Admin Lite, если для
     * него доступны только следующие из них: Асинхронный подсчет объектов на вкладках,  Сжатие изображений в атрибутах
     * "Текст в формате RTF", Коэффициент сжатия изображений в атрибутах "Текст в формате RTF", Максимальное количество
     * одновременно открытых вкладок для одного пользователя
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$182702582
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Установить значение параметра ru.naumen.rtfEditor.insertImageAsURLEnabled в false</li>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить настройку Облегченного интерфейса настройки:<ul>
     *     <li>Настройка системы: установить 4 настройки:<ul>
     *         <li>Администрирование -> Прочие настройки -> Асинхронный подсчет объектов на вкладках</li>
     *         <li>Администрирование -> Прочие настройки -> Сжатие изображений в атрибутах "Текст в формате RTF"</li>
     *         <li>Администрирование -> Прочие настройки -> Коэффициент сжатия изображений в атрибутах "Текст в формате
     *         RTF"</li>
     *         <li>Администрирование -> Прочие настройки -> Максимальное количество одновременно открытых вкладок для
     *         одного пользователя</li>
     *     </ul></li>
     *     <li>Доступен группам пользователей = Group</li>
     * </ul></li>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти под пользователем employee</li>
     *     <li>Перейти в ИА на страницу Администрирование</li>
     *     <li>Открыть форму редактирования Прочих настроек</li>
     *     <li>Проверить, что на форме имеется 4 параметра: Асинхронный подсчет объектов на вкладках, Сжатие изображений
     *     в атрибутах "Текст в формате RTF", Коэффициент сжатия изображений в атрибутах "Текст в формате RTF",
     *     Максимальное количество одновременно открытых вкладок для одного пользователя</li>
     *     <li>Проверить, что на форме отсутствуют параметры: Вход в систему с неполным набором лицензий,
     *     Название системы, Анимация элементов интерфейса, Лог уведомлений</li>
     *     <li>Заполнить поля:<ul>
     *         <li>Асинхронный подсчет объектов на вкладках = да</li>
     *         <li>Сжатие изображений в атрибутах "Текст в формате RTF" = да</li>
     *         <li>Коэффициент сжатия изображений в атрибутах "Текст в формате RTF" = 0.5</li>
     *         <li>Максимальное количество одновременно открытых вкладок для одного пользователя = 5</li>
     *     </ul></li>
     * <li>Сохранить форму</li>
     * <li>Проверить, что измененные параметры отображаются в контенте Прочие настройки</li>
     * </ol>
     */
    @Test
    public void testEditingOtherAdminSettingsUnderUserWithAdminLiteRights()
    {
        //Подготовка
        DSLConfiguration.setInsertImageInFroalaAsURL(false);
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(Lists.newArrayList(AdminLitePage.OTHER_ADMIN_OPTIONS_ASYNC.getCode(),
                AdminLitePage.OTHER_ADMIN_OPTIONS_NEED_COMPRESS_IMG.getCode(),
                AdminLitePage.OTHER_ADMIN_OPTIONS_COMPRESSION_RATIO.getCode(),
                AdminLitePage.OTHER_ADMIN_OPTIONS_TABS.getCode()));
        settings.setGroups(Lists.newArrayList(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        //Действия и проверки
        GUILogon.login(employee);
        GUIAdmin.goToCard();
        GUITester.assertPresent(GUIAdmin.LITE_ASYNC_OBJ_COUNT_PATH_TITLE, "Отсутствует параметр "
                                                                          + "\"Асинхронный подсчет объектов на "
                                                                          + "вкладках\"");
        GUITester.assertPresent(GUIAdmin.LITE_NEED_COMPRESS_IMAGE_PATH_TITLE, "Отсутствует параметр "
                                                                              + "\"Сжатие изображений в атрибутах "
                                                                              + "Текст в формате RTF\"");
        GUITester.assertPresent(GUIAdmin.LITE_COMPRESS_RATIO_PATH_TITLE, "Отсутствует параметр "
                                                                         + "\"Коэффициент сжатия изображений в "
                                                                         + "атрибутах \"Текст в формате RTF\"");
        GUITester.assertPresent(GUIAdmin.LITE_COMPRESS_RATIO_PATH_TITLE, "Отсутствует параметр "
                                                                         + "\"Максимальное количество одновременно "
                                                                         + "открытых вкладок для одного "
                                                                         + "пользователя\"");
        GUITester.assertAbsent(GUIAdmin.LITE_UI_CSS_TRANSITIONS_ENABLED_PATH_TITLE, "Присутствует параметр "
                                                                                    + "\"Анимация элементов "
                                                                                    + "интерфейса\"");
        GUITester.assertAbsent(GUIAdmin.LITE_SYSTEM_NAME_PATH_TITLE, "Присутствует параметр "
                                                                     + "\"Название системы\"");
        GUITester.assertAbsent(String.format(GUIXpath.Any.ANY_CAPTION, "isCompleteLicenseSetRequiredCheckmark"),
                "Присутствует параметр \"Вход в систему с неполным набором лицензий\"");
        GUITester.assertAbsent(String.format(GUIXpath.Any.ANY_CAPTION, "notificationLogEnabled"),
                "Присутствует параметр \"Лог уведомлений\"");

        String compressRTF = "0.5";
        String maxCountTabs = "5";
        DSLAdmin.cleanOtherAdminOptions();

        GUIAdmin.clickEditOtherAdminLiteSettings();
        tester.setCheckbox(GUIAdmin.ASYNC_OBJ_COUNT_CHECKBOX, true);
        tester.setCheckbox(GUIAdmin.NEED_COMPRESS_IMAGE_CHECKBOX, true);
        tester.sendKeys(GUIAdmin.COMPRESS_RATIO_INPUT, compressRTF);
        tester.sendKeys(GUIAdmin.MAX_BROWSER_TABS_PER_USER_FORM_INPUT, maxCountTabs);
        GUIForm.applyForm();

        GUITester.assertPresent(GUIAdmin.ASYNC_OBJ_COUNT_ENABLED_IMAGE, "Не установлен параметр "
                                                                        + "\"Асинхронный подсчет объектов на "
                                                                        + "вкладках\"");
        GUITester.assertPresent(GUIAdmin.NEED_COMPRESS_IMAGE_ENABLED_IMAGE, "Не установлен параметр "
                                                                            + "\"Сжатие изображений в атрибутах Текст"
                                                                            + " в формате RTF\"");
        GUITester.assertTextPresent(GUIAdmin.COMPRESS_RATIO_ADMIN_PAGE_TEXT, compressRTF);
        GUITester.assertTextPresent(GUIAdmin.MAX_BROWSER_TABS_PER_USER_INPUT, maxCountTabs);
    }

    /**
     * Тестирование логирования в логе технолога изменения доступности Прочих настроек администрирования для
     * пользователя с правами Admin Lite (Облегченный интерфейс настройки)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$182702583
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить настройку Облегченного интерфейса настройки:<ul>
     *     <li>Настройка системы: установить настройки:<ul>
     *         <li>Администрирование -> Прочие настройки -> Анимация элементов интерфейса</li>
     *     </ul></li>
     *     <li>Доступен группам пользователей = Group</li>
     * </ul></li>
     * </ol>
     * <b>Действия:</b>
     * <ol>
     * <li>Войти под naumen в ИА.</li>
     * <li>Перейти в Лог действий технолога</li>
     * </ol>
     * <b>Проверки:</b>
     * <ol>
     * <li>В логе есть запись с действием "Изменение настроек облегченного интерфейса настройки"</li>
     * </ol>
     */
    @Test
    public void testLoggingAvailabilityOtherAdminSettings()
    {
        //Подготовка
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(Lists.newArrayList(AdminLitePage.OTHER_ADMIN_OPTIONS_ANIMATION.getCode()));
        settings.setGroups(Lists.newArrayList(securityGroup.getCode()));
        DSLAdminLite.save(settings);

        //Действия и проверки
        GUILogon.asSuper();
        GUIAdminLogList.goToAdminLog();
        GUIAdminLogList logList = GUIAdminLogList.advlist();
        LogEntry reportLogUuid = DSLAdminLog.getLastByCategoryCode(Constants.CategoryCode.ADMIN_LITE_SETTINGS);
        logList.content().asserts().attrValue(reportLogUuid, GUIAdminLogList.ADMIN_LOG_CATEGORY_NAME_ATTR,
                "Изменение настроек облегченного интерфейса настройки");
    }

    /**
     * Тестирование входа в систему из под пользователя admin-lite под сотрудником, который ассоциирован
     * с суперпользователем.
     *
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00120
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$212685457
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Задать настройки облегченного интерфейса настройки:
     * Настройка системы: Активные пользователи</li>
     * <li>Создать суперпользователя superUser и связать его с новым сотрудником superOperator</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Из под employee залогиниться под superOperator</li>
     * <li>Проверить, что получили ошибку: Невозможно войти в систему. Учётная запись суперпользователя связана
     * с учётной записью пользователя. Разорвите связь или обратитесь к администратору.</li>
     * </ol>
     */
    @Test
    public void testLoginAsSuperOperatorFromAdminLite()
    {
        //Подготовка
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(List.of(AdminLitePage.ACTIVE_USERS.getCode()));
        settings.setGroups(List.of(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        Bo superOperator = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(superOperator);

        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser, superOperator, false);

        //Выполнение действий и проверки
        String accessKey = DSLApplication.createAccessKey(employee.getLogin());
        DSLSession.tryLoginAsUserByUuid(superOperator.getUuid(), accessKey, "Невозможно войти в систему."
                                                                            + " Учётная запись суперпользователя "
                                                                            + "связана с учётной записью пользователя."
                                                                            + " Разорвите связь или обратитесь к "
                                                                            + "администратору.");
    }

    /**
     * Тестирование загрузки лицензионного файла в облегченном интерфейсе, если в лицензионном файле отключается метка,
     * связанная с включенным ДПС по наступлению времени в атрибуте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00438
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00738
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$225264768
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Подготовка общих настроек для класса}</li>
     * <li>Создать класс userClass и его тип userCase</li>
     * <li>В классе userClass созадть атрибут dateTimeAttr типа "Дата/время"</li>
     * <li>Задать настройки облегченного интерфейса:
     * Настройка системы: Загрузка лицензии</li>
     * <li>Создать метку immutableTag1, убедиться что метка включена</li>
     * <li>Создать ДПС eventAction:
     * <pre>
     *     Объекты: userCase
     *     Метки: immutableTag1
     *     Событие: наступление времени атрибута
     *     Атрибут: dateTimeAttr
     *     Время действия относительно наступления события: в одно время
     *     Действие: скрипт
     *     Скрипт: return true
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <li>Перейти в раздел администрирования</li>
     * <li>Загрузить лицензию immutableTagLicenseForAdminLite.xml</li>
     * <li>Проверить, что ошибки на странице отсутствуют</li>
     * <li>Войти в систему по сотрудником</li>
     * <li>Перейти на карточку метки immutableTag1</li>
     * <li>Проверить, что метка immutableTag1 выключена</li>
     * </ol>
     */
    @Test
    public void testDownloadLicenseInAdminLiteIfDisableTagInOnsetTimeOfAttr()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute dateTimeAttr = DAOAttribute.createDateTime(userClass.getFqn());
        DSLAttribute.add(dateTimeAttr);

        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(Lists.newArrayList(AdminLitePage.UPLOAD_LICENSE.getCode()));
        settings.setGroups(Lists.newArrayList(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        final String TAG_CODE = "immutableTag1";
        Tag immutableTag1 = DAOTag.createTag(TAG_CODE, TAG_CODE, true);
        DSLTag.add(immutableTag1);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return 1;");
        DSLScriptInfo.addScript(script);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.onsetTimeOfAttr, script.getCode(), true,
                false, EventAction.PlannedEventCategory.IN_TIME, dateTimeAttr.getCode(), 0, userCase);
        eventAction.setTags(immutableTag1);
        DSLEventAction.add(eventAction);

        Cleaner.afterTest(() ->
        {
            DSLEventAction.delete(eventAction);
            DSLTag.delete(immutableTag1);
        });

        //Выполнение действий и проверки
        GUILogon.login(employee);
        GUIAdmin.goToCard();
        GUIFileAdmin.uploadFile(GUIAdmin.X_LICENSE_UPLOAD, DSLAdmin.getUpdatedLicense(
                DSLAdmin.IMMUTABLE_TAGS_LICENSE_ADMIN_LITE_PATH));
        GUIError.assertErrorAbsence();
        if (!tester.waitAppear(GUIXpath.Div.INFO_DIALOG))
        {
            throw new ErrorInCodeException("Ошибка при загрузке файла лицензии.");
        }
        GUIForm.applyInfoDialog();
        Cleaner.afterTest(true, () -> DSLAdmin.installLicense(DSLAdmin.MOBILE_AND_ADMINLITE_LICENSE_PATH, false));

        GUILogon.asTester();
        GUITag.goToTagCard(immutableTag1.getCode());
        GUITag.assertTagEnabled(false);
    }

    /**
     * Тестирование выгрузки лицензии в облегченном интерфейсе технолога
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$182702585
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить настройку Облегченного интерфейса настройки:<ul>
     *     <li>Настройка системы: установить настройки:<ul>
     *         <li>Администрирование -> Выгрузка/Загрузка -> Выгрузка лицензии</li>
     *     </ul></li>
     *     <li>Доступен группам пользователей = securityGroup</li>
     * </ul></li>
     * </ol>
     * <b>Выполнение действий и проверок</b>
     * <ol>
     * <li>Войти под сотрудником в employee</li>
     * <li>Перейти в ИА на вкладку Администрирование</li>
     * <li>Проверить, что присутствует кнопка "Выгрузить" в строке лицензии</li>
     * <li>Нажать на кнопку "Выгрузить" в строке лицензии и проверить, что файл загрузился</li>
     * </ol>
     */
    @Test
    public void testAdminLiteExportLicense()
    {
        //Подготовка
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(List.of(AdminLitePage.EXPORT_LICENSE.getCode()));
        settings.setGroups(List.of(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        //Выполнение действий и проверок
        GUILogon.login(employee);
        GUIAdmin.goToCard();
        GUIAdmin.assertPresentButtonOnXpath(
                GUIAdmin.EXPORT_LICENSE_BUTTON,
                "Отсутствует кнопка выгрузки лицензии");
        GUIExportUtils.export(GUIAdmin.EXPORT_LICENSE_BUTTON, "license", ".xml");
    }

    /**
     * Тестирование включения/отключения метки через панель массовых операций для пользователя с правами Admin-Lite<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00738 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$228945642 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Подготовка общих настроек для класса}</li>
     * <li>Создать класс userClass и его тип userCase</li>
     * <li>Задать настройки облегченного интерфейса: Настройка бизнес-процессов -> Метки</li>
     * <li>Создать метку tag, выключенную по умолчанию</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <li>Перейти в раздел администрирования</li>
     * <li>Включить метку tag через панель массовых операций</li>
     * <li>Проверить что ошибки отсутствуют и метка включена</li>
     * <li>Выключить метку tag через панель массовых операций</li>
     * <li>Проверить что ошибки отсутствуют и метка выключена</li>
     * </ol>
     */
    @Test
    public void testOnOffTagFromMassPanelInAdminLite()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setProcessSettings(List.of(AdminLitePage.TAGS.getCode()));
        settings.setGroups(List.of(securityGroup.getCode()));
        settings.setEnabled(true);

        DSLAdminLite.save(settings);

        String tagCode = "tag";
        Tag tag = DAOTag.createTag(tagCode, tagCode, false);
        DSLTag.add(tag);

        //Выполнение действий и проверки
        GUILogon.login(employee);
        GUINavigational.goToTags();
        GUITag.assertPresence(tag);

        GUITag.advlist().mass().selectElements(tag);
        GUITag.advlist().mass().clickOperationLight(MassOperation.ENABLE_TAGS);

        GUITag.assertTagEnabled(true, tagCode);

        GUITag.advlist().mass().selectElements(tag);
        GUITag.advlist().mass().clickOperationLight(MassOperation.DISABLE_TAGS);

        GUITag.assertTagEnabled(false, tagCode);
    }

    /**
     * Тестирование отображения вкладки "Управление логом событий" и настроек в режиме Админ-лайт. Golden Case 28
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00988
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$217246852
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить одно правило хранения лога событий и включить его</li>
     * <li>В настройках Облегченного интерфейса разрешить Админ-лайту просмотр правил хранения логов событий,
     * редактирование параметров задачи очистки.</li>
     * <b>Выполнение действий и проверки.</b>
     * <li>Авторизоваться под сотрудником, обладающего ролью "Админ-лайт"</li>
     * <li>Перейти в интерфейс облегченной настройки</li>
     * <li>Проверки: В разделе "Администрирование" отображается вкладка "Управление логом событий"</li>
     * <li>Перейти на вкладку "Управление логом событий"</li>
     * <li>Проверки: На вкладке отображается контент "Правила хранения лога событий"</li>
     * <li>Проверки: Для правила в списке не отображается кнопка "Выключить"</li>
     * <li>Проверки: Контент "Параметры задачи очистки" не отображается</li>
     * <li>В настройках Облегченного интерфейса разрешить Админ-лайту просмотр параметров задачи очистки,
     * включение/выключение правил хранения лога событий.</li>
     * <li>Перейти в интерфейс облегченной настройки</li>
     * <li>Проверки: В разделе "Администрирование" отображается вкладка "Управление логом событий"</li>
     * <li>Перейти на вкладку "Управление логом событий"</li>
     * <li>Проверки: На вкладке отображается 2 контента "Параметры задачи очистки" и "Правила хранения лога
     * событий"</li>
     * <li>Проверки: У правила отображается кнопка Выключить</li>
     * <li>Нажать кнопку Выключить</li>
     * <li>Правило выключилось</li>
     * <ol>
     */
    @Test
    public void testDisplayEventCleanerTab()
    {
        // Подготовка
        EventStorageRule rule = DAOEventStorageRule.createRule(List.of("logout"), List.of(), 1, Map.of());
        rule.setEnabled(true);
        DSLEventStorageRule.add(rule);

        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(List.of(
                AdminLitePage.VIEW_EVENT_STORAGE_RULE.getCode(),
                AdminLitePage.EDIT_EVENT_CLEANER_JOB_SETTINGS.getCode()));
        settings.setGroups(List.of(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        // Выполнение действий и проверки
        GUILogon.login(employee);
        GUINavigational.goToAdministration();
        GUIEventCleanerListUtil.assertEventStorageRulesBlockExists(true);
        GUIEventCleanerListUtil.assertCleanerJobBlockExists(false);
        GUIEventCleanerListUtil advlist = GUIEventCleanerListUtil.advlist();
        advlist.content().asserts().pictAbsence(rule, GUIEventCleanerListUtil.PICT_TOGGLE);

        settings.setSystemSettings(List.of(
                AdminLitePage.VIEW_EVENT_STORAGE_RULE.getCode(),
                AdminLitePage.EDIT_EVENT_CLEANER_JOB_SETTINGS.getCode(),
                AdminLitePage.VIEW_EVENT_CLEANER_JOB_SETTINGS.getCode(),
                AdminLitePage.TOGGLE_EVENT_STORAGE_RULE.getCode()
        ));
        DSLAdminLite.save(settings);

        tester.refresh();
        GUIEventCleanerListUtil.assertEventStorageRulesBlockExists(true);
        GUIEventCleanerListUtil.assertCleanerJobBlockExists(true);
        advlist.content().asserts().pictPresence(rule, GUIEventCleanerListUtil.PICT_TOGGLE);
        advlist.content().clickPict(rule, GUIEventCleanerListUtil.PICT_TOGGLE);

        GUIEventCleaner.goToCard(rule);
        GUIEventCleaner.assertEnabled(false);
    }

    /**
     * Тестирование доступности блока "Комментарии" в облегченном интерфейсе настройки.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00949
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$130121835
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На карточку компании вывести контент "Комментарии"</li>
     * <li>В блоке Облегченный интерфейс настройки нажать на кнопку "редактировать"</li>
     * <li>Установить параметры:<ul>
     *     <li>Страницы интерфейса: Настройка системы - Комментарии</li>
     *     <li>Параметры доступа: em/GROUP</li>
     * </ul>Сохранить.</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Зайти под сотрудником Empl1 : Emp/123</li>
     * <li>Перейти в ИА.</li>
     * <b>Проверка:</b> Для сотрудника доступен интерфейс администратора - блок "Комментарии"
     * <li>В блоке "Комментарии" включить Inline добавление комментария, форма - компактная</li>
     * <li>Перейти в ИО на карточку компании</li>
     * <b>Проверка:</b> Для комментариев установлена компактная форма
     * </ol>
     */
    @Test
    public void testCommentsBlockInAdminLite()
    {
        //Подготовка
        ContentForm commentList = DAOContentCard.createCommentList(DAORootClass.create());
        DSLContent.add(commentList);

        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(List.of(AdminLitePage.COMMENTS_SETTINGS.getCode()));
        settings.setGroups(List.of(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        //Действия и проверки
        GUILogon.login(employee);
        GUINavigational.goToAdministration();
        GUIAdmin.assertPresentAdminLiteCommentsSettingsBlock(true);

        GUIAdmin.clickEditCommentsSettingsLite();
        tester.setCheckbox(GUIAdmin.ADD_COMMENT_INLINE_FORM_INPUT, true);
        GUISelect.select(GUIAdmin.DEFAULT_FORM_PRESENTATION_INPUT, AddCommentInlineFormPresentation.Compact.name());
        GUIForm.applyModalForm();
        Cleaner.afterTest(() -> DSLAdmin.setAddCommentInlineForm(false, AddCommentInlineFormPresentation.Full));

        GUINavigational.goToOperatorUI();
        GUICommentList.clickAddCommentInlineMinForm(commentList);
        GUICommentList.assertAddCommentInlineCompactFormPresent();
    }

    /**
     * Тестирование изменения параметра "Включено" для ДПС из загруженного файла метаинформации, когда
     * метаинформация была выгружена с параметром WithoutAdminLite и загружается с полным замещением, а в блоке
     * "Облегченный интерфейс настройки" выбран параметр "Действия по событиям"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00546
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$254894243
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить настройки облегченного интерфейса технолога:
     * <ul>
     *      <li>Настройка системы - Настройка системы</li>
     *      <li>Доступен группам пользователей - securityGroup</li>
     *      <li>Включено - true</li>
     * </ul>
     * </li>
     * <li>Создать ДПС eventAction1:
     * <ul>
     *      <li>Действие - Оповещение</li>
     *      <li>Объект - Компания</li>
     *      <li>Тип события - Пользовательское событие</li>
     *      <li>Включено - true</li>
     * </ul>
     * </li>
     * <li>Создать ДПС eventAction2:
     * <ul>
     *      <li>Действие - Скрипт</li>
     *      <li>Объект - Компания</li>
     *      <li>Тип события - Пользовательское событие</li>
     *      <li>Включено - true</li>
     * </ul>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Выгрузить метаинформацию в файл без настроек доступных в админлайт</li>
     * <li>Выключить eventAction1 и eventAction2</li>
     * <li>Загрузить метаинформацию из файла с полным замещением</li>
     * <li>Зайти в ИА под технологом</li>
     * <li>Перейти на карточку eventAction1</li>
     * <li>Проверить состояние действия - выключено</li>
     * <li>Перейти на карточку eventAction2</li>
     * <li>Проверить состояние действия - включено</li>
     * </ol>
     */
    @Test
    public void testEnableEventActionWhenImportMetainfoWithoutAdminLiteParameter()
    {
        //Подготовка
        MetaClass root = DAORootClass.create();
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(
                List.of(AdminLitePage.EVENT_ACTION_CARD.getCode(), AdminLitePage.EVENT_ACTIONS.getCode()));
        settings.setGroups(List.of(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return true");
        DSLScriptInfo.addScript(script);

        EventAction eventAction1 = DAOEventAction.createNotification(root, EventType.userEvent);
        EventAction eventAction2 = DAOEventAction.createEventScript(EventType.userEvent, script, true, root);
        DSLEventAction.add(eventAction1, eventAction2);

        //Выполнение действий и проверок
        File metainfoWithoutAdminLite = DSLMetainfoTransfer.exportMetainfoithoutAdminLite();

        eventAction1.setEnable(Boolean.FALSE.toString());
        eventAction2.setEnable(Boolean.FALSE.toString());
        DSLEventAction.edit(eventAction1, eventAction2);

        DSLMetainfoTransfer.importFullReloadMetainfo(metainfoWithoutAdminLite);

        GUILogon.asSuper();
        GUIEventAction.goToCardWithRefresh(eventAction1);
        GUIEventAction.assertEnable(false);

        GUIEventAction.goToCardWithRefresh(eventAction2);
        GUIEventAction.assertEnable(true);
    }

    /**
     * Тестирование редактирования настроек логотипов UI 2 пользователем admin-lite
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00515
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252231291
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Включить отображение настроек логотипов UI 2</li>
     * <li>Разрешить пользователю admin-lite доступ к настройкам интерфейса</li>
     * <b>Действия и проверки</b>
     * <li>Войти под employee</li>
     * <li>Перейти на страницу «Интерфейс и навигация»</li>
     * <li>Открыть форму редактирования логотипов UI 2</li>
     * <li>Проверить, что для всех логотипов выбраны стандартные значения</li>
     * <li>Проверить, что для логотипов светлых тем доступны только варианты "SMP" и "из файла"</li>
     * <li>Проверить, что для логотипов темных тем доступны варианты "SMP для темного режима", "Логотип для светлого
     * режима" и "из файла"</li>
     * <li>Для "Логотип" выбрать значение файл, загрузить файл user_logo1</li>
     * <li>Для "Мини логотип" выбрать значение файл, загрузить файл user_logo2</li>
     * <li>Для "Логотип для темного режима" выбрать значение "Логотип для светлого режима"</li>
     * <li>Для "Мини логотип для темного режима" выбрать значение "Логотип для светлого режима"</li>
     * <li>Сохранить форму</li>
     * <li>Проверить, что логотипы изменились</li>
     * <li>Проверить, что на месте "Логотип для темного режима" отображается логотип из "Логотип"</li>
     * <li>Проверить, что на месте "Мини логотип для темного режима" отображается логотип из "Мини логотип"</li>
     * <li>Открыть форму редактирования логотипов UI 2</li>
     * <li>Для "Логотип" выбрать значение SMP</li>
     * <li>Для "Мини логотип" SMP</li>
     * <li>Для "Логотип для темного режима" выбрать значение файл, загрузить файл user_logo1</li>
     * <li>Для "Мини логотип для темного режима" в выбрать значение файл, загрузить файл user_logo2</li>
     * <li>Сохранить форму</li>
     * <li>Проверить, что логотипы изменились</li>
     * </ol>
     */
    @Test
    public void testChangeUi2Logo()
    {
        Cleaner.afterTest(DSLInterface::setDefaultInterfaceSettings);
        // Подготовка
        DSLConfiguration.setUi2AdminLogoSettingsEnabled(true);
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(List.of(AdminLitePage.INTERFACE.getCode()));
        settings.setGroups(List.of(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        // Действия и проверки
        GUILogon.login(employee);
        GUINavigational.goToAdministration();
        GUINavigational.goToInterfaceSettings();
        GUILogoUi2.assertUi2AdminLogoSettingsPresent();
        String standardLight = GUILogoUi2.getLightLogo();
        String standardMiniLight = GUILogoUi2.getMiniLightLogo();
        String standardDark = GUILogoUi2.getDarkLogo();
        String standardMiniDark = GUILogoUi2.getMiniDarkLogo();
        GUILogoUi2.openEditUi2LogoForm();
        GUILogoUi2.assertStandardSelect();
        GUILogoUi2.assertAvailableValues();
        GUILogoUi2.selectLogoValue(X_LIGHT_LOGO_INPUT, LIST_FROM_FILE_ID);
        GUILogoUi2.selectLogoValue(X_MINI_LIGHT_LOGO_INPUT, LIST_FROM_FILE_ID);
        GUILogoUi2.selectLogoValue(X_DARK_LOGO_INPUT, LIST_LIGHT_LOGO_ID);
        GUILogoUi2.selectLogoValue(X_MINI_DARK_LOGO_INPUT, LIST_LIGHT_LOGO_ID);

        GUIFileAdmin.uploadFile(X_LIGHT_LOGO_FILE_UPLOAD, USER_LOGO_1);
        GUIFileAdmin.uploadFile(X_MINI_LIGHT_LOGO_FILE_UPLOAD, USER_LOGO_2);
        GUIForm.applyForm();
        String userLogo1 = GUILogoUi2.getLightLogo();
        String userLogo2 = GUILogoUi2.getMiniLightLogo();
        Assert.assertNotEquals(standardLight, userLogo1);
        Assert.assertNotEquals(standardMiniLight, userLogo2);
        Assert.assertNotEquals(standardDark, GUILogoUi2.getDarkLogo());
        Assert.assertNotEquals(standardMiniDark, GUILogoUi2.getMiniDarkLogo());

        Assert.assertEquals("Должен использоваться логотип для светлого режима", GUILogoUi2.getLightLogo(),
                GUILogoUi2.getDarkLogo());
        Assert.assertEquals("Должен использоваться мини-логотип для светлого режима", GUILogoUi2.getMiniLightLogo(),
                GUILogoUi2.getMiniDarkLogo());

        GUILogoUi2.openEditUi2LogoForm();
        GUILogoUi2.selectLogoValue(X_LIGHT_LOGO_INPUT, LIST_STANDARD_ID);
        GUILogoUi2.selectLogoValue(X_MINI_LIGHT_LOGO_INPUT, LIST_STANDARD_ID);
        GUILogoUi2.selectLogoValue(X_DARK_LOGO_INPUT, LIST_FROM_FILE_ID);
        GUILogoUi2.selectLogoValue(X_MINI_DARK_LOGO_INPUT, LIST_FROM_FILE_ID);

        GUIFileAdmin.uploadFile(X_DARK_LOGO_FILE_UPLOAD, USER_LOGO_1);
        GUIFileAdmin.uploadFile(X_MINI_DARK_LOGO_FILE_UPLOAD, USER_LOGO_2);
        GUIForm.applyForm();

        Assert.assertEquals(standardLight, GUILogoUi2.getLightLogo());
        Assert.assertEquals(standardMiniLight, GUILogoUi2.getMiniLightLogo());
        Assert.assertEquals(userLogo1, GUILogoUi2.getDarkLogo());
        Assert.assertEquals(userLogo2, GUILogoUi2.getMiniDarkLogo());
    }

    /**
     * Тестирование импорта/экспорта настроек логотипов UI 2
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00515
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252231291
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Включить отображение настроек логотипов UI 2</li>
     * <li>Разрешить пользователю admin-lite доступ к настройкам интерфейса</li>
     * <b>Действия и проверки</b>
     * <li>Войти под employee</li>
     * <li>Перейти на страницу «Интерфейс и навигация»</li>
     * <li>Открыть форму редактирования логотипов UI 2</li>
     * <li>Для "Логотип" выбрать значение файл, загрузить файл user_logo1</li>
     * <li>Для "Мини логотип" выбрать значение файл, загрузить файл user_logo2</li>
     * <li>Для "Логотип для темного режима" выбрать значение "Логотип для светлого режима"</li>
     * <li>Для "Мини логотип для темного режима" выбрать значение "Логотип для светлого режима"</li>
     * <li>Сохранить форму</li>
     * <li>Выполнить частичный экспорт метаинформации, раздел Интерфейс</li>
     * <li>Открыть форму редактирования логотипов UI 2, выставить у всех полей стандартное значение, сохранить
     * форму</li>
     * <li>Выполнить импорт ранее сохраненной метаинформации</li>
     * <li>обновить страницу</li>
     * <b>Проверки</b>
     * <li>проверить, что значения логотипов соответствуют тем, что были в метаинформации</li>
     * </ol>
     */
    @Test
    public void testImportExportUi2Logo()
    {
        Cleaner.afterTest(DSLInterface::setDefaultInterfaceSettings);
        // Подготовка
        DSLConfiguration.setUi2AdminLogoSettingsEnabled(true);

        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(List.of(AdminLitePage.INTERFACE.getCode()));
        settings.setGroups(List.of(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);
        // Действия
        GUILogon.login(employee);
        GUINavigational.goToInterfaceSettings();
        GUILogoUi2.openEditUi2LogoForm();
        GUILogoUi2.selectLogoValue(X_LIGHT_LOGO_INPUT, LIST_FROM_FILE_ID);
        GUILogoUi2.selectLogoValue(X_MINI_LIGHT_LOGO_INPUT, LIST_FROM_FILE_ID);
        GUILogoUi2.selectLogoValue(X_DARK_LOGO_INPUT, LIST_LIGHT_LOGO_ID);
        GUILogoUi2.selectLogoValue(X_MINI_DARK_LOGO_INPUT, LIST_LIGHT_LOGO_ID);
        GUIFileAdmin.uploadFile(X_LIGHT_LOGO_FILE_UPLOAD, USER_LOGO_1);
        GUIFileAdmin.uploadFile(X_MINI_LIGHT_LOGO_FILE_UPLOAD, USER_LOGO_2);
        GUIForm.applyForm();
        File file = DSLMetainfoTransfer.exportMetainfo(
                DAOMetainfoExport.createExportModelByExportPaths(new String[] { MetainfoElementIdBuilder.INTERFACE }));

        GUILogoUi2.openEditUi2LogoForm();
        GUILogoUi2.selectLogoValue(X_LIGHT_LOGO_INPUT, LIST_STANDARD_ID);
        GUILogoUi2.selectLogoValue(X_MINI_LIGHT_LOGO_INPUT, LIST_STANDARD_ID);
        GUILogoUi2.selectLogoValue(X_DARK_LOGO_INPUT, LIST_STANDARD_ID);
        GUILogoUi2.selectLogoValue(X_MINI_DARK_LOGO_INPUT, LIST_STANDARD_ID);
        GUIForm.applyForm();
        DSLMetainfoTransfer.importMetainfo(file);
        tester.refresh();
        //Ждем отображения лого на странице
        GUILogoUi2.getLightLogo();
        GUILogoUi2.openEditUi2LogoForm();
        // Проверки
        GUILogoUi2.assertLogoValue(X_LIGHT_LOGO_INPUT, LIST_FROM_FILE_VALUE);
        GUILogoUi2.assertLogoValue(X_MINI_LIGHT_LOGO_INPUT, LIST_FROM_FILE_VALUE);
        GUILogoUi2.assertLogoValue(X_DARK_LOGO_INPUT, LIST_LIGHT_LOGO_VALUE);
        GUILogoUi2.assertLogoValue(X_MINI_DARK_LOGO_INPUT, LIST_LIGHT_LOGO_VALUE);
    }

    /**
     * Тестирование работы admin-lite при включении функционала профилей администрирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01014
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$305742203
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Включить доступность профилей администрирования на стенде</li>
     * <li>Разрешить пользователю admin-lite доступ к настройкам действий по событиям</li>
     * <b>Действия и проверки</b>
     * <li>Войти под employee</li>
     * <li>Перейти в ИА</li>
     * <li>Проверить, что в навигационном меню присутствует элемент "Действия по событиям"</li>
     * <li>Проверить, что в навигационном меню отсутствует элемент "Профили администрирования"</li>
     * <li>Выйти из системы</li>
     * <li>Создать профиль администрирования adminProfile, выдать ему следующие права: Интерфейс оператора - Доступ,
     * Интерфейс администратора - Доступ, Профили администрирования - Просмотр</li>
     * <li>Создать суперпользователя superUser, связать его с сотрудником employee, выдать ему профиль
     * администрирования adminProfile</li>
     * <li>Войти под employee</li>
     * <li>Перейти в ИА</li>
     * <li>Проверить, что в навигационном меню отсутствует элемент "Действия по событиям"</li>
     * <li>Проверить, что в навигационном меню присутствует элемент "Профили администрирования"</li>
     * </ol>
     */
    @Test
    public void testAdminLiteCheckRightsWithCustomAdminProfilesEnabled()
    {
        // Подготовка
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(List.of(AdminLitePage.EVENT_ACTIONS.getCode()));
        settings.setGroups(List.of(securityGroup.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        // Действия и проверки
        GUILogon.login(employee);
        GUINavigational.goToAdminUI();
        GUIAdminNavigationTree.expandItem(AdminLiteSettings.SYSTEM_SETTINGS, SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent(EVENT_ACTIONS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent(ADMIN_PROFILES_ITEM_ID);
        GUILogon.logout();

        SuperUser superUser = DAOSuperUser.create();
        superUser.setEmployee(employee);
        AdminProfile adminProfile = DAOAdminProfile.createAdminProfile();
        superUser.setAdminProfiles(adminProfile);
        DSLAdminProfile.add(adminProfile);
        DSLSuperUser.add(superUser);

        AdminProfileAccessMarkerMatrix accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix.addAdministrationInterfacePermission();
        accessMarkerMatrix.addAccessMarkerPermission(ADMINISTRATION_PROFILES, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(OPERATOR_INTERFACE, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUILogon.login(employee);
        GUINavigational.goToAdminUI();
        GUIAdminNavigationTree.expandItem(AdminLiteSettings.SYSTEM_SETTINGS, SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent(EVENT_ACTIONS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent(ADMIN_PROFILES_ITEM_ID);
    }
}