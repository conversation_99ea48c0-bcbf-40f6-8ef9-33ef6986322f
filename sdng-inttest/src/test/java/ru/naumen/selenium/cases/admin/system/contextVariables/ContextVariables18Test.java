package ru.naumen.selenium.cases.admin.system.contextVariables;

import java.util.Collections;
import java.util.List;

import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.catalog.TimeZones;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.metaclass.DSLActionCondition;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLTransition;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.metaclass.ActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.ResponsibleType;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.Strategy;
import ru.naumen.selenium.casesutil.model.metaclass.DAOActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOCommentClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTransition;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.ActionType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.TimerStatus;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.wf.TransitionItem;
import ru.naumen.selenium.context.CVConsts;
import ru.naumen.selenium.context.CVGUIMethods;
import ru.naumen.selenium.context.CVMethods;
import ru.naumen.selenium.context.CVModels;
import ru.naumen.selenium.context.CVModels.ReturnType;
import ru.naumen.selenium.context.asserts.CVAssertUtils;
import ru.naumen.selenium.context.asserts.CVAsserts;
import ru.naumen.selenium.core.AbstractTestCaseForContextVariable;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerState;
import ru.naumen.selenium.security.rights.IRight;
import ru.naumen.selenium.util.StringUtils;

/**
 * Процесс: Смена статуса. Сохранение формы
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/RequirementsLog/ContextValuables
 * <AUTHOR>
 * @since 25.04.2017
 */
public class ContextVariables18Test extends AbstractTestCaseForContextVariable
{
    private static List<Attribute> attributesSc;
    private static Bo employee;
    private static GroupAttr groupSc;
    private static Bo sc, scOrigin;
    private static MetaClass scCase;
    private static BoStatus status;
    private static SecurityMarker changeStateMarker;
    private static String changeStateButtonTitle;
    private static Attribute counter;
    private static ScriptInfo scriptTrue, scriptPause;

    /**
     * Очистка
     */
    @AfterClass
    public static void clean()
    {
        CVMethods.cleanSystemAfterCV();
    }

    /**
     * <ol>
     * <b>Общая подготовка:</b>
     * <li>В классах Сотрудник и Запрос добавить пользовательские атрибуты всех типов</li>
     * <li>Создать на стенде все необходимые типы и объекты для создания запроса, также для сотрудника, под которым
     * будет
     * осуществлен вход, заполнить все системные и пользовательские атрибуты</li>
     * <li>Создать запрос с заполнением всех системных и пользовательских атрибутов, которые могут быть заполнены</li>
     * <li>В классе Запрос создать статус test, настроить доступные переходы «Зарегистрирован»-> «test»-> «Закрыт»</li>
     * <li>На вход в статус test сделать возможным заполнение всех атрибутов (необязательное заполнение)</li>
     * <li>В матрице переходов создать кнопку быстрой смены статуса «button» для перехода «Зарегистрирован»->
     *     «test»</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        CVMethods.initCV();

        attributesSc = CVMethods.getAllScAttributes();
        groupSc = DAOGroupAttr.create(CVModels.getScCase());
        DSLGroupAttr.add(groupSc, attributesSc.toArray(new Attribute[attributesSc.size()]));

        scCase = CVModels.getScCase();
        scOrigin = CVModels.getScBo();
        employee = CVModels.getEmployeeBo();

        status = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.EMPLOYEE, Strategy.CURRENT);
        DSLBoStatus.add(status);

        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        DSLBoStatus.setTransitions(registered, status, closed);
        changeStateButtonTitle = StringUtils.toLowerCaseLatinic(ModelUtils.createCode());
        DSLBoStatus.addNamedTransition(scCase, registered, status, changeStateButtonTitle);
        CVMethods.getAllScAttributes().stream()
                .filter(it -> CVGUIMethods.PRESENCE_ATTIBUTES_ON_CHANGE_STATE_FORM.contains(it.getCode()))
                .forEach((it) -> DSLBoStatus.setAttrInState(it, status, true, true, 1, 0));
        changeStateMarker = new SecurityMarkerState(scCase).addTransition(registered, status).apply();

        scriptTrue = DAOScriptInfo.createNewScriptInfo("return true");
        scriptPause = DAOScriptInfo
                .createNewScriptInfo("if (subject.state!= 'test') {return true} else {return false}");
        DSLScriptInfo.addScript(scriptPause);
        DSLScriptInfo.addScript(scriptTrue);
    }

    /**
     * Создание нового запроса для каждого теста и выдача прав пользователю
     */
    @Before
    public void initBeforeEachTest()
    {
        sc = CVModels.getScBo(ModelUtils.createText(6));
        CVMethods.initScCaseBo(sc);
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте уточнения права на смену статуса при
     * сохранении формы
     * смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00344
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>{@link CVMethods#grantAllPermissionsWithScriptOnRight(IRight)} На право изменение статуса добавить скрипт
     * с вычислением КП</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptChangeStatus()
    {
        CVMethods.grantAllPermissionsWithScriptOnRight(changeStateMarker);
        selectScInAdvListAndPressFastChangeState();
        assertChangeStatus();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте действия по событию с действием «Оповещение»
     * по событию
     * «Изменение объекта» при сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать ДПС  test:</li>
     * <li>Объекты: Запрос (класс)</li>
     * <li>Событие: Смена статуса</li>
     * <li>Действие: Скрипт</li>
     * <li>Синхронное: нет</li>
     * <li>Скрипт: return true + проверяющий КП</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptCreateEventChangeStateAsync()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo script = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.BOOLEAN_TRUE, null, null, status.getCode());

        EventAction eventAction = DAOEventAction.createEventScript(EventType.changeState, script.getCode(), true,
                scCase);
        eventAction.setSubject(CVConsts.EMAIL_SUBJECT);
        eventAction.setActionType(ActionType.ScriptEventAction.name());
        eventAction.setTxType(Boolean.FALSE.toString());
        DSLEventAction.add(eventAction);

        selectScInAdvListAndPressFastChangeState();
        assertEventScript();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте синхронного действия по событию
     * с действием «Скрипт» по событию «Смена статуса» при сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Добавить в класс комментарий атрибуты всех типов</li>
     * <li>Добавить на карточку перехода между статусами «Зарегистрирован»-> «test» элемент Комментарий, Группа
     * атрибутов для добавления комментария: Форма добавления</li>
     * <li>Добавить на форму добавления комментария атрибуты всех типов</li>
     * <li>Создать ДПС test:</li>
     * <li>Объекты: Запрос (класс)</li>
     * <li>Событие: Смена статуса</li>
     * <li>Действие: Скрипт</li>
     * <li>Синхронное: да</li>
     * <li>Скрипт: return true + проверяющий КП</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()} Изменить статус запроса sc на status</li>
     * <li>Заполнить текст комментария, нажать сохранить</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptCreateEventChangeStateSync()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo script = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.VALIDATE_STATE, null, null, status.getCode());

        MetaClass commentClass = DAOCommentClass.create();
        List<Attribute> attributesComments = CVMethods.getUserAttrOffAllType(commentClass, CVModels.getUserCase(),
                false, true);
        CVMethods.addUserAttrsOfAllType(commentClass);
        GroupAttr groupComment = DAOGroupAttr.createAddForm(commentClass.getFqn());
        DSLGroupAttr.edit(groupComment, attributesComments.toArray(new Attribute[attributesComments.size()]),
                new Attribute[0]);
        TransitionItem transitionItem = DAOTransition.createTransitionItemComment(groupComment);
        DSLTransition.addTransitionItems(scCase, DAOBoStatus.createRegistered(scCase.getFqn()), status, transitionItem);

        EventAction eventAction = DAOEventAction.createEventScript(EventType.changeState, script.getCode(), true,
                scCase);
        eventAction.setActionType(ActionType.ScriptEventAction.name());
        eventAction.setTxType(Boolean.TRUE.toString());
        DSLEventAction.add(eventAction);

        GUILogon.login(employee);
        GUIBo.goToCard(sc);
        GUISc.openChangeStateFormAndSetState(status.getCode());
        CVGUIMethods.setAttributesByChangeCase(attributesSc);
        GUIComment.fillCommentAddForm(CVConsts.ATTR_TEXT_VALUE, false);
        GUIForm.applyForm();

        ModelMap subjectValues = subjectValues();
        subjectValues.put(CVConsts.ATTR_OF_LINKED_BO, CVConsts.OU_TITLE);
        ModelMap oldSubjectValues = oldSubjectValues();
        oldSubjectValues.put(CVConsts.ATTR_AGGREGATE_CODE,
                CVAssertUtils.getValueAggregateJSON(null, CVModels.getOuBo(), CVModels.getVisorBo()));
        oldSubjectValues.put(SystemAttrEnum.SOLVED_BY.getCode(),
                CVAssertUtils.getValueAggregateJSON(CVModels.getTeamBo(), null, CVModels.getVisorBo()));
        oldSubjectValues.put(CVConsts.ATTR_OF_LINKED_BO, CVConsts.OU_TITLE);

        ModelMap sourceObject = sourceObjectValues();
        sourceObject.put(CVConsts.ATTR_FILE_CODE, CVConsts.COLLECTION_IMAGE);
        sourceObject.put(CVConsts.ATTR_OF_LINKED_BO, String.format(CVConsts.COLLECTION, CVConsts.OU_TITLE));

        List<String> ignores = Lists.newArrayList(CVConsts.ATTR_FILE_CODE);

        ModelMap replacedValues = ModelMap.newMap(
                CVConsts.ATTR_CASE_LIST_CODE, null,
                CVConsts.ATTR_AGGREGATE_CODE, CVAssertUtils.getValueAggregateVisor(),
                CVConsts.ATTR_AGGR_CODE, CVAssertUtils.getValueAggregateVisor(),
                CVConsts.ATTR_DATE_CODE, CVConsts.ATTR_DATE_VALUE,
                CVConsts.ATTR_TEXT_RTF_CODE, CVConsts.ATTR_TEXT_RTF_VALUE,
                CVConsts.ATTR_BOOL_CODE, CVConsts.ATTR_BOOL_VALUE,
                CVConsts.ATTR_TIME_ZONE_CODE, CVConsts.TIME_ZONE_TITLE,
                CVConsts.ATTR_INTEGER_CODE, CVConsts.ATTR_INTEGER_VALUE,
                CVConsts.ATTR_OF_LINKED_BO, CVConsts.EMPTY_MAP,
                CVConsts.ATTR_STRING_CODE, CVConsts.ATTR_STRING_VALUE,
                CVConsts.ATTR_HYPER_LINK_CODE, CVConsts.ATTR_HYPER_LINK_VALUE_STRING,
                CVConsts.ATTR_TIME_INTERVAL_CODE, CVConsts.ATTR_RESOLUTION_TIME_VALUE,
                CVConsts.ATTR_DATE_TIME_CODE, CVConsts.ATTR_DATE_TIME_VALUE,
                CVConsts.ATTR_FILE_CODE, CVConsts.EMPTY_MAP,
                CVConsts.ATTR_TEXT_CODE, CVConsts.ATTR_TEXT_VALUE,
                CVConsts.ATTR_DOUBLE_CODE, CVConsts.ATTR_DOUBLE_VALUE,
                CVConsts.ATTR_AGGREGATE_CODE_OU, CVConsts.OU_TITLE,
                CVConsts.ATTR_AGGREGATE_CODE_EM, CVAssertUtils.getValueAggregateVisor(),
                CVConsts.ATTR_CATALOG_ITEM_CODE, CVConsts.ATTR_CATALOG_ITEM_VALUE,
                SystemAttrEnum.SOURCE.getCode(), sc.getUuid()
        );

        List<String> ignoresCommentObject = Lists.newArrayList(CVConsts.ATTR_OF_LINKED_BO);

        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertComment()
                    .isEmpty(ModelMap.newMap("",
                            "\"\\u003cdiv\\u003e" + CVConsts.ATTR_TEXT_VALUE + "\\u003c/div\\u003e\""))
                    .assertIsCommentPrivate()
                    .isEmpty(ModelMap.newMap("", Boolean.FALSE.toString()))
                    .assertCommentObject()
                    .assertsAllAttrValues(replacedValues, ignoresCommentObject)
                    .assertCurrentSubject()
                    .isChanged(scOrigin, subjectValues, ignores)
                    .assertSubject()
                    .isChanged(scOrigin, subjectValues, ignores)
                    .assertChangedAttributes()
                    .isCodes(changeAttributesCodes())
                    .assertOldSubject()
                    .isBo(scOrigin, oldSubjectValues)
                    .assertSourceObject()
                    .isChanged(sc, sourceObject)
                    .run();
        });
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте-условии асинхронного ДПС с
     * действием «Скрипт» по событию «Смена статуса» при сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать ДПС  test:</li>
     * <li>Объекты: Запрос (класс)</li>
     * <li>Событие: Изменение объекта</li>
     * <li>Действие: Скрипт</li>
     * <li>Синхронное: нет</li>
     * <li>Скрипт: return true</li>
     * <li>Создать условие ДПС:</li>
     * <li>Скрипт return '' + вычисление КП</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptCreateEventEditAsync()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo script = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.EMPTY_STRING, null, null, status.getCode());

        EventAction eventAction = DAOEventAction.createEventScript(EventType.edit, scriptTrue.getCode(), true, scCase);
        eventAction.setActionType(ActionType.ScriptEventAction.name());
        eventAction.setTxType(Boolean.FALSE.toString());
        DSLEventAction.add(eventAction);
        ActionCondition condition = DAOActionCondition.create(eventAction, script.getCode());
        DSLActionCondition.add(condition);

        selectScInAdvListAndPressFastChangeState();
        assertEventScript();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте-условии синхронного ДПС с
     * действием «Скрипт» по событию «Изменение объекта» при сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать ДПС  test:</li>
     * <li>Объекты: Запрос (класс)</li>
     * <li>Событие: Изменение объекта</li>
     * <li>Действие: Скрипт</li>
     * <li>Синхронное: да</li>
     * <li>Скрипт: return true</li>
     * <li>Создать условие ДПС:</li>
     * <li>Скрипт return '' + вычисление КП</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptCreateEventEditSync()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo script = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.EMPTY_STRING, null, null, status.getCode());

        EventAction eventAction = DAOEventAction.createEventScript(EventType.edit, scriptTrue.getCode(), true, scCase);
        eventAction.setActionType(ActionType.ScriptEventAction.name());
        eventAction.setTxType(Boolean.TRUE.toString());
        DSLEventAction.add(eventAction);
        ActionCondition condition = DAOActionCondition.create(eventAction, script.getCode());
        DSLActionCondition.add(condition);

        selectScInAdvListAndPressFastChangeState();
        assertCreateEventScript();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте действия по событию с действием 
     * «Оповещение» по событию «Смена статуса» при сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать ДПС  test:</li>
     * <li>Объекты: Запрос (класс)</li>
     * <li>Событие: Смена статуса</li>
     * <li>Действие: Оповещение</li>
     * <li>Кому: абсолютная роль Сотрудник</li>
     * <li>Скрипт: return true + проверяющий КП</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptCreateNotificationChangeState()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo script = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.BOOLEAN_TRUE, null, null, status.getCode());

        EventAction eventAction = DAOEventAction.createNotification(EventType.changeState,
                List.of(employee), false, scCase);
        eventAction.setSubject(CVConsts.EMAIL_SUBJECT);
        eventAction.setScript(script.getCode());
        DSLEventAction.add(eventAction);

        selectScInAdvListAndPressFastChangeState();
        assertCreateNotification();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте действия по событию с действием 
     * «Оповещение» по событию «Изменение объекта» при сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать ДПС  test:</li>
     * <li>Объекты: Запрос (класс)</li>
     * <li>Событие: Изменение объекта</li>
     * <li>Действие: Оповещение</li>
     * <li>Скрипт: return true + проверяющий КП</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptCreateNotificationEdit()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo script = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.BOOLEAN_TRUE, null, null, status.getCode());

        EventAction eventAction = DAOEventAction.createNotification(EventType.edit,
                List.of(employee), false, scCase);
        eventAction.setSubject(CVConsts.EMAIL_SUBJECT);
        eventAction.setScript(script.getCode());
        DSLEventAction.add(eventAction);

        selectScInAdvListAndPressFastChangeState();
        assertCreateNotification();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте действия на выход из статуса 
     * при сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00345
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать скрипт с вычислением КП, возвращающий пустую строку, на действие на выход статуса 
     * "Зарегистрирован"</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptPostAction()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo scriptInfo = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.EMPTY_STRING);
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        DSLBoStatus.addPostAction(registered, scriptInfo);

        selectScInAdvListAndPressFastChangeState();
        assertScriptAction();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте условии на выход из статуса 
     * при сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00345
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать скрипт с вычислением КП, возвращающий пустую строку, на условие выхода из статуса
     * "Зарегистрирован"</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptPostCondition()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo scriptInfo = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.EMPTY_STRING);
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        DSLBoStatus.addPostCondition(registered, scriptInfo);

        selectScInAdvListAndPressFastChangeState();
        assertScrpitConditionTimer();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте действия на вход из статуса при 
     * сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00345
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать скрипт с вычислением КП, возвращающий пустую строку, на действие на вход в статус "status"</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptPreAction()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo scriptInfo = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.EMPTY_STRING);
        DSLBoStatus.addPreAction(status, scriptInfo);

        selectScInAdvListAndPressFastChangeState();
        assertScriptAction();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте условии на вход из статуса при 
     * сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00345
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать скрипт с вычислением КП, возвращающий пустую строку, на условие входа в статус "status"</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptPreCondition()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo scriptInfo = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.EMPTY_STRING);
        DSLBoStatus.addPreCondition(status, scriptInfo);

        selectScInAdvListAndPressFastChangeState();
        assertScrpitConditionTimer();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте условии окончания отсчета счетчика 
     * времени при сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00369
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать счетчик test:</li>
     * <li>Объекты: Запрос (класс)</li>
     * <li>Метрика времени: Астрономическое время</li>
     * <li>Часовой пояс: Часовой пояс (подставился атрибут из класса Запрос)</li>
     * <li>Тип условия: по скрипту</li>
     * <li>Условие начала отсчета: return true</li>
     * <li>Условие окончания отсчета: скрипт “if (subject.state == ‘test’) {return true} else {return false}” и
     * проверяющий КП</li>
     * <li>В классе Запрос создать атрибут Счетчик времени: выбрать счетчик test</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * <li>Проверить, что статус Счетчика – приостановлен (utils.get(UUID запроса).test.status.code == ‘p')</li>
     * </ol>
     */
    @Test
    public void testScriptTimerPause()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo script = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.VALIDATE_STATE, null, null, status.getCode());
        TimerDefinition defenition = initTimerDefinition();

        defenition.setPauseCondition(script.getCode());
        defenition.setStartCondition(scriptTrue.getCode());

        initTimer(defenition);
        selectScInAdvListAndPressFastChangeState();
        assertScrpitActionTimer();
        DSLTimerDefinition.assertCountStatus(sc, counter, TimerStatus.PAUSED);
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте условии возобновления отсчета счетчика 
     * времени при сохранении формы смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00369
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать счетчик test:</li>
     * <li>Объекты: Запрос (класс)</li>
     * <li>Метрика времени: Астрономическое время</li>
     * <li>Часовой пояс: Часовой пояс (подставился атрибут из класса Запрос)</li>
     * <li>Тип условия: по скрипту</li>
     * <li>Условие начала отсчета: return true</li>
     * <li>Условие приостановки отсчета: скрипт “if (subject.state!= ‘test’) {return true} else {return false}”</li>
     * <li>Условие приостановки отсчета: скрипт “if (subject.state== ‘test’) {return true} else {return false}” 
     * и проверяющий КП</li>
     * <li>В классе Запрос создать атрибут Счетчик времени: выбрать счетчик test</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * <li>Проверить, что статус Счетчика – активен (utils.get(UUID запроса).test.status.code == ‘a')</li>
     * </ol>
     */
    @Test
    public void testScriptTimerResume()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo script = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.VALIDATE_STATE, null, null, status.getCode());
        TimerDefinition timerDefenition = initTimerDefinition();

        timerDefenition.setResumeCondition(script.getCode());
        timerDefenition.setPauseCondition(scriptPause.getCode());
        timerDefenition.setStartCondition(scriptTrue.getCode());
        initTimer(timerDefenition);
        selectScInAdvListAndPressFastChangeState();
        assertScrpitActionTimer();
        DSLTimerDefinition.assertCountStatus(sc, counter, TimerStatus.ACTIVE);
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте условии начала отсчета счетчика времени при
     * сохранении формы
     *  смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00345
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать счетчик test:</li>
     * <li>Объекты: Запрос (класс)</li>
     * <li>Метрика времени: Астрономическое время</li>
     * <li>Часовой пояс: Часовой пояс (подставился атрибут из класса Запрос)</li>
     * <li>Тип условия: по скрипту</li>
     * <li>Условие начала отсчета: скрипт “if (subject.state == ‘test’) {return true} else {return false}” и
     * проверяющий КП</li>
     * <li>В классе Запрос создать атрибут Счетчик времени: выбрать счетчик test</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testScriptTimerStart()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo script = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.VALIDATE_STATE, null, null, status.getCode());
        TimerDefinition timerDefinition = initTimerDefinition();

        timerDefinition.setStartCondition(script.getCode());

        initTimer(timerDefinition);

        selectScInAdvListAndPressFastChangeState();
        assertScrpitActionTimer();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте условии окончания отсчета счетчика времени
     * при сохранении формы
     *  смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00369
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>Создать счетчик test:</li>
     * <li>Объекты: Запрос (класс)</li>
     * <li>Метрика времени: Астрономическое время</li>
     * <li>Часовой пояс: Часовой пояс (подставился атрибут из класса Запрос)</li>
     * <li>Тип условия: по скрипту</li>
     * <li>Условие окончания отсчета: скрипт “if (subject.state == ‘test’) {return true} else {return false}” и
     * проверяющий КП</li>
     * <li>В классе Запрос создать атрибут Счетчик времени: выбрать счетчик test</li>
     * <li>Действия:</li>
     * <li>{@link #selectScInAdvListAndPressFastChangeState()}  Изменить статус запроса sc на status</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * <li> Проверить ,что статус Счетчика – остановлен (utils.get(UUID запроса).test.status.code == ‘s')</li>
     * </ol>
     */
    @Test
    public void testScriptTimerStop()
    {
        //Подготовка
        grantAllPermissions();
        ScriptInfo script = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.VALIDATE_STATE, null, null, status.getCode());
        TimerDefinition timerDefinition = initTimerDefinition();

        timerDefinition.setStopCondition(script.getCode());
        timerDefinition.setStartCondition(scriptTrue.getCode());

        initTimer(timerDefinition);
        selectScInAdvListAndPressFastChangeState();
        assertScrpitActionTimer();
        DSLTimerDefinition.assertCountStatus(sc, counter, TimerStatus.STOPPED);
    }

    /**
     * Выполннить проверки КП, если КП проверяются в скрипте изменения статуса
     */
    private static void assertChangeStatus()
    {
        ModelMap subjectValues = ModelMap.newMap(CVConsts.ATTR_BACK_BO_LINK_CODE, CVConsts.EMPTY_MAP,
                SystemAttrEnum.UUID.getCode(), sc.getUuid(), CVConsts.ATTR_CASE_LIST_CODE, CVConsts.EMPTY_MAP,
                SystemAttrEnum.TIMEZONE.getCode(), CVConsts.ATTR_TIME_ZONE_VALUE);
        ModelMap oldSubjectValues = ModelMap.newMap(CVConsts.ATTR_BACK_BO_LINK_CODE, CVConsts.EMPTY_MAP,
                SystemAttrEnum.UUID.getCode(), sc.getUuid());

        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV().getCVByEditObjectAction()
                    .assertSubject().isBo(scOrigin, subjectValues)
                    .assertOldSubject().isBo(scOrigin, oldSubjectValues)
                    .assertInitialValues().isChanged(sc, getInitialAttrsValuesWithCodes(),
                            Lists.newArrayList(CVConsts.ATTR_FILE_CODE, SystemAttrEnum.SYSTEM_ICON.getCode(),
                                    SystemAttrEnum.FOLDERS.getCode(), CVConsts.ATTR_DATE_TIME_CODE,
                                    CVConsts.ATTR_AGGR_CODE,
                                    CVAssertUtils.getValueAggregateJSON(CVModels.getTeamBo(), null,
                                            CVModels.getVisorBo()),
                                    CVConsts.ATTR_AGGR_CODE_EM, CVAssertUtils.getValueAggregateVisor(),
                                    CVConsts.ATTR_AGGR_CODE_TE, CVConsts.TEAM_TITLE))
                    .assertSourceObject().isNull()
                    .assertProcess().isEditObject()
                    .run();
        });
    }

    /**
     * Выполнить проверки КП, если КП проверяются в скрипте-условии синхронного ДПС с
     * действием «Скрипт» по событию «Изменение объекта» при сохранении формы смены статуса
     */
    private static void assertCreateEventScript()
    {
        ModelMap subjectValues = subjectValues();
        subjectValues.put(CVConsts.ATTR_OF_LINKED_BO, CVConsts.OU_TITLE);
        ModelMap oldSubjectValues = oldSubjectValues();
        oldSubjectValues.put(CVConsts.ATTR_AGGREGATE_CODE,
                CVAssertUtils.getValueAggregateJSON(null, CVModels.getOuBo(), CVModels.getVisorBo()));
        oldSubjectValues.put(SystemAttrEnum.SOLVED_BY.getCode(),
                CVAssertUtils.getValueAggregateJSON(CVModels.getTeamBo(), null, CVModels.getVisorBo()));
        oldSubjectValues.put(CVConsts.ATTR_OF_LINKED_BO, CVConsts.OU_TITLE);

        ModelMap sourceObject = sourceObjectValues();
        sourceObject.put(CVConsts.ATTR_FILE_CODE, CVConsts.COLLECTION_IMAGE);
        sourceObject.put(CVConsts.ATTR_OF_LINKED_BO, String.format(CVConsts.COLLECTION, CVConsts.OU_TITLE));

        List<String> ignores = Lists.newArrayList(CVConsts.ATTR_FILE_CODE);
        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertComment().isNull()
                    .assertIsCommentPrivate().isNull()
                    .assertCommentObject().isNull()
                    .assertCurrentSubject().isChanged(scOrigin, subjectValues, ignores)
                    .assertSubject().isChanged(scOrigin, subjectValues, ignores)
                    .assertChangedAttributes().isCodes(changeAttributesCodes())
                    .assertOldSubject().isBo(scOrigin, oldSubjectValues)
                    .assertSourceObject().isChanged(sc, sourceObject)
                    .run();
        });
    }

    /**
     * Выполннить проверки КП, если КП проверяются в скрипте создания оповещения
     */
    private static void assertCreateNotification()
    {
        ModelMap subjectValues = subjectValues();
        subjectValues.put(CVConsts.ATTR_OF_LINKED_BO, CVConsts.OU_TITLE);
        ModelMap oldSubjectValues = oldSubjectValues();
        oldSubjectValues.put(CVConsts.ATTR_OF_LINKED_BO, CVConsts.OU_TITLE);
        ModelMap currentSubjectValues = subjectValues();
        currentSubjectValues.put(CVConsts.ATTR_OF_LINKED_BO, String.format(CVConsts.COLLECTION, CVConsts.OU_TITLE));
        ModelMap sourceObject = sourceObjectValues();
        sourceObject.put(CVConsts.ATTR_OF_LINKED_BO, String.format(CVConsts.COLLECTION, CVConsts.OU_TITLE));
        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertComment().isNull()
                    .assertIsCommentPrivate().isNull()
                    .assertCommentObject().isNull()
                    .assertNotification().isNotification()
                    .assertCurrentSubject().isChanged(scOrigin, currentSubjectValues)
                    .assertSubject().isChanged(scOrigin, subjectValues)
                    .assertChangedAttributes().isCodes(changeAttributesCodes())
                    .assertOldSubject().isBo(scOrigin, oldSubjectValues)
                    .assertSourceObject().isChanged(sc, sourceObject)
                    .run();
        });
    }

    /**
     * Выполннить проверки КП, если КП проверяются в скрипте действия по событию
     */
    private static void assertEventScript()
    {
        ModelMap subjectValues = subjectValues();
        subjectValues.put(CVConsts.ATTR_OF_LINKED_BO, CVConsts.OU_TITLE);
        ModelMap oldSubjectValues = oldSubjectValues();
        oldSubjectValues.put(CVConsts.ATTR_OF_LINKED_BO, CVConsts.OU_TITLE);
        ModelMap currentSubjectValues = subjectValues();
        currentSubjectValues.put(CVConsts.ATTR_OF_LINKED_BO, String.format(CVConsts.COLLECTION, CVConsts.OU_TITLE));
        ModelMap sourceObject = sourceObjectValues();
        sourceObject.put(CVConsts.ATTR_OF_LINKED_BO, String.format(CVConsts.COLLECTION, CVConsts.OU_TITLE));
        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertComment().isNull()
                    .assertIsCommentPrivate().isNull()
                    .assertCommentObject().isNull()
                    .assertCurrentSubject().isChanged(scOrigin, currentSubjectValues)
                    .assertSubject().isChanged(scOrigin, subjectValues)
                    .assertChangedAttributes().isCodes(changeAttributesCodes())
                    .assertOldSubject().isBo(scOrigin, oldSubjectValues)
                    .assertSourceObject().isChanged(sc, sourceObject)
                    .run();
        });
    }

    /**
     * Выполннить проверки КП, если КП проверяются в скрипте действий
     */
    private static void assertScriptAction()
    {
        ModelMap subjectValues = subjectValues();
        subjectValues.put(SystemAttrEnum.SOLVED_BY_TEAM.getCode(), null, SystemAttrEnum.SOLVED_BY_EMPLOYEE.getCode(),
                null, SystemAttrEnum.SOLVED_BY.getCode(), null, CVConsts.ATTR_FILE_CODE, CVConsts.COLLECTION_IMAGE,
                CVConsts.ATTR_OF_LINKED_BO, String.format(CVConsts.COLLECTION, CVConsts.OU_TITLE));
        ModelMap oldSubjectValues = ModelMap.newMap(CVConsts.ATTR_BACK_BO_LINK_CODE, CVConsts.EMPTY_MAP,
                SystemAttrEnum.UUID.getCode(), sc.getUuid());

        List<String> ignore = Collections.emptyList();
        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertComment().isNull()
                    .assertIsCommentPrivate().isNull()
                    .assertCommentObject().isNull()
                    .assertSubject().isChanged(scOrigin, subjectValues, ignore)
                    .assertOldSubject().isBo(scOrigin, oldSubjectValues, ignore)
                    .assertInitialValues().isChanged(sc, getInitialAttrsValuesWithNames())
                    .run();
        });
    }

    /**
     * Выполннить проверки КП, если КП проверяются в скрипте действий с таймером
     */
    private static void assertScrpitActionTimer()
    {
        ModelMap subjectValues = subjectValues();
        subjectValues.put(SystemAttrEnum.SOLVED_BY_TEAM.getCode(), CVConsts.TEAM_TITLE,
                SystemAttrEnum.SOLVED_BY_EMPLOYEE.getCode(), CVAssertUtils.getValueAggregateVisor(),
                SystemAttrEnum.SOLVED_BY.getCode(),
                CVAssertUtils.getValueAggregateJSON(CVModels.getTeamBo(), null, CVModels.getVisorBo()),
                CVConsts.ATTR_FILE_CODE, CVConsts.COLLECTION_IMAGE, CVConsts.ATTR_OF_LINKED_BO,
                String.format(CVConsts.COLLECTION, CVConsts.OU_TITLE));
        ModelMap oldSubjectValues = ModelMap.newMap(CVConsts.ATTR_BACK_BO_LINK_CODE, CVConsts.EMPTY_MAP,
                SystemAttrEnum.UUID.getCode(), sc.getUuid());

        List<String> ignore = Lists.newArrayList(counter.getCode());
        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertComment().isNull()
                    .assertIsCommentPrivate().isNull()
                    .assertCommentObject().isNull()
                    .assertSubject().isChanged(scOrigin, subjectValues, ignore)
                    .assertOldSubject().isBo(scOrigin, oldSubjectValues, ignore)
                    .assertInitialValues().isChanged(sc, getInitialAttrsValuesWithNames())
                    .run();
        });
    }

    /**
     *  Выполннить проверки КП, если КП проверяются в скрипте при проверки действий с таймером
     */
    private static void assertScrpitConditionTimer()
    {
        ModelMap subjectValues = ModelMap.newMap(CVConsts.ATTR_BACK_BO_LINK_CODE, CVConsts.EMPTY_MAP,
                SystemAttrEnum.UUID.getCode(), sc.getUuid(), CVConsts.ATTR_CASE_LIST_CODE, CVConsts.EMPTY_MAP,
                SystemAttrEnum.TIMEZONE.getCode(), CVConsts.ATTR_TIME_ZONE_VALUE);
        ModelMap oldSubjectValues = ModelMap.newMap(CVConsts.ATTR_BACK_BO_LINK_CODE, CVConsts.EMPTY_MAP,
                SystemAttrEnum.UUID.getCode(), sc.getUuid());
        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertComment().isNull()
                    .assertIsCommentPrivate().isNull()
                    .assertCommentObject().isNull()
                    .assertSubject().isBo(scOrigin, subjectValues)
                    .assertOldSubject().isBo(scOrigin, oldSubjectValues)
                    .assertInitialValues().isChanged(sc, getInitialAttrsValuesWithNames())
                    .run();
        });
    }

    /**
     * Получить значения changeAttributes
     * @return массив с значениями проверки
     */
    private static String[] changeAttributesCodes()
    {
        String[] codes = { SystemAttrEnum.TIME_ALLOWANCE_TIMER.getCode(), SystemAttrEnum.TOTAL_TIME_TIMER.getCode(),
                SystemAttrEnum.PROCESSING_TIME_TIMER.getCode(), SystemAttrEnum.STATE.getCode(),
                SystemAttrEnum.STATE_START_TIME.getCode(), String.format(CVConsts.ATTR_BACK_TIMER_CODE, "sc") };
        return codes;
    }

    /**
     * Получить значения initialValues с значениями атрибутов в виде uuid
     * @return ModelMap с значениями проверок
     */
    private static ModelMap getInitialAttrsValuesWithCodes()
    {
        ModelMap values = ModelMap.newMap();
        values.put(CVConsts.ATTR_CATALOG_ITEM_SET_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_FILE_CODE, CVConsts.COLLECTION_IMAGE);
        values.put(CVConsts.ATTR_BO_LINKS_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_CASE_LIST_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_AGGR_CODE,
                CVAssertUtils.getValueAggregateJSON(CVModels.getTeamBo(), null, CVModels.getVisorBo()));
        values.put(CVConsts.ATTR_AGGR_CODE_EM, CVAssertUtils.getValueAggregateVisor());
        values.put(CVConsts.ATTR_AGGR_CODE_TE, CVConsts.TEAM_TITLE);
        values.put(CVConsts.ATTR_DATE_CODE, "12.12.2015 00:00");
        values.put(CVConsts.ATTR_DATE_TIME_CODE, "Sat Dec 12 03:00:00");
        values.put("ProccessResponsibleOperation#checkTransfer", Boolean.TRUE.toString());
        values.put(SystemAttrEnum.CODE_OF_CLOSING.getCode(), SharedFixture.closureCode().getUuid());
        values.put(SystemAttrEnum.MASS_PROBLEM_SLAVES.getCode(), CVConsts.EMPTY_MAP);
        values.put(SystemAttrEnum.TIMEZONE.getCode(), CVModels.getTimeZone(TimeZones.MADRID).getUuid());
        values.put(SystemAttrEnum.CLOSED_BY_EMPLOYEE.getCode(), null);
        values.put(SystemAttrEnum.CLOSED_BY.getCode(), CVAssertUtils.getValueAggregateVisor());
        values.put(SystemAttrEnum.FOLDERS.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_OU.getCode(), null);
        values.put(SystemAttrEnum.CLIENT.getCode(), null);
        values.put(SystemAttrEnum.RESOLUTION_TIME.getCode(), null);
        values.put(SystemAttrEnum.MASS_PROBLEM.getCode(), null);
        values.put(SystemAttrEnum.METACLASS.getCode(), null);
        values.put(SystemAttrEnum.MASS_PROBLEM_SLAVES.getCode(), null);
        values.put(SystemAttrEnum.SOLVED_BY_TEAM.getCode(), null);
        values.put(SystemAttrEnum.CLOSED_BY_TEAM.getCode(), null);
        values.put(SystemAttrEnum.STATE.getCode(), null);
        values.put(SystemAttrEnum.AGREEMENT.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_EMPLOYEE.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_LINK_NAME.getCode(), null);
        values.put(SystemAttrEnum.PRIORITY.getCode(), null);
        values.put(SystemAttrEnum.SERVICE_TIME.getCode(), null);
        values.put(SystemAttrEnum.STATE.getCode(), status.getCode());
        values.put(SystemAttrEnum.FOLDERS.getCode(), null);
        values.put(SystemAttrEnum.UUID.getCode(), null);
        values.put(SystemAttrEnum.SERVICE_TIME.getCode(), null);
        values.put(SystemAttrEnum.AUTHOR.getCode(), null);
        values.put(SystemAttrEnum.IMPACT.getCode(), null);
        values.put(SystemAttrEnum.REMOVED.getCode(), null);
        values.put(CVConsts.ATTR_TIME_ZONE_CODE, CVModels.getTimeZone(TimeZones.MADRID).getUuid());
        return values;
    }

    /**
     * Получить значения initialValues с значениями атрибутов в виде titles
     * @return ModelMap с значениями проверок
     */
    private static ModelMap getInitialAttrsValuesWithNames()
    {
        ModelMap values = ModelMap.newMap();

        values.put(CVConsts.ATTR_BACK_BO_LINK_CODE, CVConsts.COLLECTION_EMPTY);
        values.put(CVConsts.ATTR_CATALOG_ITEM_SET_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_AGGREGATE_CODE_OU, null);
        values.put(CVConsts.ATTR_AGGREGATE_CODE_EM, null);
        values.put(CVConsts.ATTR_AGGR_CODE, null);
        values.put(CVConsts.ATTR_AGGR_CODE_EM, null);
        values.put(CVConsts.ATTR_AGGR_CODE_TE, null);
        values.put(SystemAttrEnum.AGREEMENT.getCode(), null);
        values.put(CVConsts.ATTR_BO_LINK_CODE_SPECIAL, null);
        values.put(CVConsts.ATTR_FILE_CODE, CVConsts.ATTR_FILE_CHANGED_VALUES);
        values.put(SystemAttrEnum.AUTHOR.getCode(), null);
        values.put(SystemAttrEnum.CLIENT.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_EMPLOYEE.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_LINK_NAME.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_OU.getCode(), null);
        values.put(SystemAttrEnum.CLOSED_BY.getCode(),
                CVAssertUtils.getValueAggregateJSON(CVModels.getTeamBo(), null, CVModels.getVisorBo()));
        values.put(SystemAttrEnum.CLOSED_BY_EMPLOYEE.getCode(), null);
        values.put(SystemAttrEnum.CLOSED_BY_TEAM.getCode(), null);
        values.put(CVConsts.ATTR_CASE_LIST_CODE, CVConsts.EMPTY_MAP);
        values.put(SystemAttrEnum.CODE_OF_CLOSING.getCode(), SharedFixture.closureCode().getTitle());
        values.put(SystemAttrEnum.REQUEST_DESCRIPTION.getCode(), CVConsts.ATTR_TEXT_VALUE);
        values.put(SystemAttrEnum.MASS_PROBLEM_SLAVES.getCode(), null);
        values.put(SystemAttrEnum.PRIORITY.getCode(), null);
        values.put(SystemAttrEnum.REMOVED.getCode(), Boolean.FALSE.toString());
        values.put(SystemAttrEnum.RESOLUTION_TIME.getCode(), null);
        values.put(SystemAttrEnum.SERVICE_TIME.getCode(), null);
        values.put(SystemAttrEnum.URGENCY.getCode(), SharedFixture.urgency().getTitle());
        values.put(SystemAttrEnum.METACLASS.getCode(), null);
        values.put(SystemAttrEnum.STATE.getCode(), status.getCode());
        values.put(SystemAttrEnum.UUID.getCode(), null);
        values.put(SystemAttrEnum.URGENCY.getCode(), null);

        return values;
    }

    /**
     * Выдать все права пользователю
     */
    private static void grantAllPermissions()
    {
        SecurityProfile secProfile = CVMethods.grantAllPermissionsToEmployee();
        DSLSecurityProfile.setRights(scCase, secProfile, changeStateMarker);
    }

    /**
     * Инциализировать значения таймера
     * @param timerDefinition - модель счетчика времени
     */
    private static void initTimer(TimerDefinition timerDefinition)
    {
        DSLTimerDefinition.add(timerDefinition);
        counter = DAOAttribute.createTimer(scCase.getFqn(), timerDefinition,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(counter);

        sc = CVModels.getScBo(ModelUtils.createText(6));
        CVMethods.initScCaseBo(sc);
    }

    /**
     * Создать модель счетчика времени
     * @return счетчик времени
     */
    private static TimerDefinition initTimerDefinition()
    {
        return DAOTimerDefinition.createAstroTimerByScript(scCase.getFqn(),
                SysAttribute.timeZone(scCase).getCode());
    }

    /**
     * Значения аттрибутов КП oldSubject, которые отличаются от значений проверок по умолчанию
     * @return значения атрибутов
     */
    private static ModelMap oldSubjectValues()
    {
        return ModelMap.newMap(CVConsts.ATTR_BACK_BO_LINK_CODE, CVConsts.EMPTY_MAP, SystemAttrEnum.UUID.getCode(),
                sc.getUuid(), SystemAttrEnum.MASS_PROBLEM_SLAVES.getCode(), null, CVConsts.ATTR_BACK_BO_LINK_CODE, null,
                SystemAttrEnum.SOLVED_BY.getCode(), CVAssertUtils.getValueAggregateJSON(null, null, null),
                CVConsts.ATTR_AGGREGATE_CODE, CVAssertUtils.getValueAggregateJSON(null, null, null));
    }

    /**
     * Зайти под сотрудником и сменить статус у запроса sc
     * <li>Войти в систему под пользователем employee</li>
     * <li>Перейти на карточку запроса sc</li>
     * <li>Нажать кнопку "Изменить статус" и выбрать статус status</li>
     * <br>
     */
    private static void selectScInAdvListAndPressFastChangeState()
    {
        GUILogon.login(employee);
        GUIBo.goToCard(sc);
        GUISc.openChangeStateFormAndSetState(status.getCode());
        CVGUIMethods.setAttributesByChangeCase(attributesSc);
        GUIForm.applyForm();
    }

    /**
     * Значения аттрибутов КП sourceObject, которые отличаются от значений проверок по умолчанию
     * @return значения атрибутов
     */
    private static ModelMap sourceObjectValues()
    {
        return ModelMap.newMap(SystemAttrEnum.STATE.getCode(), status.getCode(), CVConsts.ATTR_BACK_BO_LINK_CODE,
                CVConsts.EMPTY_MAP, CVConsts.ATTR_CATALOG_ITEM_SET_CODE, CVConsts.EMPTY_MAP, CVConsts.ATTR_FILE_CODE,
                CVConsts.ATTR_FILE_CHANGED_VALUES, SystemAttrEnum.RESOLUTION_TIME.getCode(),
                CVConsts.ATTR_RESOLUTION_TIME_VALUE);
    }

    /**
     * Значения аттрибутов КП subject, которые отличаются от значений проверок по умолчанию
     * @return значения атрибутов
     */
    private static ModelMap subjectValues()
    {
        return ModelMap.newMap(CVConsts.ATTR_BACK_BO_LINK_CODE, CVConsts.EMPTY_MAP, SystemAttrEnum.UUID.getCode(),
                sc.getUuid(), SystemAttrEnum.STATE.getCode(), status.getCode(), CVConsts.ATTR_CATALOG_ITEM_SET_CODE,
                CVConsts.EMPTY_MAP, CVConsts.ATTR_FILE_CODE, CVConsts.ATTR_FILE_CHANGED_VALUES,
                SystemAttrEnum.RESOLUTION_TIME.getCode(), CVConsts.ATTR_RESOLUTION_TIME_VALUE);
    }
}
