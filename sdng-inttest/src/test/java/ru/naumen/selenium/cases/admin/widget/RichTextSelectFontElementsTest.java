package ru.naumen.selenium.cases.admin.widget;

import org.junit.BeforeClass;
import org.junit.Test;
import org.openqa.selenium.Keys;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.interfaceelement.GUIFrame;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichTextGwt;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.RichTextType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.BrowserTS.WebBrowserType;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование логики работы элементов выбора шрифта виджета "Текст в формате RTF"
 * (старый GWT-based редактор)
 *
 * <AUTHOR>
 * @since 02.07.2015
 */
public class RichTextSelectFontElementsTest extends AbstractTestCase
{
    /**
     * <ol>
     * <b>Общая подготовка:</b>
     * <li>Включить RTF редактор GWT</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.setUseFroalaRtfEditor(false);
    }

    /**
     * Тестирование значений по умолчанию в элементах выбора шрифта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00590
     * http://sd-jira.naumen.ru/browse/NSDPRD-4060
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Добавить в тип отдела атрибут attr типа Текст RTF, представление безопасное</li>
     * <li>Вывести его на карточку отдела</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>Создать отдел ou</li>
     * <li>Заполнить атрибут attr</li>
     * <br>
     * <b>Проверка:</b>
     * <li>Перейти на карточку отдела ou</li>
     * <li>Перейти на форму редактирование атрибута attr</li>
     * <li>Кликнуть на редактор RTF</li>
     * <li>Проверить, что в тулбаре значения Arial, xsmall, черный</li>
     * </ol>
     */
    @Test
    public void testDefaultSelectValues()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        Attribute attr = DAOAttribute.createTextRTF(ouCase.getFqn());
        attr.setViewPresentation(RichTextType.VIEW);
        DSLAttribute.add(attr);

        GroupAttr ouGroupAttr = DAOGroupAttr.create(ouCase.getFqn());
        DSLGroupAttr.add(ouGroupAttr, attr);

        ContentForm ouParams = DAOContentCard.createPropertyList(ouCase, ouGroupAttr);
        DSLContent.add(ouParams);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(ouCase, MetaclassCardTab.OBJECTCARD));

        //Создаем объект
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIContent.clickEdit(ouParams);

        //Проверка
        GUIRichTextGwt.clickRichTextEditor(attr);
        GUITester.assertValue(GUIRichTextGwt.X_COLOR_LIST, "#000000");
        GUITester.assertValue(GUIRichTextGwt.X_FONT_LIST, "Arial");
        GUITester.assertValue(GUIRichTextGwt.X_FONT_SIZE_LIST, "xsmall");
    }

    /**
     * Тестирование значений по умолчанию в элементах выбора шрифта после форматирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00590
     * http://sd-jira.naumen.ru/browse/NSDPRD-4060
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Добавить в тип отдела атрибут attr типа Текст RTF, представление безопасное</li>
     * <li>Вывести его на карточку отдела</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>Создать отдел ou</li>
     * <li>Заполнить атрибут attr</li>
     * <br>
     * <b>Проверка:</b>
     * <li>Перейти на карточку отдела ou</li>
     * <li>Перейти на форму редактирование атрибута attr</li>
     * <li>Кликнуть на редактор RTF</li>
     * <li>Выбрать все содержимое</li>
     * <li>Кликнуть на кнопку очистки формата</li>
     * <li>Кликнуть на редактор RTF</li>
     * <li>Проверить, что в тулбаре значения Arial, xsmall, черный</li>
     * </ol>
     */
    @IgnoreConfig(cause = "NSDAT-10178", ignoreBrowser = { WebBrowserType.FIREFOX })
    @Test
    public void testDefaultSelectValuesAfterRemoveFormatting()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        Attribute attr = DAOAttribute.createTextRTF(ouCase.getFqn());
        attr.setViewPresentation(RichTextType.VIEW);
        DSLAttribute.add(attr);

        GroupAttr ouGroupAttr = DAOGroupAttr.create(ouCase.getFqn());
        DSLGroupAttr.add(ouGroupAttr, attr);

        ContentForm ouParams = DAOContentCard.createPropertyList(ouCase, ouGroupAttr);
        DSLContent.add(ouParams);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(ouCase, MetaclassCardTab.OBJECTCARD));

        //Создаем объект
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        attr.setValue("<font color=\"#ff0000\">a</font>");
        DSLBo.editAttributeValue(ou, attr);

        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIContent.clickEdit(ouParams);

        //Проверка
        GUIRichTextGwt.clickRichTextEditor(attr);
        GUIFrame.clickText(GUIRichTextGwt.rtfIframeXpath(attr.getCode()), "a");
        tester.actives().pressingHightlightAll();
        GUIRichTextGwt.clickRemoveFormatting(attr.getCode());

        GUIRichTextGwt.clickRichTextEditor(attr);
        GUIFrame.clickText(GUIRichTextGwt.rtfIframeXpath(attr.getCode()), "a");
        tester.actives().sendKeys(Keys.ARROW_DOWN);

        GUITester.assertValue(GUIRichTextGwt.X_COLOR_LIST, "#000000");
        GUITester.assertValue(GUIRichTextGwt.X_FONT_LIST, "Arial");
        GUITester.assertValue(GUIRichTextGwt.X_FONT_SIZE_LIST, "xsmall");
    }

    /**
     * Тестирование корректного изменения значения в элементе выбора цвета шрифта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00590
     * http://sd-jira.naumen.ru/browse/NSDPRD-4060
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Добавить в тип отдела атрибут attr типа Текст RTF, представление безопасное</li>
     * <li>Вывести его на карточку отдела</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>Создать отдел ou</li>
     * <li>Заполнить атрибут attr 2 строчками, первая красного цвета вторая синего
     * <code>"&lt;font color="#ff0000"&gt;aaaaaa&lt;/font&gt;&lt;div&gt;&lt;font color="#0000ff"&gt;bbbbbb&lt;
     * /font&gt;&lt;/div&gt;"</code></li>
     * <br>
     * <b>Проверка:</b>
     * <li>Перейти на карточку отдела ou</li>
     * <li>Перейти на форму редактирование атрибута attr</li>
     * <li>Кликнуть на 1 строку и проверить что в тулбаре красный</li>
     * <li>Кликнуть на 2 строку и проверить что в тулбаре синий</li>
     * </ol>
     */
    @Test
    public void testSelectColorValue()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        Attribute attr = DAOAttribute.createTextRTF(ouCase.getFqn());
        attr.setViewPresentation(RichTextType.VIEW);
        DSLAttribute.add(attr);

        GroupAttr ouGroupAttr = DAOGroupAttr.create(ouCase.getFqn());
        DSLGroupAttr.add(ouGroupAttr, attr);

        ContentForm ouParams = DAOContentCard.createPropertyList(ouCase, ouGroupAttr);
        DSLContent.add(ouParams);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(ouCase, MetaclassCardTab.OBJECTCARD));

        //Создаем объект
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        attr.setValue("<font color=\"#ff0000\">aaaaaa</font><div><font color=\"#0000ff\">bbbbbb</font></div>");
        DSLBo.editAttributeValue(ou, attr);

        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIContent.clickEdit(ouParams);

        GUIRichTextGwt.clickRichTextEditor(attr);
        GUIFrame.clickText(GUIRichTextGwt.rtfIframeXpath(attr.getCode()), "aaaaaa");
        GUITester.assertValue(GUIRichTextGwt.X_COLOR_LIST, "#ff0000");
        tester.actives().sendKeys(Keys.ARROW_DOWN);
        GUITester.assertValue(GUIRichTextGwt.X_COLOR_LIST, "#0000ff");
    }

    /**
     * Тестирование корректного изменения значения в элементе выбора размера шрифта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00590
     * http://sd-jira.naumen.ru/browse/NSDPRD-4060
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Добавить в тип отдела атрибут attr типа Текст RTF, представление безопасное</li>
     * <li>Вывести его на карточку отдела</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>Создать отдел ou</li>
     * <li>Заполнить атрибут attr 2 словами, через пробел, размер шрифта первого слова xsmall, второго слова medium
     * <code>&lt;span style="font-size: 13px;"&gt;a &lt;/span&gt;&lt;font size="4"&gt;b&lt;/font&gt;</code></li>
     * <br>
     * <b>Проверка:</b>
     * <li>Перейти на карточку отдела ou</li>
     * <li>Перейти на форму редактирование атрибута attr</li>
     * <li>Кликнуть на начало 2 слова и проверить что в тулбаре xsmall</li>
     * <li>Кликнуть на конец 2 слова и проверить что в тулбаре medium</li>
     * </ol>
     */
    @Test
    public void testSelectSizeValue()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        Attribute attr = DAOAttribute.createTextRTF(ouCase.getFqn());
        attr.setViewPresentation(RichTextType.VIEW);
        DSLAttribute.add(attr);

        GroupAttr ouGroupAttr = DAOGroupAttr.create(ouCase.getFqn());
        DSLGroupAttr.add(ouGroupAttr, attr);

        ContentForm ouParams = DAOContentCard.createPropertyList(ouCase, ouGroupAttr);
        DSLContent.add(ouParams);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(ouCase, MetaclassCardTab.OBJECTCARD));

        //Создаем объект
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        attr.setValue("<span style=\"font-size: 13px;\">a </span><font size=\"4\">b</font>");
        DSLBo.editAttributeValue(ou, attr);

        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIContent.clickEdit(ouParams);

        GUIRichTextGwt.clickRichTextEditor(attr);
        GUIFrame.clickText(GUIRichTextGwt.rtfIframeXpath(attr.getCode()), "a ");
        //переходим в начало строки ввода
        tester.actives().sendKeys(Keys.ARROW_LEFT);
        tester.actives().sendKeys(Keys.ARROW_RIGHT);
        tester.actives().sendKeys(Keys.ARROW_RIGHT);
        GUITester.assertValue(GUIRichTextGwt.X_FONT_SIZE_LIST, "xsmall");
        tester.actives().sendKeys(Keys.ARROW_RIGHT);
        GUITester.assertValue(GUIRichTextGwt.X_FONT_SIZE_LIST, "medium");
    }
}
