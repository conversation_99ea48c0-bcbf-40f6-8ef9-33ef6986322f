package ru.naumen.selenium.cases.operator.files;

import java.util.List;
import java.util.Set;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIFileList;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.file.GUIFileDnD;
import ru.naumen.selenium.casesutil.file.GUIFileDnD.FileToDrag;
import ru.naumen.selenium.casesutil.file.GUIFileOperator;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOFileClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на загрузку файлов через drag and drop
 *
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00598
 *
 * <AUTHOR>
 * @since 09 нояб. 2015 г.
 */
public class FilesDnDUploadTest extends AbstractTestCase
{
    /**
     * Тестирование загрузки файла в атрибут типа "Файл" через "drag and drop" на форме добавления отдела при обрыве
     * сессии и включенным csrf
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$71475406
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70116825
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>В типе отдела ouCase добавить атрибут attr типа "Файл"</li>
     * <li>Вывести атрибут attr на форму добавления отдела ouCase</li>
     * <li>Включить csrf посредством скрипта</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на форму добавления отдела типа ouCase</li>
     * <li>Заполняем обязательные поля на форме</li>
     * <li>Разрываем текущую сессию - для этого кликаем по ссылке "Выход" с зажатой кнопкой Ctrl так чтобы создалась
     * новая вкладка браузера</li>
     * <li>Переходим на новую закладку и повторно заходим под этим же сотрудником</li> 
     * <li>Возвращаемся на первую закладку с открытой формой</li> 
     * <li>Перетаскиваем файл file на форму и при этом проверяем, что в поле значения атрибута отображается зона для
     * перетаскивания файлов</li>
     * <li>Проверяем что появилось диалоговое окно с предложением остаться на странице</li> 
     * </ol>
     */
    @Test
    public void testAddFileInAttributeIfSessionAreExpiredAndCsrfDisableFalse()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute attr = DAOAttribute.createFile(ouCase.getFqn());
        DSLAttribute.add(attr);

        GroupAttr group = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(group, attr);

        ContentForm editablePropList = DAOContentAddForm.createEditablePropertyList(ouCase, group);
        DSLContent.add(editablePropList);
        new ScriptRunner("beanFactory.getBean('csrfConfig').setIsCsrfDisabled(false)").runScript();

        //Выполнение действий и проверки
        GUILogon.asTester();
        String firstWindowHandle = tester.getWindowHandle();
        Bo ou = DAOOu.create(ouCase);
        GUIBo.goToAddForm(ou);

        GUIBo.fillOuMainFields(ou);

        //DSLSession.disconectTesterSession(tester); // не используем - ломает drag and drop
        //Открываем ссылку "Выход" в новой закладке браузера (фокус еще остается на первой закладке)
        tester.openCurrentUrlInNewTab();
        List<String> windowHandlers = tester.getWindowHandlers();
        Assert.assertTrue("Новая вкладка браузера не создалась", windowHandlers.size() > 1);
        //Переходим на новую закладку браузера
        tester.getWebDriver().switchTo().window(windowHandlers.get(windowHandlers.size() - 1));
        GUILogon.asTester();

        //Возвращаемся на первую закладку
        tester.getWebDriver().switchTo().window(firstWindowHandle);
        FileToDrag file = FileToDrag.create();
        GUIFileDnD.grabFiles(file);
        GUIFileDnD.dragOnPage();
        GUIFileDnD.assertDropzone(attr);
        GUIFileDnD.dropOn(attr);

        GUIForm.assertQuestionAppear("Сообщение с текстом 'Операция не может быть выполнена' не появилось.");
        GUIForm.confirmByYes();
    }

    /**
     * Тестирование загрузки файла в атрибут типа "Файл" через "drag and drop" на форме добавления отдела при обрыве
     * сессии и выключенным csrf
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$71475406
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70116825
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>В типе отдела ouCase добавить атрибут attr типа "Файл"</li>
     * <li>Вывести атрибут attr на форму добавления отдела ouCase</li>
     * <li>Выключить csrf посредством скрипта</li> 
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на форму добавления отдела типа ouCase</li>
     * <li>Заполняем обязательные поля на форме</li>
     * <li>Разрываем текущую сессию - для этого кликаем по ссылке "Выход" с зажатой кнопкой Ctrl так чтобы создалась
     * новая вкладка браузера</li>
     * <li>Возвращаемся на первую закладку с открытой формой</li> 
     * <li>Перетаскиваем файл file на форму и при этом проверяем, что в поле значения атрибута отображается зона для
     * перетаскивания файлов</li>
     * <li>Проверяем что появилось диалоговое окно с предложением остаться на странице</li> 
     * </ol>
     */
    @Test
    public void testAddFileInAttributeIfSessionAreExpiredAndCsrfDisableTrue()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute attr = DAOAttribute.createFile(ouCase.getFqn());
        DSLAttribute.add(attr);

        GroupAttr group = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(group, attr);

        ContentForm editablePropList = DAOContentAddForm.createEditablePropertyList(ouCase, group);
        DSLContent.add(editablePropList);
        new ScriptRunner("beanFactory.getBean('csrfConfig').setIsCsrfDisabled(true)").runScript();

        //Выполнение действий и проверки
        GUILogon.asTester();
        String firstWindowHandle = tester.getWindowHandle();
        Bo ou = DAOOu.create(ouCase);
        GUIBo.goToAddForm(ou);

        GUIBo.fillOuMainFields(ou);

        //DSLSession.disconectTesterSession(tester); // не используем - ломает drag and drop
        //Открываем ссылку "Выход" в новой закладке браузера (фокус еще остается на первой закладке)
        tester.openCurrentUrlInNewTab();
        Assert.assertTrue("Новая вкладка браузера не создалась", tester.getWindowHandlers().size() > 1);
        GUILogon.logout();

        //Возвращаемся на первую закладку (для повышения стабильности в других версиях браузера)
        tester.getWebDriver().switchTo().window(firstWindowHandle);
        FileToDrag file = FileToDrag.create();
        GUIFileDnD.grabFiles(file);
        GUIFileDnD.dragOnPage();
        GUIFileDnD.assertDropzone(attr);
        GUIFileDnD.dropOn(attr);

        GUIForm.assertQuestionAppear("Сообщение с текстом 'Операция не может быть выполнена' не появилось.");
        GUIForm.confirmByYes();
        new ScriptRunner("beanFactory.getBean('csrfConfig').setIsCsrfDisabled(false)").runScript();
    }

    /**
     * Тестирование множественной загрузки файлов в атрибут типа "Файл" с через "drag and drop"
     * на форме добавления объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00598
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>В типе отдела ouCase добавить атрибут attr типа "Файл"</li>
     * <li>Вывести атрибут attr на форму добавления отдела ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на форму добавления отдела типа ouCase</li>
     * <li>Заполняем обязательные поля на форме</li>
     * <li>Перетаскиваем файлы file1 и file2 на форму</li>
     * <li>Проверяем что у атрибута attr отобразилать область для перетаскивания</li>
     * <li>Сбрасываем файлы на область для перетаскивания атрибута attr</li>
     * <li>Проверяем что область для перетаскивания атрибута attr исчезла</li>
     * <li>Проверяем что в поле значения атрибута attr отобраются названия файлов file1 и file2</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Проверяем что отдел создался с прикрепленными файлами</li>
     * </ol>
     */
    @Test
    public void testAddFilesInAttribureOnAddForm()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute attr = DAOAttribute.createFile(ouCase.getFqn());
        DSLAttribute.add(attr);

        GroupAttr group = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(group, attr);

        ContentForm editablePropList = DAOContentAddForm.createEditablePropertyList(ouCase, group);
        DSLContent.add(editablePropList);

        //Выполнение действий и проверки
        GUILogon.asTester();

        Bo ou = DAOOu.create(ouCase);
        GUIBo.goToAddForm(ou);

        GUIBo.fillOuMainFields(ou);

        FileToDrag file1 = FileToDrag.create();
        FileToDrag file2 = FileToDrag.create();

        GUIFileDnD.grabFiles(file1, file2);
        GUIFileDnD.dragOnPage();

        GUIFileDnD.assertDropzone(attr);

        GUIFileDnD.dropOn(attr);
        GUIFileDnD.assertNoDropzone(attr);

        GUIFileOperator.assertFilesInAttr(attr, file1.getDnDFilename(), file2.getDnDFilename());

        GUIForm.applyForm();
        GUIBo.setUuidByUrl(ou);

        DSLFile.assertFilesCount(ou, 2);
    }

    /**
     * Тестирование множественной загрузки файлов в контент типа "Список файлов" с через "drag and drop"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела ouCase вывести контент fileList типа "Список файлов"</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Перетаскиваем файлы file1 и file2 на страницу</li>
     * <li>Проверяем что у контента fileList отобразилать область для перетаскивания</li>
     * <li>Сбрасываем файлы на область для перетаскивания контента fileList</li>
     * <li>Проверяем что область для перетаскивания контента fileList исчезла</li>
     * <li>Проверяем что к отделу ou прикрепились и отображаются в списке файлов fileList оба файла file1 и file2</li>
     * </ol>
     */
    @Test
    public void testAttachFileWithDropOnFileList()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn());
        DSLContent.add(fileList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        FileToDrag file1 = FileToDrag.create();
        FileToDrag file2 = FileToDrag.create();
        GUIFileDnD.grabFiles(file1, file2);

        GUIFileDnD.dragOnPage();
        GUIFileDnD.assertDropzone(fileList);

        GUIFileDnD.dropOn(fileList);
        GUIFileDnD.assertNoDropzone(fileList);

        GUIFileList.assertFilePresence(fileList, file1.getDnDFilename());
        GUIFileList.assertFilePresence(fileList, file2.getDnDFilename());

        DSLFile.assertFilesCount(ou, 2);
    }

    /**
     * Тестирование загрузки файлa недопустимого размера в контент типа "Список файлов" с через "drag and drop"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела ouCase вывести контент fileList типа "Список файлов"</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Установить максимально допустимый размер загружаемых файлов (1 байт) выполнив скрипт:
     *      <pre>
     *      ------------------------------------------------------
     *      beanFactory.getBean('configurationProperties').setUploadFileMaxSize(value);
     *      ------------------------------------------------------
     *      где value - целое число, кол-во байт
     *      </pre>
     * </li>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Перетаскиваем файл file1 на страницу</li>
     * <li>Проверяем что у контента fileList отобразилать область для перетаскивания</li>
     * <li>Сбрасываем файл на область для перетаскивания контента fileList</li>
     * <li>Проверяем что область для перетаскивания контента fileList исчезла</li>
     * <li>Проверяем что появилось диалоговое окно с ошибкой
     *          Файл 'file1' не был загружен. Размер файла больше допустимого (0,00 МБ).
     *  </li>
     * <li>Проверяем что к отделу ou не прикрепился и не отображается в списке файлов fileList файл file1</li>
     * <li>Сбросить значение максимально допустимого размера загружаемых файлов (-1 байт) выполнив скрипт:
     *      <pre>
     *      ------------------------------------------------------
     *      beanFactory.getBean('configurationProperties').setUploadFileMaxSize(value);
     *      ------------------------------------------------------
     *      где value - целое число, кол-во байт
     *      </pre>
     * </li>
     * <li>Перетаскиваем файл file1 на страницу</li>
     * <li>Проверяем что у контента fileList отобразилать область для перетаскивания</li>
     * <li>Сбрасываем файл на область для перетаскивания контента fileList</li>
     * <li>Проверяем что область для перетаскивания контента fileList исчезла</li>
     * <li>Проверяем что к отделу ou прикрепился и отображается в списке файлов fileList файл file1</li>
     * </ol>
     */
    @Test
    public void testAttachFileWithExceededSizeByDropOnFileList()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn());
        DSLContent.add(fileList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий и проверки
        String script = "beanFactory.getBean('configurationProperties').setUploadFileMaxSize(%d);";
        new ScriptRunner(String.format(script, 1)).runScript();
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        FileToDrag file1 = FileToDrag.create();
        GUIFileDnD.grabFiles(file1);

        GUIFileDnD.dragOnPage();
        GUIFileDnD.assertDropzone(fileList);

        GUIFileDnD.dropOn(fileList);
        GUIFileDnD.assertNoDropzone(fileList);

        GUIError.assertDialogError(String.format(
                "%s Файл не был загружен. Размер файла превышает ограничение, заданное администратором системы (0,00 "
                + "МБ).",
                file1.getDnDFilename()));
        GUIFileList.assertFileAbsence(fileList, file1.getDnDFilename());
        DSLFile.assertFilesCount(ou, 0);

        new ScriptRunner(String.format(script, -1)).runScript();

        GUIFileDnD.grabFiles(file1);

        GUIFileDnD.dragOnPage();
        GUIFileDnD.assertDropzone(fileList);

        GUIFileDnD.dropOn(fileList);
        GUIFileDnD.assertNoDropzone(fileList);

        GUIFileList.assertFilePresence(fileList, file1.getDnDFilename());

        DSLFile.assertFilesCount(ou, 1);
    }

    /**
     * Тестирование множественной загрузки файлов с именами файлов, содержащими спецсимволы и/или точку с запятой в
     * контент типа "Список файлов" через "drag and drop"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$79477808
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела ouCase вывести контент fileList типа "Список файлов"</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Перетаскиваем файлы со спецсимволами в именах (file1 и file2) на страницу</li>
     * <li>Проверяем что у контента fileList отобразилать область для перетаскивания</li>
     * <li>Сбрасываем файлы на область для перетаскивания контента fileList</li>
     * <li>Проверяем что область для перетаскивания контента fileList исчезла</li>
     * <li>Проверяем что к отделу ou прикрепились и отображаются в списке файлов fileList оба файла file1 и file2</li>
     * </ol>
     */
    @Test
    public void testAttachFilesNamedWithSpecialSymbolsWithDropOnFileList()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn());
        DSLContent.add(fileList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        FileToDrag file1 = FileToDrag.create(";;Name;With;Semicolons;;");
        FileToDrag file2 = FileToDrag.create("Name ;With; Semicolons");
        GUIFileDnD.grabFiles(file1, file2);

        GUIFileDnD.dragOnPage();
        GUIFileDnD.assertDropzone(fileList);

        GUIFileDnD.dropOn(fileList);
        GUIFileDnD.assertNoDropzone(fileList);

        GUIFileList.assertFilePresence(fileList, file1.getDnDFilename());
        GUIFileList.assertFilePresence(fileList, file2.getDnDFilename());

        DSLFile.assertFilesCount(ou, 2);
    }

    /**
     * Тестирование того, что при перетаскивании файлов на модальную форму, открытую поверх контента типа "Список
     * файлов",
     * область для перетаскивания контента не активируется.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела ouCase вывести контент fileList типа "Список файлов"</li>
     * <li>В типе отдела ouCase создать атрибут attr типа "Файл"</li>
     * <li>В типе отдела ouCase создать группу атрибутов attrGroup с единственным атрибутом attr</li>
     * <li>На карточку отдела ouCase вывести контент propertyList типа "Параметры объекта" (группа атрибутов: group)
     * </li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Кливаем по ссылке "редактировать" в контенте propertyList</li>
     * <li>Перетаскиваем на страницу файл file</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем что область для перетаскивания атрибута attr в открывшейся модальной форме подсветилась</li>
     * <li>Проверяем что область для перетаскивания списка файлов fileList не подсветилась</li>
     * </ol>
     */
    @Test
    public void testDisableDropzonesOnCardIfModalFormIsOpened()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn());
        DSLContent.add(fileList);

        Attribute attr = DAOAttribute.createFile(ouCase.getFqn());
        DSLAttribute.add(attr);

        GroupAttr group = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(group, attr);

        ContentForm propertyList = DAOContentCard.createPropertyList(ouCase, group);
        DSLContent.add(propertyList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIPropertyList.clickEditLink(propertyList);

        FileToDrag file = FileToDrag.create();
        GUIFileDnD.grabFiles(file);
        GUIFileDnD.dragOnPage();

        //Проверки
        GUIFileDnD.assertDropzone(attr);
        GUIFileDnD.assertNoDropzone(fileList);
    }

    /**
     * Тестирование множественной загрузки файлов в атрибут типа "Файл" с через "drag and drop"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00598
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>В типе отдела ouCase создать атрибут attr типа "Файл"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attr</li>
     * <li>На карточку отдела ouCase вывести контент propertyList типа "Параметры объекта" (группа атрибутов: group)
     * </li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Кликаем по ссылке "редактировать" в контенте propertyList</li>
     * <li>Перетаскиваем файлы file1 и file2 на страницу</li>
     * <li>Проверяем что у атрибута attr отобразилать область для перетаскивания</li>
     * <li>Сбрасываем файлы на область для перетаскивания атрибута attr</li>
     * <li>Проверяем что область для перетаскивания атрибута attr исчезла</li>
     * <li>Проверяем что в атрибуте attr отображаются оба сброшенных файла</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Проверяем что к объекту прикрепились два файла</li>
     * </ol>
     */
    @Test
    public void testDnDUploadFileInAttr()
    {
        //Подготока
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute attr = DAOAttribute.createFile(ouCase.getFqn());
        DSLAttribute.add(attr);

        GroupAttr group = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(group, attr);

        ContentForm propertyList = DAOContentCard.createPropertyList(ouCase, group);
        DSLContent.add(propertyList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIPropertyList.clickEditLink(propertyList);

        FileToDrag file1 = FileToDrag.create();
        FileToDrag file2 = FileToDrag.create();
        GUIFileDnD.grabFiles(file1, file2);

        GUIFileDnD.dragOnPage();
        GUIFileDnD.assertDropzone(attr);

        GUIFileDnD.dropOnDialog(attr);
        GUIFileDnD.assertNoDropzone(attr);
        GUIFileOperator.assertFilesInAttr(attr, file1.getDnDFilename(), file2.getDnDFilename());

        GUIForm.applyModalForm();

        DSLFile.assertFilesCount(ou, 2);
    }

    /**
     * Тестирование того, что при перетаскивании файлов на свернутый контент типа "Список файлов",
     * область для перетаскивания контента активируется, файлы добавляются, контент разворачивается.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На форму добавления отдела ouCase вывести контент fileList типа "Список файлов"</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Сворачиваем список файлов fileList</li>
     * <li>Перетаскиваем файл file на страницу</li>
     * <li>Проверяем что у контента fileList отобразилать область для перетаскивания</li>
     * <li>Сбрасываем файл на область для перетаскивания контента fileList</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем что контент fileList раскрылся и в нем отображается файл file</li>
     * </ol>
     */
    @Test
    public void testDropFilesOnClosedContent()
    {
        //Подготока
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn());
        DSLContent.add(fileList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIContent.expand(fileList);

        FileToDrag file = FileToDrag.create();
        GUIFileDnD.grabFiles(file);

        GUIFileDnD.dragOnPage();
        GUIFileDnD.assertDropzone(fileList);

        GUIFileDnD.dropOn(fileList);

        //Проверка
        GUIFileList.assertFilePresence(fileList, file.getDnDFilename());
    }

    /**
     * Тестирование зоны сброса файлов атрибута типа файл, выведеного на форму смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00598
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase</li>
     * <li>В типе запроса scCase добавить атрибут attr типа "Файл"</li>
     * <li>Сделать атрибут attr заполняемым при переходе в статус "Разрешен"</li>
     * <li>Создать запрос sc типа scCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку запроса sc</li>
     * <li>Открываем форму смены статуса, выбираем статус "Разрешен"</li>
     * <li>Перетаскиваем файл file на страницу</li>
     * <li>Проверяем что у атрибута attr на форме смены статуса отобразилать область для перетаскивания</li>
     * <li>Сбрасываем файл на область для перетаскивания атрибута attr</li>
     * <li>Проверяем что у атрибута attr скрылась область для перетаскивания</li>
     * <li>Проверяем в значении атрибута attr присутствует файл file</li>
     * </ol>
     */
    @Test
    public void testDropzoneOnChangeStateForm()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        Attribute attr = DAOAttribute.createFile(scCase.getFqn());
        DSLAttribute.add(attr);

        BoStatus resolved = DAOBoStatus.createResolved(scCase);
        DSLBoStatus.setAttrInState(attr, resolved, true, true, 1, 1);

        //Выполнение действий и проверки
        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        GUILogon.asTester();
        GUIBo.goToCard(sc);

        GUISc.openChangeStateFormAndSetState(resolved.getCode());

        FileToDrag file = FileToDrag.create();
        GUIFileDnD.grabFiles(file);

        GUIFileDnD.dragOnPage();
        GUIFileDnD.assertDropzone(attr);

        GUIFileDnD.dropOnDialog(attr);
        GUIFileDnD.assertNoDropzone(attr);

        GUIFileOperator.assertFilesInAttr(attr, file.getDnDFilename());
    }

    /**
     * Тестирование множественной загрузки файлов в контент типа "Список файлов" с через "drag and drop"
     * на форме добавления объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На форму добавления отдела ouCase вывести контент fileList типа "Список файлов"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на форму добавления отдела типа ouCase</li>
     * <li>Заполяем все необходимые поля на форме</li>
     * <li>Перетаскиваем файлы file1 и file2 на страницу</li>
     * <li>Проверяем что у контента fileList отобразилать область для перетаскивания</li>
     * <li>Сбрасываем файлы на область для перетаскивания контента fileList</li>
     * <li>Проверяем что область для перетаскивания контента fileList исчезла</li>
     * <li>Проверяем что в списке файлов fileList отображаются оба файла file1 и file2</li>
     * <li>Нажимаем кнопку "Сохранить"</li>
     * <li>Проверяем что отдел создался с прикрепленными файлами file1 и file2</li>
     * </ol>
     */
    @Test
    public void testFileDnDAttachOnAddForm()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentAddForm.createFileList(ouCase.getFqn());
        DSLContent.add(fileList);

        //Выполение действий и проверки
        GUILogon.asTester();

        Bo ou = DAOOu.create(ouCase);
        GUIBo.goToAddForm(ou);

        GUIBo.fillOuMainFields(ou);

        FileToDrag file1 = FileToDrag.create();
        FileToDrag file2 = FileToDrag.create();
        GUIFileDnD.grabFiles(file1, file2);

        GUIFileDnD.dragOnPage();

        GUIFileDnD.assertDropzone(fileList);

        GUIFileDnD.dropOn(fileList);
        GUIFileDnD.assertNoDropzone(fileList);

        GUIFileList.assertFilePresence(fileList, file1.getDnDFilename());
        GUIFileList.assertFilePresence(fileList, file2.getDnDFilename());

        GUIForm.applyForm();

        GUIBo.setUuidByUrl(ou);

        DSLFile.assertFilesCount(ou, 2);
    }

    /**
     * Тестирование скрытия зон для перетаскивания файлов при перетаскивания файла за пределы страницы
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела ouCase вывести контенты fileList1 и fileList2 типа "Список файлов"</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Перетаскиваем файл file на страницу</li>
     * <li>Проверяем что появились зоня для перетаскивания контентов fileList1 и fileList2</li>
     * <li>Перетаскиваем файл file за пределы страницы</li>
     * <li>Проверяем что зоны для перетаскивания файлов контентов fileList1 и fileList2 скрылись</li>
     * </ol>
     */
    @Test
    public void testHideDropzonesOnDragLeave()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList1 = DAOContentCard.createFileList(ouCase.getFqn());
        ContentForm fileList2 = DAOContentCard.createFileList(ouCase.getFqn());
        DSLContent.add(fileList1, fileList2);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        FileToDrag file = FileToDrag.create();
        GUIFileDnD.grabFiles(file);

        GUIFileDnD.dragOnPage();
        GUIFileDnD.assertDropzone(fileList1);
        GUIFileDnD.assertDropzone(fileList2);

        GUIFileDnD.dragOutOfPage();
        GUIFileDnD.assertNoDropzone(fileList1);
        GUIFileDnD.assertNoDropzone(fileList2);
    }

    /**
     * Тестирование того, что если у пользователя отсутствуют права на добавление файлов и он перетаскивает
     * файл в область для перетаскивания контента Список файлов, область не активируется.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела ouCase вывести контент fileList типа "Список файлов"</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>В отдел ou добавить сотрудника employee</li>
     * <li>Создать профиль profile для лицензированных пользователей (роль: "Сотрудник отдела")</li>
     * <li>Выдать профилю profile все права во всех метаклассах кроме права на добавление файла в типе ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Перетаскиваем файл file на страницу</li>
     * <li>Проверяем что у контента fileList не отобразилать область для перетаскивания</li>
     * <li>Сбрасываем файл на список файлов fileList</li>
     * <li>Проверяем что файл не был прикреплен</li>
     * </ol>
     */
    @Test
    public void testNoDropzoneIfUserHasNoPermission()
    {
        //Подготока
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn());
        DSLContent.add(fileList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        MetaClass emplCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(emplCase, ou, false, true);
        DSLBo.add(employee);

        SecurityProfile profile = DAOSecurityProfile.create(true, null, SysRole.ouMember());
        DSLSecurityProfile.add(profile);

        DSLSecurityProfile.grantAllPermissions(profile);
        DSLSecurityProfile.removeRights(ouCase, profile, AbstractBoRights.ADD_FILE);

        //Выполнение действий и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(ou);

        FileToDrag file = FileToDrag.create();
        GUIFileDnD.grabFiles(file);
        GUIFileDnD.dragOnPage();

        GUIFileDnD.assertNoDropzone(fileList);

        GUIFileDnD.dropOn(fileList);

        DSLFile.assertFilesCount(ou, 0);
    }

    /**
     * Тестирование того, что при перетаскивании текста на страницу зоны для перетаскивания не подсвечиваются
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела ouCase вывести контент fileList типа "Список файлов"</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Перетаскиваем выделенный текст на страницу</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что у контента fileList не отобразилать область для перетаскивания</li>
     * </ol>
     */
    @Test
    public void testNoDropzoneOnTextDrag()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn());
        DSLContent.add(fileList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIFileDnD.dragTextOnPage();

        //Проверка
        GUIFileDnD.assertNoDropzone(fileList);
    }

    /**
     * Тестирование синхронизации содержимого списков объектов при перетаскивании на них файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела ouCase вывести контенты fileList1 и fileList2 типа "Список файлов"</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Сбрасываем файл file на список файлов fileList1</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем что в списке файлов fileList1 отображается файл file</li>
     * <li>Проверяем что в списке файлов fileList2 отображается файл file</li>
     * </ol>
     */
    @Test
    public void testSyncronizationBetweenFileLists()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList1 = DAOContentCard.createFileList(ouCase.getFqn());
        ContentForm fileList2 = DAOContentCard.createFileList(ouCase.getFqn());
        DSLContent.add(fileList1, fileList2);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        FileToDrag file = FileToDrag.create();
        GUIFileDnD.grabFiles(file);

        GUIFileDnD.dropOn(fileList1);

        //Проверки
        GUIFileList.assertFilePresence(fileList1, file.getDnDFilename());
        GUIFileList.assertFilePresence(fileList2, file.getDnDFilename());
    }

    /**
     * Тестирование выставления признака временности при загрузке файла в контент типа "Список файлов" через "drag
     * and drop"
     * на форме добавления объекта до его сохранения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/RequirementsLog/FileStorage
     * http://sd-jira.naumen.ru/browse/NSDPRD-5928
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На форму добавления отдела ouCase вывести контент fileList типа "Список файлов"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на форму добавления отдела типа ouCase</li>
     * <li>Заполяем все необходимые поля на форме</li>
     * <li>Перетаскиваем файл file1 на страницу</li>
     * <li>Проверяем что у контента fileList отобразилать область для перетаскивания</li>
     * <li>Сбрасываем файл на область для перетаскивания контента fileList</li>
     * <li>Проверяем что область для перетаскивания контента fileList исчезла</li>
     * <li>Проверяем что в списке файлов fileList отображается файл file1</li>
     * <li>Проверяем что в системе прикрепленный файл имеет признак временности</li>
     * </ol>
     */
    @Test
    public void testTemporaryAttrSetOnFileDnDAttachOnAddForm()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentAddForm.createFileList(ouCase.getFqn());
        DSLContent.add(fileList);

        Attribute temporary = DAOAttribute.createBool("file");
        temporary.setCode("temporary");
        temporary.setValue("true");

        //Выполение действий и проверки
        GUILogon.asTester();

        Set<String> oldUuids = DSLBo.getUuidsByFqn("file");

        Bo ou = DAOOu.create(ouCase);
        GUIBo.goToAddForm(ou);

        GUIBo.fillOuMainFields(ou);

        FileToDrag file1 = FileToDrag.create();
        GUIFileDnD.grabFiles(file1);

        GUIFileDnD.dragOnPage();

        GUIFileDnD.assertDropzone(fileList);

        GUIFileDnD.dropOn(fileList);
        GUIFileDnD.assertNoDropzone(fileList);

        GUIFileList.assertFilePresence(fileList, file1.getDnDFilename());

        Bo file = DSLBo.getNewBoModel(oldUuids, DAOFileClass.create());
        DSLBo.assertAttributes(file, temporary);
    }

    /**
     * Тестирование выставления признака временности при загрузке файла в контент типа "Список файлов" через "drag
     * and drop"
     * на форме добавления объекта до его сохранения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00213
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120177218
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На форму добавления отдела ouCase вывести контент fileList типа "Список файлов"</li>
     * <li>Создать атрибут attr типа "Файл" в ouCase и добавить в системную группу атрибутов</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на форму добавления отдела типа ouCase</li>
     * <li>Перетаскиваем файл file1 на страницу</li>
     * <li>Сбрасываем файл на область для перетаскивания контента fileList</li>
     * <li>Проверить, что в списке файлов fileList отображается файл file1</li>
     * <li>Проверить, что в атрибуте attr нет файлов</li>
     * </ol>
     */
    @Test
    public void testDnDUploadOnFileListOnAddForm()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentAddForm.createFileList(ouCase.getFqn());
        DSLContent.add(fileList);

        Attribute attr = DAOAttribute.createFile(ouCase.getFqn());
        DSLAttribute.add(attr);
        GroupAttr groupAttr = DAOGroupAttr.createSystem(ouCase);
        DSLGroupAttr.edit(groupAttr, new Attribute[] { attr }, new Attribute[] {});

        //Выполнение действий и проверки
        GUILogon.asTester();

        Bo ou = DAOOu.create(ouCase);
        GUIBo.goToAddForm(ou);

        FileToDrag file1 = FileToDrag.create();
        GUIFileDnD.grabFiles(file1);
        GUIFileDnD.dragOnPage();
        GUIFileDnD.dropOn(fileList);
        GUIFileList.assertFilePresence(fileList, file1.getDnDFilename());

        tester.waitDisappear(String.format(GUIFileOperator.ATTR_UPLOAD_FORM_X_PATH, attr.getCode()));
    }
}
