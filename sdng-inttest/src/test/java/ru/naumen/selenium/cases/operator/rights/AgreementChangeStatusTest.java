package ru.naumen.selenium.cases.operator.rights;

import java.util.Arrays;
import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.rights.impl.AgreementBoActionsContext;
import ru.naumen.selenium.casesutil.rights.impl.actions.BoActionsTestActions;
import ru.naumen.selenium.casesutil.rights.interfaces.IBoActionsContext;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.role.AgreementRecipientRole;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.security.rights.IRight;
import ru.naumen.selenium.security.role.AbstractRoleContext;

import java.util.ArrayList;

/**
 * Тестирование блока прав Работа с объектами для класса "Соглашение"
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
 * <AUTHOR>
 * @since 26.04.2016
 */
public class AgreementChangeStatusTest extends AbstractTestCase
{
    /**Контекст для тестирования группы прав*/
    private static IBoActionsContext rightContext;

    /**Реализация тестовых действий для объектов*/
    private static BoActionsTestActions contextActions;

    @BeforeClass
    public static void prepareFixture()
    {
        rightContext = new AgreementBoActionsContext();
        contextActions = new BoActionsTestActions(rightContext);
    }

    /**
     * Тестирование отсутствия права на изменение системных статусов для типа Соглашения для Лицензированного
     * пользователя с ролью 'Получатель соглашения'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
     * http://sd-jira.naumen.ru/browse/NSDWRK-10608
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип "agreementCase" класса "Соглашение"</li>
     * <li>Создать соглашение "agreement" типа "agreementCase"</li>
     * <li>Добавить тип сотрудника "employeeCase"</li>
     * <li>Создать лицензированного сотрудника со всеми правами "employee" типа "employeeCase"</li>
     * <li>Создать группу пользователей "userGroup"</li>
     * <li>Поместить "employee" в "userGroup"</li>
     * <li>Создать профиль "profile" (Лицензированные пользователи, роль 'Получатель соглашения', "userGroup")</li>
     * <li>Для профиля "profile" в типе "agreementCase" выдать все права</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под сотрудником "employee"</li>
     * <li>Перейти на карточку соглашения "agreement"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствия кнопки "Изменить статус"</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightChangeStateForUserLicensedWithRoleAgreementRecipient()
    {
        //Подготовка
        AbstractRoleContext roleContext = new AgreementRecipientRole(true);
        Bo currentUser = roleContext.getCurrentUser();

        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));

        //Выполнение действий и проверка
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noChangeStateActions(currentUser);
    }

    /**
     * Тестирование отсутствия права на изменение пользовательских статусов для типа Соглашения для Лицензированного
     * пользователя с ролью 'Сотрудник'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
     * http://sd-jira.naumen.ru/browse/NSDWRK-10608
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип "agreementCase" класса "Соглашение"</li>
     * <li>В тип "agreementCase" добавить статус "status"</li>
     * <li>Создать соглашение "agreement" типа "agreementCase"</li>
     * <li>Добавить тип сотрудника "employeeCase"</li>
     * <li>Создать лицензированного сотрудника со всеми правами "employee" типа "employeeCase"</li>
     * <li>Создать группу пользователей "userGroup"</li>
     * <li>Поместить "employee" в "userGroup"</li>
     * <li>Создать профиль "profile" (Лицензированные пользователи, роль 'Сотрудник', "userGroup")</li>
     * <li>Для профиля "profile" в типе "agreementCase" выдать все права</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под сотрудником "employee"</li>
     * <li>Перейти на карточку соглашения "agreement"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствия кнопки "Изменить статус"</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightChangeStateForUserLicensedWithRoleEmployee()
    {
        //Подготовка
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();

        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));

        //Выполнение действий и проверка
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noChangeStateActions(currentUser);
    }

    /**
     * Тестирование отсутствия права на изменение системных статусов для типа Соглашения для Нелицензированного
     * пользователя с ролью 'Получатель соглашения'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
     * http://sd-jira.naumen.ru/browse/NSDWRK-10608
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип "agreementCase" класса "Соглашение"</li>
     * <li>Создать соглашение "agreement" типа "agreementCase"</li>
     * <li>Добавить тип сотрудника "employeeCase"</li>
     * <li>Создать нелицензированного сотрудника со всеми правами "employee" типа "employeeCase"</li>
     * <li>Создать группу пользователей "userGroup"</li>
     * <li>Поместить "employee" в "userGroup"</li>
     * <li>Создать профиль "profile" (Нелицензированные пользователи, роль 'Получатель соглашения', "userGroup")</li>
     * <li>Для профиля "profile" в типе "agreementCase" выдать все права</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под сотрудником "employee"</li>
     * <li>Перейти на карточку соглашения "agreement"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствия кнопки "Изменить статус"</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightChangeStateForUserUnlicensedWithRoleAgreementRecipient()
    {
        //Подготовка
        AbstractRoleContext roleContext = new AgreementRecipientRole(false);
        Bo currentUser = roleContext.getCurrentUser();

        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));

        //Выполнение действий и проверка
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noChangeStateActions(currentUser);
    }

    /**
     * Тестирование отсутствия права на изменение пользовательских статусов для типа Соглашения для
     * Нелицензированного пользователя с ролью 'Сотрудник'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
     * http://sd-jira.naumen.ru/browse/NSDWRK-10608
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип "agreementCase" класса "Соглашение"</li>
     * <li>В тип "agreementCase" добавить статус "status"</li>
     * <li>Создать соглашение "agreement" типа "agreementCase"</li>
     * <li>Добавить тип сотрудника "employeeCase"</li>
     * <li>Создать нелицензированного сотрудника со всеми правами "employee" типа "employeeCase"</li>
     * <li>Создать группу пользователей "userGroup"</li>
     * <li>Поместить "employee" в "userGroup"</li>
     * <li>Создать профиль "profile" (Нелицензированные пользователи, роль 'Сотрудник', "userGroup")</li>
     * <li>Для профиля "profile" в типе "agreementCase" выдать все права</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под сотрудником "employee"</li>
     * <li>Перейти на карточку соглашения "agreement"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствия кнопки "Изменить статус"</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightChangeStateForUserUnlicensedWithRoleEmployee()
    {
        //Подготовка
        AbstractRoleContext roleContext = new EmployeeRole(false);
        Bo currentUser = roleContext.getCurrentUser();

        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));

        //Выполнение действий и проверка
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noChangeStateActions(currentUser);
    }

}