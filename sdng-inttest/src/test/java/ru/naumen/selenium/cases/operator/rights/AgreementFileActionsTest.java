package ru.naumen.selenium.cases.operator.rights;

import java.util.Arrays;
import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import java.util.ArrayList;

import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.rights.impl.AgreementFileContext;
import ru.naumen.selenium.casesutil.rights.impl.actions.FileTestActions;
import ru.naumen.selenium.casesutil.rights.interfaces.IFileContext;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.role.AgreementRecipientRole;
import ru.naumen.selenium.casesutil.role.AgreementServiceResponsibleRole;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.casesutil.role.FileAuthorRole;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.security.rights.IRight;
import ru.naumen.selenium.security.role.AbstractRoleContext;

/**
 * Тестирование блока прав Работа с файлами для класса Соглашение
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
 * http://sd-jira.naumen.ru/browse/NSDWRK-15021
 * <AUTHOR>
 * @since 27.04.2016
 */
public class AgreementFileActionsTest extends AbstractTestCase
{
    /**Контекст для тестирования группы прав*/
    private static IFileContext rightContext;

    /**Реализация тестовых действий для файлов*/
    private static FileTestActions contextActions;

    @BeforeClass
    public static void prepareFixture()
    {
        rightContext = new AgreementFileContext();
        contextActions = new FileTestActions(rightContext);
    }

    /**
     * Тестирование отсутствия права "Добавление файлов" для типа Соглашения для Лицензированного пользователя с
     * ролью 'Получатель соглашения'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
     * http://sd-jira.naumen.ru/browse/NSDWRK-15021
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase с конкурентной лицензией</li>
     * <li>Создать тип класса Солашение agreementCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Назначить сотрудника employee получателем соглашения</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В agreementCase выдать все права кроме права на добавление файлов для роли Получатель соглашения,
     *  группа userGroup</li>
     * <li>Добавить контент fileList типа Список файлов на карточку agreementCase</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в fileList отсутствует ссылка Добавить</li>
     * </ol>
     */
    @Test
    public void testAddFileLicensedAgreementRecipient()
    {
        noAddFile(new AgreementRecipientRole(true));
    }

    /**
     * Тестирование отсутствия права "Добавление файлов" для типа Соглашения для Нелицензированного пользователя с
     * ролью 'Получатель соглашения'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
     * http://sd-jira.naumen.ru/browse/NSDWRK-15021
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Солашение agreementCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Назначить сотрудника employee получателем соглашения</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В agreementCase выдать все права кроме права на добавление файлов для роли Получатель соглашения,
     *  группа userGroup</li>
     * <li>Добавить контент fileList типа Список файлов на карточку agreementCase</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в fileList отсутствует ссылка Добавить</li>
     * </ol>
     */
    @Test
    public void testAddFileNotLicensedAgreementRecipient()
    {
        noAddFile(new AgreementRecipientRole(false));
    }

    /**
     * Тестирование отсутствия права "Просмотр файлов" для типа Соглашения для Лицензированного пользователя с ролью
     * 'Куратор услуги, связанной с соглашением'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
     * http://sd-jira.naumen.ru/browse/NSDWRK-15021
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase с конкурентной лицензией</li>
     * <li>Создать тип класса Солашение agreementCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Создать услугу service, команду team, добавить в нее employee</li>
     * <li>Добавить услугу service к соглашению agreement, назначить куратором услуги сотрудника employee</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В agreementCase выдать все права кроме права на просмотр файлов для роли Куратор услуги, связанной с
     * соглашением,
     *  группа userGroup</li>
     * <li>Добавить контент fileList типа Список файлов на карточку agreementCase</li>
     * <li>Добавить файл с названием fileName к объекту agreement</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в fileList отсутствует файл с названием fileName</li>
     * </ol>
     */
    @Test
    public void testListFileLicensedAgreementServiceResponsible()
    {
        noListFile(new AgreementServiceResponsibleRole(true));
    }

    /**
     * Тестирование отсутствия права "Просмотр файлов" для типа Соглашения для Нелицензированного пользователя с
     * ролью 'Сотрудник'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
     * http://sd-jira.naumen.ru/browse/NSDWRK-15021
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Солашение agreementCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В agreementCase выдать все права кроме права на просмотр файлов для роли Сотрудник,
     *  группа userGroup</li>
     * <li>Добавить контент fileList типа Список файлов на карточку agreementCase</li>
     * <li>Добавить файл с названием fileName к объекту agreement</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в fileList отсутствует файл с названием fileName</li>
     * </ol>
     */
    @Test
    public void testListFileNotLicensedEmployee()
    {
        noListFile(new EmployeeRole(false));
    }

    /**
     * Тестирование отсутствия права "Удаление файлов" для типа Соглашения для Лицензированного пользователя с ролью
     * 'Автор файла'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
     * http://sd-jira.naumen.ru/browse/NSDWRK-15021
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase с конкурентной лицензией</li>
     * <li>Создать тип класса Солашение agreementCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В agreementCase выдать все права кроме права на удаление файлов для роли Автор файла,
     *  группа userGroup</li>
     * <li>Добавить контент fileList типа Список файлов на карточку agreementCase</li>
     * <li>Добавить файл с названием fileName к объекту agreement</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>В fileList выбрать fileName</li>
     * <li>Проверить отсутствие ссылки массовой операции "удалить"</li>
     * </ol>
     */
    @Test
    public void testDeleteFileLicensedFileAuthor()
    {
        noDeleteFile(new FileAuthorRole(true));
    }

    /**
     * Тестирование отсутствия права "Удаление файлов" для типа Соглашения для Нелицензированного пользователя с
     * ролью 'Автор файла'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00189
     * http://sd-jira.naumen.ru/browse/NSDWRK-15021
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Солашение agreementCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В agreementCase выдать все права кроме права на удаление файлов для роли Автор файла,
     *  группа userGroup</li>
     * <li>Добавить контент fileList типа Список файлов на карточку agreementCase</li>
     * <li>Добавить файл с названием fileName к объекту agreement</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>В fileList выбрать fileName</li>
     * <li>Проверить отсутствие ссылки массовой операции "удалить"</li>
     * </ol>
     */
    @Test
    public void testDeleteFileNotLicensedFileAuthor()
    {
        noDeleteFile(new FileAuthorRole(false));
    }

    /**
     * Проверка отсутствия права 'Добавление файлов'
     * @param roleContext роль
     */
    private void noAddFile(AbstractRoleContext roleContext) // NOPMD
    {
        Bo currentUser = roleContext.getCurrentUser();
        AbstractRoleContext[] allRoles = AbstractRoleContext.getAllRolesForAuthor(roleContext);
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.ADD_FILE);
        rightContext.setRight(rights, currentUser, allRoles);
        //Выполнение действия и проверки
        contextActions.noAddFileAction(currentUser);
    }

    /**
     * Проверка отсутствия права 'Удаление файлов'
     * @param roleContext роль
     */
    private void noDeleteFile(AbstractRoleContext roleContext) // NOPMD
    {
        Bo currentUser = roleContext.getCurrentUser();
        AbstractRoleContext[] allRoles = AbstractRoleContext.getAllRolesForAuthor(roleContext);
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.DELETE_FILE);
        rightContext.setRight(rights, currentUser, allRoles);
        //Выполнение действия и проверки
        contextActions.noDeleteFileAction(currentUser);
    }

    /**
     * Проверка отсутствия права 'Просмотр файлов'
     * @param roleContext роль
     */
    private void noListFile(AbstractRoleContext roleContext) // NOPMD
    {
        Bo currentUser = roleContext.getCurrentUser();
        AbstractRoleContext[] allRoles = AbstractRoleContext.getAllRolesForAuthor(roleContext);
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.LIST_FILE);
        rightContext.setRight(rights, currentUser, allRoles);
        //Выполнение действия и проверки
        contextActions.noListFileAction(currentUser);
    }
}
