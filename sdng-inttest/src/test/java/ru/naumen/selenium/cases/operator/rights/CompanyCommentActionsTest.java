package ru.naumen.selenium.cases.operator.rights;

import java.util.Arrays;
import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import java.util.ArrayList;

import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.rights.impl.CompanyCommentContext;
import ru.naumen.selenium.casesutil.rights.impl.actions.CommentTestActions;
import ru.naumen.selenium.casesutil.rights.interfaces.ICommentContext;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.security.rights.IRight;
import ru.naumen.selenium.security.role.AbstractRoleContext;

/**
 * Тестирование блока прав Работа с комментариями для класса Компания
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00313
 * <AUTHOR>
 * @since 27.04.2016
 */
public class CompanyCommentActionsTest extends AbstractTestCase
{
    /**Контекст для тестирования группы прав*/
    private static ICommentContext rightContext;

    /**Реализация тестовых действий для комментариев*/
    private static CommentTestActions contextActions;

    @BeforeClass
    public static void prepareFixture()
    {
        rightContext = new CompanyCommentContext();
        contextActions = new CommentTestActions(rightContext);
    }

    /**
     * Тестирование отсутствия права на Добавление комментариев в объект класса Компания для роли "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00313
     * http://sd-jira.naumen.ru/browse/NSDWRK-10607
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В классе Компании выдать все права кроме права на добавление комментариев для роли Сотрудник,
     *  группа userGroup</li>
     * <li>Добавить контент content типа Комментарии к объекту на карточку Компании</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в content отсутствует ссылка Добавить комментарий</li>
     * </br>
     */
    @Test
    public void testAddCommentEmployeeRole()
    {
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.ADD_COMMENT);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.noAddCommentAction(currentUser);
    }

    /**
     * Тестирование отсутствия права на Добавление приватных комментариев в объект класса Компания для роли "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00313
     * http://sd-jira.naumen.ru/browse/NSDWRK-10607
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В классе Компании выдать все права кроме права на добавление приватных комментариев для роли Сотрудник,
     *  группа userGroup</li>
     * <li>Добавить контент content типа Комментарии к объекту на карточку Компании</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в content отсустует возможность добавления приватного комментария</li>
     * </br>
     */
    @Test
    public void testAddPrivateCommentEmployeeRole()
    {
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.ADD_PRIVATE_COMMENT);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.noAddPrivateCommentAction(currentUser);
    }

    /**
     * Тестирование отсутствия права на Удаление комментариев в объект класса Компания для роли "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00313
     * http://sd-jira.naumen.ru/browse/NSDWRK-10607
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В классе Компании выдать все права кроме права на удаление комментариев для роли Сотрудник,
     *  группа userGroup</li>
     * <li>Добавить контент content типа Комментарии к объекту на карточку Компании</li>
     * <li>К компании добавить приватный и не приватный комментарии: privateComment, comment</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в commentList у комментариев privateComment и comment отсутствуют
     * пиктограммы для удаления</li>
     * </br>
     */
    @Test
    public void testDeleteCommentEmployeeRole()
    {
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.DELETE_COMMENT);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.noDeleteCommentAction(currentUser);
    }

    /**
     * Тестирование отсутствия права на Редактирование комментариев в объект класса Компания для роли "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00313
     * http://sd-jira.naumen.ru/browse/NSDWRK-10607
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В классе Компании выдать все права кроме права на редактирование комментариев для роли Сотрудник,
     *  группа userGroup</li>
     * <li>Добавить контент content типа Комментарии к объекту на карточку Компании</li>
     * <li>К компании добавить приватный и не приватный комментарии: privateComment, comment</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в commentList у комментариев privateComment и comment отсутствуют
     * пиктограммы для редактирования</li>
     * </br>
     */
    @Test
    public void testEditCommentEmployeeRole()
    {
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.EDIT_COMMENT);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.noEditCommentAction(currentUser);
    }

    /**
     * Тестирование отсутствия права на Просмотр комментариев в объект класса Компания для роли "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00313
     * http://sd-jira.naumen.ru/browse/NSDWRK-10607
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В классе Компании выдать все права кроме права на просмотр комментариев для роли Сотрудник,
     *  группа userGroup</li>
     * <li>Добавить контент content типа Комментарии к объекту на карточку Компании</li>
     * <li>К компании добавить приватный и не приватный комментарии: privateComment, comment</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в commentList отсутствуют комментарии privateComment, comment</li>
     * </br>
     */
    @Test
    public void testViewCommentEmployeeRole()
    {
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.VIEW_COMMENT);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.noViewCommentAction(currentUser);
    }

    /**
     * Тестирование отсутствия права на Просмотр приватных комментариев в объект класса Компания для роли "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00313
     * http://sd-jira.naumen.ru/browse/NSDWRK-10607
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В классе Компании выдать все права кроме права на просмотр приватных комментариев для роли Сотрудник,
     *  группа userGroup</li>
     * <li>Добавить контент content типа Комментарии к объекту на карточку Компании</li>
     * <li>К компании добавить приватный и не приватный комментарии: privateComment, comment</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в commentList отсутствуют комментарии privateComment</li>
     * </br>
     */
    @Test
    public void testViewPrivateCommentEmployeeRole()
    {
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.VIEW_PRIVATE_COMMENT);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.notViewPersonCommentAction(currentUser);
    }

}
