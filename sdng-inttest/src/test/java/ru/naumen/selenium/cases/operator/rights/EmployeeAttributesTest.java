package ru.naumen.selenium.cases.operator.rights;

import java.util.Set;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOMetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerEditAttrs;
import ru.naumen.selenium.security.SecurityMarkerViewAttrs;
import ru.naumen.selenium.security.rights.IRight;

/**
 * Тестирование блока прав Действия с атрибутами для класса Сотрудник
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
 * <AUTHOR>
 * @since 25.05.2015
 *
 */

public class EmployeeAttributesTest extends AbstractTestCase
{
    /**
     * Тестирование добавления объекта класса Сотрудник 
     * при незаполненном необязательном атрибуте "Группы пользователей сотрудника (отдела/команды)"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00266
     * http://sd-jira.naumen.ru/browse/NSDPRD-6021
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип emplCase класса Сотрудник</li>
     * <li>Изменить свойства системного атрибута "Группы пользователей сотрудника (отдела/команды)" 
     * secGroupsAttr на редактируемый</li>
     * <li>Создать группу атрибутов attrGroup и добавить туда атрибут secGroupsAttr</li>
     * <li>Добавить контент propertyListAddForm типа Параметры на форме, группа атрибутов - attrGroup</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму добавления сотрудника типа userCase</li>
     * <li>Заполнить основные поля на форме добавления (поле атрубута secGroupsAttr должно оставаться пустым)</li>
     * <li>Нажать кнопку сохранить и проверить, что форма успешно закрылась</li>
     * <li>Проверить, что добавленный сотрудник присутствует</li>
     */
    @Test
    public void testAddEmployeeWithEmptySecGroupsAttr()
    {
        //Подготовка 
        MetaClass emplCase = DAOMetaClass.createCase(DAOEmployeeCase.createClass());
        DSLMetaClass.add(emplCase);
        Bo employee = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);

        Attribute secGroupsAttr = DAOAttribute.createSecGroups(emplCase.getFqn(), "employeeSecGroups");
        secGroupsAttr.setEditable(Boolean.TRUE.toString());
        DSLAttribute.edit(secGroupsAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(emplCase);
        DSLGroupAttr.add(attrGroup, secGroupsAttr);

        ContentForm propertyListAddForm = DAOContentAddForm.createEditablePropertyList(emplCase, attrGroup);
        DSLContent.add(propertyListAddForm);

        //Действия и проверки
        GUILogon.asTester();

        Set<String> oldUuids = DSLBo.getUuidsByFqn(emplCase.getFqn());
        GUIBo.goToAddForm(employee, emplCase.getFqn());
        GUIBo.fillMainFields(employee);
        GUIForm.applyForm();

        String emplUuid = DSLBo.getCreatedObjectUuid(emplCase.getFqn(), oldUuids);
        employee.setUuid(emplUuid);
        employee.setExists(true);
    }

    /**
     * Тестирование отсутствия права на Редактирование атрибутов объекта класс Сотрудник для роли "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип emplCase класса Сотрудник</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В emplCase выдать права на просмотр карточки объекта и атрибутов emplCase для роли Сотрудник, 
     * группа userGroup</li>
     * <li>Добавить контент content типа Параметры объекта на карточку emplCase, группа атрибутов - Системные</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в content отсутствует ссылка Редактировать</li>
     * <li>В emplCase выдать права на редактирование атрибутов для роли Сотрудник, группа userGroup</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в content присутствует ссылка Редактировать</li>
     */
    @Test
    public void testEditAttributesEmployeeRole()
    {
        //Подготовка
        MetaClass employeeClass = DAOEmployeeCase.createClass();
        MetaClass employeeCase = DAOEmployeeCase.create(employeeClass);
        DSLMetaClass.add(employeeCase);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(emplProfile);
        IRight rights[] = { AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES };
        DSLSecurityProfile.setRights(employeeCase, emplProfile, rights);

        GroupAttr systemGroup = DAOGroupAttr.createSystem(employeeCase);
        ContentForm content = DAOContentCard.createPropertyList(employeeCase, systemGroup);
        DSLContent.add(content);

        //Действия и проверки
        GUILogon.login(employee);
        GUIContent.assertLinkAbsense(content, GUIContent.LINK_EDIT);

        DSLSecurityProfile.setRights(employeeCase, emplProfile, new IRight[] { AbstractBoRights.EDIT_REST_ATTRIBUTES });
        tester.refresh();
        GUIContent.assertLinkPresent(content, GUIContent.LINK_EDIT);
    }

    /**
     * Тестирование отсутствия права на Редактирование атрибутов объекта класс Сотрудник для роли 
     * "Лидер команды Сотрудника"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип emplCase класса Сотрудник</li>
     * <li>Создать сотрудников employee1, employee2 типа emplCase</li>
     * <li>Создать команду team, добавить в нее employee1, сделать employee1 лидером команды team</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee1</li>
     * <li>В emplCase выдать права на просмотр карточки объекта и атрибутов для роли Сотрудник, группа userGroup</li>
     * <li>В emplCase выдать права на просмотр карточки объекта и атрибутов + на редактирование атрибутов для роли
     * Лидер команды сотрудника, группа userGroup</li>
     * <li>Добавить контент content типа Параметры объекта на карточку emplCase, группа атрибутов - Системные</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee1</li>
     * <li>Проверить, что в content присутствует ссылка Редактировать</li>
     * <li>Перейти на карточку employee2</li>
     * <li>Проверить, что в content отсутствует ссылка Редактировать</li>
     */
    @Test
    public void testEditAttributesTeamLeaderRole()
    {
        //Подготовка
        MetaClass employeeClass = DAOEmployeeCase.createClass();
        MetaClass emplCase = DAOEmployeeCase.create(employeeClass);
        DSLMetaClass.add(emplCase);
        Bo employee1 = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        Bo employee2 = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(employee1, employee2, team);
        DSLTeam.addEmployees(team, employee1);
        DSLTeam.setLeader(team, employee1);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee1);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        SecurityProfile leaderProfile = DAOSecurityProfile.create(true, userGroup, SysRole.userTeamHead());
        DSLSecurityProfile.add(emplProfile, leaderProfile);
        IRight rights[] = { AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES,
                AbstractBoRights.EDIT_REST_ATTRIBUTES };
        DSLSecurityProfile.setRights(emplCase, leaderProfile, rights);
        DSLSecurityProfile.setRights(emplCase, emplProfile,
                new IRight[] { AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES });

        GroupAttr systemGroup = DAOGroupAttr.createSystem(emplCase);
        ContentForm content = DAOContentCard.createPropertyList(emplCase, systemGroup);
        DSLContent.add(content);

        //Действия и проверки
        GUILogon.login(employee1);
        GUIContent.assertLinkPresent(content, GUIContent.LINK_EDIT);
        GUIBo.goToCard(employee2);
        GUIContent.assertLinkAbsense(content, GUIContent.LINK_EDIT);
    }

    /**
     * Тестирование отсутствия права на Редактирование одного атрибута объекта класс Сотрудник для роли "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип emplCase класса Сотрудник</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать в emplCase атрибуты attr1, attr2 типа Строка, группу атрибутов attrGroup, добавить в нее attr1,
     * attr2</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В emplCase создать маркер прав marker на редактирование атрибута attr1</li>
     * <li>В emplCase выдать права на просмотр карточки объекта, атрибутов + на редактирование Остальных атрибутов
     * для роли Сотрудник, группа userGroup</li>
     * <li>Добавить контент content типа Параметры объекта на карточку emplCase, группа атрибутов - attrGroup</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Нажать ссылку Редактировать content</li>
     * <li>Проверить, что на форме редактирования присутствует attr2 и отсутствует attr1</li>
     * <li>В emplCase выдать права на marker для роли Сотрудник, группа userGroup</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что на форме редактирования присутствуют attr1 и attr2</li>
     */
    @Test
    public void testEditOneAttrEmployeeRole()
    {
        //Подготовка 
        MetaClass employeeClass = DAOEmployeeCase.createClass();
        MetaClass employeeCase = DAOEmployeeCase.create(employeeClass);
        DSLMetaClass.add(employeeCase);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        Attribute attr1 = DAOAttribute.createString(employeeCase);
        Attribute attr2 = DAOAttribute.createString(employeeCase);
        DSLAttribute.add(attr1, attr2);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(emplProfile);

        SecurityMarker marker = new SecurityMarkerEditAttrs(employeeCase).addAttributes(attr1).apply();
        DSLSecurityProfile.setRights(employeeCase, emplProfile, new IRight[] { AbstractBoRights.VIEW_CARD,
                AbstractBoRights.VIEW_REST_ATTRIBUTES, AbstractBoRights.EDIT_REST_ATTRIBUTES });

        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase);
        DSLGroupAttr.add(attrGroup, attr1, attr2);
        ContentForm content = DAOContentCard.createPropertyList(employeeCase, attrGroup);
        DSLContent.add(content);

        //Действия и проверки
        GUILogon.login(employee);
        GUIContent.clickEdit(content);
        GUIForm.assertAttrAbsence(attr1);
        GUIForm.assertAttrPresent(attr2);

        DSLSecurityProfile.setRights(employeeCase, emplProfile, marker);
        tester.refresh();
        GUIContent.clickEdit(content);
        GUIForm.assertAttrPresent(attr1, attr2);
    }

    /**
     * Тестирование отсутствия права на Редактирование одного атрибута объекта класс Сотрудник для роли
     * "Лидер команды Сотрудника"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип emplCase класса Сотрудник</li>
     * <li>Создать сотрудников employee1, employee2 типа emplCase</li>
     * <li>Создать команду team, добавить в нее employee1, сделать employee1 лидером команды team</li>
     * <li>Создать в emplCase атрибуты attr1, attr2 типа Строка, группу атрибутов attrGroup, добавить в нее attr1,
     * attr2</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee1</li>
     * <li>В emplCase создать маркер прав marker на редактирование атрибута attr</li>
     * <li>В emplCase выдать права на просмотр карточки объекта и атрибутов + на редактирование Остальных атрибутов 
     * для роли Сотрудник, группа userGroup</li>
     * <li>В emplCase выдать права на marker для роли Лидер команды сотрудника, группа userGroup</li>
     * <li>Добавить контент content типа Параметры объекта на карточку emplCase, группа атрибутов - attrGroup</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee1</li>
     * <li>Нажать на ссылку Редактировать content</li>
     * <li>Проверить, что на форме редактирования присутствуют attr1, attr2</li>
     * <li>Нажать Отмена</li>
     * <li>Перейти на карточку employee2</li>
     * <li>Нажать на ссылку Редактировать content</li>
     * <li>Проверить, что на форме редактирования присутствует attr2, отсутствует attr1</li>
     */
    @Test
    public void testEditOneAttrTeamLeaderRole()
    {
        //Подготовка 
        MetaClass emplCase = SharedFixture.employeeCase();
        Bo employee1 = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        Bo employee2 = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(employee1, employee2, team);
        DSLTeam.addEmployees(team, employee1);
        DSLTeam.setLeader(team, employee1);
        Attribute attr1 = DAOAttribute.createString(emplCase);
        Attribute attr2 = DAOAttribute.createString(emplCase);
        DSLAttribute.add(attr1, attr2);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee1);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        SecurityProfile leaderProfile = DAOSecurityProfile.create(true, userGroup, SysRole.userTeamHead());
        DSLSecurityProfile.add(emplProfile, leaderProfile);

        SecurityMarker marker = new SecurityMarkerEditAttrs(emplCase).addAttributes(attr1).apply();
        DSLSecurityProfile.setRights(emplCase, leaderProfile, marker);
        DSLSecurityProfile.setRights(emplCase, emplProfile, new IRight[] { AbstractBoRights.VIEW_CARD,
                AbstractBoRights.VIEW_REST_ATTRIBUTES, AbstractBoRights.EDIT_REST_ATTRIBUTES });

        GroupAttr attrGroup = DAOGroupAttr.create(emplCase);
        DSLGroupAttr.add(attrGroup, attr1, attr2);
        ContentForm content = DAOContentCard.createPropertyList(emplCase, attrGroup);
        DSLContent.add(content);
        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(
                emplCase, MetaclassCardTab.OBJECTCARD));

        //Действия и проверки
        GUILogon.login(employee1);
        GUIContent.clickEdit(content);
        GUIForm.assertAttrPresent(attr1, attr2);
        GUIForm.cancelForm();
        GUIBo.goToCard(employee2);
        GUIContent.clickEdit(content);
        GUIForm.assertAttrPresent(attr2);
        GUIForm.assertAttrAbsence(attr1);
    }

    /**
     * Тестирование отсутствия права на Просмотр атрибутов объекта класс Сотрудник для роли "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип emplCase класса Сотрудник</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В emplCase выдать права на просмотр карточки объекта для роли Сотрудник, группа userGroup</li>
     * <li>Добавить контент content типа Параметры объекта на карточку emplCase, группа атрибутов - Системные</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в content отсутствуют атрибуты Фамилия, Имя, Отчество, Должность</li>
     * <li>В emplCase выдать права на просмотр атрибутов объекта emplCase для роли Сотрудник, группа userGroup</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в content присутствуют атрибуты Фамилия, Имя, Отчество, Должность</li>
     */
    @Test
    public void testViewAttributesEmployeeRole()
    {
        //Подготовка 
        MetaClass emplCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(emplProfile);
        IRight rights[] = { AbstractBoRights.VIEW_CARD };
        DSLSecurityProfile.setRights(emplCase, emplProfile, rights);

        GroupAttr systemGroup = DAOGroupAttr.createSystem(emplCase);
        ContentForm content = DAOContentCard.createPropertyList(emplCase, systemGroup);
        DSLContent.add(content);
        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(emplCase, MetaclassCardTab.OBJECTCARD));

        //Действия и проверки
        GUILogon.login(employee);
        Attribute[] attrs = { SysAttribute.lastName(emplCase), SysAttribute.firstName(emplCase),
                SysAttribute.middleName(emplCase), SysAttribute.post(emplCase) };
        GUIPropertyList.assertAbsenceAttribute(content, attrs);

        DSLSecurityProfile.setRights(emplCase, emplProfile, new IRight[] { AbstractBoRights.VIEW_REST_ATTRIBUTES });
        tester.refresh();
        GUIPropertyList.assertPresentsAttribute(content, attrs);
    }

    /**
     * Тестирование отсутствия права на Просмотр атрибутов объекта класс Сотрудник для роли "Лидер команды Сотрудника"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип emplCase класса Сотрудник</li>
     * <li>Создать сотрудников employee1, employee2 типа emplCase</li>
     * <li>Создать команду team, добавить в нее employee1, сделать employee1 лидером команды team</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee1</li>
     * <li>В emplCase выдать права на просмотр карточки объекта для роли Сотрудник, группа userGroup</li>
     * <li>В emplCase выдать права на просмотр карточки объекта + на просмотр атрибутов для роли
     * Лидер команды сотрудника, группа userGroup</li>
     * <li>Добавить контент content типа Параметры объекта на карточку emplCase, группа атрибутов - Системные</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee1</li>
     * <li>Проверить, что в content присутствуют атрибуты Фамилия, Имя, Отчество, Должность</li>
     * <li>Перейти на карточку employee2</li>
     * <li>Проверить, что в content отсутствуют атрибуты Фамилия, Имя, Отчество, Должность</li>
     */
    @Test
    public void testViewAttributesTeamLeaderRole()
    {
        //Подготовка 
        MetaClass emplCase = SharedFixture.employeeCase();
        Bo employee1 = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        Bo employee2 = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(employee1, employee2, team);
        DSLTeam.addEmployees(team, employee1);
        DSLTeam.setLeader(team, employee1);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee1);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        SecurityProfile leaderProfile = DAOSecurityProfile.create(true, userGroup, SysRole.userTeamHead());
        DSLSecurityProfile.add(emplProfile, leaderProfile);
        IRight rights[] = { AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES };
        DSLSecurityProfile.setRights(emplCase, leaderProfile, rights);
        DSLSecurityProfile.setRights(emplCase, emplProfile, new IRight[] { AbstractBoRights.VIEW_CARD });

        GroupAttr systemGroup = DAOGroupAttr.createSystem(emplCase);
        ContentForm content = DAOContentCard.createPropertyList(emplCase, systemGroup);
        DSLContent.add(content);
        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(emplCase, MetaclassCardTab.OBJECTCARD));

        //Действия и проверки
        GUILogon.login(employee1);

        Attribute[] attrs = { SysAttribute.lastName(emplCase), SysAttribute.firstName(emplCase),
                SysAttribute.middleName(emplCase), SysAttribute.post(emplCase) };
        GUIPropertyList.assertPresentsAttribute(content, attrs);
        GUIBo.goToCard(employee2);
        GUIPropertyList.assertAbsenceAttribute(content, attrs);
    }

    /**
     * Тестирование отсутствия права на Просмотр одного атрибута объекта класс Сотрудник для роли "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип emplCase класса Сотрудник</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать в emplCase атрибут attr типа Строка, группу атрибутов attrGroup, добавить в нее attr</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В emplCase создать маркер прав marker на просмотр атрибута attr</li>
     * <li>В emplCase выдать права на просмотр карточки объекта + на просмотр Остальных атрибутов 
     * для роли Сотрудник, группа userGroup</li>
     * <li>Добавить контент content типа Параметры объекта на карточку emplCase, группа атрибутов - attrGroup</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что content отсутствует на карточке</li>
     * <li>В emplCase выдать права на marker для роли Сотрудник, группа userGroup</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в content присутствует attr</li>
     */
    @Test
    public void testViewOneAttrEmployeeRole()
    {
        //Подготовка 
        MetaClass emplCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        Attribute attr = DAOAttribute.createString(emplCase);
        DSLAttribute.add(attr);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(emplProfile);

        SecurityMarker marker = new SecurityMarkerViewAttrs(emplCase).addAttributes(attr).apply();
        DSLSecurityProfile.setRights(emplCase, emplProfile,
                new IRight[] { AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES });

        GroupAttr attrGroup = DAOGroupAttr.create(emplCase);
        DSLGroupAttr.add(attrGroup, attr);
        ContentForm content = DAOContentCard.createPropertyList(emplCase, attrGroup);
        DSLContent.add(content);
        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(
                emplCase, MetaclassCardTab.OBJECTCARD));

        //Действия и проверки
        GUILogon.login(employee);
        GUIContent.assertAbsence(content);

        DSLSecurityProfile.setRights(emplCase, emplProfile, marker);
        tester.refresh();
        GUIPropertyList.assertPresentsAttribute(content, attr);
    }

    /**
     * Тестирование отсутствия права на Просмотр одного атрибута объекта класс Сотрудник для роли "Лидер команды
     * Сотрудника"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип emplCase класса Сотрудник</li>
     * <li>Создать сотрудников employee1, employee2 типа emplCase</li>
     * <li>Создать команду team, добавить в нее employee1, сделать employee1 лидером команды team</li>
     * <li>Создать в emplCase атрибут attr типа Строка, группу атрибутов attrGroup, добавить в нее attr</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee1</li>
     * <li>В emplCase создать маркер прав marker на просмотр атрибута attr</li>
     * <li>В emplCase выдать права на просмотр карточки объекта и на просмотр маркера Остальные атрибуты
     * для роли Сотрудник, группа userGroup</li>
     * <li>В emplCase выдать права на marker для роли Лидер команды сотрудника, группа userGroup</li>
     * <li>Добавить контент content типа Параметры объекта на карточку emplCase, группа атрибутов - attrGroup</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee1</li>
     * <li>Проверить, в content присутствует attr</li>
     * <li>Перейти на карточку employee2</li>
     * <li>Проверить, content отсутствует на карточке</li>
     */
    @Test
    public void testViewOneAttrTeamLeaderRole()
    {
        //Подготовка 
        MetaClass emplCase = SharedFixture.employeeCase();
        Bo employee1 = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        Bo employee2 = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(employee1, employee2, team);
        DSLTeam.addEmployees(team, employee1);
        DSLTeam.setLeader(team, employee1);
        Attribute attr = DAOAttribute.createString(emplCase);
        DSLAttribute.add(attr);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee1);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        SecurityProfile leaderProfile = DAOSecurityProfile.create(true, userGroup, SysRole.userTeamHead());
        DSLSecurityProfile.add(emplProfile, leaderProfile);

        SecurityMarker marker = new SecurityMarkerViewAttrs(emplCase).addAttributes(attr).apply();
        DSLSecurityProfile.setRights(emplCase, leaderProfile, marker);
        DSLSecurityProfile.setRights(emplCase, emplProfile,
                new IRight[] { AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES });

        GroupAttr attrGroup = DAOGroupAttr.create(emplCase);
        DSLGroupAttr.add(attrGroup, attr);
        ContentForm content = DAOContentCard.createPropertyList(emplCase, attrGroup);
        DSLContent.add(content);
        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(emplCase, MetaclassCardTab.OBJECTCARD));

        //Действия и проверки
        GUILogon.login(employee1);
        GUIPropertyList.assertPresentsAttribute(content, attr);
        GUIBo.goToCard(employee2);
        GUIContent.assertAbsence(content);
    }
}
