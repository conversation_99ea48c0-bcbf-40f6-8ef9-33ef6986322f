package ru.naumen.selenium.cases.operator.rights;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIAccessMatrix;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.rights.IRight;

/**
 * Тестирование блоков прав Просмотр истории событий и Наследование для класса Сотрудник
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
 * <AUTHOR>
 * @since 25.05.2015
 *
 */

public class EmployeeRightsTest extends AbstractTestCase
{
    /**
     * Тестирование разрыва наследования прав на Редактирование атрибутов объекта в типе класса Сотрудник
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать в классе Сотрудник атрибут attr типа Строка, группу атрибутов attrGroup, добавить в нее attr</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В классе Сотрудник выдать права только на просмотр карточки объекта и атрибутов для роли Сотрудник, 
     *  группа userGroup</li>
     * <li>Добавить контент content типа Параметры объекта на карточку класса Сотрудник, группа атрибутов -
     * attrGroup</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, в content отсутствует ссылка Редактировать</li>
     * <li>В emplCase выдать права на редактирование атрибутов для роли Сотрудник, группа userGroup, разорвав тем самым
     * наследование </li>
     * <li>Обновить страницу</li>
     * <li>Нажать на ссылку Редактировать в content</li>
     * <li>Проверить, что attr присутствует на форме редактирования</li>
     */
    @Test
    public void testBreakInheritance()
    {
        //Подготовка 
        MetaClass emplClass = DAOEmployeeCase.createClass();
        MetaClass emplCase = DAOEmployeeCase.create(emplClass);
        DSLMetaClass.add(emplCase);
        Bo employee = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        Attribute attr = DAOAttribute.createString(emplClass);
        DSLAttribute.add(attr);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(emplProfile);
        IRight rights[] = { AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES };
        DSLSecurityProfile.setRights(emplClass, emplProfile, rights);

        GroupAttr attrGroup = DAOGroupAttr.create(emplClass);
        DSLGroupAttr.add(attrGroup, attr);
        ContentForm content = DAOContentCard.createPropertyList(emplClass, attrGroup);
        DSLContent.add(content);

        //Действия и проверки
        GUILogon.login(employee);
        GUIContent.assertLinkAbsense(content, GUIContent.LINK_EDIT);

        DSLSecurityProfile.setRights(emplCase, emplProfile, new IRight[] { AbstractBoRights.EDIT_REST_ATTRIBUTES });
        tester.refresh();
        GUIContent.clickEdit(content);
        GUIForm.assertAttrPresent(attr);
    }

    /**
     * Тестирование возобновления наследования прав на Просмотр комментариев в типе класса Сотрудник
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В классе Сотрудник выдать все права для роли Сотрудник, группа userGroup</li>
     * <li>В emplCase убрать право на просмотр комментариев для роли Сотрудник, группа userGroup</li>
     * <li>Добавить контент content типа Комментарии к объекту на карточку класса Сотрудник</li>
     * <li>Добавить к объекту employee комментарий с текстом commentText</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Перейти на вкладку Права доступа emplCase</li>
     * <li>Нажать на кнопку Сбросить настройки</li>
     * <li>Зайти под employee</li>
     * <li>Проверить, что в content присутствует комментарий с текстом commentText</li>
     */
    @Test
    public void testResumeInheritance()
    {
        //Подготовка 
        MetaClass emplClass = DAOEmployeeCase.createClass();
        MetaClass emplCase = DAOEmployeeCase.create(emplClass);
        DSLMetaClass.add(emplCase);
        Bo employee = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(emplProfile);
        IRight rights[] = { AbstractBoRights.VIEW_COMMENT };
        DSLSecurityProfile.grantAllPermissionsForCase(emplProfile, emplClass);
        DSLSecurityProfile.removeRights(emplCase, emplProfile, rights);

        ContentForm content = DAOContentCard.createCommentList(emplClass.getFqn());
        DSLContent.add(content);
        String commentText = ModelUtils.createTitle();
        DSLComment.add(employee.getUuid(), commentText);

        //Действия и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(emplCase, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickRefreshWithConfirmByYes();

        GUILogon.login(employee);
        GUIComment.assertCommentPresent(content, commentText);
    }

    /**
     * Тестирование отсутствия права на Просмотр истории событий в объекте класса Сотрудник для роли "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В emplCase выдать права на просмотр карточки объекта для роли Сотрудник, группа userGroup</li>
     * <li>Добавить контент content типа История изменений объекта на карточку emplCase</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что content отсутствует на карточке</li>
     * <li>В emplCase выдать права на просмотр истории событий для роли Сотрудник, группа userGroup</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в content присутствует на карточке</li>
     */
    @Test
    public void testViewEventEmployeeRole()
    {
        //Подготовка 
        MetaClass emplCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(emplProfile);
        IRight rights[] = { AbstractBoRights.VIEW_CARD };
        DSLSecurityProfile.setRights(emplCase, emplProfile, rights);

        ContentForm content = DAOContentCard.createEventList(emplCase.getFqn(), PresentationContent.DEFAULT);
        DSLContent.add(content);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.OBJECTCARD));

        //Действия и проверки
        GUILogon.login(employee);
        GUIContent.assertAbsence(content);

        DSLSecurityProfile.setRights(emplCase, emplProfile, new IRight[] { AbstractBoRights.VIEW_EVENT });
        tester.refresh();
        GUIContent.assertPresent(content);
    }

    /**
     * Тестирование отсутствия права на Просмотр истории событий в объекте класса Сотрудник для роли 
     * "Лидер команды Сотрудника"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудников employee1, employee2 типа emplCase</li>
     * <li>Создать команду team, добавить в нее employee1, employee2, сделать employee1 лидером команды</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee1</li>
     * <li>В emplCase выдать права на просмотр карточки объекта, просмотр истории событий для роли Лидер
     * команды Сотрудника, группа userGroup</li>
     * <li>В emplCase выдать права на просмотр карточки объекта для роли Сотрудник, группа userGroup</li>
     * <li>Добавить контент content типа История изменений объекта на карточку emplCase</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee1</li>
     * <li>Проверить, что в content присутствует на карточке</li>
     * <li>Перейти на карточку employee2</li>
     * <li>Проверить, что content отсутствует на карточке</li>
     */
    @Test
    public void testViewEventTeamLeaderRole()
    {
        //Подготовка 
        MetaClass emplCase = SharedFixture.employeeCase();
        Bo ou = SharedFixture.ou();
        Bo employee1 = DAOEmployee.create(emplCase, ou, false, true);
        Bo employee2 = DAOEmployee.create(emplCase, ou, false, true);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(employee1, employee2, team);
        DSLTeam.addEmployees(team, employee1);
        DSLTeam.setLeader(team, employee1);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee1);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        SecurityProfile leaderProfile = DAOSecurityProfile.create(true, userGroup, SysRole.userTeamHead());
        DSLSecurityProfile.add(emplProfile, leaderProfile);

        DSLSecurityProfile.setRights(emplCase, leaderProfile, new IRight[] { AbstractBoRights.VIEW_CARD,
                AbstractBoRights.VIEW_EVENT });
        DSLSecurityProfile.setRights(emplCase, emplProfile, new IRight[] { AbstractBoRights.VIEW_CARD });

        ContentForm content = DAOContentCard.createEventList(emplCase.getFqn(), PresentationContent.DEFAULT);
        DSLContent.add(content);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.OBJECTCARD));

        //Действия и проверки
        GUILogon.login(employee1);
        GUIContent.assertPresent(content);
        GUIBo.goToCard(employee2);
        GUIContent.assertAbsence(content);
    }
}
