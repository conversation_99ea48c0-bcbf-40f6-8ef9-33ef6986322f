package ru.naumen.selenium.cases.operator.rights;

import java.io.IOException;

import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;

import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLSession;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.mobile.rest.auth.AuthenticationType;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на логику лицензий при работе с api мобильного клиента
 * Выполняются с конкурентной лицензией на одного сотрудника
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00570
 *
 * @since 05.09.2014
 * <AUTHOR>
 */
public class MobileLicenseTest extends AbstractTestCase
{
    @BeforeClass
    public static void prepareFixture()
    {
        // Установить лицензию
        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_2_PATH);
    }

    /**
     * Проверка, что логин на мобильном клиенте после логина через
     * Web-интерфейс занимает ту же лицензию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00570
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Загрузить конкурентную лицензию на одного employee</li>
     * <li>Создать лицензированного employee типа SharedFixture.employeeCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Логинимся как employee через Web</li>
     * <li>Логинимся как employee через api мобильного клиента</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что статус ответа 200</li>
     * </ol>
     */
    @Test
    public void testLoginMKAfterWebSuccess()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        // Выполнение действий
        DSLSession.getNewSessionCookie(employee);
        ValidatableResponse authResponse = DSLMobileAuth.auth(employee, AuthenticationType.ACCESS_KEY);

        authResponse.statusCode(200);
    }

    /**
     * Проверка, что логин через Web-интерфейс после логина через 
     * мобильный клиент занимает ту же лицензию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00570
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Загрузить конкурентную лицензию на одного employee</li>
     * <li>Создать лицензированного employee типа SharedFixture.employeeCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Логинимся как employee через api мобильного клиента</li>
     * <li>Логинимся как employee через Web</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что отсутствует ошибка авторизации</li>
     * </ol>
     */
    @Test
    public void testLoginWebAfterMKSuccess() throws IOException
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        // Выполнение действий
        DSLMobileAuth.authAs(employee, AuthenticationType.ACCESS_KEY);
        DSLSession.getNewSessionCookie(employee);
    }

    /**
     * Проверка, окончания сессии по таймауту в МК
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00120
     * http://sd-jira.naumen.ru/browse/NSDPRD-4434
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Загрузить лицензию с количеством конкурентных сотрудников равным 1 и таймаутом сессии 1 минута</li>
     * <li>Создать лицензированных сотрудников employee, employee2</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Логинимся как employee через api мобильного клиента</li>
     * <li>Ждать 2 минуты</li>
     * <li>Логинимся как employee2 через Web</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что отсутствует ошибка авторизации</li>
     * </ol>
     */
    @Test
    public void testLoginWebAfterMKTimeOut() throws IOException
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        Bo employee2 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee, employee2);

        // Выполнение действий
        DSLMobileAuth.authAs(employee, AuthenticationType.ACCESS_KEY);
        WaitTool.waitMills(120000);
        DSLSession.getNewSessionCookie(employee2);
    }

    /**
     * Проверка, что логин через МК падает с ошибкой авторизации,
     * если все лицензии заняты
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00570
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Включить параметр "Вход в систему с неполным набором лицензий".</li>
     * <li>Загрузить конкурентную лицензию на одного employee</li>
     * <li>Создать лицензированного employee1 типа SharedFixture.employeeCase</li>
     * <li>Создать лицензированного employee2 типа SharedFixture.employeeCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Логинимся как employee1 через Web</li>
     * <li>Логинимся как employee2 через api мобильного клиента</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что статус ответа 200</li>
     * </ol>
     */
    @Test
    public void testTwoEmployeesMKLoginSuccessAfterWeb()
    {
        // Подготовка
        DSLAdmin.enableCompleteSetOfLicensesNotRequired(true);
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employee1 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        Bo employee2 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee1, employee2);

        // Выполнение действий
        DSLSession.getNewSessionCookie(employee1);
        ValidatableResponse authResponse = DSLMobileAuth.auth(employee2, AuthenticationType.ACCESS_KEY);

        authResponse.statusCode(200);
    }

    /**
     * Проверка, что логин через Web падает с ошибкой авторизации,
     * если все лицензии заняты мобильным клиентом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00570
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Включить параметр "Вход в систему с неполным набором лицензий".</li>
     * <li>Загрузить конкурентную лицензию на одного employee</li>
     * <li>Создать лицензированного employee1 типа SharedFixture.employeeCase</li>
     * <li>Создать лицензированного employee2 типа SharedFixture.employeeCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Логинимся как employee1 через api мобильного клиента</li>
     * <li>Логинимся как employee2 через Web</li>
     * <br>
     * <b>Проверки</b>
     * <li>что отсутствует ошибка авторизации</li>
     * </ol>
     */
    @Test
    public void testTwoEmployeesWebLoginSuccessAfterMK()
    {
        // Подготовка
        DSLAdmin.enableCompleteSetOfLicensesNotRequired(true);
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employee1 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        Bo employee2 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee1, employee2);

        // Выполнение действий
        DSLMobileAuth.authAs(employee1, AuthenticationType.ACCESS_KEY);
        DSLSession.getNewSessionCookie(employee2);
    }
}
