package ru.naumen.selenium.cases.operator.rights;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.rights.matrix.ResponsibleRights;
import ru.naumen.selenium.casesutil.rights.matrix.ScRights;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.casesutil.role.ScEmpOfClientOURole;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.RightGroup;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerEditAttrs;
import ru.naumen.selenium.security.SecurityProfile;
import ru.naumen.selenium.security.UserGroup;

/**
 * Тесты на дефекты в правах 
 * <AUTHOR>
 * @since 07.02.2013
 */
public class Rights1Test extends AbstractTestCase
{
    /**
     * Тестирование того, что при добавлении запроса с карточки сотрудника, не происходит проверки прав
     * доступа "Добавление запроса для клиента-команды", "Добавление запроса для клиента-отдела"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * http://sd-jira.naumen.ru/browse/NSDPRD-3239
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать сотрудника с лицензией employee и соглашение agreement</li>
     * <li>Добавить сотрудника employee как получателя соглашения agreement</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Добавить сотрудника employee в эту группу</li>
     * <li>Cоздать профиль profile (Лицензированные пользователи, роль сотрудник, userGroup)</li>
     * <li>Для профиля profile выдать права на просмотр карточки employee,
     * и все права для типа запроса scCase</li>
     * <li>Установить скрипты проверки прав, вызывающие исключение при проверке прав 
     * "Добавление запроса для клиента-команды", "Добавление запроса для клиента-отдела" в профиле profile
     * для типа запроса scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee</li>
     * <li>Зайти в карточку employee</li>
     * <li>Нажать кнопку Добавить запрос</li>
     * <li>Ввести все данные</li>
     * <li>Подтвердить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Запрос создан</li>
     * </ol>
     */
    @Test
    public void testAddSCForEmployeeNoCheckOtherRights()
    {
        //Подготовка
        MetaClass scCase = SharedFixture.scCase();

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        employee.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        Bo agreement = DAOAgreement.createWithRules(SharedFixture.agreementCase(), SharedFixture.serviceTime(),
                SharedFixture.serviceTime(), SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        DSLBo.add(employee, agreement);
        DSLAgreement.addToRecipients(agreement, employee);
        Bo sc = DAOSc.create(scCase, employee, agreement, SharedFixture.timeZone());

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);

        String script = "throw new Exception('Этот скрипт проверки права не должен срабатывать!!!');";
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(script);
        DSLScriptInfo.addScript(scriptInfo);

        ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile secProfile = DAOSecurityProfile.create(true,
                userGroup, SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.setRights(SharedFixture.employeeCase(), secProfile, AbstractBoRights.VIEW_CARD);
        DSLSecurityProfile.grantAllPermissionsForCase(secProfile, scCase);

        DSLSecurityProfile.setScriptRight(scCase, secProfile, ScRights.ADD_TO_TEAM, scriptInfo.getCode());
        DSLSecurityProfile.setScriptRight(scCase, secProfile, ScRights.ADD_TO_OU, scriptInfo.getCode());

        //Выполнение действия и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUIBo.fillScMainFields(sc);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);

        //Проверка
        DSLBo.assertPresent(sc);
    }

    /**
     * Тестирование того, что при добавлении запроса с карточки отдела, не происходит проверки прав
     * доступа "Добавление запроса для клиента-сотрудника", "Добавление запроса для клиента-команды"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * http://sd-jira.naumen.ru/browse/NSDPRD-3239
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать сотрудника с лицензией employee и соглашение agreement</li>
     * <li>Добавить отдел ou как получателя соглашения agreement</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Добавить сотрудника employee в эту группу</li>
     * <li>Cоздать профиль profile (Лицензированные пользователи, роль сотрудник, userGroup)</li>
     * <li>Для профиля profile выдать права на просмотр карточек ou и employee,
     * и все права для типа запроса scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee</li>
     * <li>Зайти в карточку ou</li>
     * <li>Установить скрипты проверки прав, вызывающие исключение для прав 
     * "Добавление запроса для клиента-сотрудника", "Добавление запроса для клиента-команды" в профиле profile
     * для типа запроса scCase</li>
     * <li>Нажать кнопку Добавить запрос</li>
     * <li>Ввести все данные</li>
     * <li>Подтвердить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Запрос создан</li>
     * </ol>
     */
    @Test
    public void testAddSCForOuNoCheckOtherRights()
    {
        //Подготовка
        MetaClass scCase = SharedFixture.scCase();

        Bo ou = SharedFixture.ou();
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        employee.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        Bo agreement = DAOAgreement.createWithRules(SharedFixture.agreementCase(), SharedFixture.serviceTime(),
                SharedFixture.serviceTime(), SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        DSLBo.add(employee, agreement);
        DSLAgreement.addToRecipients(agreement, ou);
        Bo sc = DAOSc.create(scCase, ou, agreement, SharedFixture.timeZone());

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);

        String script = "throw new Exception('Этот скрипт проверки права не должен срабатывать!!!');";
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(script);
        DSLScriptInfo.addScript(scriptInfo);

        ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile secProfile = DAOSecurityProfile.create(true,
                userGroup, SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.setRights(SharedFixture.employeeCase(), secProfile, AbstractBoRights.VIEW_CARD);
        DSLSecurityProfile.setRights(SharedFixture.ouCase(), secProfile, AbstractBoRights.VIEW_CARD);
        DSLSecurityProfile.grantAllPermissionsForCase(secProfile, scCase);

        //Выполнение действия и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(ou);

        // Эта установка скриптов должна выполняться после перехода на карточку отдела, иначе скрипт будет
        // срабатывать при логине сотрудника, после которого его перекидывает на свою карточку
        DSLSecurityProfile.setScriptRight(scCase, secProfile, ScRights.ADD_TO_EMPLOYEE, scriptInfo.getCode());
        DSLSecurityProfile.setScriptRight(scCase, secProfile, ScRights.ADD_TO_TEAM, scriptInfo.getCode());

        GUIButtonBar.addSC();
        GUIBo.fillScMainFields(sc);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);

        //Проверка
        DSLBo.assertPresent(sc);
    }

    /**
     * Тестирование того, что при добавлении запроса с карточки команды, не происходит проверки прав
     * доступа "Добавление запроса для клиента-сотрудника", "Добавление запроса для клиента-отдела"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * http://sd-jira.naumen.ru/browse/NSDPRD-3239
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать сотрудника с лицензией employee и соглашение agreement</li>
     * <li>Добавить команду team как получателя соглашения agreement</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Добавить сотрудника employee в эту группу</li>
     * <li>Cоздать профиль profile (Лицензированные пользователи, роль сотрудник, userGroup)</li>
     * <li>Для профиля profile выдать права на просмотр карточек team и employee,
     * и все права для типа запроса scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee</li>
     * <li>Зайти в карточку team</li>
     * <li>Установить скрипты проверки прав, вызывающие исключение при проверке прав 
     * "Добавление запроса для клиента-сотрудника", "Добавление запроса для клиента-отдела" в профиле profile
     * для типа запроса scCase</li>
     * <li>Нажать кнопку Добавить запрос</li>
     * <li>Ввести все данные</li>
     * <li>Подтвердить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Запрос создан</li>
     * </ol>
     */
    @Test
    public void testAddSCForTeamNoCheckOtherRights()
    {
        //Подготовка
        MetaClass scCase = SharedFixture.scCase();

        Bo team = SharedFixture.team();
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        employee.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        Bo agreement = DAOAgreement.createWithRules(SharedFixture.agreementCase(), SharedFixture.serviceTime(),
                SharedFixture.serviceTime(), SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        DSLBo.add(employee, agreement);
        DSLAgreement.addToRecipients(agreement, team);
        Bo sc = DAOSc.create(scCase, team, agreement, SharedFixture.timeZone());

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);

        String script = "throw new Exception('Этот скрипт проверки права не должен срабатывать!!!');";
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(script);
        DSLScriptInfo.addScript(scriptInfo);

        ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile secProfile = DAOSecurityProfile.create(true,
                userGroup, SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.setRights(SharedFixture.employeeCase(), secProfile, AbstractBoRights.VIEW_CARD);
        DSLSecurityProfile.setRights(SharedFixture.teamCase(), secProfile, AbstractBoRights.VIEW_CARD);
        DSLSecurityProfile.grantAllPermissionsForCase(secProfile, scCase);

        //Выполнение действия и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(team);

        // Эта установка скриптов должна выполняться после перехода на карточку команды, иначе скрипт будет
        // срабатывать при логине сотрудника, после которого его перекидывает на свою карточку
        DSLSecurityProfile.setScriptRight(scCase, secProfile, ScRights.ADD_TO_EMPLOYEE, scriptInfo.getCode());
        DSLSecurityProfile.setScriptRight(scCase, secProfile, ScRights.ADD_TO_OU, scriptInfo.getCode());

        GUIButtonBar.addSC();
        GUIBo.fillScMainFields(sc);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);

        //Проверка
        DSLBo.assertPresent(sc);
    }

    /**
     * Тестирование отсутствия формы добавления, если на операцию нет прав
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00512
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$172622701
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass и тип userCase</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать профиль profile(роль Сотрудник, userGroup)</li>
     * <li>Забрать у профиля profile право на добавление объекта</li>
     * <li>Создать employee поместить в userGroup</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <li>Перейти на форму добавления userCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что на странице присутствует надпись "У Вас нет прав на выполнение этой операции"</li>
     * <li>На странице отсутствует форма добавления</li>
     * </ol>
     */
    @Test
    public void testAddFormAbsenceWithoutPermission()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        UserGroup userGroup = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(userGroup, new EmployeeRole(), true);
        RightGroup rights = new RightGroup(profile, userCase);
        rights.addAllRights(userCase);
        rights.removeRight(userCase, AbstractBoRights.ADD);
        rights.apply();

        //Выполнение действия
        GUILogon.login(employee);
        GUIBo.goToAddFormWithoutCheck(userCase.getFqn(), SharedFixture.clientOu().getUuid());

        //Проверки
        GUIBo.assertNotRights();
        GUITester.assertAbsent(Div.FORM, "Форма добавления присутствует на странице");
    }

    /**
     * Неверное сообщение в списке объектов у кнопки Добавить, если нет прав на добавление объектов этого класса
     * NSDPRD-149
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00270
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип команды teamCase</li>
     * <li>Создать контент contentTeamListInCompany в компании со списком команд</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать профиль profile(роль Сотрудник, userGroup)</li>
     * <li>Выдать профилю profile все права в классах Компания, команда, кроме прав на просмотр атрибутов класса
     * Отдел и добавления команды</li>
     * <li>Создать employee поместить в userGroup</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <li>Зайти в карточку компании</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что в контенте contentTeamListInCompany кнопка "Добавить" отсутствует</li>
     * </ol>
     */
    @Test
    public void testAddTeamRight()
    {
        //Подготовка
        MetaClass companyClass = DAORootClass.create();
        MetaClass teamClass = DAOTeamCase.createClass();

        MetaClass teamCase = DAOTeamCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(teamCase, employeeCase, ouCase);

        ContentForm contentTeamListInCompany = DAOContentCard.createObjectList(companyClass.getFqn(), teamClass,
                teamCase);
        DSLContent.add(contentTeamListInCompany);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, false);
        employee.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        employee.setPassword(ModelUtils.createPassword());
        DSLBo.add(employee);

        //Настраиваем права
        UserGroup userGroup = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(userGroup, new EmployeeRole(), true);
        RightGroup rights = new RightGroup(profile, companyClass, teamClass);
        rights.addAllRights(companyClass);
        rights.addAllRights(teamClass);
        rights.removeRight(teamClass, AbstractBoRights.ADD);
        rights.apply();

        //Выполнение действия
        GUILogon.login(employee);
        GUINavigational.goToOperatorUI();
        GUIContent.assertLinkAbsense(contentTeamListInCompany, GUIContent.LINK_ADD);
    }

    /**
     * Тестирование смены ответственного через контент "Параметры объетка" при отсутствии прав из блока "Изменение
     * ответственного"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00342
     * http://sd-jira.naumen.ru/browse/NSDPRD-1392
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>На карточку scCase добавить контент Параметры объекта propertyList с атрибутами Описание и Ответственный</li>
     * <li>Создать сотрудников employee1..3 типа employeeCase, команды team1..2 типа teamCase</li>
     * <li>Добавить employee2 в team1, employee3 в team1..2</li>
     * <li>Создать запрос типа scCase, назначить ответственного team1/employee2</li>
     * <li>Создать группу пользователей userGroup, добавить в нее сотрудника employee1</li>
     * <li>Создать профиль profile(роль Сотрудник, userGroup)</li>
     * <li>Выдать профилю profile все права в scCase, кроме блока Изменение ответственного</li>
     * <li>Создать employee1 поместить в userGroup</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником employee1</li>
     * <li>Зайти в карточку запроса sc</li>
     * <li>Нажать редактировать в propertyList</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что в поле Ответственный присутствует только team1/employee2</li>
     * <li>Изменить описание, сохранить, проверить, что описание изменилось</li>
     * </ol>
     */
    @Test
    @IgnoreConfig(cause = "NSDPRD-1910")
    public void testChangeResponsibleFromContent()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass scCase = DAOScCase.create();
        MetaClass teamCase = DAOTeamCase.create();
        MetaClass employeeCase = SharedFixture.employeeCase();
        DSLMetaClass.add(scCase, teamCase);

        //Создаем контент
        Attribute responsible = SysAttribute.responsible(scCase);
        Attribute description = SysAttribute.description(scCase);
        GroupAttr attrGroup = DAOGroupAttr.create(scCase);
        DSLGroupAttr.add(attrGroup, responsible, description);

        ContentForm propertyList = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(propertyList);

        //Создаем объекты
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        Bo team1 = DAOTeam.create(teamCase);
        Bo team2 = DAOTeam.create(teamCase);
        DSLBo.add(ou, team1, team2);

        Bo employee1 = DAOEmployee.create(employeeCase, ou, false, true);
        Bo employee2 = DAOEmployee.create(employeeCase, ou, true, true);
        Bo employee3 = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(employee1, employee2, employee3);

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        DSLAgreement.addToRecipients(agreement, ou);
        DSLTeam.addEmployees(team1, employee2, employee3);
        DSLTeam.addEmployees(team2, employee3);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);
        DSLSc.setResponsible(sc, team1, employee2);

        //Подготовка прав
        UserGroup userGroup = new UserGroup(employee1);
        SecurityProfile profile = new SecurityProfile(userGroup, new EmployeeRole(), true);
        RightGroup rights = new RightGroup(profile, scCase);
        rights.addAllRights(scCase);
        rights.removeRightGroup(scCase, ResponsibleRights.values());
        rights.apply();

        //Выполнение действия и проверки
        GUILogon.login(employee1);

        GUIBo.goToCard(sc);
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_CHANGE_RESPONSIBLE);

        GUIContent.clickEdit(propertyList);
        //Проверки
        //TODO дописать проверки после исправления http://sd-jira.naumen.ru/browse/NSDPRD-1910
    }

    /**
     * Тестирование роли "Сотрудник отдела-контрагента запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00316
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать отдел ou типа ouCase, в нем отдел childOu</li>
     * <li>Создать сотрудников типа employeeCase: employee1 - в ou1, employee3 - в ou1, employee2 - в ou2</li>
     * <li>Создать команду team типа teamCase</li>
     * <li>Создать запросы типа scCase: sc1 - с привязкой к ou, sc2 - с привязкой к childOu, 
     * sc3 - с привязкой к employee3, sc4 - с привязкой к employee1, sc5 - с привязкой к team</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Добавить в userGroup employee1..2</li>
     * <li>Создать профиль profile со всеми правами в scCase(Лицензированные пользователи, роль 
     * Сотрудник отдела-контрагента запроса, userGroup)</li>
     * <br>
     * <b>Выполнение действия и проверки.</b>
     * <li>Залогиниться под employee1</li>
     * <li>Проверить, что employee1 может просматривать карточки запросов sc1, sc3, sc4</li>
     * <li>Проверить, что employee1 не может просматривать карточки запросов sc2, sc5</li>
     * <li>Переместить employee1 в childOu</li>
     * <li>Проверить, что employee1 не может просматривать карточку запроса sc1</li>
     * <li>Залогиниться под employee2</li>
     * <li>Проверить, что employee2 может просматривать карточку запроса sc2</li>
     * <li>Проверить, что employee2 не может просматривать карточку запроса sc1</li>
     */
    @Test
    public void testEmpOfClientOURole()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass scCase = DAOScCase.create();
        MetaClass teamCase = DAOTeamCase.create();
        MetaClass employeeCase = SharedFixture.employeeCase();
        DSLMetaClass.add(scCase, teamCase);

        //Создаем объекты
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(ou, team);

        Bo childOu = DAOOu.create(ouCase, ou);
        Bo employee1 = DAOEmployee.create(employeeCase, ou, false, true);
        Bo employee3 = DAOEmployee.create(employeeCase, ou, false, false);
        DSLBo.add(childOu, employee1, employee3);

        Bo employee2 = DAOEmployee.create(employeeCase, childOu, false, true);
        DSLBo.add(employee2);

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        DSLAgreement.addToRecipients(agreement, ou, childOu, employee1, employee3, team);

        Bo sc1 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        Bo sc2 = DAOSc.create(scCase, childOu, agreement, timeZoneItem);
        Bo sc3 = DAOSc.create(scCase, employee3, agreement, timeZoneItem);
        Bo sc4 = DAOSc.create(scCase, employee1, agreement, timeZoneItem);
        Bo sc5 = DAOSc.create(scCase, team, agreement, timeZoneItem);
        DSLBo.add(sc1, sc2, sc3, sc4, sc5);

        //Подготовка прав
        UserGroup userGroup = new UserGroup(employee1, employee2);
        SecurityProfile profile = new SecurityProfile(userGroup, new ScEmpOfClientOURole(), true);
        RightGroup rights = new RightGroup(profile, scCase);
        rights.addAllRights(scCase);
        rights.apply();

        //Выполнение действия и проверки
        GUILogon.login(employee1);

        GUIBo.goToCard(sc1);
        GUIBo.assertThatBoCard(sc1);

        GUIBo.goToCard(sc2);
        GUIBo.assertNotRights();

        GUIBo.goToCard(sc3);
        GUIBo.assertThatBoCard(sc3);

        GUIBo.goToCard(sc4);
        GUIBo.assertThatBoCard(sc4);

        GUIBo.goToCard(sc5);
        GUIBo.assertNotRights();

        DSLBo.move(employee1, childOu);
        GUIBo.goToCard(sc1);
        GUIBo.assertNotRights();

        GUILogon.login(employee2);

        GUIBo.goToCard(sc2);
        GUIBo.assertThatBoCard(sc2);

        GUIBo.goToCard(sc1);
        GUIBo.assertNotRights();
    }

    /**
     * Права на кнопку быстрого создания объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00512
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать типы команды teamCase1..2</li>
     * <li>Добавить teamCase1..2 в меню быстрого создания</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать профиль profile(роль Сотрудник, userGroup)</li>
     * <li>Выдать профилю profile все права на добавление объектов типа teamCase1, 
     * забрать права на добавление объектов типа teamCase2</li>
     * <li>Создать employee поместить в userGroup</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Раскрываем выпадающий список Добавить, проверяем, что там присутствует teamCase1 и отсутствует teamCase2</li>
     * </ol>
     */
    @Test
    public void testFastCreate()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass teamCase1 = DAOTeamCase.create();
        MetaClass teamCase2 = DAOTeamCase.create();
        MetaClass employeeCase = SharedFixture.employeeCase();
        DSLMetaClass.add(teamCase1, teamCase2);

        DSLNavSettings.editVisibilitySettings(true, true);
        MenuItem addButton = DAOMenuItem.createAddButton(true, teamCase1, teamCase2);
        DSLMenuItem.add(addButton);

        //Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, false, true);
        DSLBo.add(employee);

        //Подготовка прав
        UserGroup userGroup = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(userGroup, new EmployeeRole(), true);
        RightGroup rights = new RightGroup(profile, teamCase1, teamCase2);
        rights.removeAllRights(teamCase1);
        rights.addRight(teamCase1, AbstractBoRights.ADD);
        rights.addAllRights(teamCase2);
        rights.removeRight(teamCase2, AbstractBoRights.ADD);
        rights.apply();

        //Выполнение действия и проверки
        GUILogon.login(employee);

        GUINavSettingsOperator.clickMenuItem(addButton.getCode());
        GUINavSettingsOperator.assertMenuItemExists(true, teamCase1.getFqn());
        GUINavSettingsOperator.assertMenuItemExists(false, teamCase2.getFqn());
    }

    /**
     * Тестирование методов api.web.add(String, String, Map) для формы добавления запроса с отсутствием прав для
     * указанного типа запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * http://sd-jira.naumen.ru/browse/NSDPRD-1861
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать типы запросов scCase1..2</li>
     * <li>Создать отдел ou типа ouCase, в нем сотрудника employee типа employeeCase</li>
     * <li>Создать профиль profile(роль Сотрудник, userGroup)</li>
     * <li>Выдать профилю profile все права на добавление запросов типа scCase2 с привязкой к отделу, 
     * забрать права на добавление запросов типа scCase1 с привязкой к отделу</li>
     * <br>
     * <b>Выполнение действия и проверки.</b>
     * <li>Получить ссылку url - результат выполнения скрипта: 
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     return api.web.add('scCase1.FQN', 'ou.UUID', [:]);
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Перейти по ссылке url</li>
     * <li>Проверить, что отсутствуют права на просмотр формы добавления БО</li>
     * <br>
     * <li>Получить ссылку url - результат выполнения скрипта: 
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     return api.web.add('scCase2.FQN', 'ou.UUID', [:]);
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Перейти по ссылке url</li>
     * <li>Заполнить обязательные поля на форме добавления запроса</li>
     * <li>Нажать сохранить</li>
     * <li>Проверить, что запрос создался без ошибок</li>
     */
    @Test
    public void testGetLinkToAddScForm()
    {
        //Подготовка
        MetaClass scCase1 = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        DSLMetaClass.add(scCase1, scCase2);

        CatalogItem timezone = DAOCatalogItem.createTimeZone("America/Campo_Grande");
        DSLCatalogItem.add(timezone);

        //Создаем объекты
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        DSLAgreement.addRecipients(ou, agreement);

        Bo employee = DAOEmployee.create(employeeCase, ou, false, true);
        DSLBo.add(employee);

        //Подготовка прав
        UserGroup userGroup = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(userGroup, new EmployeeRole(), true);
        RightGroup rights = new RightGroup(profile, scCase1, scCase2);
        rights.addAllRights(scCase1);
        rights.removeRight(scCase1, ScRights.ADD_TO_OU);
        rights.addAllRights(scCase2);
        rights.apply();

        //Выполнение действия
        GUILogon.login(employee);
        String scriptPattern = "return api.web.add('%s', '%s', [:]);";

        ScriptRunner script = new ScriptRunner(String.format(scriptPattern, scCase1.getFqn(), ou.getUuid()));
        String url = script.runScript().get(0).trim();
        tester.goToPage(url);

        GUIBo.assertNotRightsAddForm();

        script = new ScriptRunner(String.format(scriptPattern, scCase2.getFqn(), ou.getUuid()));
        url = script.runScript().get(0).trim();
        tester.goToPage(url);

        Bo sc2 = DAOSc.create(scCase2, ou, agreement, timezone);
        GUIBo.fillScMainFields(sc2);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc2);
    }

    /**
     * Тестирование переменной oldSubject в скриптах, уточняющих право при работе через интерфейс.
     * Проверка, что process равен 'EditObject' или 'AddObject' и oldSubject не null при попытке сохранения изменений.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * http://sd-jira.naumen.ru/browse/NSDPRD-5288
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать атрибут stringAttr типа  "Строка" у типа userCase и добавить его в системную группу атрибутов</li>
     * <li>Создаем объект bo типа userCase</li>
     * <li>Создать лицензионного сотрудника employee без прав</li>
     * <li>Создать группу пользователей userGroup и добавить в неё employee</li>
     * <li>Создать профиль profile(роль Сотрудник, userGroup)</li>
     * <li>В профиле profile в действии на редактировании и на просмотре атрибута stringAttr добавить скрипт:
     * <pre>
     *      if ((process == 'EditObject'  || process == 'AddObject') && oldSubject != null) return false; return true;
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <li>Переходим на форму редактирования bo</li>
     * <li>Изменяем значение атрибута stringAttr</li>
     * <li>Нажимаем кнопку сохранить и проверяем, что появилось сообщение об ошибке с указанием отсутствия прав на
     * редактирование</li>
     * <li>Переходим на форму создания bo</li>
     * <li>Заполняем значение атрибута stringAttr</li>
     * <li>Нажимаем кнопку сохранить и проверяем, что появилось сообщение об ошибке с указанием отсутствия прав на
     * изменение атрибута</li>
     * <br>
     * </ol>
     */
    @Test
    public void testOldSubjectScriptRightByInterfaceEdit()
    {
        //Создаем типы
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        //Создаем атрибут
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLAttribute.add(stringAttr);
        GroupAttr sysAttrGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(sysAttrGroup, new Attribute[] { stringAttr }, new Attribute[] {});

        SecurityMarker marker = new SecurityMarkerEditAttrs(userClass).addAttributes(stringAttr).apply();

        //Подготовка
        Bo bo = DAOUserBo.create(userCase);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(bo, employee);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);

        DSLSecurityGroup.addUsers(userGroup, employee);

        String bodyScript = "if ((process == 'EditObject' || process == 'AddObject') && oldSubject != null) return "
                            + "false; return true;";
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(bodyScript);
        DSLScriptInfo.addScript(scriptInfo);

        ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile secProfile = DAOSecurityProfile.create(true,
                userGroup, SysRole.employee());
        DSLSecurityProfile.add(secProfile);

        DSLSecurityProfile.setScriptRight(userClass, secProfile, marker, scriptInfo.getCode());
        DSLSecurityProfile.setRights(userClass, secProfile, AbstractBoRights.VIEW_REST_ATTRIBUTES,
                AbstractBoRights.EDIT_REST_ATTRIBUTES, AbstractBoRights.ADD, AbstractBoRights.VIEW_CARD);

        //Выполнение действия и проверки
        GUILogon.login(employee);

        GUIBo.goToEditForm(bo);
        GUIBo.assertThatEditForm(bo);
        tester.sendKeys(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, ModelUtils.createTitle(), stringAttr.getCode());
        GUIForm.applyFormAssertError(String.format(ErrorMessages.NO_RIGHTS_EDIT_ATTR_IN_GANTT, stringAttr.getTitle(),
                userCase.getTitle()));
        GUIForm.cancelForm();

        GUIBo.goToAddForm(bo);
        GUIBo.assertThatAddForm(userCase);
        GUIBo.fillMainFields(bo);
        tester.sendKeys(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, ModelUtils.createTitle(), stringAttr.getCode());
        GUIForm.applyFormAssertError(String.format(ErrorMessages.NO_RIGHTS_EDIT_ATTR_IN_GANTT, stringAttr.getTitle(),
                userCase.getTitle()));
        GUIForm.cancelForm();
    }

    /**
     * Тестирование переменной oldSubject в скриптах, уточняющих право при работе через интерфейс.
     * Проверка, что subject содержит актуальное (отредактированное) состояние, а oldSubject предыдущие состояние.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать атрибут stringAttr типа  "Строка" у типа userCase и добавить его в системную группу атрибутов</li>
     * <li>Создаем объект bo типа userCase</li>
     * <li>Создать лицензионного сотрудника employee без прав</li>
     * <li>Создать группу пользователей userGroup и добавить в неё employee</li>
     * <li>Создать профиль profile(роль Сотрудник, userGroup)</li>
     * <li>В профиле profile в действии на редактировании и на просмотре атрибута stringAttr добавить скрипт:
     * <pre>
     *      if (process == 'EditObject'){
     *          return oldSubject.stringAttr == ПЕРВОНАЧАЛЬНОЕ_ЗНАЧЕНИЕ && subject.stringAttr == НОВОЕ_ЗНАЧЕНИЕ
     *      }
     *      else return true
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <li>Переходим на форму редактирования bo</li>
     * <li>Изменяем значения атрибута stringAttr</li>
     * <li>Нажимаем кнопку сохранить и проверяем, что форма закрылась</li>
     * <br>
     * </ol>
     */
    @Test
    public void testOldSubjectScriptRightByInterfaceEditWithChangeAttr()
    {
        //Создаем типы
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        String firstValue = "Первоначальное значение";
        String secondValue = "Отредактированное значение";

        //Создаем атрибут
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLAttribute.add(stringAttr);
        GroupAttr sysAttrGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(sysAttrGroup, new Attribute[] { stringAttr }, new Attribute[] {});
        stringAttr.setValue(firstValue);

        //Подготовка
        Bo bo = DAOUserBo.create(userCase);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(bo, employee);
        DSLBo.editAttributeValue(bo, stringAttr);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);

        DSLSecurityGroup.addUsers(userGroup, employee);

        // @formatter:off
        String bodyScript = "if (process == 'EditObject') "
                + "{"
                + "return oldSubject." + stringAttr.getCode() + " == '" + firstValue + "'"
                +        "&& subject." + stringAttr.getCode() + " == '" + secondValue + "'"
                + "} "
                + "else return true";
        // @formatter:on
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(bodyScript);
        DSLScriptInfo.addScript(scriptInfo);

        ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile secProfile = DAOSecurityProfile.create(true,
                userGroup, SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.setScriptRight(userClass, secProfile, AbstractBoRights.EDIT_REST_ATTRIBUTES,
                scriptInfo.getCode());
        DSLSecurityProfile.setRights(userClass, secProfile, AbstractBoRights.VIEW_REST_ATTRIBUTES,
                AbstractBoRights.VIEW_CARD);

        //Выполнение действия и проверки
        GUILogon.login(employee);

        GUIBo.goToEditForm(bo);
        GUIForm.fillAttribute(stringAttr, secondValue);
        GUIForm.applyForm();
    }

    /**
     * Тестирование переменной oldSubject в скриптах, уточняющих право при работе через rest-сервис.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать атрибут stringAttr типа  "Строка" у типа userCase и добавить его в системную группу атрибутов</li>
     * <li>Создаем объект bo типа userCase</li>
     * <li>Создать лицензионного сотрудника employee без прав</li>
     * <li>Создать группу пользователей userGroup и добавить в неё employee</li>
     * <li>Создать профиль profile(роль Сотрудник, userGroup)</li>
     * <li>В профиле profile в действие на редактирование атрибута stringAttr добавить скрипт:
     * <pre>
     *      if (process == 'EditObject') return oldSubject.stringAttr == ОТРЕДАКТИРОВАННОЕ_ЗНАЧЕНИЕ && subject.stringAttr == ПЕРВОНАЧАЛЬНОЕ_ЗНАЧЕНИЕ else return true;
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <li>Редактируем значение атрибута stringAttr посредством рест сервиса, изменяя значени атрибута stringAttr</li>
     * <li>Переходим на карточку объекта</li>
     * <li>Проверяем, что значение атрибута stringAttr изменилось</li>
     * <br>
     * </ol>
     */
    @Test
    public void testOldSubjectScriptRightByRestEdit()
    {
        //Создаем типы
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        //Создаем атрибут
        String firstValue = "Первоначальное значение";
        String editedValue = "Отредактированное значение";
        Attribute stringAttr = DAOAttribute.createString(userClass);
        stringAttr.setValue(firstValue);
        DSLAttribute.add(stringAttr);
        GroupAttr sysAttrGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(sysAttrGroup, new Attribute[] { stringAttr }, new Attribute[] {});

        //Подготовка
        Bo bo = DAOUserBo.create(userCase);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(bo, employee);
        DSLBo.editAttributeValue(bo, stringAttr);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);

        DSLSecurityGroup.addUsers(userGroup, employee);

        String bodyScript = "if (process == 'EditObject') return oldSubject." + stringAttr.getCode() + " == '"
                            + stringAttr.getValue() + "' && subject." + stringAttr.getCode() + " == '" + editedValue
                            + "'; else return true;";
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(bodyScript);
        DSLScriptInfo.addScript(scriptInfo);

        ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile secProfile = DAOSecurityProfile.create(true,
                userGroup, SysRole.employee());
        DSLSecurityProfile.add(secProfile);

        stringAttr.setValue(editedValue);
        DSLSecurityProfile.setScriptRight(userClass, secProfile, AbstractBoRights.EDIT_REST_ATTRIBUTES,
                scriptInfo.getCode());
        GUILogon.login(employee);

        String link = Config.get().getWebAddress() + "services/rest/edit/%s/{\"%s\":\"%s\"}";
        //Переходим по ссылке
        tester.refresh();
        tester.goToPage(String.format(link, bo.getUuid(), stringAttr.getCode(), editedValue));
        DSLBo.assertStringAttr(bo, stringAttr);
    }

    /**
     * Тестирование переменной oldSubject в скриптах, уточняющих право при работе через интерфейс.
     * Проверка, что oldSubject == subject при октрытии карточки объекта.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создаем объект bo типа userCase</li>
     * <li>Создать лицензионного сотрудника employee без прав</li>
     * <li>Создать группу пользователей userGroup и добавить в неё employee</li>
     * <li>Создать профиль profile(роль Сотрудник, userGroup)</li>
     * <li>В профиле profile в действии на просмотр карточки:
     * <pre>
     *      return oldSubject.title == subject.title;
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <li>Переходим на карточку bo</li>
     * <li>Проверяем, что находимся на карточке объекта</li>
     * <br>
     * </ol>
     */
    @Test
    public void testOldSubjectScriptRightByViewCard()
    {
        //Создаем типы
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        //Подготовка
        Bo bo = DAOTeam.create(userCase);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(bo, employee);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);

        DSLSecurityGroup.addUsers(userGroup, employee);

        String bodyScript = "return oldSubject.title == subject.title";
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(bodyScript);
        DSLScriptInfo.addScript(scriptInfo);

        ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile secProfile = DAOSecurityProfile.create(true,
                userGroup, SysRole.employee());
        DSLSecurityProfile.add(secProfile);

        DSLSecurityProfile.setScriptRight(userCase, secProfile, AbstractBoRights.VIEW_CARD, scriptInfo.getCode());

        //Выполнение действия и проверки
        GUILogon.login(employee);

        GUIBo.goToCard(bo);
        GUIBo.assertThatBoCard(bo);
    }
}
