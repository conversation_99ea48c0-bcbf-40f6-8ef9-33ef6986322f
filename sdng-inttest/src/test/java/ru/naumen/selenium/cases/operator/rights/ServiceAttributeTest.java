package ru.naumen.selenium.cases.operator.rights;

import java.util.Arrays;
import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import java.util.ArrayList;

import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.rights.impl.ServiceAttributeContext;
import ru.naumen.selenium.casesutil.rights.impl.actions.AttributeTestActions;
import ru.naumen.selenium.casesutil.rights.interfaces.IAttributeContext;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.role.CommentAuthorRole;
import ru.naumen.selenium.casesutil.role.CompanyHeadRole;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.security.SecurityMarkerEditAttrs;
import ru.naumen.selenium.security.SecurityMarkerViewAttrs;
import ru.naumen.selenium.security.rights.IRight;
import ru.naumen.selenium.security.role.AbstractRoleContext;

/**
 * Тестирование блока прав Действия с атрибутами для класса Услуга
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00188
 * <AUTHOR>
 * @since 04.05.2016
 */
public class ServiceAttributeTest extends AbstractTestCase
{
    /**Контекст для тестирования группы прав*/
    private static IAttributeContext rightContext;

    /**Реализация тестовых действий для тестирования действий с атрибутами*/
    private static AttributeTestActions contextActions;

    @BeforeClass
    public static void prepareFixture()
    {
        rightContext = new ServiceAttributeContext();
        contextActions = new AttributeTestActions(rightContext);
    }

    /**
     * Тестирование отсутствия права на редактирование атрибутов объекта для типа Услуги для Лицензированного
     * пользователя с ролью 'Директор компании'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00188
     * http://sd-jira.naumen.ru/browse/NSDWRK-10609
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Услуга serviceCase</li>
     * <li>Создать услугу service типа serviceCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>Создать атрибут attribute</li>
     * <li>Создать группу атрибутов attrGroup, добавить в неё attribute</li>
     * <li>Добавить контент content типа Параметры объекта на карточку service, группа атрибутов - attrGroup</li>
     * <li>В типе serviceCase выдать все права кроме права на редактирование атрибутов объекта для роли Директор
     * компании,
     *  группа userGroup</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Перейти на карточку service</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствие ссылки "редактировать" на контенте content</li>
     * </ol>
     */
    @Test
    public void testEditAttrLicensedCompanyHeadRole()
    {
        AbstractRoleContext roleContext = new CompanyHeadRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.EDIT_REST_ATTRIBUTES);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.noEditAttributesActions(currentUser);
    }

    /**
     * Тестирование отсутствия права на редактирование пользовательского атрибута объекта для типа Услуги для
     * Лицензированного пользователя с ролью 'Сотрудник'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00188
     * http://sd-jira.naumen.ru/browse/NSDWRK-10609
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Услуга serviceCase</li>
     * <li>Создать услугу service типа serviceCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>Создать атрибут attribute</li>
     * <li>Создать группу атрибутов attrGroup, добавить в неё attribute</li>
     * <li>Добавить контент content типа Параметры объекта на карточку service, группа атрибутов - attrGroup</li>
     * <li>В типе serviceCase выдать все права кроме права на редактирование пользовательского атрибута объекта для
     * роли Сотрудник,
     *  группа userGroup</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Перейти на карточку service</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствие ссылки "редактировать" на контенте content</li>
     * </ol>
     */
    @Test
    public void testEditAttrLicensedEmployeeRole()
    {
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        new SecurityMarkerEditAttrs(rightContext.getObjectCase()).addAttributes(rightContext.getAttribute()).apply();
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.noEditAttributesActions(currentUser);
    }

    /**
     * Тестирование отсутствия права на редактирование атрибутов объекта для типа Услуги для Нелицензированного
     * пользователя с ролью 'Сотрудник'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00188
     * http://sd-jira.naumen.ru/browse/NSDWRK-10609
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Услуга serviceCase</li>
     * <li>Создать услугу service типа serviceCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>Создать атрибут attribute</li>
     * <li>Создать группу атрибутов attrGroup, добавить в неё attribute</li>
     * <li>Добавить контент content типа Параметры объекта на карточку service, группа атрибутов - attrGroup</li>
     * <li>В типе serviceCase выдать все права кроме права на редактирование атрибутов объекта для роли Сотрудник,
     *  группа userGroup</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Перейти на карточку service</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствие ссылки "редактировать" на контенте content</li>
     * </ol>
     */
    @Test
    public void testEditAttrUnlicensedEmployeeRole()
    {
        AbstractRoleContext roleContext = new EmployeeRole(false);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.EDIT_REST_ATTRIBUTES);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.noEditAttributesActions(currentUser);
    }

    /**
     * Тестирование отсутствия права на просмотр атрибутов объекта для типа Услуги для Лицензированного пользователя
     * с ролью 'Сотрудник'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00188
     * http://sd-jira.naumen.ru/browse/NSDWRK-10609
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Услуга serviceCase</li>
     * <li>Создать услугу service типа serviceCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>Создать атрибут attribute</li>
     * <li>Создать группу атрибутов attrGroup, добавить в неё attribute</li>
     * <li>Добавить контент content типа Параметры объекта на карточку service, группа атрибутов - attrGroup</li>
     * <li>В типе serviceCase выдать все права кроме права на просмотр атрибутов объекта для роли Сотрудник,
     *  группа userGroup</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Перейти на карточку service</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствие контента content</li>
     * </ol>
     */
    @Test
    public void testViewAttrLicensedEmployeeRole()
    {
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.VIEW_REST_ATTRIBUTES);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.noViewAttributesActions(currentUser);
    }

    /**
     * Тестирование отсутствия права на просмотр атрибутов объекта для типа Услуги для Нелицензированного
     * пользователя с ролью 'Автор комментария'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00188
     * http://sd-jira.naumen.ru/browse/NSDWRK-10609
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Услуга serviceCase</li>
     * <li>Создать услугу service типа serviceCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>Создать атрибут attribute</li>
     * <li>Создать группу атрибутов attrGroup, добавить в неё attribute</li>
     * <li>Добавить контент content типа Параметры объекта на карточку service, группа атрибутов - attrGroup</li>
     * <li>В типе serviceCase выдать все права кроме права на просмотр атрибутов объекта для роли Автор комментария,
     *  группа userGroup</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Перейти на карточку service</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствие контента content</li>
     * </ol>
     */
    @Test
    public void testViewAttrUnlicensedCommentAuthorRole()
    {
        AbstractRoleContext roleContext = new CommentAuthorRole(false);
        Bo currentUser = roleContext.getCurrentUser();
        AbstractRoleContext[] allRoles = AbstractRoleContext.getAllRolesForAuthor(roleContext);
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.VIEW_REST_ATTRIBUTES);
        rightContext.setRight(rights, currentUser, allRoles);
        //Выполнение действия и проверки
        contextActions.noViewAttributesActions(currentUser);
    }

    /**
     * Тестирование отсутствия права на просмотр пользовательского атрибута объекта для типа Услуги для
     * Лицензированного пользователя с ролью 'Сотрудник'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00188
     * http://sd-jira.naumen.ru/browse/NSDWRK-10609
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Услуга serviceCase</li>
     * <li>Создать услугу service типа serviceCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>Создать атрибут attribute</li>
     * <li>Создать группу атрибутов attrGroup, добавить в неё attribute</li>
     * <li>Добавить контент content типа Параметры объекта на карточку service, группа атрибутов - attrGroup</li>
     * <li>В типе serviceCase выдать все права кроме права на просмотр пользовательского атрибута объекта для роли
     * Сотрудник,
     *  группа userGroup</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Перейти на карточку service</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствие контента content</li>
     * </ol>
     */
    @Test
    public void testViewUserAttrLicensedEmployeeRole()
    {
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        new SecurityMarkerViewAttrs(rightContext.getObjectCase()).addAttributes(rightContext.getAttribute()).apply();
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.noViewAttributesActions(currentUser);
    }
}
