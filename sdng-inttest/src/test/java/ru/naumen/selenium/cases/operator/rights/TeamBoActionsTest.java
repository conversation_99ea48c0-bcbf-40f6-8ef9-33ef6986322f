package ru.naumen.selenium.cases.operator.rights;

import java.util.Arrays;
import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import java.util.ArrayList;

import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.impl.TeamBoActionsContext;
import ru.naumen.selenium.casesutil.rights.impl.actions.BoActionsTestActions;
import ru.naumen.selenium.casesutil.rights.interfaces.IBoActionsContext;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.role.CommentAuthorRole;
import ru.naumen.selenium.casesutil.role.CompanyHeadRole;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.casesutil.role.TeamHeadOuEmployeeRole;
import ru.naumen.selenium.casesutil.role.TeamHeadOuHeadRole;
import ru.naumen.selenium.casesutil.role.TeamLeaderRole;
import ru.naumen.selenium.casesutil.role.TeamParticipantRole;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.security.rights.IRight;
import ru.naumen.selenium.security.role.AbstractRoleContext;

/**
 * Тестирование блока прав Работа с объектами для класса Команда
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
 * <AUTHOR>
 * @since 19.04.2016
 */
public class TeamBoActionsTest extends AbstractTestCase
{
    /**Контекст для тестирования группы прав*/
    private static IBoActionsContext rightContext;

    /**Реализация тестовых действий для комментариев*/
    private static BoActionsTestActions contextActions;

    @BeforeClass
    public static void prepareFixture()
    {
        rightContext = new TeamBoActionsContext();
        contextActions = new BoActionsTestActions(rightContext);
    }

    /**
     * Тестирование отсутствия права "Помещение объекта в архив" для типа Команды для Лицензированного пользователя с
     * ролью 'Участник команды'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-10606
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип сотрудника employeeCase</li>
     * <li>Создать лицензированного сотрудника со всеми правами employee типа employeeCase</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Поместить employee в userGroup</li>
     * <li>Создать тип команды teamCase</li>
     * <li>Для teamCase создать профиль profile (Лицензированные пользователи, роль 'Участник команды', userGroup)</li>
     * <li>Для profile в teamCase выдать все права кроме: "Помещение объекта в архив"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Создать команду userTeam типа teamCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствия права на "Помещение объекта в архив" для типа Команды</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightBOAddInArchiveCaseForUserLicensedWithRoleTeamParticipant()
    {
        AbstractRoleContext roleContext = new TeamParticipantRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.ARCHIVE);
        //Выполнение действия и проверки
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noMassOperationActions(currentUser, MassOperation.ARCHIVE, GUIButtonBar.BTN_REMOVE);
    }

    /**
     * Тестирование отсутствия права "Копирование объекта" для типа Команды для Лицензированного пользователя с ролью
     * 'Сотрудник курирующего отдела'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-10606
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип сотрудника employeeCase</li>
     * <li>Создать лицензированного сотрудника со всеми правами employee типа employeeCase</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Поместить employee в userGroup</li>
     * <li>Создать тип команды teamCase</li>
     * <li>Для teamCase создать профиль profile (Лицензированные пользователи, роль 'Сотрудник курирующего отдела',
     * userGroup)</li>
     * <li>Для profile в teamCase выдать все права кроме: "Копирование объекта"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Создать команду userTeam типа teamCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствия права на "Копирование объекта" для типа Команды</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightBOCopyCaseForUserLicensedWithRoleTeamHeadOuEmployee()
    {
        AbstractRoleContext roleContext = new TeamHeadOuEmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.COPY);
        //Выполнение действия и проверки
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noMassOperationActions(currentUser, MassOperation.COPY, GUIButtonBar.BTN_COPY);
    }

    /**
     * Тестирование отсутствия права "Просмотр карточки объекта" для типа Команды для Лицензированного пользователя с
     * ролью 'Директор компании'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-10606
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип сотрудника employeeCase</li>
     * <li>Создать лицензированного сотрудника со всеми правами employee типа employeeCase</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Поместить employee в userGroup</li>
     * <li>Создать тип команды teamCase</li>
     * <li>Для teamCase создать профиль profile (Лицензированные пользователи, роль 'Директор компании', userGroup)</li>
     * <li>Для profile в teamCase выдать все права кроме: "Просмотр карточки объекта"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Создать команду userTeam типа teamCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствия прав на "Просмотр карточки объекта" для типа Команды</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightBOViewCaseForUserLicensedWithRoleСompanyHead()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new CompanyHeadRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.VIEW_CARD);
        //Выполнение действия и проверки
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noViewCardObject(currentUser);
    }

    /**
     * Тестирование отсутствия права "Просмотр карточки объекта" для типа Команды для Нелицензированного пользователя
     * с ролью 'Сотрудник'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-10606
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип сотрудника employeeCase</li>
     * <li>Создать нелицензированного сотрудника со всеми правами employee типа employeeCase</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Поместить employee в userGroup</li>
     * <li>Создать тип команды teamCase</li>
     * <li>Для teamCase создать профиль profile (Нелицензированные пользователи, роль 'Сотрудник', userGroup)</li>
     * <li>Для profile в teamCase выдать все права кроме: "Просмотр карточки объекта"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Создать команду userTeam типа teamCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствия прав на "Просмотр карточки объекта" для типа Команды</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightBOViewCaseForUserUnlicensedWithRoleEmployee()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new EmployeeRole(false);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.VIEW_CARD);
        //Выполнение действия и проверки
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noViewCardObject(currentUser);
    }

    /**
     * Тестирование отсутствия права "Удаление объекта" для типа Команды для Лицензированного пользователя с ролью
     * 'Сотрудник'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-10606
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип сотрудника employeeCase</li>
     * <li>Создать лицензированного сотрудника со всеми правами employee типа employeeCase</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Поместить employee в userGroup</li>
     * <li>Создать тип команды teamCase</li>
     * <li>Для teamCase создать профиль profile (Лицензированные пользователи, роль 'Сотрудник', userGroup)</li>
     * <li>Для profile в teamCase выдать все права кроме: "Удаление объекта"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Создать команду userTeam типа teamCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>В списке команд компании отметить userTeam и проверить отсутствие ссылки 'удалить'</li>
     * <li>Проверить, что в карточке userTeam нет кнопки "Удалить"</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightDeleteTeamCaseForUserLicensedWithRoleEmployee()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.DELETE);
        //Выполнение действия и проверки
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noMassOperationActions(currentUser, MassOperation.DELETE, GUIButtonBar.BTN_DEL);
    }

    /**
     * Тестирование отсутствия права "Добавление объекта" для типа Команды для Лицензированного пользователя с ролью
     * 'Лидер команды'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-10606
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип сотрудника employeeCase</li>
     * <li>Создать лицензированного сотрудника со всеми правами employee типа employeeCase</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Поместить employee в userGroup</li>
     * <li>Создать тип команды teamCase</li>
     * <li>Для teamCase создать профиль profile (Лицензированные пользователи, роль 'Лидер команды', userGroup)</li>
     * <li>Для profile в teamCase выдать все права кроме: "Добавление объекта"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Создать команду userTeam типа teamCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствия прав на "Добавление объекта" для типа Команды</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightTypeBOAddCaseForUserLicensedWithRoleTeamLeader()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new TeamLeaderRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.ADD);
        //Выполнение действия и проверки
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noAddActions(currentUser);
    }

    /**
     * Тестирование отсутствия права "Изменение типа объекта" для типа Команды для Лицензированного пользователя с
     * ролью 'Руководитель курирующего отдела'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-10606
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип сотрудника employeeCase</li>
     * <li>Создать лицензированного сотрудника со всеми правами employee типа employeeCase</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Поместить employee в userGroup</li>
     * <li>Создать тип команды teamCase</li>
     * <li>Для teamCase создать профиль profile (Лицензированные пользователи, роль 'Руководитель курирующего
     * отдела', userGroup)</li>
     * <li>Для profile в teamCase выдать все права кроме: "Изменение типа объекта"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Создать команду userTeam типа teamCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствия права на "Изменение типа объекта" для типа Команды</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightTypeChangeCaseForUserLicensedWithRoleTeamHeadOuHead()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new TeamHeadOuHeadRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.CHANGE_CASE);
        //Выполнение действия и проверки
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noMassOperationActions(currentUser, MassOperation.CHANGE_CASE, GUIButtonBar.BTN_CHANGE_CASE);
    }

    /**
     * Тестирование отсутствия права "Восстановление объекта из архива" для типа Команды для Лицензированного
     * пользователя с ролью 'Автор комментария'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-10606
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить тип сотрудника employeeCase</li>
     * <li>Создать лицензированного сотрудника со всеми правами employee типа employeeCase</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Поместить employee в userGroup</li>
     * <li>Создать тип команды teamCase</li>
     * <li>Для teamCase создать профиль profile (Лицензированные пользователи, роль 'Автор комментария', userGroup)</li>
     * <li>Для profile в teamCase выдать все права кроме: "Восстановление объекта из архива"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под employee</li>
     * <li>Создать команду userTeam типа teamCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствия права на "Восстановление объекта из архива" для типа Команды</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightUnArchiveCaseForUserLicensedWithRoleAuthorComment()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new CommentAuthorRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        AbstractRoleContext[] allRoles = AbstractRoleContext.getAllRolesForAuthor(roleContext);
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.UNARCHIVE);
        //Выполнение действия и проверки
        rightContext.setRight(rights, currentUser, allRoles);
        contextActions.absenceRightUnArchiveActions(currentUser);
    }

    /**
     * Тестирование отсутствия права "Добавление объекта" для типа Команды для роли 'Сотрудник'
     * (лиц. пользователь, состоящий в группе пользователей)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-15018
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employeee</li>
     * <li>Создать тип класса Команда teamCase</li>
     * <li>Создать команду team типа teamCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В teamCase выдать все права кроме права на добавление объекта для роли Сотрудник,
     *  группа userGroup</li>
     * <li>На карточку teamCase добавить контент content типа Спиок объектов</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в content отсутствует кнопка Добавить</li>
     * </br>
     */
    @Test
    public void testEmployeeAddBOLicensedEmployeeRole()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.ADD);
        //Выполнение действия и проверки
        rightContext.setRight(rights, currentUser, roleContext);
        contextActions.noAddActions(currentUser);
    }

    /**
     * Тестирование отсутствия права "Добавление объекта" для типа Команды для роли 'Сотрудник' 
     * (лиц. пользователь, входящий в отдел из группы пользователей)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-15018
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Команда teamCase</li>
     * <li>Создать команду team типа teamCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее ou</li>
     * <li>В teamCase выдать все права кроме права на добавление объекта для роли Сотрудник,
     *  группа userGroup</li>
     * <li>На карточку teamCase добавить контент content типа Спиок объектов</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в content отсутствует кнопка Добавить</li>
     * </br>
     */
    @Test
    public void testOuAddBOLicensedEmployeeRole()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.ADD);
        //Выполнение действия и проверки
        rightContext.setRight(rights, roleContext.getUserOu(), roleContext);
        contextActions.noAddActions(currentUser);
    }

    /**
     * Тестирование отсутствия права "Добавление объекта" для типа Команды для роли 'Сотрудник' 
     * (лиц. пользователь, входящий в команду из группы пользователей)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-15018
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Команда teamCase</li>
     * <li>Создать команду team типа teamCase</li>
     * <li>Добавить сотрудника employee в команду team</li>
     * <li>Создать группу пользователей userGroup, добавить в нее team</li>
     * <li>В teamCase выдать все права кроме права на добавление объекта для роли Сотрудник,
     *  группа userGroup</li>
     * <li>На карточку teamCase добавить контент content типа Спиок объектов</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что в content отсутствует кнопка Добавить</li>
     * </br>
     */
    @Test
    public void testTeamAddBOLicensedEmployeeRole()
    {
        //Подготовка 
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(emplCase);
        Bo employee = DAOEmployee.create(emplCase, ou, false, true);
        DSLBo.add(employee);

        MetaClass teamCase = DAOTeamCase.create();
        DSLMetaClass.add(teamCase);
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(team);
        DSLTeam.addEmployees(team, employee);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, team);

        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(emplProfile);
        DSLSecurityProfile.grantAllPermissions(emplProfile);
        DSLSecurityProfile.removeRights(DAOTeamCase.createClass(), emplProfile, AbstractBoRights.ADD);

        ContentForm content = DAOContentCard.createObjectAdvList(teamCase.getFqn(), DAOTeamCase.createClass());
        DSLContent.add(content);

        //Действия
        GUILogon.login(employee);
        GUIBo.goToCard(team);

        //Проверки
        content.advlist().toolPanel().asserts().buttonsAbsence(GUIAdvListXpath.BTN_ADD);
    }

    /**
     * Тестирование отсутствия права "Просмотр истории событий" для типа Команды для Лицензированного пользователя с
     * ролью 'Сотрудник'
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00239
     * http://sd-jira.naumen.ru/browse/NSDWRK-15018
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Отдел ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип класса Сотрудник emplCase</li>
     * <li>Создать сотрудника employee типа emplCase</li>
     * <li>Создать тип класса Команда teamCase</li>
     * <li>Создать команду team типа teamCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее employee</li>
     * <li>В teamCase выдать все права кроме права на просмотр истории событий для роли Сотрудник,
     *  группа userGroup</li>
     * <li>На карточку teamCase добавить контент content типа История изменений объекта</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под employee</li>
     * <li>Проверить, что отсутствует контент content</li>
     * </br>
     */
    @Test
    public void testViewEventToTeamForLicensedUserWithEmployeeRole()
    {
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        AbstractRoleContext[] allRoles = AbstractRoleContext.getAllRolesForAuthor(roleContext);
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.addAll(Arrays.asList(AbstractBoRights.allRightWithoutChangeState()));
        rights.remove(AbstractBoRights.VIEW_EVENT);
        rightContext.setRight(rights, currentUser, allRoles);
        //Выполнение действия и проверки
        contextActions.noViewEventAction(currentUser);
    }
}