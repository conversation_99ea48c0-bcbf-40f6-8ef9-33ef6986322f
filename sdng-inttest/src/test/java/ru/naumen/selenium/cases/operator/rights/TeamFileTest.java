package ru.naumen.selenium.cases.operator.rights;

import java.nio.file.Paths;
import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.rights.impl.TeamFileContext;
import ru.naumen.selenium.casesutil.rights.impl.actions.FileTestActions;
import ru.naumen.selenium.casesutil.rights.interfaces.IFileContext;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.security.rights.IRight;
import ru.naumen.selenium.security.role.AbstractRoleContext;

import java.util.ArrayList;

/**
 * Тестирование блока прав Работа с прикрепленными файлами для Команды
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
 * <AUTHOR>
 * @since 21.01.2013
 */
//Только для карточки объекта 
public class TeamFileTest extends AbstractTestCase
{
    /**Контекст для тестирования группы прав*/
    private static IFileContext rightContext;

    /**Реализация тестовых действий для файлов*/
    private static FileTestActions contextActions;

    @BeforeClass
    public static void prepareFixture()
    {
        rightContext = new TeamFileContext();
        contextActions = new FileTestActions(rightContext);
    }

    /**
     * Тестирование права доступа "Добавление файлов" для команды
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать команду team тима teamCase</li>
     * <li>Создать контент fileList Список файлов в teamCase</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать ou типа ouCase</li>
     * <li>Создать сотрудника employee типа employeeCase с лицензией</li>
     * <li>Для userCase создать профиль profile без прав(Лицензированные пользователи, роль сотрудник, userGroup)</li>
     * <br>
     * <li>Для profile в блоке Действия с объектом оставить только права:
     * "Добавление файлов",
     * "Просмотр файлов"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee</li>
     * <li>Зайти в карточку team</li>
     * <li>Нажать кнопку Добавить файл</li>
     * <li>Выбрать файл</li>
     * <li>Подтвердить</li>
     * <br>
     * <b>Проверки</b>
     * <li>В карточке team: файл добавлен</li>
     */
    @Test
    public void testAddFileOnCard()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.add(AbstractBoRights.VIEW_CARD);
        rights.add(AbstractBoRights.ADD_FILE);
        rights.add(AbstractBoRights.LIST_FILE);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.addFileAction(currentUser);
    }

    /**
     * Тестирование права доступа "Удаление файлов" для команды
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать команду team типа teamCase</li>
     * <li>Создать контент fileList Список файлов в teamCase</li>
     * <li>Добавить файл для team</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать ou типа ouCase</li>
     * <li>Создать сотрудника employee типа employeeCase с лицензией</li>
     * <li>Для userCase создать профиль profile без прав(Лицензированные пользователи, роль сотрудник, userGroup)</li>
     * <br>
     * <li>Для profile в блоке Действия с объектом оставить только права:
     * "Удаление файлов",
     * "Просмотр файлов"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee</li>
     * <li>Зайти в карточку team</li>
     * <li>Нажать кнопку Удалить файл</li>
     * <li>Выбрать файл</li>
     * <li>Подтвердить</li>
     * <br>
     * <b>Проверки</b>
     * <li>В карточке team: файл удален</li>
     */
    @Test
    public void testDeleteFile()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.add(AbstractBoRights.VIEW_CARD);
        rights.add(AbstractBoRights.DELETE_FILE);
        rights.add(AbstractBoRights.LIST_FILE);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.deleteFileAction(currentUser);
    }

    /**
     * Тестирование права доступа "Просмотр файлов" для команды
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать команду team тима teamCase</li>
     * <li>Создать контент fileList Список файлов в teamCase</li>
     * <li>Добавить файл для team</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать ou типа ouCase</li>
     * <li>Создать сотрудника employee типа employeeCase с лицензией</li>
     * <li>Для userCase создать профиль profile без прав(Лицензированные пользователи, роль сотрудник, userGroup)</li>
     * <br>
     * <li>Для profile в блоке Действия с объектом оставить только права:
     * "Просмотр файлов"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee</li>
     * <li>Зайти в карточку team</li>
     * <br>
     * <b>Проверки</b>
     * <li>В карточке team в контенте fileList отсутствуют кнопки добавить и удалить файл/ Присутствует добавленый
     * файл</li>
     */
    @Test
    public void testListOnlyFile()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.add(AbstractBoRights.VIEW_CARD);
        rights.add(AbstractBoRights.LIST_FILE);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.listFileAction(currentUser);
    }

    /**
     * Тестирование отсутствия прав доступа на файлы для команды
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать команду team тима teamCase</li>
     * <li>Создать контент fileList Список файлов в teamCase</li>
     * <li>Добавить файл для team</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать ou типа ouCase</li>
     * <li>Создать сотрудника employee типа employeeCase с лицензией</li>
     * <li>Для userCase создать профиль profile без прав(Лицензированные пользователи, роль сотрудник, userGroup)</li>
     * <br>
     * <li>Для profile в блоке Действия с объектом оставить только права:
     * "Просмотр файлов"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee</li>
     * <li>Зайти в карточку team</li>
     * <br>
     * <b>Проверки</b>
     * <li>В карточке team в контенте fileList отсутствуют кнопки добавить и удалить файл, отсутствует файл</li>
     */
    @Test
    public void testNoRights()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new EmployeeRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.add(AbstractBoRights.VIEW_CARD);
        rightContext.setRight(rights, currentUser, roleContext);
        //Допнастройка
        DSLFile.add(rightContext.getObject(), DSLFile.FILE_FOR_UPLOAD_VIEW);
        String fileName = Paths.get(DSLFile.FILE_FOR_UPLOAD_VIEW).toFile().getName();
        //Выполнение действия и проверки
        contextActions.noRightsAction(currentUser, fileName);
    }
}
