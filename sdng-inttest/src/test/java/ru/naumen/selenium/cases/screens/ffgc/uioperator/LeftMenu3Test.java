package ru.naumen.selenium.cases.screens.ffgc.uioperator;

import static ru.naumen.selenium.screenmaker.differ.AssertConfiguration.screenMaker;

import java.io.File;

import org.junit.After;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.GUINavSettings;
import ru.naumen.selenium.casesutil.operator.GUIFavorites;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.BrowserTS;
import ru.naumen.selenium.core.screen.LayoutConstants;
import ru.naumen.selenium.layout.LayoutTestCase;
import ru.naumen.selenium.layout.LayoutTester;
import ru.naumen.selenium.layout.LayoutXPath;
import ru.naumen.selenium.layout.models.LayoutBo;
import ru.naumen.selenium.screenmaker.ScreenUtils;
import ru.naumen.selenium.screenmaker.ScreenshotInfo;
import ru.naumen.selenium.screenmaker.differ.AssertConfiguration.AssertConfigurationBuilder;

/**
 * Тестирование левого меню в интерфейсе оператора
 *
 * <AUTHOR>
 * @since 20 янв. 2021 г.
 *
 */
public class LeftMenu3Test extends LayoutTestCase
{
    private static final String SCREENSHOT_DIR = LayoutConstants.SCREENSHOT_DIR + "operator" + File.separator +
                                                 "navpanel" + File.separator;

    @After
    public void afterTest()
    {
        BrowserTS.get().clearLocalStorage();
    }

    /**
     * Тестирование отображения разделов в левом меню в стиле форматирования «секция»
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$97358266
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * В систему добавлен отдел «Отдел для левого меню» и в него пользователь Менюшкин Лаврентий Петрович mlp/123, он
     * добавлен в группу «Группа для меню», создан профиль прав «Для меню» для роли «Сотрудник» и группы «Группа для
     * меню». В меню добавлен раздел «Кейсы», в него раздел «Секции», и в него еще три раздела с форматированием
     * «Секция», два из них содержат вложенные ссылки.
     *
     * <b>Выполнение действий</b>
     * <li>Войти в систему под пользователем uuid:employee$76401 логин/пароль mlp/123</li>
     * <li>Развернуть штору меню по плитке Показать все меню</li>
     * <li>Развернуть раздел «Кейсы» и в нем раздел «Секции»</li>
     * <li>Развернуть раздел «Секция вторая, содержащая подпункты»</li>
     * <li>Навести указатель мыши на шеврон элемента «Секция вторая, содержащая подпункты»</li>
     *
     * <b>Проверка:</b>
     * <li>Секции выделяются жирным шрифтом, серым цветом написания и полосками над пунктом меню. Секции без
     * наведения на них указателя мыши не имеют шевронов, при наведении шеврон отображается, элемент подсвечивается
     * серым, у шеврона появляется темно-серая подложка. Длинное название перед шевроном обрезается с многоточием,
     * многоточие уходит под градиент. Все пункты, содержащиеся внутри секции, на которую наведен указатель мыши,
     * подсвечиваются светло-серым. После раскрытой или последней в списке секции нет черты. </li>
     */
    @Test
    public void testDisplaySectionTileInLeftMenu()
    {
        GUILogon.login(LayoutBo.EMPL1_9);
        GUINavigational.showNavPanelOperator();

        GUINavigational.expandLeftMenuItems("Кейсы", "Секции");
        GUINavigational.expandLeftMenuItems("Секция вторая, содержащая подпункты");

        String sectionXpath = String.format(GUINavigational.MENU_ITEM_BY_TITLE, "Секция вторая, содержащая подпункты");
        String chevronXpath = String.format(GUINavigational.MENU_ITEM_BY_TITLE_NODE_EXPANDED,
                "Секция вторая, содержащая подпункты");
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR +
                                                             "displaySectionTileInLeftMenu.png");
        assertImages(expected, (maker) ->
        {
            LayoutTester.moveTo(sectionXpath, 1, 1);
            LayoutTester.moveTo(chevronXpath, 1, 1);
            return maker.takeScreenshotByXpath(GUINavigational.NAV_TREE_PANEL);
        }, new AssertConfigurationBuilder().screenMaker(
                screenMaker().ignoredElements(LayoutXPath.FOOTER_PANEL_XPATH)));
    }

    /**
     * Тестирование отображения формы добавления элемента в Избранное с сообщением-предупреждением о повторном
     * добавлении элемента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$105890621
     * <br>
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     *     <li>В систему добавлен пользователь Избранный Гарри Джеймсович, логин/пароль: ihj / 123. Под его учетной
     *     записью настроено Избранное</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Войти в систему под пользователем uuid:employee$85801, логин/пароль: ihj / 123</li>
     * <li>Нажать на иконку добавления текущей страницы пользователя в Избранное</li>
     * <b>Проверка.</b>
     * <li>Заголовок открывшейся формы "Добавление страницы в избранное", форма имеет иконку сворачивания в шапке
     * справа. На форме имеется сообщение-предупреждение о повторном добавлении элемента в Избранное, далее идет поле
     * Название, нередактируемое поле "Вложить в папку" и контрол открытия формы выбора папки "Изменить", он имеет
     * акцентный цвет</li>
     * <ol>
     */
    @Test
    public void testFavoritesAddForm()
    {
        // Действия
        GUILogon.login(LayoutBo.EMPL1_13);
        GUIFavorites.openAddFavoritesForm();

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR +
                                                             "favoritesAddForm.png");
        assertImages(expected, (maker) -> maker.takeScreenshotByXpath(GUIFavorites.ADD_FAVORITES_FORM));
    }

    /**
     * Тестирование отображения формы редактирования Избранного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$105890621
     * <br>
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     *     <li>В систему добавлен пользователь Избранный Гарри Джеймсович, логин/пароль: ihj / 123. Под его учетной
     *     записью настроено Избранное</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Войти в систему под пользователем uuid:employee$85801, логин/пароль: ihj / 123</li>
     * <li>Развернуть левое меню по плитке "Показать все меню"</li>
     * <li>Открыть форму редактирования Избранного</li>
     * <b>Проверка.</b>
     * <li>Заголовок открывшейся формы "Редактирование избранного", форма имеет иконку сворачивания в шапке справа.
     * На форме слева вверху есть кнопка «Добавить папку», ниже расположена таблица элементов Избранного. Слева у
     * элементов одного уровня присутствуют стрелки перемещения, справа в конце строки иконки редактирования и
     * удаления. Вложенные элементы отображаются с отступом относительно родительских. У папок имеется иконка папки.
     * Длинные названия элементов переносятся на вторую и далее строки</li>
     * <ol>
     */
    @Test
    public void testFavoritesEditForm()
    {
        // Действия
        GUILogon.login(LayoutBo.EMPL1_13);
        GUINavigational.showNavPanelOperator();
        GUIFavorites.clickEditFavorites();

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR +
                                                             "favoritesEditForm.png");
        assertImages(expected, (maker) -> maker.takeScreenshotByXpath(GUIFavorites.EDIT_FAVORITES_FORM));
    }

    /**
     * Тестирование отображения Избранного с папками в левом меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$105890621
     * <br>
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     *     <li>В систему добавлен пользователь Избранный Гарри Джеймсович, логин/пароль: ihj / 123. Под его учетной
     *     записью настроено Избранное</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Войти в систему под пользователем uuid:employee$85801, логин/пароль: ihj / 123</li>
     * <li>Развернуть левое меню по плитке "Показать все меню"</li>
     * <li>Развернуть Избранное, в нем развернуть все папки: "Проекты", "Мои проекты"</li>
     * <li>Навести указатель мыши на иконку редактирования Избранного</li>
     * <b>Проверка.</b>
     * <li>Область Избранного подсвечена серым фоном, иконка редактирования Избранного имеет темно-серую подложку. В
     * избранном находятся папки и вложенные в них элементы и папки, вложенные элементы расположены с отступом
     * относительно родительских. Названия папок обрезаются в одну строку, названия элементов — в три. Пустые папки
     * отображаются светло-серым шрифтом (в скрин можно брать только избранное; историю и далее можно отбросить)</li>
     * <ol>
     */
    @Test
    public void testNavPanelFavorites()
    {
        // Действия
        GUILogon.login(LayoutBo.EMPL1_13);
        GUINavigational.showNavPanelOperator();
        GUIFavorites.clickFavorites();
        GUINavigational.expandLeftMenuItems("Проекты", "Мои проекты");

        String favoritesXpath = String.format("(%s)[1]", LayoutXPath.MENU_IEM + LayoutXPath.MENU_IEM);

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR +
                                                             "navPanelFavorites.png");
        assertImages(expected, (maker) ->
        {
            LayoutTester.moveTo(GUINavSettingsOperator.FAVORITES_MENU_ITEM_TITLE, 1, 1);
            LayoutTester.moveTo(GUIFavorites.EDIT_FAV_BTN, 1, 1);
            return maker.takeScreenshotByXpath(favoritesXpath);
        });
    }

    /**
     * Тестирование отображения панели быстрого доступа левого меню, если включена только неизменяемая область панели
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$97358266
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * В систему добавлен отдел «Отдел для левого меню» и в него пользователь Менюшкин Лаврентий Петрович mlp/123, он
     * добавлен в группу «Группа для меню», создан профиль прав «Для меню» для роли «Сотрудник» и группы «Группа для
     * меню». В меню добавлены элементы Избранное, История, Компания, несколько разделов и ссылок на страницы, для
     * них созданы плитки на панели быстрого доступа, часть в неизменяемой области, другая часть — в пользовательской
     * области панели.
     *
     * <b>Подготовка</b>
     * <li>В ИА в «Настройка системы» - «Интерфейс и навигация» - «Навигация» в разделе «Видимость» отключить
     * настройку «Показывать пользовательскую область панели быстрого доступа»</li>
     *
     * <b>Выполнение действий</b>
     * <li>Войти в систему под пользователем uuid:employee$76401 логин/пароль mlp/123</li>
     *
     * <b>Проверка:</b>
     * <li>В панели быстрого доступа левого меню отображается 5 плиток: бургер, избранное, история, компания и РС,
     * отчерчивающей полосы после них нет. </li>
     */
    @Test
    public void testQuickAccessPanelDisplayTurnOnOnlyConstantArea()
    {
        GUILogon.asSuper();
        GUINavigational.goToNavigationSettings();
        GUINavSettings.clickOpenEditNavSettings();
        GUINavSettings.setFormNavSettingsUserValues(false);
        // Добавлено дополнительное ожидание для устранения нестабильности в рамках задачи NSDAT-12286 - иногда
        // требуется больше времени для закрытия формы
        GUIForm.applyModalForm(60);

        try
        {
            GUILogon.login(LayoutBo.EMPL1_9);

            ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR +
                                                                 "quickAccessPanelDisplayTurnOnOnlyConstantArea.png");
            assertImages(expected, (maker) ->
                    maker.takeScreenshotByXpath(GUINavSettingsOperator.QUICK_ACCESS_MENU));
        }
        finally
        {
            GUILogon.asSuper();
            GUINavigational.goToNavigationSettings();
            GUINavSettings.clickOpenEditNavSettings();
            GUINavSettings.setFormNavSettingsUserValues(true);
            // Добавлено дополнительное ожидание для устранения нестабильности в рамках задачи NSDAT-12286 - иногда
            // требуется больше времени для закрытия формы
            GUIForm.applyModalForm(60);
        }
    }

    /**
     * Тестирование отображения панели быстрого доступа левого меню, если включена только пользовательская область
     * панели
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$97358266
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * В систему добавлен отдел «Отдел для левого меню» и в него пользователь Менюшкин Лаврентий Петрович mlp/123, он
     * добавлен в группу «Группа для меню», создан профиль прав «Для меню» для роли «Сотрудник» и группы «Группа для
     * меню». В меню добавлены элементы Избранное, История, Компания, несколько разделов и ссылок на страницы, для
     * них созданы плитки на панели быстрого доступа, часть в неизменяемой области, другая часть — в пользовательской
     * области панели.
     *
     * <b>Подготовка</b>
     * <li>В ИА в «Настрйка системы» - «Интерфейс и навигация» - «Навигация» в разделе «Видимость» отключить настройку
     * «Показывать неизменяемую область панели быстрого доступа»</li>
     *
     * <b>Выполнение действий</b>
     * <li>Войти в систему под пользователем uuid:employee$76401 логин/пароль mlp/123</li>
     *
     * <b>Проверка:</b>
     * <li>В панели быстрого доступа левого меню отображается 4 плитки: список, РИ, человечек и ОТ. Отчерчивающей
     * полосы до и после них нет.</li>
     */
    @Test
    public void testQuickAccessPanelDisplayTurnOnOnlyUserArea()
    {
        GUILogon.asSuper();
        GUINavigational.goToNavigationSettings();
        GUINavSettings.clickOpenEditNavSettings();
        GUINavSettings.setFormNavSettingsAdminValues(false);
        GUIForm.applyModalForm();

        try
        {
            GUILogon.login(LayoutBo.EMPL1_9);

            ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR +
                                                                 "quickAccessPanelDisplayTurnOnOnlyUserArea.png");
            assertImages(expected, (maker) ->
                    maker.takeScreenshotByXpath(GUINavSettingsOperator.QUICK_ACCESS_MENU));
        }
        finally
        {
            GUILogon.asSuper();
            GUINavigational.goToNavigationSettings();
            GUINavSettings.clickOpenEditNavSettings();
            GUINavSettings.setFormNavSettingsAdminValues(true);
            GUIForm.applyModalForm();
        }
    }

    /**
     * Тестирование отображения скролла в панели быстрого доступа левого меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$97358266
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * В систему добавлен отдел «Отдел для левого меню» и в него пользователь Менюшкин Скролл Денисович msd/123, он
     * добавлен в группу «Группа для меню скролл», создан профиль прав «Для меню скролл» для роли «Сотрудник» и
     * группы «Группа для меню скролл». В левом меню создан раздел Кейсы, в нем раздел «Плитки внутри» и в нем 12
     * различных элементов. Видимость их ограничена профилем «Для меню скролл» Для этих элементов создано 12 плиток в
     * панели быстрого доступа.
     *
     * <b>Выполнение действий</b>
     * <li>Войти в систему под пользователем «Менюшкин Скролл Денисович» uuid:employee$76402 логин/пароль msd/123 </li>
     * <li>Раскрыть штору левого меню по плитке Показать все меню (бургер)</li>
     * <li>Навести указатель мыши на плитку Д5 </li>
     *
     * <b>Проверка:</b>
     * <li>В панели быстрого доступа отображается скролл, белый, полупрозрачный. Внизу плитки, которые не влезли,
     * скрываются за белым градиентом. Плитка-ссылка Д5 выделена белой подложкой. </li>
     */
    @Test
    public void testScrollDisplayOfQuickAccessPanel()
    {
        tester.resizeWindow(BrowserTS.STANDARD_SCREEN_SIZE.width, 600);

        try
        {
            GUILogon.login(LayoutBo.EMPL1_10);

            GUINavigational.showNavPanelOperator();

            String tileXpath = String.format(GUINavigational.TITLE_MENU_NAV_USER, "Для массовых плиток 5");
            ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR +
                                                                 "scrollDisplayOfQuickAccessPanel.png");
            assertImages(expected, (maker) ->
            {
                LayoutTester.moveTo(tileXpath, 1, 1);
                return maker.takeScreenshotByXpath(GUINavSettingsOperator.QUICK_ACCESS_MENU);
            });
        }
        finally
        {
            tester.resizeWindow(BrowserTS.STANDARD_SCREEN_SIZE.width, BrowserTS.STANDARD_SCREEN_SIZE.height);
        }
    }

    /**
     * Тестирование отображения векторных иконок разных размеров в панели быстрого доступа левого меню в интерфейсе
     * оператора
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00638
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$116509143
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     * <li>В справочник «Иконки для элементов управления (векторные)» загружены 3 иконки в векторном формате. 1 — без
     * указания размеров в коде иконки, 2 — с указанием размеров, превышающих 16х16 px, и 3 — с указанием размеров
     * меньше чем 16х16px. В настройках навигации добавлен элемент левого меню (раздел). Векторные иконки и 3 вложенных
     * в него элемента: раздел «Аватар без размеров», ссылка на карточку «Аватар большой» и ссылка на карточку «Аватар
     * маленький», для которых в качестве маркера установлены соответствующие иконки. Для этих трех элементов созданы
     * плитки в панели быстрого доступа левого меню. В системе создан пользователь Векторный Василий Петрович vvp/123.
     * Он добавлен в созданную группу пользователей «Группа для тестирования справочника векторных иконок». Создан
     * профиль прав «Профиль для векторных иконок», настроенный на эту группу. Для элемента левого меню «Векторные
     * иконки» установлено ограничение прав на этот профиль.</li>
     * </ul>
     * <b>Действия:</b>
     * <li>Войти в систему под пользователем uuid:employee$93301 vvp/123</li>
     * <li>Открыть левое меню кликом по плитке «Аватар без размеров» (первый аватар из трех) панели быстрого
     * доступа</li>
     * <li>Навести указатель мыши на плитку «Аватар маленький» (третий аватар)</li>
     * <b>Проверка:</b>
     * <li>Все три иконки аватара отображаются в панели быстрого доступа левого меню серым цветом, имеют размер
     * 16x16px. Первый аватар имеет голубую подложку, третий — белую.</li>
     * <ol>
     */
    @Test
    public void testDisplayIconsInQuickPanelOfLeftMenu()
    {
        //Действия
        GUILogon.login(LayoutBo.EMPL1_17);
        tester.click(String.format(GUINavigational.TITLE_MENU_NAV_USER, "Аватар без размеров"));
        LayoutTester.moveTo(String.format(GUINavigational.TITLE_MENU_NAV_USER, "Аватар маленький"), 0, 0);

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR +
                                                             "displayIconsInQuickPanelOfLeftMenu.png");
        assertImages(expected, (maker) ->
                maker.takeScreenshotByXpath(GUINavSettingsOperator.QUICK_ACCESS_MENU + String.format(Div.ANY,
                        "scrollableArea")));
    }

}

