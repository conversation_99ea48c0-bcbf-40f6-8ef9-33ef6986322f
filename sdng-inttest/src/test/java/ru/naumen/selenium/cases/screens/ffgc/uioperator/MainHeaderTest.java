package ru.naumen.selenium.cases.screens.ffgc.uioperator;

import static ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator.MENUITEM_BY_TITLE_PATTERN;
import static ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator.NAVIGATION_MENU_FRAME;
import static ru.naumen.selenium.screenmaker.differ.AssertConfiguration.screenMaker;

import java.io.File;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUISearch;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.security.GUIPCModule;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.BrowserTS;
import ru.naumen.selenium.core.BrowserTS.WebBrowserType;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.WebTester.ScrollAlignment;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.core.screen.LayoutConstants;
import ru.naumen.selenium.layout.LayoutTestCase;
import ru.naumen.selenium.layout.LayoutTester;
import ru.naumen.selenium.layout.LayoutXPath;
import ru.naumen.selenium.layout.models.LayoutBo;
import ru.naumen.selenium.layout.models.LayoutContentTab;
import ru.naumen.selenium.screenmaker.ScreenUtils;
import ru.naumen.selenium.screenmaker.ScreenshotInfo;
import ru.naumen.selenium.screenmaker.differ.AssertConfiguration.AssertConfigurationBuilder;
import ru.yandex.qatools.ashot.cropper.indent.IndentCropper;

/**
 * Тестирование верстки элементов шапки системы
 * <p>
 * Выполненная настройка на дампе:
 * </p>
 * <ul>
 * <li>Добавили в верхнее меню элементы:
 *  <ul>
 *      <li>не вложенные в разделы:
 *          <ul>
 *              <li>кнопка добавления объектов — название "Добавить срочный запрос для скорейшего решения",
 *              содержимое "Запрос"</li>
 *              <li>ссылка на карточку — название "Service Desk", Вкладка карточки - Компания(root)</li>
 *              <li>раздел меню — "Новый раздел меню для вложенных подразделов"</li>
 *              <li>история — название "."</li>
 *              <li>избранное — название "избранные страницы Service Desk"</li>
 *          </ul>
 *      </li>
 *      <li>вложенные в раздел "Новый раздел меню для вложенных подразделов":
 *          <ul>
 *              <li>кнопка добавления объектов — название "Добавить новый отдел в структуру компании", содержимое -
 *              "Отдел"</li>
 *              <li>ссылка на карточку — название "Сссылканакарточкукомпаниивкоторойнетпробелов", вкладка карточки -
 *              Компания(root)</li>
 *              <li>раздел меню — название "Подраздел с подразделами"</li>
 *          </ul>
 *      </li>
 *      <li>вложенные в раздел "Подраздел с подразделами":
 *          <ul>
 *              <li>избранное — название "Избранное в подразделе"</li>
 *              <li>история — название "!".</li>
 *          </ul>
 *      </li>
 *      <li>Добавлен в избранное пользователя naumen отдел "Отдел" (uuid:ou$4201)</li>
 *  </ul>
 * </li>
 * </ul>
 *
 * <AUTHOR>
 * @since 19.11.2019
 */
public class MainHeaderTest extends LayoutTestCase
{
    private static final String SCREENSHOT_DIR = LayoutConstants.SCREENSHOT_DIR + "operator" + File.separator +
                                                 "mainheader" + File.separator;

    /**
     * Тестирование наличия тени у всплывающей подсказки элемента "Продвинутого поиска"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00838
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     * <li>В настройках Поиска включена настройка "Использовать язык продвинутого поиска". В персональных настройках
     * пользователя ako, установлены настройки: Отображать элементы продвинутого поиска — включено; Использовать язык
     * продвинутого поиска — включено.</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Войти под пользователем uuid: employee$17901</li>
     * <li>Развернуть подсказку по продвинутому поиску</li>
     * <b>Проверки.</b>
     * <li>Всплывающая подсказка обрамляется тенью</li>
     * <ol>
     */
    @Test
    public void testAdvancedSearchPopupShadow()
    {
        // Действия
        GUILogon.login(LayoutBo.EMPL1_1);
        GUISearch.openAdvancedSearchHelpPopup();

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "advancedSearchPopupShadow.png");
        // немного увеличиваем границу вокруг подсказки, чтобы на скриншоте была её тень
        assertImages(expected, (maker) -> maker.takeScreenshotByXpath(LayoutXPath.ANY_POPUP),
                new AssertConfigurationBuilder()
                        .screenMaker(screenMaker().imageCropper(new IndentCropper(10))));
    }

    /**
     * Тестирование отображения цепочки хлебных крошек и названия объекта в шапке карточки объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00571
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00613
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     *    <li>Создан тип  "1. Шапка и меню" в классе "1. Основной класс для тестирования"</li>
     *    <li>Выполнены настройки хлебных крошек:
     *      <ul>
     *          <li>Объекты — "1. Основной класс для тестирования: 1. Шапка и меню", атрибуты связи — Ответственный
     *          (сотрудник)</li>
     *          <li>Объекты — Отдел, атрибуты связи — Родитель</li>
     *          <li>Объекты — Сотрудник, атрибуты связи — Отдел</li>
     *      </ul>
     *    </li>
     *    <li>Создан объект "Тестирование отображения цепочки хлебных крошек и названия объекта в шапке карточки
     *    объекта: цепочка переносятся на вторую строку и не наезжает на иконки добавления в избранное и перехода на
     *    домашнюю страницу" (uuid:mainTestClass$5201)</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Уменьшить окно браузера до 1024х900</li>
     * <li>Перейти на карточку объекта uuid:mainTestClass$5201</li>
     * <b>Проверки.</b>
     * <li>Хлебные крошки в шапке карточки объекта содержат: кнопку "Назад", название компании, название отдела, имя
     * сотрудника, название объекта, - и отображаются в 4 строки, не наезжая на иконки. Название объекта
     * расположено под хлебными крошками в две строки. Первая строка хлебных крошек расположена на одном уровне с
     * пунктом "Избранное" в левом навигационном меню</li>
     * <ol>
     */
    @Test
    @IgnoreConfig(cause = "NSDSTAB-137")
    public void testBreadCrumbsBoHeader()
    {
        //Действия
        tester.resizeWindow(1024, 900);
        try
        {
            GUILogon.asSuper();
            GUINavigational.goToOperatorUI();
            GUINavigational.showNavPanelOperator();
            GUIBo.goToCard(LayoutBo.USERBO1_1_1);

            //Проверка
            ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "breadCrumbsBoHeader.png");
            assertImages(expected, (maker) -> maker.takeScreenshotByXpath(LayoutXPath.BO_HEADER_XPATH,
                    GUINavigational.NAV_TREE_PANEL), new AssertConfigurationBuilder()
                    .screenMaker(screenMaker().ignoredElements(LayoutXPath.FOOTER_PANEL_XPATH)));
        }
        finally
        {
            tester.resetWindowSize();
        }
    }

    /**
     * Тестирование отображения кнопок разного вида с названиями разной длины на панели действий в шапке карточки
     * объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00123
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     *    <li>Добавлено ДПС "ДПС return true", код "buttons", Объекты - "1. Основной класс для тестирования", Событие
     *    - "Пользовательское событие", Действие - "скрипт", скрипт: return true; ДПС включено.</li>
     *    <li>Добавлено ДПС "Выключенное ДПС return true", код "VykluchennoeDPS", Объекты - "1. Основной класс для
     *    тестирования", Событие - "Пользовательское событие", Действие - "скрипт", скрипт: return true</li>
     *    <li>Создан тип "2. Кнопки на панелях действий" в классе "1. Основной класс для тестирования", в нем
     *    настроены элементы панели действий в шапке карточки объекта следующим образом:
     *      <ul>
     *          <li>Название - "Кнопка редактирования с иконкой и подписью длиной 64 символа ааа", действие -
     *          "Редактировать", иконка - "Редактировать"</li>
     *          <li>Разделитель групп</li>
     *          <li>Название - ".", Внешний вид - "Кнопка с подписью", Действие - "Переместить"</li>
     *          <li>Название - "Удалить", Внешний вид - "кнопка с иконкой", Иконка - "Удалить", действие -
     *          "Удалить"</li>
     *          <li>Разделитель групп</li>
     *          <li>Ряд кнопок с названиями "Очень длинный" "Ряд красивых кнопок" "Для тщательной проверки"
     *          "Переноса" "На следующую" "Строку", Внешний вид - "Кнопка с подписью", действие - "ДПС return true"</li>
     *          <li>Между кнопками "Очень длинный" и "Ряд красивых кнопок — кнопка Название "Невидимая кнопка
     *          выключенного ДПС", Внешний вид - "Кнопка с подписью", Действие - "Выключенное ДПС return true"</li>
     *          <li>2 разделителя групп</li>
     *          <li>Название - "Кнопка перемещения в архив для проверки кнопки-ссылки 64 символа", Внешний вид -
     *          "ссылка", действие "Поместить в архив"</li>
     *          <li>Название - ":", Внешний вид - "Ссылка", действие - "Копировать"</li>
     *          <li>Разделитель групп</li>
     *          <li>Ряд кнопок с названиями "Еще один длинный ряд", "Красивых кнопок-ссылок" "Для тщательной
     *          проверки" "Переноса" "На следующую" "Строку", Внешний вид - "Ссылка", действие - "ДПС return true"</li>
     *          <li>Между кнопками "Еще один длинный ряд", "Красивых кнопок-ссылок" Название "Невидимая ссылка
     *          выключенного ДПС", Внешний вид - "ссылка", Действие - "Выключенное ДПС return true"</li>
     *          <li>Название - "Кнопка в одном ряду со ссылкой", Внешний вид - "Кнопка с подписью", Действие - "ДПС
     *          return true"</li>
     *      </ul>
     *    </li>
     *    <li>Создан объект типа "2. Кнопки на панелях действий" "Тестирование отображения кнопок разного вида с
     *    названиями разной длины на панели действий в шапке карточки объекта."  (uuid:mainTestClass$5601)</li>
     *    <li>Левое навигационное меню развернуто по умолчанию</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Уменьшить окно браузера до 1024х900</li>
     * <li>Перейти на карточку объекта "Тестирование отображения кнопок разного вида с названиями разной длины на
     * панели действий в шапке карточки объекта." (uuid:mainTestClass$5601)</li>
     * <b>Проверки.</b>
     * <li>Кнопки на панели действий карточки объекта отображаются в соответствии с дизайн-макетами</li>
     * <ol>
     */
    @Test
    public void testLongTitleButtonsOnBoHeader()
    {
        //Действия
        tester.resizeWindow(1024, 900);
        try
        {
            GUILogon.asSuper();
            GUINavigational.goToOperatorUI();
            GUINavigational.showNavPanelOperator();
            GUIBo.goToCard(LayoutBo.USERBO1_2_1);

            //Проверка
            ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "longTitleButtonsOnBoHeader.png");
            assertImages(expected, (maker) -> maker.takeScreenshotByXpath(LayoutXPath.BO_HEADER_XPATH));
        }
        finally
        {
            tester.resetWindowSize();
        }
    }

    /**
     * Тестирование отображения полного имени пользователя и элементов "Продвинутого поиска" в верхнем меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00838
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     * <li>В отделе "Отдел" создали пользователя "Апполинария Константиновна Оченьдлиннофамильевская", тип
     * "Сотрудник". Задали ему логин "ako", пароль "123". В настройках Поиска включена настройка "Использовать язык
     * продвинутого поиска". В персональных настройках ользователя ako, установлены настройки: Отображать элементы
     * продвинутого поиска — включено; Использовать язык продвинутого поиска — включено.</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Войти под пользователем uuid: employee$17901</li>
     * <li>Развернуть подсказку по продвинутому поиску</li>
     * <b>Проверки.</b>
     * <li>Переключатель "Использовать язык продвинутого поиска" отображается акцентным цветом в поле поиска,
     * всплывающая подсказка отображается под полем поиска</li>
     * <li>ФИО пользователя отображается полностью в правой части верхнего меню в три строки между окном поиска и
     * кнопкой "Выход"</li>
     * <ol>
     */
    @Test
    public void testMainHeaderAndSearchPopup()
    {
        // Действия
        GUILogon.login(LayoutBo.EMPL1_1);
        GUISearch.openAdvancedSearchHelpPopup();

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "mainHeaderAndSearchPopup.png");
        assertImages(expected, (maker) ->
                maker.takeScreenshotByXpath(LayoutXPath.HEADER_PANEL_XPATH, LayoutXPath.ANY_POPUP));
    }

    /**
     * Тестирование сворачивания вкладок в шапке карточки объекта в меню "Еще"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00021
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     * <li>На карточке объекта типа "3. Вкладки на карточке" созданы вкладки "Первая вкладка с основной информацией об
     * отделе", "Вторая вкладка с историей действий над объектом", "Третья вкладка, призванная свернуться в Еще", ".",
     * "Вкладкасназваниембезспробелов". На них выведены контенты, чтобы вкладки отображались</li>
     * <li>Создан объект типа "3. Вкладки на карточке" "Тестирование сворачивания вкладок в шапке карточки объекта в
     * меню "Еще"" (uuid:mainTestClass$6001)</li>
     * <li>Левое навигационное меню развернуто по умолчанию</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Уменьшить окно браузера до 1024х900</li>
     * <li>Перейти на карточку объекта "Тестирование сворачивания вкладок в шапке карточки объекта в меню «Еще»"
     * (uuid:mainTestClass$6001)</li>
     * <li>Навести курсор на пункт "Третья вкладка, призванная свернуться в Еще" (перевести в состояние hover)</li>
     * <b>Проверки.</b>
     * <li>Меню "Еще" развернулось, содержит три пункта, пункт "Третья вкладка, призванная свернуться в Еще" выделен
     * серым и содержит белый текст."</li>
     * <ol>
     */
    @Test
    public void testMoreTabBoHeader()
    {
        //Действия
        tester.resizeWindow(1024, 900);
        try
        {
            GUILogon.asSuper();
            GUINavigational.goToOperatorUI();
            GUINavigational.showNavPanelOperator();
            GUIBo.goToCard(LayoutBo.USERBO1_3_1);
            // не всегда открывается выпадающий список Еще
            WaitTool.waitMills(1000);
            tester.click(GUITab.MORE_TABS);

            //Проверка
            ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "moreTabBoHeader.png");
            assertImages(expected, (maker) ->
            {
                LayoutTester.moveTo(Div.ID_PATTERN, 5, 5, LayoutContentTab.TAB1_1.getCode());
                return maker.takeScreenshotByXpath(LayoutXPath.BO_HEADER_XPATH,
                        LayoutXPath.ANY_POPUP + GUIXpath.Other.TBODY);
            });
        }
        finally
        {
            tester.resetWindowSize();
        }
    }

    /**
     * Тестирование поведения верхнего меню, содержащего кнопки, разделы и подразделы с длинными названиями
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Действия</b>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Развернуть верхнее меню по пути "Новый раздел меню для вложенных подразделов" - "Подраздел с подразделами"
     * - "Избранное в подразделе" - "Отдел, добавленный в избранное пользователя naumen"</li>
     * <li>Навести курсор на крайний пункт выпавшего меню - "Отдел, добавленный в избранное пользователя naumen"</li>
     * <b>Проверка</b>
     * <li>Выпадающее меню развернулось, отображается три уровня выпадающего меню. Пункты меню "Новый раздел меню для
     * вложенных подразделов" - "Подраздел с подразделами" - "Избранное в подразделе" - "Тестовый отдел седьмого
     * уровня" выделены акцентным цветом, в пункте "Тестовый отдел седьмого уровня" отображается иконка удаления из
     * избранного</li>
     * <ol>
     */
    @Test
    public void testNestedMenuItemsWithLongTitle()
    {
        // Действия
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();
        String menuXpath = String.format(NAVIGATION_MENU_FRAME + MENUITEM_BY_TITLE_PATTERN,
                "Новый раздел меню для вложенных подразделов");
        tester.click(menuXpath);
        GUINavSettingsOperator.moveMouseOverMenuItemByTitle("Подраздел с подразделами");
        GUINavSettingsOperator.moveMouseOverMenuItemByTitle("Избранное в подразделе");
        GUINavSettingsOperator.moveMouseOverMenuItemByTitle("Отдел, добавленный в избранное пользователя naumen");

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "nestedMenuItemsWithLongTitle.png");
        assertImages(expected, (maker) -> maker.takeScreenshotByXpath(LayoutXPath.HEADER_PANEL_XPATH,
                GUIXpath.Div.POPUP_CONTENT));
    }

    /**
     * Тестирование отображения панели модуля проверки прав
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$116018217
     * <br>
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     *     <li>В системе создан пользователь «Компактный Иван Иванович», обладающий лицензией concurrent, 4
     *     различными профилями, ролью Сотрудники, и входящий в группы Основная группа и Тест матрицы группа 1</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Войти в систему под naumen в ИО</li>
     * <li>Открыть панель модуля проверки прав / иконка глаз в шапке /</li>
     * <li>Выбрать в поле Пользователь - Компактный Иван Иванович - uuid:employee$42201</li>
     * <li>Нажать «Отобразить»</li>
     * <b>Проверка.</b>
     * <li>Панель отображается на черном полупрозрачном фоне, в ней имеется заголовок, кнопка сворачивания панели,
     * поле выбора пользователя, две кнопки «отобразить» и «Отключить модуль», а также перечисление лицензий,
     * профилей, ролей и групп пользователя.</li>
     * <ol>
     */
    @Test
    public void testDisplayModuleRight()
    {
        // Действия
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();
        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(LayoutBo.OU1_0, LayoutBo.EMPL1_3);
        GUIPCModule.clickShowButton();

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "displayModuleRight.png");
        assertImages(expected, (maker) ->
        {
            return maker.takeScreenshotByXpath(GUIPCModule.X_MODULE_PANEL);
        });
    }

    /**
     * Тестирование отображения меню Ещё в шапке карточки объекта на английском языке.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00021
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00463
     * <br>
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * На стенд добавлен сотрудник Английский Джон Сноу englishUser/123 с выбранным языком интерфейса English.
     * На карточку объекта типа "3. Вкладки на карточке" класса "1. Основной класс для тестирования" добавлено 5
     * вкладок:
     * "Первая вкладка с основной информацией об отделе", "Вторая вкладка с историей действий над объектами",
     * "Третья вкладка призванная свернуться в "Ещё", ".", "Вкладкасназваниембезпробелов"
     * <br>
     * <b>Действия:</b>
     * <li>Зайти под пользователем englishUser/123.</li>
     * <li>Перейти на карточку объекта uuid:mainTestClass$6001</li>
     * <b>Проверка:</b>
     * (ширина окна браузера 1024px)
     * Последняя вкладка свернулась в More, стрелочка рядом с More не наезжает на текст
     */
    @IgnoreConfig(cause = "NSDPRD-29020", ignoreBrowser = { WebBrowserType.FIREFOX })
    @Test
    public void testDisplayOfMenuAlsoInHeaderObjectCardEN()
    {
        try
        {
            //Действия
            tester.resizeWindow(1024, BrowserTS.STANDARD_SCREEN_SIZE.height);
            GUILogon.login(LayoutBo.EMPL1_18);
            GUIBo.goToCard(LayoutBo.USERBO1_3_1);

            //Проверка
            ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR +
                                                                 "displayOfMenuAlsoInHeaderObjectCardEN.png");
            assertImages(expected, (maker) -> maker.takeScreenshotByXpath(GUIXpath.Div.MAIN_TAB_BAR,
                    String.format(Div.ANY, "tabLayoutPanelContentContainer")));
        }
        finally
        {
            tester.resetWindowSize();
        }
    }

    /**
     * Тестирование отображения меню Ещё в шапке панели вкладок на английском языке.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00021
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00463
     * <br>
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * На стенд добавлен сотрудник Английский Джон Сноу englishUser/123 с выбранным языком интерфейса English.
     * На карточку объекта типа "8. Контенты" подтипа "8.3 Панель вкладок" класса "1. Основной класс для тестирования"
     * добавлен контент типа "Панель вкладок" (код: tabs_Proveryaem) с 6 вкладками: "Проверяем", "Сворачивание",
     * "Вкладок в ещё", "И подсчет количества", "Элементов на вкладке", "Эта вкладка уже не влезет"
     * <br>
     * <b>Действия:</b>
     * <li>Зайти под пользователем englishUser/123.</li>
     * <li>Перейти на карточку объекта uuid:mainTestClass$44101</li>
     * <b>Проверка:</b>
     * (ширина окна браузера 1124px)
     * Последние три вкладки свернулись в More, стрелочка рядом с More не наезжает на текст
     */
    @IgnoreConfig(cause = "NSDPRD-29020", ignoreBrowser = { WebBrowserType.FIREFOX })
    @Test
    public void testDisplayOfMenuAlsoInHeaderTabBarEN()
    {
        try
        {
            //Действия
            tester.resizeWindow(1124, BrowserTS.STANDARD_SCREEN_SIZE.height);
            GUILogon.login(LayoutBo.EMPL1_18);
            GUIBo.goToCard(LayoutBo.USERBO1_8_3_1);

            //Проверка
            String xpath = String.format(Div.ANY, "TabBar.tabs_Proveryaem");
            LayoutTester.scrollIntoView(xpath, ScrollAlignment.END, ScrollAlignment.END);
            ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR +
                                                                 "displayOfMenuAlsoInHeaderTabBarEN.png");
            assertImages(expected, (maker) ->
                    maker.takeScreenshotByXpath(xpath));
        }
        finally
        {
            tester.resetWindowSize();
        }
    }

    /**
     * Тестирование отсутствия пустого пространства в шапке карточки объекта при выборе варианта заголовка объекта
     * [Без заголовка]
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00613
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$200596289
     * <ol>
     * <p>Выполненная настройка на дампе:</p>
     * <li>В классе “1. Основной класс для тестирования”, в типе “1. Шапка и меню” добавлен тип “1.2 Шапка карточки
     * объекта”. На карточке объекта этого типа разорвано наследование с родительским метаклассом по кнопке
     * “Редактировать настройки”. На форме редактирования заголовка объекта выбрано значение [Без заголовка]. В ИО на
     * вкладке “Основной класс для тестирования” карточки Компании в группе "Группа: Тест-кейсы на тестирование
     * верстки элементов шапки системы и левого навигационного меню" создан объект типа “1.2 Шапка карточки объекта”:
     * “Тестирование отсутствия пустого пространства в шапке карточки объекта при выборе варианта заголовка объекта
     * [Без заголовка]”.</li>
     * <b>Действия</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО перейти в объект uuid:mainTestClass$127401</li>
     * <b>Проверка</b>
     * <li>Между кнопкой “← Назад” в шапке карточки и панелью действий карточки нет пустого пространства</li>
     * </ol>
     */
    @Test
    public void testBoHeaderWithoutHeader()
    {
        // Действия
        tester.resizeWindow(1124, 550);
        try
        {
            GUILogon.asSuper();
            GUINavigational.goToOperatorUI();
            GUIBo.goToCard(LayoutBo.USERBO1_1_2);

            // Проверка
            ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "boHeaderWithoutHeader.png");
            assertImages(expected, (maker) -> maker.takeScreenshotByXpath(LayoutXPath.BO_HEADER_XPATH));
        }
        finally
        {
            tester.resetWindowSize();
        }
    }
}
