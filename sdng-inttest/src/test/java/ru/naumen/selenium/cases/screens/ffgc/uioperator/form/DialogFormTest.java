package ru.naumen.selenium.cases.screens.ffgc.uioperator.form;

import java.io.File;

import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUISearch;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.screen.LayoutConstants;
import ru.naumen.selenium.layout.LayoutTestCase;
import ru.naumen.selenium.layout.models.LayoutBo;
import ru.naumen.selenium.screenmaker.ScreenUtils;
import ru.naumen.selenium.screenmaker.ScreenshotInfo;

/**
 * <AUTHOR>
 * @since 16.08.2021
 */
public class DialogFormTest extends LayoutTestCase
{
    private static final String SCREENSHOT_DIR = LayoutConstants.SCREENSHOT_DIR + "operator" + File.separator +
                                                 "dialogform" + File.separator;

    private static final String GET_VALUE_SCRIPT = "beanFactory.getBean('advlistExportParameters').getAdvlistSize()";
    private static final String SET_VALUE_SCRIPT = "beanFactory.getBean('advlistExportParameters').setAdvlistSize(%s)";
    private static String oldValue = "";

    /**
     * Общая подготовка
     * <li>В dbaccess.properties стенда установить параметр ru.naumen.advlist.export.size=1100.</li>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        oldValue = new ScriptRunner(GET_VALUE_SCRIPT).runScript().get(0);
        new ScriptRunner(String.format(SET_VALUE_SCRIPT, 1100)).runScript();
    }

    /**
     * <li>В dbaccess.properties стенда установить параметр ru.naumen.advlist.export.size равный прежнему значению</li>
     */
    @AfterClass
    public static void after()
    {
        new ScriptRunner(String.format(SET_VALUE_SCRIPT, oldValue)).runScript();
    }

    /**
     * Тестирование верстки формы экспорта списка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00085
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$131181601
     * <br>
     * <ol>
     * <p> Выполненная настройка на дампе: </p>
     * <ul>
     * <li>В dbaccess.properties стенда установлен параметр ru.naumen.advlist.export.size=1100. </li>
     * <li>В классе «1. Основной класс для тестирования» создан тип «9.2 Сообщения», на его карточку выведен список
     * вложенных объектов В ИО создан объект «Тестирование верстки формы экспорта списка» типа «9.2 Сообщения», и в
     * нем 1301 вложенный объект.</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Войти в систему под naumen</li>
     * <li>Перейти в объект uuid:mainTestClass$95406</li>
     * <li>В списке вложенных объектов нажать кнопку «Экспорт списка»</li>
     * <b>Проверка.</b>
     * <li>Открывшаяся форма имеет шапку и подвал, в подвале расположены кнопки «Ок» и «Отмена», расположенные слева.
     * На форме имеется информационный текст и поле для ввода е-мейла. Текст и поле имеют отступы, не наезжают друг
     * на друга.</li>
     * </ol>
     */
    @Test
    public void testExportListForm()
    {
        // Действия
        GUILogon.asSuper();
        GUIBo.goToCard(LayoutBo.USERBO1_9_2_1);
        tester.click(String.format(GUIXpath.Div.ANY_CONTAINS2, "gwt-debug-exportAdvlist"));

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "exportListForm.png");
        assertImages(expected, (maker) -> maker.takeScreenshotByXpath(GUIXpath.Div.PROPERTY_DIALOG_BOX));
    }

    /**
     * Тестирование отображения модального диалогового окна с кнопкой «Ок»
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00399
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$131181601
     * <br>
     * <ol>
     * <b>Действия.</b>
     * <li>Войти в систему под naumen</li>
     * <li>В поле быстрого поиска ввести запрос «модальное окно»</li>
     * <b>Проверка.</b>
     * <li>Появившаяся форма содержит шапку с надписью «Сообщение», текст диалога и подвал с кнопкой «Ок». Кнопка
     * расположена справа. Между элементами формы имеются отступы, элементы не наезжают друг на друга.</li>
     * </ol>
     */
    @Test
    public void testOkButtonOnModalForm()
    {
        // Действия
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();
        String notFoundPhrase = "модальное окно";
        GUISearch.simpleSearch(notFoundPhrase);

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "okButtonOnModalForm.png");
        assertImages(expected, (maker) -> maker.takeScreenshotByXpath(GUIXpath.Div.INFO_DIALOG));
    }

    /**
     * Тестирование отображения модального диалогового окна с кнопками «Да» и «Нет»
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00292
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$131181601
     * <br>
     * <ol>
     * <p> Выполненная настройка на дампе: </p>
     * <ul>
     * <li>В классе «1. Основной класс для тестирования» создан тип «9.2 Сообщения», на его карточку выведен список
     * вложенных объектов.</li>
     * <li>В ИО создан объект «Тестирование верстки формы экспорта списка» типа «9.2 Сообщения», и вложенные в него
     * объекты</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Войти в систему под naumen</li>
     * <li>Перейти в объект uuid:mainTestClass$95406</li>
     * <li>В панели действий карточки нажать кнопку «Удалить»</li>
     * <b>Проверка.</b>
     * <li>Появившаяся форма содержит шапку с надписью «Подтверждение удаления», текст диалога и подвал с кнопками
     * «Да» и «Нет». Кнопки расположены справа. Между элементами формы имеются отступы, элементы не наезжают друг на
     * друга.</li>
     * </ol>
     */
    @Test
    public void testYesNoButtonsOnModalForm()
    {
        // Действия
        GUILogon.asSuper();
        GUIBo.goToCard(LayoutBo.USERBO1_9_2_1);
        tester.click(GUIXpath.Div.DEL_CONTAINS);

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "yesNoButtonsOnModalForm.png");
        assertImages(expected, (maker) -> maker.takeScreenshotByXpath(GUIXpath.Any.QUESTION_DIALOG));
    }
}
