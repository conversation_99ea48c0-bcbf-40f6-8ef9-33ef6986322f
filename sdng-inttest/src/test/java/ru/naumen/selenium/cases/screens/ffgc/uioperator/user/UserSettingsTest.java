package ru.naumen.selenium.cases.screens.ffgc.uioperator.user;

import java.io.File;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.screen.LayoutConstants;
import ru.naumen.selenium.layout.LayoutTestCase;
import ru.naumen.selenium.layout.models.LayoutBo;
import ru.naumen.selenium.screenmaker.ScreenUtils;
import ru.naumen.selenium.screenmaker.ScreenshotInfo;

/**
 * Тестирование персональных настроек пользователя: цветовых схем тем интерфейса, размера шрифта и языка
 *
 * <AUTHOR>
 * @since 02.06.2020
 */
public class UserSettingsTest extends LayoutTestCase
{
    private static final String SCREENSHOT_DIR = LayoutConstants.SCREENSHOT_DIR + "operator" + File.separator +
                                                 "user" + File.separator;

    /**
     * Тестирование верстки контентов при выборе уменьшенного шрифта в персональных настройках пользователя в
     * компактном режиме отображения интерфейса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00463
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     * <li>В типе "8. Контенты" создан тип "8.6 Персональные настройки". В нем добавлены группы атрибутов "Параметры
     * объекта" и "Простой список" с атрибутами различных типов. На карточку объекта добавлена вкладка "Уменьшенный
     * шрифт", на которую выведены контенты: "Параметры объекта" по группе атрибутов "Параметры объекта", два сложных
     * списка вложенных объектов с настройками вида по умолчанию (по этой же группе) и простой список по группе
     * атрибутов "Простой список"</li>
     * <li> В ИО создан объект "Тестирование верстки контентов при выборе уменьшенного шрифта в персональных
     * настройках пользователя" в нем заполнены все необходимые атрибуты. Создан пользователь "Компактноуменьшенный
     * Варфаламей Иосифович" с логином compsm, паролем 123, у пользователя в персональных настройках задан размер
     * шрифта "уменьшенный" и вид интерфейса "компактный"</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Войти в систему под пользователем uuid:employee$59701, логин compsm, пароль123</li>
     * <li>Перейти в объект uuid:mainTestClass$59101</li>
     * <b>Проверки.</b>
     * <li>На странице расположены контенты: Параметры объекта, Вложенные объекты (2 контента) и Простой список
     * вложенных объектов. В контенте Параметры объекта названия и значения атрибутов имеют шрифт 13px. В списках
     * значения атрибутов в строках объектов имеют шрифт 13px. Названия и значения не наезжают друг на друга, не
     * выходят за пределы блоков, строк и тд. Высота строк в списках 24px</li>
     * <ol>
     */
    @Test
    public void testSmallFontOnContentsCompactView()
    {
        // Действия
        GUILogon.login(LayoutBo.EMPL1_8);
        GUINavigational.showNavPanelOperator();
        GUIBo.goToCard(LayoutBo.USERBO1_8_6_1);

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "smallFontOnContentsCompactView.png");
        assertImages(expected, (maker) -> maker.takeScreenshotByXpath(GUIContent.X_LAYOUT_BLOCK));
    }

    /**
     * Тестирование верстки контентов при выборе уменьшенного шрифта в персональных настройках пользователя
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00463
     * <ol>
     * <p>
     * Выполненная настройка на дампе:
     * </p>
     * <ul>
     * <li>В типе "8. Контенты" создан тип "8.6 Персональные настройки". В нем добавлены группы атрибутов "Параметры
     * объекта" и "Простой список" с атрибутами различных типов. На карточку объекта добавлена вкладка "Уменьшенный
     * шрифт", на которую выведены контенты: Параметры объекта по группе атрибутов "Параметры объекта", два сложных
     * списка вложенных объектов с настройками вида по умолчанию (по этой же группе) и простой список по группе
     * атрибутов "Простой список"</li>
     * <li>В ИО создан объект "Тестирование верстки контентов при выборе уменьшенного шрифта в персональных
     * настройках пользователя" в нем заполнены все необходимые атрибуты. Создан пользователь "Уменьшенный Ильдар
     * Рафаилович" с логином small, паролем123, у пользователя в персональных настройках задан размер шрифта
     * "уменьшенный"</li>
     * </ul>
     * <b>Действия.</b>
     * <li>Войти в систему под пользователем uuid:employee$58401, логин small, пароль123</li>
     * <li>Перейти в объект uuid:mainTestClass$59101</li>
     * <b>Проверки.</b>
     * <li>На странице расположены контенты: "Параметры объекта", "Вложенные объекты" (2 контента) и "Простой список
     * вложенных объектов". В контенте "Параметры объекта" названия и значения атрибутов имеют шрифт 13px. В списках
     * значения атрибутов в строках объектов имеют шрифт 13px. Названия и значения не наезжают друг на друга, не
     * выходят за пределы блоков, строк и тд</li>
     * <ol>
     */
    @Test
    public void testSmallFontOnContents()
    {
        // Действия
        GUILogon.login(LayoutBo.EMPL1_7);
        GUINavigational.showNavPanelOperator();
        GUIBo.goToCard(LayoutBo.USERBO1_8_6_1);

        // Проверка
        ScreenshotInfo expected = ScreenUtils.loadScreenshot(SCREENSHOT_DIR + "smallFontOnContents.png");
        assertImages(expected, (maker) -> maker.takeScreenshotByXpath(GUIContent.X_LAYOUT_BLOCK));
    }
}
