package ru.naumen.mobile.mapping.dto.form;

import java.util.Map;

import jakarta.annotation.Nullable;

import ru.naumen.mobile.controllers.forms.MobileFormContext;

/**
 * Интерфейс всех компонент, занимающихся построением формы основываясь на
 * {@link MobileFormContext}
 *
 * <AUTHOR>
 * @since Oct 17, 2016
 */
public interface MobileFormAttributesComposer
{
    /**
     * Тип формы, для которой применим данный компонент
     */
    MobileFormType getFormType();

    /**
     * Извлекает список атрибутов отображаемых на форме
     *
     * @param attributes контекст запрашиваемой формы
     * @param formCode код запрашиваемой формы (для формы добавления и редактирования)
     * @param executionContext контекст выполнения действия
     * @return результирующий список атрибутов
     */
    MobileFormResult compose(final Map<String, Object> attributes, final @Nullable String formCode,
            final MobileFormExecutionContext executionContext);
}
