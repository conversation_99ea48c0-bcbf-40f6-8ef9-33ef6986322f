package ru.naumen.mobile.mapping.dto.tx;

import java.util.List;

/**
 * DTO для передачи статуса и доступные для перехода статусы на сторону мобильного клиента
 *
 * <AUTHOR>
 * @since 13.12.2019
 */
public class TransitionsDtObject
{
    /** Текущий статус */
    private final StateDtObject currentState;
    /** Доступные для перехода статусы */
    private final List<StateDtObject> transitions;

    public TransitionsDtObject(StateDtObject currentState, List<StateDtObject> transitions)
    {
        this.currentState = currentState;
        this.transitions = transitions;
    }

    public StateDtObject getCurrentState()
    {
        return currentState;
    }

    public List<StateDtObject> getTransitions()
    {
        return transitions;
    }
}
