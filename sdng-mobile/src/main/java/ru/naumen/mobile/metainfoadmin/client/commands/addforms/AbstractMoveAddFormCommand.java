package ru.naumen.mobile.metainfoadmin.client.commands.addforms;

import java.util.Collections;
import java.util.List;

import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.MobileContentUtil;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsContext;
import ru.naumen.mobile.metainfoadmin.client.MobileViewRow;
import ru.naumen.mobile.metainfoadmin.client.commands.AbstractMoveCommand;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileCommandParam;

/**
 *
 * <AUTHOR>
 * @since 14.02.2017
 */
public abstract class AbstractMoveAddFormCommand
        extends AbstractMoveCommand<MobileSettingsContext, MobileViewRow<AddForm>>
{
    public AbstractMoveAddFormCommand(MobileCommandParam<MobileSettingsContext, MobileViewRow<AddForm>, Void> param,
            int direction)
    {
        super(param, direction);
    }

    @Override
    protected int getSize(Object object)
    {
        if (!(object instanceof MobileViewRow<?>))
        {
            return 0;
        }
        AbstractMobileView form = ((MobileViewRow<?>)object).getView();
        if (!(form instanceof AddForm))
        {
            return 0;
        }

        return MobileContentUtil.getParentFormList(form.getUuid(), getContext().getSettings()).size();
    }

    @Override
    protected int indexOf(Object list)
    {
        if (!(list instanceof MobileViewRow<?>))
        {
            return -1;
        }
        AbstractMobileView form = ((MobileViewRow<?>)list).getView();
        if (!(form instanceof AddForm))
        {
            return -1;
        }

        List<AddForm> forms = MobileContentUtil.getParentFormList(form.getUuid(), getContext().getSettings());
        return forms.indexOf(form);
    }

    @Override
    protected void moveOnClient(MobileViewRow<AddForm> addForm)
    {
        int indx = indexOf(addForm);
        List<AddForm> forms = MobileContentUtil.getParentFormList(addForm.getView().getUuid(),
                getContext().getSettings());
        Collections.swap(forms, indx, indx + getDirection());
        param.getCallback().onSuccess(null);
    }
}
