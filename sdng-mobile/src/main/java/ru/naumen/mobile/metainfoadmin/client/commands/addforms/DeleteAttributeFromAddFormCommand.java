package ru.naumen.mobile.metainfoadmin.client.commands.addforms;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsMessages;
import ru.naumen.mobile.metainfoadmin.client.addforms.MobileAddFormContext;
import ru.naumen.mobile.metainfoadmin.client.commands.AbstractDeleteAttributeCommand;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileCommandParam;
import ru.naumen.mobile.metainfoadmin.client.events.ValidateContentEvent;

/**
 * Команда удаления атрибута с формы добавления объекта
 *
 * <AUTHOR>
 * @since 14.02.2017
 */
public class DeleteAttributeFromAddFormCommand extends AbstractDeleteAttributeCommand<MobileAddFormContext>
{
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private MobileSettingsMessages messages;

    @Inject
    public DeleteAttributeFromAddFormCommand(
            @Assisted MobileCommandParam<MobileAddFormContext, MobileAttribute, Void> param)
    {
        super(param);
    }

    @Override
    public boolean isPossible(Object input)
    {
        AddForm addForm = getContext().getContent();
        return null == addForm.getParent() || !Boolean.TRUE.equals(addForm.getAttrsOnFormInherited());
    }

    @Override
    protected void deleteOnServer(MobileAttribute value, AsyncCallback<Void> callback)
    {
        service.deleteAttributeFromAddForm(getContext().getContent(), value, callback);
    }

    @Override
    protected String getConfirmQuestion(MobileAttribute value)
    {
        if (value == null)
        {
            return "";
        }
        String attrTitle = getAttrTitle(value);
        String formTitle = metainfoUtils.getLocalizedValue(getContext().getContent().getCaption());
        String formCode = " (" + getContext().getContent().getCode() + ")";
        return messages.addFormConfirmDeleteAttrQuestion(attrTitle, formTitle, formCode);
    }

    @Override
    protected void onSuccess(MobileAttribute attribute)
    {
        super.onSuccess(attribute);
        getContext().getEventBus().fireEvent(new ValidateContentEvent());
    }

}
