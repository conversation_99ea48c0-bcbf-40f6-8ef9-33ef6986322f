package ru.naumen.mobile.metainfoadmin.client.commands.editforms;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.mobile.metainfoadmin.client.MobileSettingsContext;
import ru.naumen.mobile.metainfoadmin.client.commands.AbstractMobileContentCommand;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileCommandParam;
import ru.naumen.mobile.metainfoadmin.client.editforms.AddMobileEditFormFormPresenter;

/**
 * Команда добавления формы редактирования объекта
 *
 * <AUTHOR>
 * @since 09.11.2916
 */
public class AddMobileEditFormCommand extends
        AbstractMobileContentCommand<MobileSettingsContext, AddMobileEditFormFormPresenter>
{
    @Inject
    public AddMobileEditFormCommand(@Assisted MobileCommandParam<MobileSettingsContext, Void, Void> param)
    {
        super(param);
    }
}
