package ru.naumen.mobile.metainfoadmin.client.commands.editforms;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.mobile.metainfoadmin.client.commands.AbstractDeleteAttributeCommand;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileCommandParam;
import ru.naumen.mobile.metainfoadmin.client.editforms.MobileEditFormContext;
import ru.naumen.mobile.metainfoadmin.client.events.ValidateContentEvent;

/**
 * Команда удаления атрибута с формы редактирования объекта
 *
 * <AUTHOR>
 * @since 09.11.2916
 */
public class DeleteAttributeFromEditFormCommand extends AbstractDeleteAttributeCommand<MobileEditFormContext>
{
    @Inject
    public DeleteAttributeFromEditFormCommand(
            @Assisted MobileCommandParam<MobileEditFormContext, MobileAttribute, Void> param)
    {
        super(param);
    }

    @Override
    protected void deleteOnServer(MobileAttribute value, AsyncCallback<Void> callback)
    {
        service.deleteAttributeFromEditForm(getContext().getContent(), value, callback);
    }

    @Override
    protected String getConfirmQuestion(MobileAttribute value)
    {
        if (value == null)
        {
            return "";
        }
        String attrTitle = getAttrTitle(value);
        return messages.confirmDeleteAttributeFromEditForm(attrTitle, getContext().getContent().getCode());
    }

    @Override
    protected void onSuccess(MobileAttribute attribute)
    {
        super.onSuccess(attribute);
        getContext().getEventBus().fireEvent(new ValidateContentEvent());
    }
}
