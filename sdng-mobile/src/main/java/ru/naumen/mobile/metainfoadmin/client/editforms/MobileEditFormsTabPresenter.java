package ru.naumen.mobile.metainfoadmin.client.editforms;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.MOBILE_APPLICATIONS;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.client.TabLayoutDisplay;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminTabContainerPresenter;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsContext;
import ru.naumen.mobile.metainfoadmin.client.events.ValidateContentEvent;
import ru.naumen.mobile.metainfoadmin.client.helper.MobileContentEditFormsValidator;

/**
 * Презентер вкладки с формами редактирования объектов на карточке настройки мобильного приложения
 *
 * <AUTHOR>
 * @since 09.11.2016
 */
public class MobileEditFormsTabPresenter extends AdminTabContainerPresenter
{
    @Inject
    private MobileEditFormsPresenter mobileEditFormsPresenter;
    @Inject
    private MobileContentEditFormsValidator contentValidator;

    private MobileSettingsContext context;

    @Inject
    public MobileEditFormsTabPresenter(TabLayoutDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(MobileSettingsContext context)
    {
        this.context = context;
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        mobileEditFormsPresenter.init(context);
        addContent(mobileEditFormsPresenter, "editForms");

        context.getEventBus().addHandler(ValidateContentEvent.getType(), event -> validateMobileSettings());

        validateMobileSettings();
    }

    private void validateMobileSettings()
    {
        setAttention(contentValidator.errorMessage(context));
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return MOBILE_APPLICATIONS;
    }
}