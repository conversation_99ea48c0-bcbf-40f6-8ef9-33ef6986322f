package ru.naumen.mobile.metainfoadmin.server;

import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.metainfo.shared.mobile.MobileContentUtil;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.shared.MobileContentType;
import ru.naumen.mobile.metainfoadmin.shared.MoveMobileFormAction;

/**
 *
 * <AUTHOR>
 * @since 20 апр. 2015 г.
 */
@Component
public class MoveMobileFormActionHandler extends TransactionalActionHandler<MoveMobileFormAction, EmptyResult>
{
    @Inject
    private MobileSettingsService mobileSettingsService;
    @Inject
    private MetainfoModification metainfoModification;

    @Override
    public EmptyResult executeInTransaction(MoveMobileFormAction action, ExecutionContext context)
            throws DispatchException
    {
        MobileSettings mobileSettings = mobileSettingsService.getSettingsOrEmpty();

        @SuppressWarnings("unchecked")
        List<AbstractMobileView> lists = (List<AbstractMobileView>)getContentList(action, mobileSettings);
        int indx = lists.indexOf(createByUuid(action));
        if (indx < 0)
        {
            throw new IllegalStateException("Object is not found");
        }

        lists.add(indx + action.getDirection(), lists.remove(indx));

        metainfoModification.modify(
                MetainfoModification.MetainfoRegion.MOBILE_SETTINGS);
        mobileSettingsService.importSettings(mobileSettings);

        return new EmptyResult();
    }

    private AbstractMobileView createByUuid(MoveMobileFormAction action)
    {
        return action.getContentType() == MobileContentType.OBJECT_CARD
                ? ObjectCard.byUuid(action.getUuid())
                : CommonMobileView.byUuid(action.getUuid());
    }

    private List<? extends AbstractMobileView> getContentList(MoveMobileFormAction action,
            MobileSettings mobileSettings)
    {
        switch (action.getContentType())
        {
            case MOBILE_LIST:
                return mobileSettings.getLists();
            case OBJECT_CARD:
                return mobileSettings.getObjectCards();
            case ADD_FORM:
                return MobileContentUtil.getParentFormList(action.getUuid(), mobileSettings);
            case EDIT_FORM:
                return mobileSettings.getEditForms();
            default:
                throw new IllegalStateException("Unknown content code: " + action.getContentType().toString());
        }
    }
}
