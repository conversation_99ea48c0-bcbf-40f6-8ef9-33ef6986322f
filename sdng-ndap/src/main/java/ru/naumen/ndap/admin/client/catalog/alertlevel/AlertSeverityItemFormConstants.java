package ru.naumen.ndap.admin.client.catalog.alertlevel;

import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.def.DefaultItemFormConstants;
import ru.naumen.ndap.shared.Constants.AlertSeverity;

/**
 * Константы для формы добавления элемента справочника "Уровни критичности тревог"
 * <AUTHOR>
 * @since Feb 4, 2016
 */
public class AlertSeverityItemFormConstants extends DefaultItemFormConstants<AlertSeverityItemFormContext>
{
    // @formatter:off
    private static final String[] CODES = {
        CatalogItem.ITEM_TITLE,
        CatalogItem.ITEM_CODE,
        CatalogItem.ITEM_PARENT,
        CatalogItem.ITEM_COLOR,
        CatalogItem.ITEM_ICON,
        AlertSeverity.LEVEL,
        CatalogItem.SETTINGS_SET
    };
    // @formatter:on

    @Override
    public String[] propertyCodes()
    {
        return CODES;
    }
}