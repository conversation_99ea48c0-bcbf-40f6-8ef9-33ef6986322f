package ru.naumen.ndap.server.scheduler;

import static ru.naumen.core.shared.Constants.ServiceUsers.NDAP_SERVER_CHECKER_USER;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.BooleanSupplier;

import org.apache.commons.lang3.time.DateUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.monitoring.stacktrace.DiagnosticsStackTraces;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.scheduler.SchedulerProperties;
import ru.naumen.core.server.scheduler.job.annotation.FixedRate;
import ru.naumen.core.server.scheduler.job.annotation.ScheduledJob;
import ru.naumen.core.server.scheduler.job.base.InterruptableAbstractJob;
import ru.naumen.core.server.util.log.container.LogConfiguration;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.ndap.server.bo.ndapserver.CentralServer;
import ru.naumen.ndap.server.bo.ndapserver.CentralServerDao;
import ru.naumen.ndap.server.bo.ndapserver.LocalServerDao;
import ru.naumen.ndap.server.bo.ndapserver.NDAPServer;
import ru.naumen.ndap.server.rest.NDAPServerService;
import ru.naumen.ndap.shared.Constants;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;

/**
 * {@link Job} для периодической проверки доступности
 * серверов мониторинга NDAP.
 *
 * <AUTHOR>
 * @since Mar 14, 2018
 */
@ScheduledJob(fixedRate = 5 * DateUtils.MILLIS_PER_MINUTE, name = "ndapServerChecker", lazyInit = true)
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class NDAPServerCheckerJob extends InterruptableAbstractJob
{
    private class ServerStatusUpdater implements BooleanSupplier
    {
        private final String serverUUID;

        ServerStatusUpdater(String serverUUID)
        {
            this.serverUUID = serverUUID;
        }

        @Override
        public boolean getAsBoolean()
        {
            try
            {
                return authorizeRunner.callAsSuperUser(NDAP_SERVER_CHECKER_USER, () -> TransactionRunner.call(() ->
                {
                    LOG.info("Running NDAPServerChecker for server={}", serverUUID);
                    NDAPServer server = loaderService.get(serverUUID);
                    boolean status = servers.checkStatus(server);
                    if (server.isSys_alive() != status)
                    {
                        edit(server, status);
                    }
                    if (status)
                    {
                        servers.requestTestMessage(server);
                    }
                    return status;
                }));
            }
            catch (Exception e)
            {
                LOG.error(e.getMessage(), e);
                return false;
            }
        }

        private void edit(NDAPServer server, boolean checkStatus)
        {
            MapProperties props = new MapProperties();
            props.put(Constants.NDAPServer.ALIVE, checkStatus);
            common.edit(server, props);
        }
    }

    private static final Logger LOG = LoggerFactory.getLogger(NDAPServerCheckerJob.class);
    private static final ExecutorService EXECUTOR_SERVICE = new ThreadPoolExecutor(1, 1, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(), new CustomizableThreadFactory("ndap-server-checker-"));

    private final NDAPServerService servers;
    private final CentralServerDao centralDao;
    private final LocalServerDao localDao;
    private final IPrefixObjectLoaderService loaderService;
    private final CommonUtils common;
    private final AuthorizationRunnerService authorizeRunner;

    public NDAPServerCheckerJob( //NOSONAR не более 8 аргументов
            DiagnosticsStackTraces diagnosticsStackTraces,
            SchedulerProperties schedulerProperties,
            LogConfiguration logConfiguration,
            NDAPServerService servers,
            CentralServerDao centralDao,
            LocalServerDao localDao,
            IPrefixObjectLoaderService loaderService,
            CommonUtils common,
            AuthorizationRunnerService authorizeRunner)
    {
        super(diagnosticsStackTraces, schedulerProperties, logConfiguration);
        this.servers = servers;
        this.centralDao = centralDao;
        this.localDao = localDao;
        this.loaderService = loaderService;
        this.common = common;
        this.authorizeRunner = authorizeRunner;
    }

    @Override
    public void executeInt() throws JobExecutionException
    {
        try
        {
            Boolean centralStatus = TransactionRunner.call(() ->
            {
                CentralServer centralServer = centralDao.getCentralServer();
                return Boolean.TRUE.equals(centralServer.isAvailable())
                       && new ServerStatusUpdater(centralServer.getUUID()).getAsBoolean();
            });
            if (Boolean.TRUE.equals(centralStatus))
            {
                checkLocalServers();
            }
        }
        catch (Exception e)
        {
            LOG.error(e.getMessage(), e);
        }
    }

    @FixedRate
    public long fixedRate()
    {
        return schedulerProperties.getNdapServerCheckerJobPeriod();
    }

    private void checkLocalServers()
    {
        List<String> localServers = localDao.listUuids(new DtoCriteria(Constants.LocalServer.FQN));

        if (localServers.isEmpty())
        {
            return;
        }

        ((ThreadPoolExecutor)EXECUTOR_SERVICE).setMaximumPoolSize(
                Math.min(schedulerProperties.getNdapServerCheckerJobThreadCount(), localServers.size()));

        localServers.stream()
                .map(ServerStatusUpdater::new)
                .map(task -> CompletableFuture.supplyAsync(task::getAsBoolean, EXECUTOR_SERVICE))
                .forEach(CompletableFuture::join);
    }
}
