package com.google.gwt.core.linker;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.RunAsyncCallback;
import com.google.gwt.core.client.impl.ScriptTagLoadingStrategy;
import com.google.gwt.core.ext.LinkerContext;
import com.google.gwt.core.ext.TreeLogger;
import com.google.gwt.core.ext.linker.ArtifactSet;

/**
 * Класс предназначен для переопределения обрамления фрагментов JavaScript кода, загружаемого
 * через {@link GWT#runAsync(RunAsyncCallback)}
 * <p>
 * Код класса убирает всякое обрамление и возвращает просто текст js
 * @see ScriptTagLoadingStrategy
 * <AUTHOR>
 * @since 14 мая 2015 г.
 */
public class NauCrossSiteIframeLinker extends CrossSiteIframeLinker
{
    @Override
    protected String wrapDeferredFragment(TreeLogger logger, LinkerContext context, int fragment, String js,
            ArtifactSet artifacts)
    {
        return js;
    }
}
