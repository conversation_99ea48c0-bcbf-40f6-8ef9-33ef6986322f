<?xml version="1.0" encoding="UTF-8" ?>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@page import="org.apache.commons.lang3.StringUtils,
                ru.naumen.core.server.SpringContext,
                ru.naumen.core.server.util.MessageFacade" %>

<%
MessageFacade messages = SpringContext.getInstance().getBean(MessageFacade.class);
%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    </head>
    <body>
        <div id="errorMessage" class="b-login-error-text">
            <%
                String errorMessage = (String)request.getAttribute("errorMessage");
                if (StringUtils.isNotEmpty(errorMessage))
                {
                    out.write(errorMessage);
                }
                else
                {
            %>
            <%=messages.getMessage("accessDeniedError")%>/>
            <%
                }
            %>
        </div>
        <br>
        <%
            String errorType = (String)request.getAttribute("errorType");
            if (StringUtils.isNotEmpty(errorType) && "employeeForIntegration".equals(errorType))
            {

            }
            else
            {
        %>
        <a href="<%= application.getContextPath() %>/operator/"><%=messages.getMessage("goToWorkInSystem")%>/></a>
        <%
            }
        %>
    </body>
</html>