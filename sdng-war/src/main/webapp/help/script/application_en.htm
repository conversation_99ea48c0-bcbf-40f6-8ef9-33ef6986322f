<!DOCTYPE html>
<html xmlns:madcap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" lang="ru-ru" xml:lang="ru-ru" data-mc-search-type="Stem" data-mc-help-system-file-name="NSD_manual.xml" data-mc-path-to-help-system="../../" data-mc-target-type="WebHelp2" data-mc-runtime-file-type="Topic" data-mc-preload-images="false" data-mc-in-preview-mode="false" data-mc-medium="non-print" data-mc-toc-path="">
  <!-- saved from url=(0016)http://localhost -->
  <head>
    <meta charset="utf-8"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style></style>
  </head>
  <body>
    <div role="main" id="mc-main-content">
      <h1 data-mc-conditions="Default.HelpScripting,Default.WEB-HELP">
        <a name="aanchor72"></a>
        <madcap:concept term="Скрипты">
          Embedded application script
        </madcap:concept>
      </h1>
      <p class="H4">
        Script description
      </p>
      <p>
        The script allows to form parameters for the embedded application.
      </p>
      <p class="H4">
        When script is executed
      </p>
      <p>
        Current script is executed before displaying application widget.
      </p>
      <p>
        For example, &#34;Application is running on an external server&#34;.
      </p>
      <p class="H4">
        Script execution result
      </p>
      <p class="beforeList">
        The execution result depends on the method defining application URL:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          &#34;Parameters are defined by system&#34; — the script returns associative array of custom data that will be transferred to the application via parameter &#34;params&#34; (in JSON format).
        </li>
        <li value="2">
          &#34;URL is fully defined by script&#34; — the script returns a URL of the embedded application. URL must be well-formed and contain HTTP protocol, for example, &#34;https://&#34;.
        </li>
        <li value="3">
          &#34;Parameters are defined by script&#34; — the script returns part of the URL placed after application address.
        </li>
      </ul>
      <p>
        If an error occurs when executing the script or if JSON string length exceeds 2000 characters, parameter &#34;scripterror&#34; displays error text.
      </p>
      <p class="H4">
        Variables and their values
      </p>
      <p class="beforeList">
        Global variables:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <b>
            user
          </b>
          — user who initiated an event (object of the class &#34;Employee&#34;).
          <p>
            User = null if event is initiated by super user.
          </p>
          <p>
            Value of &#34;User&#34; is taken from the script context if event is initiated by script (event action script, state entry script).
          </p>
          <p class="Example">
            For example: a user changed an attribute → object status changed (event action) → person in charge changed (state entry action) → a notification was sent (event action). In all scripts of this chain variable &#34;user&#34; must contain an employee, who performed the initial change of the attribute.
          </p>
        </li>
        <li value="2">
          <b>
            ip
          </b>
          — user workplace ip address. The variable is not defined if action is performed automatically by system (not by user).
        </li>
        <li value="3">
          <b>
            appVersion
          </b>
          — application version.
        </li>
        <li value="4">
          <b>
            api
          </b>
          — used to call available api methods, for example, api.utils, api.ldap, api.timing;
        </li>
        <li value="5">
          <b>
            modules
          </b>
          — used to call script module and its specified method via structure: modules. {module code}.{method name} ({method parameters}...);
        </li>
        <li value="6">
          <b>
            logger
          </b>
          — used for debugging and logging.
        </li>
        <li value="7">
          <b>
            utils
          </b>
          — api.utils synonym.
        </li>
      </ul>
      <p class="beforeList">
        Context variables:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <b>
            subject
          </b>
          — attribute owner, object under the action.
        </li>
        <li value="2">
          <b>
            contentCode
          </b>
          — application content code.
        </li>
        <li value="3">
          <p>
            <b>
              cardObject
            </b>
            — object, on the card of which the action is performed.
          </p>
          <p>
            CardObject = null if action is performed NOT on the object card.
          </p>
        </li>
      </ul>
      <p class="H4">
        Script features
      </p>
      <ul class="FirstLevel">
        <li value="1">
          Whole objects (including subject) are not allowed to return. All objects are automatically converted into strings, containing object UUID.
        </li>
        <li value="2">
          The script is executed each time the page with embedded application gets refreshed.
        </li>
        <li value="3">
          It is not recommended to edit objects (including utils.edit()) in the script body.
        </li>
      </ul>
      <p class="H4">
        Script example
      </p>
      <p>
        1. Sending title of a current subject.
      </p>
      <p class="listing">
        return &#34;Title: ${subject.title}&#34;
      </p>
      <p>
        2. Getting list of departments (department UUIDs will be transferred to application).
      </p>
      <p class="listing">
        return utils.find(&#39;ou&#39;, [:])
      </p>
      <p>
        3. Getting application content code.
      </p>
      <p class="listing">
        return contentCode
      </p>
      <p>
        4. Getting application URL (URL must contain HTTP protocol and a fully qualified domain name).
      </p>
      <p class="listing">
        return &#39;http://www.naumen.ru/sd&#39;
      </p>
      <p>
        5. Getting part of URL (placed after embedded application address).
      </p>
      <p class="listing">
        // application address http://www.naumen.ru
      </p>
      <p class="listing">
        // script returns &#39;/sd&#39;
      </p>
      <p class="listing">
        // application address http://www.naumen.ru
      </p>
      <p class="listing">
        return &#39;/sd&#39;
      </p>
    </div>
  </body>
</html>