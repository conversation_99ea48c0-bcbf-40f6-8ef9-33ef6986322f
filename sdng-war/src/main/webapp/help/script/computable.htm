<!DOCTYPE html>
<html xmlns:madcap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" lang="ru-ru" xml:lang="ru-ru" data-mc-search-type="Stem" data-mc-help-system-file-name="NSD_manual.xml" data-mc-path-to-help-system="../../" data-mc-target-type="WebHelp2" data-mc-runtime-file-type="Topic" data-mc-preload-images="false" data-mc-in-preview-mode="false" data-mc-medium="non-print" data-mc-toc-path="">
  <!-- saved from url=(0016)http://localhost -->
  <head>
    <meta charset="utf-8"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style></style>
  </head>
  <body>
    <div role="main" id="mc-main-content">
      <h1 data-mc-conditions="Default.HelpScripting,Default.WEB-HELP">
        <a name="aanchor60"></a>
        <madcap:concept term="Скрипты">
          Скрипт вычисления значения атрибута с признаком &#34;Вычислимый&#34;
        </madcap:concept>
      </h1>
      <p class="H4">
        Описание скрипта
      </p>
      <p>
        Скрипт предназначен для вычисления значения атрибута с признаком &#34;Вычислимый&#34;.
      </p>
      <p class="note">
        В скрипте для вычисления значения атрибута не допустимо обращаться к внешнему источнику.
      </p>
      <p>
        Для взаимодействия с внешними системами необходимо использовать действие по событию типа &#34;Скрипт&#34; с признаком &#34;Взаимодействие с внешней системой&#34; или/и задачу планировщика.
      </p>
      <p class="note">
        Использование вычислимых атрибутов может привести к снижению производительности приложения.
        <br/>
        Пример 1: задана сложная логикой расчета значения атрибута.
        <br/>
        Пример 2: вычислимые атрибуты выведены в списки объектов, и расчет их значений выполняется по всем объектам списка.
      </p>
      <p class="H4">
        Когда выполняется скрипт
      </p>
      <p>
        При вычислении значения атрибута: при отрисовке контента, содержащего данный атрибут, например &#34;Параметры объекта&#34; или &#34;Список объектов&#34;.
      </p>
      <p>
        Если включены параметры оптимизации получения данных, то скрипт выполняется только для видимых на форме атрибутов
      </p>
      <p class="H4">
        Результат выполнения скрипта
      </p>
      <p>
        Скрипт возвращает значение атрибута, которое зависит от типа атрибута.
      </p>
      <p>
        В случае ошибки при определении или заполнении вычислимого атрибута, атрибут заполняется значением по умолчанию, если оно задано в скрипте. В противном случае, атрибут остается пуст. Сообщение об ошибке записывается в лог.
      </p>
      <p class="H4">
        Переменные и их значения
      </p>
      <p class="beforeList">
        Глобальные переменные:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <p>
            <b>
              user
            </b>
            — пользователь, инициализировавший событие (объект класса
            <span class="BaseСотрудник">
              &#34;Сотрудник&#34; (employee)
            </span>
            ).
          </p>
          <p>
            user=null, если событие инициализировал суперпользователь.
          </p>
          <p>
            user берется из контекста скрипта, если событие инициализировано скриптом (скриптовое действие по событию, скрипт на вход в статус).
          </p>
          <p class="Example">
            Пользователь выполнил изменение атрибута → произошло изменение статуса объекта (действие по событию) → произошло изменение ответственного (действие на вход в статус) → произошло оповещение (действие по событию). Во всех скриптах этой цепочки переменная user должна содержать сотрудника, выполнившего первоначальное изменение
            				атрибута.
          </p>
        </li>
        <li value="2">
          <b>
            ip
          </b>
          — ip-адрес рабочего места пользователя user. Переменная не определяется, если действие выполняется автоматически системой (а не пользователем);
        </li>
        <li value="3">
          <b>
            appVersion
          </b>
          — версия приложения;
        </li>
        <li value="4">
          <b>
            api
          </b>
          — используется для обращения к методам api, например api.utils, api.ldap, api.timing;
        </li>
        <li value="5">
          <b>
            modules
          </b>
          — используется для обращения к скриптовому модулю и конкретному методу, определенному в нем, с помощью конструкции: modules.{код модуля}.{имя метода}({параметры метода}...);
        </li>
        <li value="6">
          <b>
            logger
          </b>
          — используется для отладки скриптов и позволяет вывести в лог на указанный уровень переданную строку.
        </li>
        <li value="7">
          <b>
            utils
          </b>
          — синоним api.utils.
        </li>
      </ul>
      <p>
        Переменные контекста:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <p>
            <b>
              subject
            </b>
            — текущий объект над которым производится действие (владелец атрибута).
          </p>
          <p class="Example">
            Если атрибут добавлен в класс
            <span class="BaseОтдел">
              &#34;Отдел&#34; (ou)
            </span>
            , то subject — это объект класса
            <span class="BaseОтдел">
              &#34;Отдел&#34; (ou)
            </span>
            ,
            				для которого будет вычисляться данный атрибут.
          </p>
        </li>
        <li value="2">
          <p>
            <b>
              cardObject
            </b>
            — объект, из карточки которого было инициировано действие. Если действие инициировано НЕ из карточки объекта, то cardObject = null.
          </p>
          <p>
            Переменная не поддерживается в мобильном приложении.
          </p>
          <p>
            Для быстрой формы добавления/редактирования, открытой с формы добавления/редактирования, которая в свою очередь открыта с карточки объекта, значение переменной cardObject будет содержать этот объект.
          </p>
        </li>
        <li value="3">
          <p>
            <b>
              origin
            </b>
            — тип месторасположения.
          </p>
          <p>
            Переменная не поддерживается в мобильном приложении.
          </p>
          <p class="beforeList">
            Возможные значения:
          </p>
          <ul class="FirstLevel">
            <li value="1">
              readForm — скрипт вычисляется на карточке объекта, на сложной форме добавления и редактирования связей (расширенная форма редактирования), в списке по ссылке на отдельной странице, в списке на странице результатов поиска, при нажатии на кнопку вызова действий в строке списка;
            </li>
            <li value="2">
              addForm — скрипт вычисляется на форме добавления, на быстрой форме добавления;
            </li>
            <li value="3">
              editForm — скрипт вычисляется на форме редактирования объекта, на форме редактирования атрибутов, вызванной из контента &#34;Параметры объекта&#34;, на форме редактирования в ячейке списка, на форме быстрого редактирования, на форме массового редактирования, на форме смены типа; на форме смены ответственного, на форме смены привязки (переклассификации), на форме работы с массовостью (управление массовостью запроса), на форме смены статуса, на форме перемещения (смены родителя);
            </li>
            <li value="4">
              addFormComment/editFormComment — скрипт вычисляется на форме добавления/редактирования комментария;
            </li>
            <li value="5">
              addFormFile/editFormFile — скрипт вычисляется на форме добавления/редактирования файла.
            </li>
          </ul>
        </li>
      </ul>
      <p class="H4">
        Особенности скрипта
      </p>
      <p>
        В скрипте не должны использоваться методы, направленные на модификацию данных (создание, копирование, удаление и прочие).
      </p>
      <p>
        В скрипте не рекомендуется использовать &#34;тяжелые&#34; операции, который получают множество объектов из базы данных, например, utils.find по слабому критерию.
      </p>
      <p class="H4">
        Примеры скрипта
      </p>
      <p>
        1. Скрипт для вычислимого атрибута типа &#34;Целое число&#34;. Скрипт выводит количество файлов, прикрепленных к объекту.
      </p>
      <p class="listing">
        return utils.count(&#39;file&#39;, [&#39;source&#39; : subject.UUID])
      </p>
      <p>
        2. Скрипт для вычисления суммы значений атрибута типа &#34;Целое число&#34; для объектов, которые указаны в атрибуте &#34;Набор элементов справочника&#34;
      </p>
      <pre>//ПАРАМЕТРЫ--------------------------------------
//Код атрибута типа &#34;Набор элементов справочника&#34;
def RER_LINKS_ATTR = &#39;attrCode&#39;
//Код атрибута типа &#34;Целое число&#34;
def AMOUNT_ATTR = &#39;amountAttrCode&#39; 
//ОСНОВНОЙ БЛОК----------------------------------      
def allCost = 0;
def containObjects = subject[RER_LINKS_ATR];
for (def item : containObjects) 
{
    allCost += item[AMOUNT_ATTR]?: 0;
}
return allCost</pre>
    </div>
  </body>
</html>