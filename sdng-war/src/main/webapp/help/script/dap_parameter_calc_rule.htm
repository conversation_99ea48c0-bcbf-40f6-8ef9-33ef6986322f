<!DOCTYPE html>
<html data-mc-conditions="Default.DAP" data-mc-help-system-file-name="NSD_manual.xml" data-mc-in-preview-mode="false"
      data-mc-medium="non-print" data-mc-path-to-help-system="../../" data-mc-preload-images="false"
      data-mc-runtime-file-type="Topic" data-mc-search-type="Stem" data-mc-target-type="WebHelp2"
      data-mc-toc-path="" lang="ru-ru" xml:lang="ru-ru" xmlns:madcap="http://www.madcapsoftware.com/Schemas/MadCap.xsd">
<!-- saved from url=(0016)http://localhost -->
<head>
    <meta charset="utf-8"/>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <style></style>
</head>
<body>
<div id="mc-main-content" role="main">
    <h1 data-mc-conditions="Default.HelpScripting,Default.WEB-HELP">
        <a name="aanchor75"></a>
        <madcap:concept term="Скрипты">
            Скрипт правила вычисления параметра мониторинга
        </madcap:concept>
    </h1>
    <p class="H4">
        Описание скрипта
    </p>
    <p>
        Скрипт вычисления значения параметра метрики.
    </p>
    <p class="H4">
        Когда выполняется скрипт
    </p>
    <p>
        Скрипт выполняется периодически (период и сервер указываются в метрике).
    </p>
    <p class="H4">
        Результат выполнения скрипта
    </p>
    <p>
        Значение метрики, которое будет записано в историю, а также будет участвовать в вычислении статусов триггеров.
        Допускается любое примитивное значение (строка, число), а также коллекции (map, list, set).
    </p>
    <p class="H4">
        Переменные и их значения
    </p>
    <p class="beforeList">
        Глобальные переменные:
    </p>
    <ul class="FirstLevel">
        <li value="1">
            log — используется для отладки скриптов и позволяет вывести в лог на указанный уровень переданную строку.
        </li>
        <li value="2">
            cache -— глобальный ассоциативный массив (в рамках всех скриптов DAP), доступный для чтения и записи. Может
            использоваться, например, для сохранения контекста между запусками одной и той же метрики.
        </li>
        <li value="3">
            api — доступ к вспомогательным методам api.
        </li>
    </ul>
    <p class="beforeList">
        Переменные контекста:
    </p>
    <ul class="FirstLevel">
        <li value="1">
            <p>
                parameter — текущий параметр.
            </p>
            <ul class="FirstLevel">
                <li value="1">
                    parameter.context.attr1 — значение атрибута контекста параметра с кодом attr1
                </li>
            </ul>
        </li>
        <li value="2">
            <p>
                metric — метрика, для которой вычисляется значение;
            </p>
            <ul class="FirstLevel">
                <li value="1">
                    metric.name — имя метрики
                </li>
                <li value="2">
                    metric.period — значение периода опроса метрики
                </li>
                <li value="3">
                    metric.context.attr1 — значение атрибута контекста метрики с кодом attr1
                </li>
            </ul>
        </li>
        <li value="3">
            <p>
                server — сервер мониторинга, связанного с метрикой;
            </p>
            <ul class="FirstLevel">
                <li value="1">
                    server.context.attr1 — значение атрибута контекста сервера мониторинга с кодом attr1
                </li>
            </ul>
        </li>
        <li value="4">
            <p>
                endpoint — подключение, связанное с метрикой, для которой вычисляется значение;
            </p>
            <ul class="FirstLevel">
                <li value="1">
                    endpoint.context.attr1 — значение атрибута контекста с кодом attr1
                </li>
                <li value="2">
                    в каждом подключении есть свои специфические методы, для извлечения значений
                </li>
            </ul>
        </li>
    </ul>
    <p class="H4">
        Особенности скрипта
    </p>
    <ul class="FirstLevel">
        <li value="1">
            Добавляется в систему в интерфейсе оператора при создании параметра.
        </li>
        <li value="2">
            Не отображается в каталоге скриптов в интерфейсе администратора.
        </li>
    </ul>
    <p>
        Для обращения к атрибутам контекста сущности мониторинга необходимо обращаться по полному имени &lt;переменная
        сущности&gt;.context.&lt;код атрибута&gt;. Например, endpoint.context.attr1.
    </p>
    <p class="H4">
        Примеры скрипта
    </p>
    <p>
        1. Скрипт получения времени отклика от узла по протоколу ICMP
    </p>
    <p class="listing">
        endpoint.getTime()
    </p>
    <p>
        2. Скрипт вычисления значения метрики, в котором используется метод драйвера, период метрики, контекстные
        атрибуты метрики и связанных с ней подключения, параметра и сервера мониторинга
    </p>
    <p class="listing">
        endpoint.getTime() + metric.period + metric.context.attr1 + parameter.context.attr2 + endpoint.context.attr3 +
        server.context.attr4
    </p>
</div>
</body>
</html>