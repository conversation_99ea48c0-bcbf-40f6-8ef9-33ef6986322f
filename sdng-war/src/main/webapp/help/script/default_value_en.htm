<!DOCTYPE html>
<html xmlns:madcap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" lang="ru-ru" xml:lang="ru-ru" data-mc-search-type="Stem" data-mc-help-system-file-name="NSD_manual.xml" data-mc-path-to-help-system="../../" data-mc-target-type="WebHelp2" data-mc-runtime-file-type="Topic" data-mc-preload-images="false" data-mc-in-preview-mode="false" data-mc-medium="non-print" data-mc-toc-path="">
  <!-- saved from url=(0016)http://localhost -->
  <head>
    <meta charset="utf-8"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style></style>
  </head>
  <body>
    <div role="main" id="mc-main-content">
      <h1 data-mc-conditions="Default.HelpScripting,Default.WEB-HELP">
        <a name="aanchor3"></a>
        <madcap:concept term="Скрипты">
          Script for computing attribute&#39;s default value
        </madcap:concept>
      </h1>
      <p class="H4">
        Script description
      </p>
      <p>
        The script computes attribute&#39;s default value.
      </p>
      <p class="H4">
        When script is executed
      </p>
      <ul class="FirstLevel">
        <li value="1">
          At the moment of add form opening (once), if an attribute with computable default value is displayed on the object add form.
        </li>
        <li value="2">
          After an object is created (once), if an attribute with computable default value is NOT displayed on the object add form.
        </li>
        <li value="3">
          When editing the defining attribute of the correspondence table in case no matches are found for the definable attribute (if the attribute value is computed with a correspondence table by default).
        </li>
      </ul>
      <p>
        The script is executed twice in the content &#34;Object parameters&#34;: when opening add form and when saving the object.
      </p>
      <p>
        If parameters of data optimization are on, then the script is executed only for the attributes that are visible on the form.
      </p>
      <p>
        Optimization parameters do not cover quick add form. When opening quick add form all object attributes are requested and all the scripts for computing default values are executed.
      </p>
      <p class="H4">
        Script execution result
      </p>
      <p>
        The script returns attribute&#39;s default value. Representation of default value depends on attribute type.
      </p>
      <p>
        In case of an error when defining or filling in the default value, attribute value is considered not defined. Error message is logged.
      </p>
      <p class="comment">
        It is NOT recommended to configure dependencies in scripts computing default values because their order of execution is NOT determined.
      </p>
      <p class="H4">
        Variables and their values
      </p>
      <p class="beforeList">
        Global variables:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <p>
            <b>
              user
            </b>
            — user who initiated an event. User is an object of the &#34;Employee&#34; class.
          </p>
          <p>
            User =null if event is initiated by super user.
          </p>
          <p>
            User value is taken from the script context if event is initiated by script (event action script, script on entering a status).
          </p>
          <p class="Example">
            For example: user changed an attribute → object status changed (event action) → responsible specialist changed (action on entering a status) → notification was sent (event action). In all scripts of this chain variable &#34;user&#34;  must contain an employee, who performed the initial change of attribute.
          </p>
        </li>
        <li value="2">
          <b>
            ip
          </b>
          — user workplace ip-address. The variable is not defined if action is performed automatically by system (not by user).
        </li>
        <li value="3">
          <b>
            appVersion
          </b>
          — application version.
        </li>
        <li value="4">
          <b>
            api
          </b>
          — used to call available api methods, for example, api.utils, api.ldap, api.timing.
        </li>
        <li value="5">
          <b>
            modules
          </b>
          — used to call script module and its specified method via structure: modules. {module code}.{method name} ({method parameters}...).
        </li>
        <li value="6">
          <b>
            logger
          </b>
          — used for debugging and logging.
        </li>
        <li value="7">
          <b>
            utils
          </b>
          — api.utils synonym.
        </li>
      </ul>
      <p class="beforeList">
        Context variables:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <p>
            <b>
              subject
            </b>
            — attribute owner, object under action.
          </p>
          <p>
            subject.UUID = null.
          </p>
          <p class="beforeList">
            It is NOT recommended to use:
          </p>
          <ul class="FirstLevel">
            <li value="1">
              subject — as a result of computing default value for referential attributes;
            </li>
            <li value="2">
              subject.UUID — for computing default value of attributes of all types.
            </li>
          </ul>
        </li>
        <li value="2">
          <b>
            attrCode
          </b>
          — code of computable attribute.
        </li>
        <li value="3">
          <p class="beforeList" data-mc-conditions="Default.417">
            <b>
              sourceForm
            </b>
            — if the script is executed:
          </p>
          <ul class="FirstLevel" data-mc-conditions="Default.417">
            <li value="1">
              when opening a quick add/ edit form, then the variable contains parent form values (i.e. the form from which the current form is opened).
            </li>
            <li value="2">
              when opening status change form, a person in charge change form, an object type change form, a mass editing form that displays a block with attributes for adding a comment, then the variable contains values of the fields of the main form (i.e. the form from which the block with attributes for adding a comment is called).
            </li>
          </ul>
          <p data-mc-conditions="Default.417">
            In other cases, the variable is null.
          </p>
          <p class="comment">
            If an attribute the default value of which is being calculated is not displayed on the quick add form, then the script for computing default value is executed after saving and closing the quick add form, and therefore sourceForm=null.
          </p>
          <p>
            Example: sourceForm.title
          </p>
          <p>
            Instead of using sourceForm as a comparison condition on a quick add form (for example, for true/false, null/ not null checking) it is recommended to use direct comparison.
          </p>
          <p>
            Example 1. Instead of:
          </p>
          <p class="listing">
            return sourceForm ? sourceForm?.printFormRule?.UUID
          </p>
          <p>
            Use this:
          </p>
          <p class="listing">
            return sourceForm != null ? sourceForm?.printFormRule?.UUID
          </p>
          <p>
            Example 2. Instead of:
          </p>
          <p class="listing">
            if (sourceForm)
          </p>
          <p>
            Use this:
          </p>
          <p class="listing">
            if (sourceForm == true)
          </p>
        </li>
        <li value="4">
          <p>
            <b>
              cardObject
            </b>
            — object, on the card of which action is performed.
          </p>
          <p>
            CardObject = null if action is performed NOT on the object card.
          </p>
          <p class="Example">
            Example: if edit form of related object is called from a ServiceCall card, then cardObject is an object of &#34;Service Call&#34;class.
          </p>
        </li>
        <li value="5">
          <p>
            <b>
              origin
            </b>
            — location type.
          </p>
          <p class="beforeList">
            Possible values:
          </p>
          <ul class="FirstLevel">
            <li value="1">
              readForm — script can be executed on the object card, the advanced form of relation adding and editing (advanced edit form), in the list linked to a separate page, the list of the search results, when pressing the action button in the list row;
            </li>
            <li value="2">
              addForm — script can be executed on the add form, the quick add form;
            </li>
            <li value="3">
              editForm — script can be executed on the object edit form; the attribute edit form called from the content &#39;Object parameters&#39;, the edit form in the list cell, the quick edit form, the mass edit form, the type change form, the form of changing a person in charge, the reclassification form, the mass form (when managing mass indicator), the status change form, the relocation form.
            </li>
            <li value="4">
              addFormComment/editFormComment — script can be executed on the comment add/edit form.
            </li>
            <li value="5">
              addFormFile/editFormFile — script can be executed on the file add/edit form.
            </li>
          </ul>
        </li>
        <li value="6" data-mc-conditions="Default.417">
          <p>
            <b>
              contentCode
            </b>
            — code of the content from which the form is called.
          </p>
          <p>
            If the form gets called by clicking a button on the action bar of the object card, then the variable contains the fqn of the card class.
          </p>
        </li>
      </ul>
      <p class="H4">
        Script features
      </p>
      <ul class="FirstLevel">
        <li value="1">
          Data modifying methods (creating, copying, deleting, etc.) must NOT be used in this script.
        </li>
        <li value="2">
          <p class="beforeList">
            Value of aggregating attribute corresponding to the one specified on the object add form is available in the script. Values of aggregated attributes are NOT available (= null).
          </p>
          <p class="Example">
            Example: for aggregating attribute &#34;initiator&#34; (with aggregated attributes &#34;initiator_em&#34; and &#34;initiator_ou&#34;) in the script for computing default value for any other attribute &#34;initiator&#34; = %value, selected on the object add form% (i.e. employee or department), but &#34;initiator_em&#34; = null, &#34;initiator_ou&#34; = null.
          </p>
        </li>
        <li value="3">
          <p>
            When creating an object via script and when initializing its attributes that are used for computing other attributes, all values should correspond to types of objects kept in the attribute.
          </p>
          <p class="Example">
            Example: if the code of catalog item is transferred as a string, not an object, then the script for computing default value will receive transferred string, not catalog item. To avoid this, catalog item should be transferred to as an object.
          </p>
          <pre xml:space="preserve">/**
* Variant 1 (malfunctioning)
*/
utils.create(&#39;ou$OU&#39;, [&#39;title&#39;:&#39;test1&#39;, &#39;catalogAttr&#39;:&#39;CatalogElementCode&#39;])
/**</pre>
          <pre xml:space="preserve">* Variant 2 (functioning)
*/
def cat = utils.get(&#39;catalogCode&#39;, [&#39;code&#39;:&#39;catalogElementCode&#39;])
utils.create(&#39;ou$OU&#39;, [&#39;title&#39;:&#39;test2&#39;, &#39;catalogAttr&#39;:cat])</pre>
        </li>
      </ul>
      <p class="beforeList">
        When using the script:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          If not displayed on the object add form, aggregate attributes of the variable &#34;subject&#34; = null at the moment of form opening. In order to get their values, &#34;utils.getAggrValueMap()&#34; method should be used.
        </li>
        <li value="2">
          In order to get object&#39;s FQN, it is recommended to use &#34;subject.getMetainfo()&#34; method rather than &#34;subject.metaClass.code&#34;.
        </li>
        <li value="3">
          When adding request of certain type and when changing the value &#34;Client&#34;, all the attributes connected to Client will recompute only if request type in Client&#39;s parameter &#34;Request binding by default&#34; corresponds to a type of request being added.
        </li>
      </ul>
      <p>
        It is NOT recommended to use this script for determining system attributes, such as &#34;Contact person&#34;, &#34;Contact email&#34;, &#34;Contact number&#34; when adding a request. In order to specify these attributes when adding a request, it is recommended to use script on entering the status &#34;Registered&#34; or event action script.
      </p>
      <p class="H4">
        Script examples
      </p>
      <ol>
        <li value="1">
          <p>
            Script computes request&#39;s default time zone.
          </p>
          <pre xml:space="preserve" data-mc-conditions="Default.PDF,Default.HelpScripting">//PARAMETERS------------------------------------------------------------
def //getting a company
ROOT = utils.get(&#39;root&#39;, [:]);
def TIMEZONE = subject?.clientEmployee?.city?.timeZone ?: ROOT.dTimeZone
//MAIN BLOCK--------------------------------------------------------
return TIMEZONE</pre>
        </li>
        <li value="2">
          <p>
            Script defines client of a linked request as a default value.
          </p>
          <pre xml:space="preserve" data-mc-conditions="Default.PDF,Default.HelpScripting">//PARAMETERS------------------------------------------------------------
//attribute code referring to the Request
def SERVICE_CALL = &#39;serviceCall&#39;
//code of the &#34;Client(employee)&#34; attribute of &#34;Request&#34; class
def CLIENT_EMPLOYEE = &#39;clientEmployee&#39;
//MAIN BLOCK--------------------------------------------------------
return subject?.SERVICE_CALL?.CLIENT_EMPLOYEE
</pre>
        </li>
        <li value="3" data-mc-conditions="Default.417">
          <p>
            Script in the &#34;Comment&#34; class for the &#34;Private&#34; attribute sets different default values ​​depending on the form of adding a comment.
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            if(sourceForm != null )
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            {
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            // on the form for switching to the &#34;Pending&#34; status - &#34;private = yes&#34;
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            if((utils.get(subject.source)?.state != sourceForm.state)&amp;&amp;(sourceForm.state == &#39;suspended&#39;))
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            {
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            return true
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            }
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            }
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            // on the form for adding a comment, called from the specified content &#34;private=yes&#34;
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            if((origin == &#39;addFormComment&#39;)&amp;&amp;(contentCode == &#39;0c0209be-2203-a611-8442-06a2e2037d08&#39;))
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            {
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            return true
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            }
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            // in other cases &#34;private = no&#34;
          </p>
          <p class="listing" data-mc-conditions="Default.HelpScripting">
            return false
          </p>
        </li>
      </ol>
    </div>
  </body>
</html>