<!DOCTYPE html>
<html xmlns:madcap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" lang="ru-ru" xml:lang="ru-ru" data-mc-search-type="Stem" data-mc-help-system-file-name="NSD_manual.xml" data-mc-path-to-help-system="../../" data-mc-target-type="WebHelp2" data-mc-runtime-file-type="Topic" data-mc-preload-images="false" data-mc-in-preview-mode="false" data-mc-medium="non-print" data-mc-toc-path="">
  <!-- saved from url=(0016)http://localhost -->
  <head>
    <meta charset="utf-8"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style></style>
  </head>
  <body>
    <div role="main" id="mc-main-content">
      <h1 data-mc-conditions="Default.HelpScripting,Default.WEB-HELP">
        <a name="aanchor65"></a>
        <madcap:concept term="Скрипты">
          <a name="aanchor66"></a>
          <madcap:concept term="Мобильное приложение">
            Скрипт кастомизации уведомления в мобильном приложении
          </madcap:concept>
        </madcap:concept>
      </h1>
      <p class="H4">
        Описание скрипта
      </p>
      <p>
        Скрипт кастомизации уточняет параметры отправки уведомления или само отправляемое уведомление.
      </p>
      <p class="H4">
        Когда выполняется скрипт
      </p>
      <p>
        Скрипт выполняется после проверки условий действия по событию, перед выполнением отправки уведомления.
      </p>
      <p class="H4">
        Результат выполнения скрипта
      </p>
      <p>
        Нет возвращаемого значения.
      </p>
      <p>
        Некоторые значения можно вложить в ассоциативный массив для последующего использования в теле уведомления. Пример: push.scriptParams[&#39;param&#39;] = 123.
      </p>
      <p class="H4">
        Переменные и их значения
      </p>
      <p class="beforeList">
        Глобальные переменные:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <p>
            <b>
              user
            </b>
            — пользователь, инициализировавший событие. Является объектом класса
            <span class="BaseСотрудник">
              &#34;Сотрудник&#34; (employee)
            </span>
            .
          </p>
          <p>
            Если событие инициализировал суперпользователь,
            					то user=null.
          </p>
          <p>
            Если событие инициализировано скриптом (скриптовое действие по событию, скрипт на вход в статус), то переменная user берется из контекста инициировавшего его скрипта.
          </p>
          <p class="Example">
            Пример: пользователь выполнил изменение атрибута → произошло изменение статуса объекта (действие по событию) → произошло изменение ответственного (действие на вход в статус) →
            произошло оповещение (действие по событию) .Во всех скриптах этой цепочки переменная user должна содержать сотрудника, выполнившего первоначальное изменение
            				атрибута.
          </p>
          <p>
            Если скрипт инициирует цепочку действий, в рамках которых выполняются скрипты различных категорий, отличных от скрипта действия по событию, то значение переменной user может потеряться, т.к. в user передается значение текущего аутентифицированного пользователя. Чтобы сохранить значение переменной user, необходимо передавать значение user из первого скрипта во все последующие скрипты цепочки действий.
          </p>
          <p class="Example">
            Пример цепочки: пользователь изменяет статус объекта → инициируется событие &#34;Смена статуса&#34; → выполняется редактирование объекта (скрипт действия при входе в статус редактирует переменную subject через utils.edit) → инициируется событие &#34;Изменение объекта&#34; → отправляется оповещение (скрипт действия по событию &#34;Изменение объекта&#34;). Если в данной цепочке в переменную utils.edit не передать значение user, то в скрипте действия по событию user=null.
          </p>
          <p>
            Пример: Передача значения переменной user в следующий скрипт цепочки действий редактировании объекта.
          </p>
          <p class="listing">
            def serviceCall = utils.get(&#39;serviceCall$3801&#39;);
          </p>
          <p class="listing">
            utils.edit(serviceCall, [&#39;title&#39; : &#39;qwerty&#39;, &#39;@user&#39; : user]);
          </p>
        </li>
        <li value="2">
          <b>
            ip
          </b>
          — ip-адрес рабочего места пользователя user. Если действие выполняется автоматически системой (а не пользователем), то переменная не определяется.
        </li>
        <li value="3">
          <b>
            appVersion
          </b>
          — версия приложения.
        </li>
        <li value="4">
          <b>
            api
          </b>
          — используется для обращения к методам api, например api.utils, api.ldap, api.timing;
        </li>
        <li value="5">
          <b>
            modules
          </b>
          — используется для обращения к скриптовому модулю и конкретному методу, определенному в нем, с помощью конструкции: modules.{код модуля}.{имя метода}({параметры метода}...);
        </li>
        <li value="6">
          <b>
            logger
          </b>
          — используется для отладки скриптов и позволяет вывести в лог на указанный уровень переданную строку.
        </li>
        <li value="7">
          <b>
            utils
          </b>
          — синоним api.utils.
        </li>
      </ul>
      <p class="beforeList">
        Переменные контекста:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <b>
            subject
          </b>
          — текущий объект, над которым производится действие.
        </li>
        <li value="2">
          <b>
            oldSubject
          </b>
          — объект до выполнения события. При использовании переменной значение атрибутов типа &#34;Обратная ссылка&#34; всегда будет равно null.
        </li>
        <li value="3">
          <b>
            currentSubject
          </b>
          — объект, над которым производится действие. В переменной currentSubject хранятся значения атрибутов объекта на момент обработки действия по событию. Переменная currentSubject недоступна для пользовательских действий по событию.
        </li>
        <li value="4">
          <b>
            comment
          </b>
          — текст комментария, заполненный на форме смены статуса или форме смены ответственного.
        </li>
        <li value="5">
          <p class="beforeList">
            <b>
              isCommentPrivate
            </b>
            — признак приватности комментария, заполненного на форме смены статуса или форме смены ответственного:
          </p>
          <ul class="FirstLevel">
            <li value="1">
              true — комментарий, заполненный при смене ответственного или при смене статуса, приватный;
            </li>
            <li value="2">
              false — комментарий не приватный.
            </li>
          </ul>
        </li>
        <li value="6" data-mc-conditions="Default.418">
          <p>
            <b>
              commentObject
            </b>
            — объект комментария, созданного в текущем бизнес-процессе.
          </p>
          <p>
            Если комментарий не был создан, имеет значение null.
          </p>
          <p>
            Если было создано несколько комментариев, берется последний созданный комментарий.
          </p>
        </li>
        <li value="7">
          <b>
            pushMobile
          </b>
          — текущее уведомление, для которого выполняется настройка.
          <ul class="FirstLevel">
            <li value="1">
              scriptParams[&#39;param&#39;] — задает значения для каждой переменной, которая будет использоваться в тексте уведомления.
            </li>
            <li value="2">
              <p>
                pushMobile.toEmployee — список сотрудников-получателей уведомления. Используется для добавления сотрудника в список получателей уведомления.
              </p>
              <p class="note">
                Поле pushMobile.toEmployee всегда будет пустым списком в момент начала выполнения скрипта. Данное поле необходимо заполнять в самом скрипте.
              </p>
            </li>
            <li value="3">
              pushMobile.toRemoveEmployee — используется для исключения сотрудника из списка получателей уведомления.
            </li>
          </ul>
        </li>
        <li value="8">
          <p>
            <b>
              pushMobile.link
            </b>
            — ссылка для перехода  на карточку объекта при нажатии на уведомлении.
          </p>
          <p>
            С версии 4.7 возможен переход на конкретный комментарий в списке комментариев, на конкретный файл в списке файлов, на конкретный объект в списке объектов.
          </p>
        </li>
        <li value="9">
          <p>
            <b>
              params
            </b>
            — значения параметров действия по событию, заполняемых в интерфейсе на форме выполнения пользовательского действия по событию.
          </p>
          <p>
            До версии 4.7 переменная &#34;params&#34; могла использоваться в скриптах для любых целей. С версии 4.7 и старше скрипты с переменной &#34;params&#34; работают по указанной системной логике (обращение к параметрами на форме), другая логика переменной игнорируется.
          </p>
          <p>
            Чтобы использовать другую логику переменной params, необходимо заменить в существующих скриптах название своей переменной &#34;params&#34; на какое-то другое.
          </p>
        </li>
        <li value="10">
          <b>
            mention
          </b>
          — параметры упоминания объекта (атрибута, в котором использовались упоминания). Переменная доступна для действия по событию &#34;Упоминание в рамках выбранных объектов&#34;.
          <ul class="FirstLevel">
            <li value="1">
              mention.changedAttribute — измененный атрибут типа &#34;Текст в формате RTF&#34;. Если упоминание добавлено в рамках комментария, то содержит &#39;text&#39;;
            </li>
            <li value="2">
              mention.newMentions — список идентификаторов новых упоминаний;
            </li>
            <li value="3">
              mention.untouchedMentions — список идентификаторов неизмененных упоминаний;
            </li>
            <li value="4">
              mention.removedMentions — список идентификаторов удаленных упоминаний.
            </li>
          </ul>
        </li>
        <li value="11">
          <p class="beforeList">
            <b>
              mentions
            </b>
            — список атрибутов, в которых использовались упоминания, в формате:
          </p>
          <p class="listing">
            mentions == [код_атрибута_1: mention, код_атрибута_2:mention...]
          </p>
          <p>
            Переменная доступна для действий по событию &#34;Добавление комментария&#34;, &#34;Редактирование комментария&#34;, &#34;Добавление объекта&#34;, &#34;Редактирование объекта&#34;, &#34;Смена статуса&#34;, &#34;Смена ответственного&#34;.
          </p>
        </li>
        <li value="12">
          <p>
            <b>
              lang
            </b>
            — локаль текущего уведомления, для которого выполняется настройка.
          </p>
        </li>
        <li value="13">
          <p>
            <b>
              sourceObject
            </b>
            , в данном скрипте sourceObject = null.
          </p>
          <p>
            Для событий &#34;Добавление комментария&#34; и &#34;Редактирование комментария&#34; в качестве sourceObject передается объект источник комментария.
          </p>
          <p>
            Для события &#34;Добавление объекта&#34; в качестве sourceObject передается ссылка на созданный объект.
          </p>
        </li>
        <li value="14">
          <p>
            <b>
              changedAttributes
            </b>
            — список кодов атрибутов, значения которых изменились при редактировании объекта.
          </p>
          <p>
            Вычислимые атрибуты не попадают в контекстную переменную changedAttributes, так как значения вычислимых атрибутов (в том числе атрибута типа &#34;Атрибут связанного объекта&#34;) вычисляются при отображении, не хранятся в базе данных и не редактируются в интерфейсе оператора.
          </p>
          <p>
            При редактировании атрибута типа &#34;Обратная ссылка&#34; на форме редактирования объекта, атрибут попадает в список измененных атрибутов. При изменении атрибута типа &#34;Обратная ссылка&#34; также изменяется объект, на который ведет прямая ссылка.
          </p>
          <p>
            При отправке уведомления асинхронно переменная changedAttributes содержит атрибуты, которые были изменены при возникновении события, и атрибуты, которые были изменены при выполнении синхронного действия по тому же событию.
          </p>
        </li>
        <li value="15">
          <b>
            objectListContext
          </b>
          — критерии выборки объектов, если действие инициировано из списка. Используя данную переменную, можно получить коллекцию объектов, отображаемых в списке.
        </li>
      </ul>
      <p class="H4">
        Особенности скрипта
      </p>
      <ul class="FirstLevel">
        <li value="1">
          Текст уведомления через pushMobile изменять не допускается.
        </li>
        <li value="2">
          Скрипт кастомизации уведомления доступен, если действие по событию — &#34;Уведомление в мобильном приложении&#34;.
        </li>
        <li value="3">
          Сначала выполняется скрипт, затем генерируется текст уведомления!
        </li>
        <li value="4">
          <p>
            Если включена локализация, то при выполнении скрипта вместо одного оповещения формируется несколько экземпляром оповещений — по одному на каждый язык. Скрипт кастомизации выполняется отдельно для каждого оповещения на каждом языке, в той локали, которая используется в формируемом оповещении.
          </p>
        </li>
      </ul>
      <p class="H4">
        Примеры скрипта
      </p>
      <p>
        1. Добавление одного сотрудника в список получателей уведомления:
      </p>
      <p class="listing">
        pushMobile.toEmployee &lt;&lt; empl
      </p>
      <p>
        2. Удаление сотрудника из получателей уведомления:
      </p>
      <p class="listing">
        pushMobile.toRemoveEmployee &lt;&lt; empl
      </p>
      <p>
        3. Ссылка для перехода на карточку объекта по клику на уведомлении:
      </p>
      <p class="listing">
        pushMobile.link &lt;&lt; api.web.open(subject)
      </p>
      <p>
        4. Ссылка для позиционирования на объекте subject в списке объектов с UUID &#34;f9eceb6e-1549-0b63-0001-00001417f2b7&#34;:
      </p>
      <p class="listing">
        pushMobile.link &lt;&lt;api.web.openObjectInList(&#39;f9eceb6e-1549-0b63-0001-00001417f2b7&#39;, subject.UUID)
      </p>
      <p>
        5. Ссылка для позиционирования на комментарии commentUUID в списке комментариев в объекте subject:
      </p>
      <p class="listing">
        pushMobile.link &lt;&lt; api.web.openCommentInList(subject.UUID, &#34;commentUUID&#34;)
      </p>
      <p>
        6. Ссылка для позиционирования на файле fileUUID в списке файлов в объекте subject:
      </p>
      <p class="listing">
        pushMobile.link &lt;&lt; api.web.openFileInList(subject.UUID, &#34;fileUUID&#34;)
      </p>
    </div>
  </body>
</html>