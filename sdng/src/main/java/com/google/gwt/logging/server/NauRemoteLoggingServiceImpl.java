/*
 * Copyright 2010 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package com.google.gwt.logging.server;

import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.LogRecord;
import java.util.logging.Logger;

import com.google.gwt.core.server.StackTraceDeobfuscator;
import com.google.gwt.logging.server.RemoteLoggingServiceUtil.RemoteLoggingException;
import com.google.gwt.logging.shared.RemoteLoggingService;
import com.google.gwt.user.server.rpc.XsrfProtect;
import com.google.gwt.user.server.rpc.jakarta.RemoteServiceServlet;

import jakarta.servlet.ServletConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.SpringContext;
import ru.naumen.sec.server.servlets.requestqueue.RequestQueue;

/**
 * Server-side code for the remote log handler.
 * Реализация перенесена в целях наследования.
 */
@XsrfProtect
public class NauRemoteLoggingServiceImpl extends RemoteServiceServlet implements RemoteLoggingService
{
    private static final Logger LOG = Logger.getLogger(NauRemoteLoggingServiceImpl.class.getName());

    // No deobfuscator by default
    protected StackTraceDeobfuscator deobfuscator = null;
    private String loggerNameOverride = null;
    private RequestQueue queue;

    @Override
    public void init(ServletConfig config) throws ServletException
    {
        super.init(config);
        queue = SpringContext.getInstance().getBean("logging-request-queue", RequestQueue.class);
    }

    @Override
    public void service(HttpServletRequest req, HttpServletResponse resp) throws ServletException
    {
        queue.enqueueRequest(req, resp, this::serviceInt);
    }

    /**
     * Logs a Log Record which has been serialized using GWT RPC on the server.
     * @return either an error message, or null if logging is successful.
     */
    @Override
    public String logOnServer(LogRecord lr)
    {
        String strongName = getPermutationStrongName();
        try
        {
            RemoteLoggingServiceUtil.logOnServer(lr, strongName, deobfuscator, loggerNameOverride);
        }
        catch (RemoteLoggingException e)
        {
            LOG.log(Level.SEVERE, "Remote logging failed", e);
            return "Remote logging failed, check stack trace for details.";
        }
        return null;
    }

    /**
     * By default, messages are logged to a logger that has the same name as
     * the logger that created them on the client. If you want to log all messages
     * from the client to a logger with another name, you can set the override
     * using this method.
     */
    public void setLoggerNameOverride(String override)
    {
        loggerNameOverride = override;
    }

    /**
     * By default, this service does not do any deobfuscation. In order to do
     * server-side deobfuscation, you must copy the symbolMaps files to a
     * directory visible to the server and set the directory using this method.
     */
    public void setSymbolMapsDirectory(String symbolMapsDir)
    {
        deobfuscator = StackTraceDeobfuscator.fromFileSystem(symbolMapsDir);
    }

    protected void serviceInt(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException
    {
        super.service(req, resp);
    }
}
