package net.customware.gwt.dispatch.server;

import java.util.concurrent.ForkJoinPool;

import jakarta.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.Result;

import ru.naumen.commons.server.utils.GenericUtils;

/**
 *  AbstractActionHandler c инициализацией пула для многопоточного выполнения
 *
 *  <AUTHOR>
 *  @since 26.05.2020
 *
 *  @param <A> The {@link Action} implementation.
 *  @param <R> The {@link Result} implementation.
 */
public abstract class AbstractActionHandlerWithMultiThreadPool<A extends Action<R>, R extends Result> implements ActionHandler<A, R>
{

    private final Class<A> actionType;

    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected AbstractActionHandlerWithMultiThreadPool()
    {
        actionType = (Class)GenericUtils.getFirstTypeParameterDeclaredOnSuperclass(this.getClass());
    }

    protected AbstractActionHandlerWithMultiThreadPool(Class<A> actionType)
    {
        this.actionType = actionType;
    }

    @Override
    public Class<A> getActionType()
    {
        return actionType;
    }

    @Override
    public void rollback(A action, R result, ExecutionContext context)
    {
    }

    protected static final Logger LOG = LoggerFactory.getLogger(AbstractActionHandlerWithMultiThreadPool.class);

    @Value("${db.max_active_connections}")
    private Integer dbMaxActiveConnections;
    @Value("${mass.edit.max.cpu.threads}")
    private Integer maxCpuThreads;

    /**
     * Пул потоков для исполнения
     */
    protected ForkJoinPool pool;

    /**
     * Инициализация пула параллельных потоков.
     * Количество параллельных потоков выбирается исходя из двух условий:
     * 1. Максимальная производительность достигается при количестве параллельно исполняемых задач, равном количеству
     * ядер * 3;
     * 2. Число ядер может быть указано вручную, если необходимо иное значение в расчётной формуле.
     * 3. Количество параллельно исполняемых задач не должно превышать (максимальное количество соединений с бд / 2),
     * т.к. каждый поток может запросить больше чем одно соединение(таким образом мы снижаем вероятность
     * возникновения дедлоков)
     */
    @PostConstruct
    void initThreadPool()
    {
        int threadsCountByProcessorsCount = maxCpuThreads > 0 ? maxCpuThreads
                : Runtime.getRuntime().availableProcessors() * 3;
        int threadsCountByDbConnectionsCount = dbMaxActiveConnections / 2;
        final int size = Math.min(threadsCountByProcessorsCount, threadsCountByDbConnectionsCount);

        pool = new ForkJoinPool(size);
    }

    /**
     * Проверка количества соединений с бд на соответствие с желательным минимумом
     */
    protected void checkDbConnectionsEnough()
    {
        int threadsCountByProcessorsCount = Runtime.getRuntime().availableProcessors() * 3;
        int threadsCountByDbConnectionsCount = dbMaxActiveConnections / 2;

        if (threadsCountByProcessorsCount > threadsCountByDbConnectionsCount)
        {
            LOG.warn("DB connections count is " + dbMaxActiveConnections + " now. It's recommended to increase it to "
                     + threadsCountByProcessorsCount + " as minimum.");
        }
    }

}
