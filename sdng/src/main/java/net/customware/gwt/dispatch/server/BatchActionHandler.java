package net.customware.gwt.dispatch.server;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.customware.gwt.dispatch.shared.*;
import net.customware.gwt.dispatch.shared.BatchAction.OnException;
import ru.naumen.core.server.util.SQLExceptionUtils;

/**
 * This handles {@link BatchAction} requests, which are a set of multiple
 * actions that need to all be executed successfully in sequence for the whole
 * action to succeed.
 *
 * <AUTHOR>
 */
public class BatchActionHandler extends AbstractActionHandler<BatchAction, BatchResult>
{
    private static final Logger LOG = LoggerFactory.getLogger(BatchActionHandler.class);

    public BatchActionHandler()
    {
        super(BatchAction.class);
    }

    @Override
    public BatchResult execute(BatchAction action, ExecutionContext context) throws DispatchException
    {
        OnException onException = action.getOnException();
        List<Result> results = new java.util.ArrayList<>();
        List<DispatchException> exceptions = new java.util.ArrayList<>();
        for (Action<?> a : action.getActions())
        {
            Result result = null;
            try
            {
                result = context.execute(a);
                exceptions.add(null);
            }
            catch (Exception e)
            {
                LOG.error(e.getMessage(), e);

                SQLExceptionUtils.throwIfDatabaseException(e);
                DispatchException de = null;
                if (e instanceof DispatchException)
                {
                    de = (DispatchException)e;
                }
                else
                {
                    de = new ServiceException(e);
                }

                if (onException == OnException.ROLLBACK)
                {
                    throw de;
                }
                else
                {
                    exceptions.add(de);
                }
            }
            results.add(result);
        }

        return new BatchResult(results, exceptions);
    }

    @Override
    public void rollback(BatchAction action, BatchResult result, ExecutionContext context)
    {
        // No action necessary - the sub actions should automatically rollback
    }

}
