package net.customware.gwt.dispatch.server;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.web.servlets.SpringStandardDispatchServlet;

/**
 * Исключение нужно, т.к. 
 * стандартный DispathException "забывает" об родительском исключении.
 * Нам же хотелось бы, чтобы родительское исключение было обработано 
 * в {@link SpringStandardDispatchServlet}
 * <AUTHOR>
 *
 */
public class DispatchFxException extends FxException
{
    private static final long serialVersionUID = 6599833975512358939L;

    public DispatchFxException()
    {
        super();
    }

    public DispatchFxException(String msg)
    {
        super(msg);
    }

    public DispatchFxException(String msg, boolean readable)
    {
        super(msg, readable);
    }

    public DispatchFxException(String msg, boolean readable, Throwable cause)
    {
        super(msg, readable, cause);
    }

    public DispatchFxException(String msg, Throwable cause)
    {
        super(msg, cause);
    }

    public DispatchFxException(Throwable cause)
    {
        super(cause);
    }
}
