package net.customware.gwt.dispatch.shared;

import net.customware.gwt.dispatch.server.Dispatch;

/**
 * These are thrown by {@link Dispatch#execute(Action)} if there is a
 * problem executing a particular {@link Action}.
 *
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class ActionException extends DispatchException
{
    public ActionException(String message)
    {
        super(message);
    }

    public ActionException(String message, Throwable cause)
    {
        super(message, cause);
    }

    public ActionException(Throwable cause)
    {
        super(cause);
    }

    protected ActionException()
    {
    }
}
