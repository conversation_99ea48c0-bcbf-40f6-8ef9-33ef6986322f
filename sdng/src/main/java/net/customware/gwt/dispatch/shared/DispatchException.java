package net.customware.gwt.dispatch.shared;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * An abstract superclass for exceptions that can be thrown by the Dispatch
 * system.
 *
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public abstract class DispatchException extends Exception implements IsSerializable
{
    private String causeClassname;

    public DispatchException(String message)
    {
        super(message);
    }

    public DispatchException(String message, Throwable cause)
    {
        super(message + " (" + cause.getMessage() + ")");
        this.causeClassname = cause.getClass().getName();
    }

    public DispatchException(Throwable cause)
    {
        super(cause.getMessage());
        this.causeClassname = cause.getClass().getName();
    }

    protected DispatchException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace)
    {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    protected DispatchException()
    {
    }

    public String getCauseClassname()
    {
        return causeClassname;
    }

    @Override
    public String toString()
    {
        return super.toString() + (causeClassname != null ? " [cause: " + causeClassname + "]" : "");
    }
}
