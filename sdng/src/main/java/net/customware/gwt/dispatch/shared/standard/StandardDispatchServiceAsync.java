package net.customware.gwt.dispatch.shared.standard;

import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.Result;

import com.google.gwt.http.client.Request;
import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Не делайте Inject этого интерфейса напрямую, используйте CoreGinjector.getDispatcher()
 * <AUTHOR>
 * @since 22.03.2011
 *
 */
public interface StandardDispatchServiceAsync
{

    /**
     * Executes the specified action.
     * 
     * @param action The action to execute.
     * @param callback The callback to execute once the action completes.
     * 
     * @see net.customware.gwt.dispatch.server.Dispatch
     */
    <R extends Result> Request execute(Action<R> action, AsyncCallback<R> callback);
}
