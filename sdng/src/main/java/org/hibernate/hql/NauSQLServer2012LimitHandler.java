package org.hibernate.hql;

import static java.util.regex.Pattern.CASE_INSENSITIVE;
import static java.util.regex.Pattern.compile;

import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.hibernate.dialect.pagination.AbstractLimitHandler;
import org.hibernate.query.spi.Limit;
import org.hibernate.query.spi.QueryOptions;

import ru.naumen.core.server.hibernate.dialect.NauSQLServer2012Dialect;

/**
 * Реализация {@link AbstractLimitHandler} совместимая с БД SQL Server 2012,
 * которая поддерживает синтаксис стандарта ANSI SQL
 * {@code OFFSET m ROWS FETCH NEXT n ROWS ONLY}.
 * В данном случае для диалекта {@link NauSQLServer2012Dialect} и выше.
 * Данный класс можно удалить, после обновления hibernate-core на версию 5.3.0 и выше.
 *
 * <AUTHOR>
 * @since 15.08.23
 */
public class NauSQLServer2012LimitHandler extends AbstractLimitHandler
{
    private static final String FOR_UPDATE = "for update";

    enum Keyword
    {

        SELECT("select(\\s+(distinct|all))?"),
        FROM("from"),
        ORDER_BY("order\\s+by"),
        AS("as"),
        WITH("with");

        Pattern pattern;

        Keyword(String keyword)
        {
            pattern = compile("^\\b" + keyword + "\\b", CASE_INSENSITIVE);
        }

        /**
         * Найти "корневое" вхождение ключевого слова в данный фрагмент SQL,
         * то есть смещение, где ключевое слово встречается без кавычек и не заключено в круглые скобки.
         *
         * @param sql фрагмент SQL
         * @return смещение, после которого начинается ключевое слово,
         * или смещение, где начинается ключевое слово, круглые скобки или 0,
         * если ключевое слово никогда не встречается не кавычек
         */
        int rootOffset(String sql)
        {
            Matcher matcher = pattern.matcher(sql).useTransparentBounds(true);

            int depth = 0;
            boolean quoted = false;
            boolean doubleQuoted = false;
            int offset = 0;
            int end = sql.length();
            while (offset < end)
            {
                int nextQuote = sql.indexOf('\'', offset);
                if (nextQuote < 0)
                {
                    nextQuote = end;
                }
                if (quoted)
                {
                    quoted = !quoted;
                    offset = nextQuote + 1;
                    continue;
                }
                for (int index = offset; index < nextQuote; index++)
                {
                    switch (sql.charAt(index))
                    {
                        case '(' -> depth++;
                        case ')' -> depth--;
                        case '"' -> doubleQuoted = !doubleQuoted;
                        case '[' -> doubleQuoted = true;
                        case ']' -> doubleQuoted = false;
                        default ->
                        {
                            if (depth != 0 || doubleQuoted)
                            {
                                continue;
                            }
                            matcher.region(index, nextQuote);
                            if (matcher.find())
                            {
                                return index;
                            }
                        }
                    }
                }
                quoted = !quoted;
                offset = nextQuote + 1;
            }
            return 0;
        }
    }

    private static final Pattern FOR_UPDATE_PATTERN = compile("\\s+for\\s+update\\b|\\s*(;|$)", CASE_INSENSITIVE);

    @Override
    public String processSql(String sql, Limit limit, QueryOptions options)
    {
        boolean hasFirstRow = hasFirstRow(limit);
        boolean hasMaxRows = hasMaxRows(limit);

        if (!hasFirstRow && !hasMaxRows)
        {
            return sql;
        }

        StringBuilder offsetFetch = new StringBuilder();
        begin(sql, offsetFetch, hasFirstRow);

        if (hasFirstRow)
        {
            offsetFetch.append(" offset ? rows");
        }
        if (hasMaxRows)
        {
            offsetFetch.append(" fetch ")
                    .append(hasFirstRow ? "next " : "first ")
                    .append('?')
                    .append(" rows only");
        }

        return insert(offsetFetch.toString(), sql);
    }

    @Override
    public final boolean supportsLimit()
    {
        return true;
    }

    @Override
    public final boolean supportsVariableLimit()
    {
        return true;
    }

    /**
     * {@code OFFSET} и {@code FETCH} должны находится в конце
     * {@code ORDER BY} выражения, и {@code OFFSET} требуется для того, чтобы иметь {@code FETCH}:
     * <pre>order by ... offset m rows [fetch next n rows only]</pre>
     * see
     * <a href="https://docs.microsoft.com/en-us/sql/t-sql/queries/select-order-by-clause-transact-sql?view=sql-server-2017">...</a>
     */
    private static void begin(String sql, StringBuilder offsetFetch, boolean hasFirstRow)
    {
        if (Keyword.ORDER_BY.rootOffset(sql) <= 0)
        {
            offsetFetch.append(" order by 1");
        }
        if (!hasFirstRow)
        {
            offsetFetch.append(" offset 0 rows");
        }
    }

    private static String insert(String offsetFetch, String sql)
    {
        Matcher forUpdateMatcher = FOR_UPDATE_PATTERN.matcher(sql);
        int forUpdateLastIndex = getForUpdateIndex(sql) - 1;
        return forUpdateMatcher.find()
                ? new StringBuilder(sql)
                .insert(forUpdateLastIndex > -1 ? forUpdateLastIndex : sql.length(), offsetFetch)
                .toString()
                : sql;
    }

    private static int getForUpdateIndex(String sql)
    {
        int forUpdateLastIndex = sql.toLowerCase(Locale.ROOT).lastIndexOf(FOR_UPDATE);
        int lastIndexOfQuote = sql.lastIndexOf('\'');
        if (forUpdateLastIndex == -1)
        {
            return forUpdateLastIndex;
        }
        if (lastIndexOfQuote == -1)
        {
            return forUpdateLastIndex;
        }
        if (lastIndexOfQuote > forUpdateLastIndex)
        {
            return -1;
        }
        return forUpdateLastIndex;
    }
}