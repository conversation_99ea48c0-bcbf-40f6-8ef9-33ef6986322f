package ru.naumen.advimport.shared.config.datasource;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Колонка источника данных.
 *
 * <AUTHOR>
 *
 */
@XmlType(name = "Column")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class Column
{
    /**
     * Название колнки. Используется в дальнейшем в конфигурации импорта для ссылки на колонку
     */
    private String name;

    /**
     * Идентифкатор колонки в источнике данных. Может отсутствовать. В этом случае значением всегда будет значение
     * по умолчанию
     */
    private String srcKey;

    /**
     * Значение по умолчанию для колонки если ооно не задано в источнике
     */
    private String defaultValue;

    public Column()
    {
    }

    public Column(String name, String srcKey)
    {
        this.name = name;
        this.srcKey = srcKey;
    }

    @XmlAttribute(name = "default-value", required = false)
    public String getDefaultValue()
    {
        return defaultValue;
    }

    @XmlAttribute(name = "name", required = true)
    public String getName()
    {
        return name;
    }

    @XmlAttribute(name = "src-key", required = false)
    public String getSrcKey()
    {
        return srcKey;
    }

    public void setDefaultValue(String defaultValue)
    {
        this.defaultValue = defaultValue;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public void setSrcKey(String srcKey)
    {
        this.srcKey = srcKey;
    }
}
