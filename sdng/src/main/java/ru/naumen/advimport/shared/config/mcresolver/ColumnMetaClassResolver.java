package ru.naumen.advimport.shared.config.mcresolver;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Определяет метакласс на основании входящих данных
 *
 * @see ByColumnMetaClassResolver
 * <AUTHOR>
 */
@XmlType
@XmlAccessorType(XmlAccessType.PROPERTY)
public class ColumnMetaClassResolver implements AbstractMetaClassResolver
{
    private String column;
    private String defaultMetaClass;

    public ColumnMetaClassResolver()
    {
    }

    public ColumnMetaClassResolver(String column)
    {
        this.column = column;
    }

    @XmlAttribute(name = "column")
    public String getColumn()
    {
        return column;
    }

    @XmlAttribute(name = "default-metaclass")
    public String getDefaultMetaClass()
    {
        return defaultMetaClass;
    }

    public void setColumn(String column)
    {
        this.column = column;
    }

    public void setDefaultMetaClass(String defaultMetaClass)
    {
        this.defaultMetaClass = defaultMetaClass;
    }
}
