package ru.naumen.advimport.shared.config.mcresolver;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.XmlValue;

@XmlType
@XmlAccessorType(XmlAccessType.PROPERTY)
public class ScriptMetaClassResolver implements AbstractMetaClassResolver
{
    private String script;
    private String mimeType;

    @XmlAttribute(name = "mime-type", required = false)
    public String getMimeType()
    {
        if (null == mimeType)
        {
            return "application/x-groovy";
        }
        return mimeType;
    }

    @XmlValue
    public String getScript()
    {
        return script;
    }

    public void setMimeType(String mimeType)
    {
        this.mimeType = mimeType;
    }

    public void setScript(String script)
    {
        this.script = script;
    }
}
