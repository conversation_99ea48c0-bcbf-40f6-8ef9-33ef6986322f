/**
 *
 */
package ru.naumen.advimport.shared.config.parameters;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.advimport.shared.config.Parameter;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Java представление параметра адвимпорта, который вычисляется через скрипт.
 *
 * <AUTHOR>
 * @since Jun 10, 2014
 */
@XmlType
@XmlAccessorType(XmlAccessType.PROPERTY)
public class ScriptParameter extends Parameter
{
    private static final long serialVersionUID = -5300509600345137216L;
    private String mimeType;

    public ScriptParameter()
    {
    }

    @XmlAttribute(name = "mime-type", required = false)
    public String getMimeType()
    {
        if (null == mimeType)
        {
            return Constants.Scripts.GROOVY;
        }
        return mimeType;
    }

    @XmlTransient
    public Script getScript()
    {
        return new Script(getMimeType(), getValue());
    }

    public void setMimeType(String mimeType)
    {
        this.mimeType = mimeType;
    }
}
