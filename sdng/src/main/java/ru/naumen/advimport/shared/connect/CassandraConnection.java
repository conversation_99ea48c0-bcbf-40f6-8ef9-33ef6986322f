package ru.naumen.advimport.shared.connect;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Параметры подключения к БД Cassandra
 *
 * <ul>
 *  <li>хост</li>
 *  <li>порт. по-умолчанию 9042</li>
 *  <li>широфвание. по-умолчанию false</li>
 *  <li>пространство ключей</li>
 * </ul>
 * <AUTHOR>
 *
 */
@XmlType(name = "cassandra-connection", propOrder = { "host" })
@XmlAccessorType(XmlAccessType.PROPERTY)
public class CassandraConnection extends AdvImportConnection
{
    private String host;
    private String keyspace;
    private int port;
    private boolean ssl;

    public CassandraConnection()
    {
        port = 9042;
        ssl = false;
    }

    /**
     * Получить адрес сервера Cassandra
     * @return
     */
    @XmlAttribute(name = "host")
    public String getHost()
    {
        return host;
    }

    /**
     * Получить пространство ключей Cassandra
     * @return
     */
    @XmlAttribute(name = "keyspace")
    public String getKeyspace()
    {
        return keyspace;
    }

    /**
     * Получить порт сервера Cassandra
     * @return
     */
    @XmlAttribute(name = "port")
    public int getPort()
    {
        return port;
    }

    /**
     * Шифровать (SSL) ли соединение
     * @return
     */
    @XmlAttribute(name = "ssl")
    public boolean isSsl()
    {
        return ssl;
    }

    public void setHost(String host)
    {
        this.host = host;
    }

    public void setKeyspace(String keyspace)
    {
        this.keyspace = keyspace;
    }

    public void setPort(int port)
    {
        this.port = port;
    }

    public void setSsl(boolean ssl)
    {
        this.ssl = ssl;
    }
}
