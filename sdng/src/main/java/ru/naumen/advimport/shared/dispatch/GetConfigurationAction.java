package ru.naumen.advimport.shared.dispatch;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.core.shared.dispatch.AbortableAction;
import ru.naumen.sec.shared.actions.AdminAction;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @since 14.02.2012
 */
@AdminAction
public class GetConfigurationAction implements Action<GetConfigurationResponse>, IUUIDIdentifiable,
        HasActionDebugTokens, AbortableAction
{
    private String uuid;

    public GetConfigurationAction(String uuid)
    {
        this.uuid = uuid;
    }

    protected GetConfigurationAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getUUID());
    }

    @Override
    public String getUUID()
    {
        return uuid;
    }

    @Override
    public String toString()
    {
        return "GetConfigurationAction [uuid=" + uuid + "]";
    }
}
