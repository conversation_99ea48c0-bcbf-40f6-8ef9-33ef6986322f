package ru.naumen.bcp.server.operations;

import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.events.EventService;
import ru.naumen.core.server.util.log.IChangeLog;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * Абстрактная реализация операции помещения записи в {@link EventService журнал событий}
 * <p>
 * По сути содержит утилитарные методы
 *
 * <AUTHOR>
 *
 */
public abstract class AbstractEventOperation<T extends IUUIDIdentifiable> extends ObjectOperationBase<T>
{
    protected IUUIDIdentifiable extractParent(AtomOperationContext<IHasObjectBOContext<T>> ctx)
    {
        return operationHelper.getObjectParent(ctx.getContext().getObject());
    }

    /**
     * Формирует описание сделанных изменений во время выполнения бизнес-процесса. Информация об изменении атриубта
     * берется из {@link IChangeLog}
     *
     * @return
     */
    protected String getChanges(AtomOperationContext<IHasObjectBOContext<T>> ctx)
    {
        return contextOperationHelper.getChangesString(ctx.getContext(), ";\n\t");
    }
}
