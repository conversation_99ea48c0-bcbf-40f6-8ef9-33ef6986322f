package ru.naumen.bcp.server.operations;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import ru.naumen.core.server.util.MessageFacade;

/**
 * Исключение предназначено для отложенного формирования группового сообщения
 * об ошибках возникающих в бизнес-процессе.
 * Идентификатором причины ошибки является {@link #getCode() код ошибки}.
 * Код ошибки является обязательным параметром
 * Набор параметров {@link #getParams()} ошибки и их смысл соответствуют
 * коду ошибки. 
 * <AUTHOR>
 * @since 15.03.2011
 *
 */
public class ForbidException extends OperationException //NOSONAR старый код
{
    private String code;

    private Object[] params;

    private String message = null;

    public ForbidException(String code)
    {
        super();
        this.code = code;
    }

    public ForbidException(String code, Throwable cause)
    {
        super(cause);
        this.code = code;
    }

    @Override
    public boolean equals(Object object)
    {
        if (object == this)
        {
            return true;
        }
        if (!(object instanceof ForbidException otherException))
        {
            return false;
        }
        EqualsBuilder equalsBuilder = new EqualsBuilder();
        equalsBuilder.append(getParams(), otherException.getParams());
        equalsBuilder.append(getCode(), otherException.getCode());
        equalsBuilder.append(getMessage(), otherException.getMessage());
        equalsBuilder.append(isReadable(), otherException.isReadable());
        return equalsBuilder.isEquals();
    }

    /**
     * @return the code
     */
    public String getCode()
    {
        return code;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getMessage()
    {
        return this.message != null ? this.message : super.getMessage();
    }

    /**
     * @return the params
     */
    public Object[] getParams()
    {
        return params;
    }

    @Override
    public int hashCode()
    {
        HashCodeBuilder hashCodeBuilder = new HashCodeBuilder();
        hashCodeBuilder.append(getParams()).append(getCode()).append(getMessage()).append(isReadable());
        return hashCodeBuilder.toHashCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ForbidException initCause(Throwable cause)
    {
        super.initCause(cause);
        return this;
    }

    public ForbidException initMessage(MessageFacade messageFacade)
    {
        return initMessage(messageFacade, getParams());
    }

    public ForbidException initMessage(MessageFacade messageFacade, Object... args)
    {
        this.message = messageFacade.getMessage(getCode(), args);
        this.setLocalizedMessages(messageFacade.getLocalizedMessages(getCode(), args));
        return this;
    }

    public ForbidException initMessage(String message)
    {
        this.message = message;
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isReadable()
    {
        return true;
    }

    /**
     * @param params the params to set
     */
    public ForbidException setParams(Object... params)
    {
        this.params = params;
        return this;
    }
}
