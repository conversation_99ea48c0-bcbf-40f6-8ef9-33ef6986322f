package ru.naumen.bcp.server.operations;

import java.util.Date;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.utils.CommonUtils;

/**
 * Операция установки атрибутов типа "Дата" и "Дата/время"
 * <AUTHOR>
 * @since 13.10.2016
 */
@Component
public class SetDateOperation<T extends IUUIDIdentifiable & IHasMetaInfo, V extends Date> extends SetAttrValueOperationWithCheck<T, V>
{
    @Inject
    private Formatters formatters;
    @Inject
    private MessageFacade messages;

    @Override
    protected void validate(AtomOperationContext<IHasObjectBOContext<T>> ctx, V oldValue, V newValue)
    {
        super.validate(ctx, oldValue, newValue);

        if (!CommonUtils.isDateValid(newValue))
        {
            throw new OperationException(messages.getMessage("dateYearValidationError",
                    formatters.formatDate(CommonUtils.MIN_DATE), formatters.formatDate(CommonUtils.MAX_DATE)), true);
        }
    }
}
