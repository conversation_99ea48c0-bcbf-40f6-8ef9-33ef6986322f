package ru.naumen.bcp.server.operations;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.common.shared.utils.LocalizedText;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * Операция устанавливает значение атрибута "Текст с локализацией"
 *
 * <AUTHOR>
 * @since 16.04.2018
 * @param <T>
 */
@Component
public class SetLocalizedTextOperation<T extends IUUIDIdentifiable & IHasMetaInfo>
        extends SetAttrValueOperationWithCheck<T, LocalizedText>
{
    @Override
    protected LocalizedText getNewValue(AtomOperationContext<IHasObjectBOContext<T>> ctx)
    {
        LocalizedText newValue = new LocalizedText();
        LocalizedText oldValue = super.getOldValue(ctx);
        if (oldValue != null)
        {
            newValue.asMap().putAll(oldValue.asMap());
        }
        newValue.asMap().putAll(super.getNewValue(ctx).asMap());
        return newValue;
    }

    @Override
    protected LocalizedText getOldValue(AtomOperationContext<IHasObjectBOContext<T>> ctx)
    {
        LocalizedText oldValue = new LocalizedText();
        LocalizedText oldv = super.getOldValue(ctx);
        if (oldv != null)
        {
            oldValue.asMap().putAll(oldv.asMap());
        }
        return oldValue;
    }
}
