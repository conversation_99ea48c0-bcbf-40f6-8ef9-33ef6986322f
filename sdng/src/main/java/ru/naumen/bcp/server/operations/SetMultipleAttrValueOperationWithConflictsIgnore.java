package ru.naumen.bcp.server.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.stream.Collectors;

import org.hibernate.Cache;
import org.hibernate.cache.spi.CacheImplementor;
import org.hibernate.cache.spi.TimestampsCache;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.persister.collection.AbstractCollectionPersister;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.bo.LazyBOHelper;
import ru.naumen.core.server.common.Accessor;
import ru.naumen.core.server.common.HasRawValueAccess;
import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.flex.HasFlexes;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.SessionFlushHandler;
import ru.naumen.core.server.jta.managed.local.TransactionalDataSource;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.HasId;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Общий класс операции редактирования атрибутов множественного типа с игнором конфликтов в БД
 *
 * <AUTHOR>
 * @since 13 фев. 2020 г.
 */

@Component
public class SetMultipleAttrValueOperationWithConflictsIgnore<T extends IUUIDIdentifiable & IHasMetaInfo & HasId>
        extends SetAttrValueOperationWithCheck<T, Collection<T>>
{
    private static final String OLD_VALUE_PREFIX = "#oldValue:";

    private static final String INSERT_SQL = "insert into %s (%s, %s) values (?, ?)";

    /**
     * Безопасный для конкурентного редактирования INSERT, игнорирующий конфликты вставки одинаковых строк
     */
    private static final String INSERT_SQL_FOR_POSTGRES = "insert into %s (%s, %s) values (?, ?) on conflict do "
                                                          + "nothing";

    /**
     * Безопасный для конкурентного редактирования INSERT, игнорирующий конфликты вставки одинаковых строк
     */
    private static final String INSERT_SQL_FOR_MSSQL = """
            MERGE INTO table target
            USING (SELECT ? as keyColumn, ? as relatedColumn) source
            ON (target.keyColumn = source.keyColumn AND target.relatedColumn = source.relatedColumn)
            WHEN NOT MATCHED THEN INSERT (keyColumn,relatedColumn) VALUES(?, ?);""";

    /**
     * Безопасный для конкурентного редактирования INSERT, игнорирующий конфликты вставки одинаковых строк
     */
    private static final String INSERT_SQL_FOR_ORACLE = "BEGIN INSERT INTO %s (%s,%s) VALUES(?, ?); EXCEPTION WHEN "
                                                        + "dup_val_on_index then null; END;";

    private static final String DELETE_SQL = "delete from %s where %s = ? and %s = ?";

    private static final Logger LOG = LoggerFactory.getLogger(SetMultipleAttrValueOperationWithConflictsIgnore.class);

    @Inject
    @Lazy
    private FlexHelper flexHelper;
    @Inject
    private TransactionalDataSource dataSource;
    @Inject
    private ConfigurationProperties confProperties;

    @Override
    protected void setNewValue(AtomOperationContext<IHasObjectBOContext<T>> ctx, T object, //NOSONAR
            @Nullable Collection<T> newValue)
    {
        Collection<T> oldValue = getOldValue(ctx);
        oldValue = oldValue != null ? oldValue : Collections.emptySet();
        newValue = newValue != null ? newValue : Collections.emptySet();

        if (oldValue.isEmpty() && newValue.isEmpty())
        {
            // При создании объекта с пустым значением нужно в любом случае инициализировать атрибут. 
            super.setNewValue(ctx, object, newValue);
            return;
        }

        Collection<T> toAdd = CollectionUtils.subtract(newValue, oldValue);
        Collection<T> toRemove = CollectionUtils.subtract(oldValue, newValue);

        if (toAdd.isEmpty() && toRemove.isEmpty())
        {
            return;
        }
        Integer thresholdSql = confProperties.getThresholdSql();

        boolean isBigOperation = oldValue.size() >= thresholdSql || toAdd.size() >= thresholdSql
                                 || toRemove.size() >= thresholdSql || newValue.size() >= thresholdSql;

        LOG.debug("IsBigOperation: {}", isBigOperation);

        //используем стандартный механизм при создании объекта и для небольших наборов ссылок, 
        //так как напрямую через sql более агрессивный сброс кэша
        if (ctx.getContext().isNewObject() || !isBigOperation)
        {
            newValue = getOldValueLazy(ctx);
            newValue = newValue != null ? new HashSet<>(newValue) : new HashSet<>();

            toAdd = LazyBOHelper.unlazy(toAdd);
            newValue.addAll(toAdd);

            toRemove = LazyBOHelper.unlazy(toRemove);
            newValue.removeAll(toRemove);

            super.setNewValue(ctx, object, newValue);
        }
        else
        {
            // Устанавливаем актуальное значение, чтобы в скриптах и при валидации было актуальное значение
            IProperties flexes = object instanceof HasFlexes hasFlexes
                    ? hasFlexes.getFlexes()
                    : null;
            if (flexes != null && !flexes.hasProperty(OLD_VALUE_PREFIX + ctx.getId()))
            {
                flexes.setProperty(OLD_VALUE_PREFIX + ctx.getId(), getOldValueLazy(ctx));
            }
            super.setNewValue(ctx, object, newValue);
            setNewValueSqlQuery(ctx, object, toAdd, toRemove);
        }
    }

    /**
     * Метод выполняет sql запросы на редактирование объекта в пакетном виде.
     */
    private void executeBatch(String sql, long objId, Collection<T> toProcess, boolean isDeleting)
            throws SQLException
    {
        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql))
        {
            int counter = 1;
            for (T objToProcess : toProcess)
            {
                statement.setLong(1, objId);
                statement.setLong(2, objToProcess.getId());
                if (DDLTool.isMSSQL() && !isDeleting)
                {
                    statement.setLong(3, objId);
                    statement.setLong(4, objToProcess.getId());
                }
                statement.addBatch();
                if (counter % Constants.MAX_BATCH_SIZE == 0 || counter == toProcess.size())
                {
                    int[] res = statement.executeBatch();
                    LOG.atDebug()
                            .addArgument(() -> Arrays.stream(res)
                                    .mapToObj(String::valueOf)
                                    .collect(Collectors.joining(", ")))
                            .log("Result: {}");
                }
                counter++;
            }
        }
    }

    @SuppressWarnings("unchecked")
    private Collection<T> getOldValueLazy(AtomOperationContext<IHasObjectBOContext<T>> ctx)
    {
        Accessor<T> accessor = getAccessor(ctx);
        if (accessor instanceof HasRawValueAccess<?>)
        {
            T object = ctx.getContext().getObject();
            return ((HasRawValueAccess<T>)accessor).getRawValue(object, getAttribute(ctx));
        }
        return getOldValue(ctx);
    }

    private void setNewValueSqlQuery(AtomOperationContext<IHasObjectBOContext<T>> ctx, T object, Collection<T> toAdd,
            Collection<T> toRemove)
    {
        Attribute attribute = getAttribute(ctx);
        AbstractCollectionPersister persister = (AbstractCollectionPersister)flexHelper
                .getCollectionPersister(attribute);

        ctx.getContext().getDeferredChanges().put(ctx.getId(), ctx.getContext().getChangeLog().getRecord(attribute));

        String tableName = persister.getTableName();
        String keyColumn = persister.getKeyColumnNames()[0];
        String relatedColumn = persister.getElementColumnNames()[0];

        Runnable beforeFlushOperation = () ->
        {
            IProperties flexes = object instanceof HasFlexes hasFlexes
                    ? hasFlexes.getFlexes()
                    : null;
            Collection<T> oldValue = flexes != null
                    ? flexes.getProperty(OLD_VALUE_PREFIX + attribute.getCode())
                    : null;
            if (oldValue != null)
            {
                /*
                 * Восстанавливается старое значение атрибута, Hibernate посчитает, что изменений не было, и не будет
                 * обновлять связи. Это необходимо, так как далее выполняется запросы через PreparedStatement.
                 */
                authorizeRunner.runWithAllPermission(() -> getAccessor(ctx).set(object, attribute, oldValue));
                flexes.removeProperty(OLD_VALUE_PREFIX + attribute.getCode());
            }
            invalidateTimestampsCacheForTables(new String[] { tableName });
        };

        Runnable afterFlushOperation = () ->
        {
            try
            {
                String insertSql = getInsertSql(tableName, keyColumn, relatedColumn);

                LOG.debug("insertSql: {}", insertSql);

                long objId = object.getId();
                executeBatch(insertSql, objId, toAdd, false);
                String deleteSql = String.format(DELETE_SQL, tableName, keyColumn, relatedColumn);
                executeBatch(deleteSql, objId, toRemove, true);
            }
            catch (SQLException e)
            {
                throw new FxException("Batch SQL execution error: " + e.getMessage(), e);
            }
            getSession().refresh(object);
        };

        getSession().addEventListeners(SessionFlushHandler.create(beforeFlushOperation, afterFlushOperation));
    }

    /**
     * Инвалидирует кэш временных меток Hibernate для указанных таблиц
     */
    private void invalidateTimestampsCacheForTables(String[] tableNames)
    {
        Cache cache = getSessionFactory().getCache();
        if (cache instanceof CacheImplementor cacheImplementor
            && getSession() instanceof SessionImplementor sessionImplementor)
        {
            TimestampsCache timestampsCache = cacheImplementor.getTimestampsCache();
            if (timestampsCache != null)
            {
                timestampsCache.invalidate(tableNames, sessionImplementor);
            }
            return;
        }
        LOG.error("Failed to invalidate the timestamps cache because the cache or session implementation is different");
    }

    private static String getInsertSql(String tableName, String keyColumn, String relatedColumn)
    {
        String insertSql;
        if (DDLTool.isPostgres())
        {
            insertSql = String.format(INSERT_SQL_FOR_POSTGRES, tableName, keyColumn, relatedColumn);
        }
        else if (DDLTool.isMSSQL())
        {
            insertSql = INSERT_SQL_FOR_MSSQL.replace("table", tableName).replace("keyColumn", keyColumn)
                    .replace("relatedColumn", relatedColumn);
        }
        else if (DDLTool.isOracle())
        {
            insertSql = String.format(INSERT_SQL_FOR_ORACLE, tableName, keyColumn, relatedColumn);
        }
        else
        {
            insertSql = String.format(INSERT_SQL, tableName, keyColumn, relatedColumn);
        }
        return insertSql;
    }
}
