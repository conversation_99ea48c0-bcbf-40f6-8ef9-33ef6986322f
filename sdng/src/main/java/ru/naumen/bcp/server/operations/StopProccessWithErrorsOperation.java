package ru.naumen.bcp.server.operations;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IBOContext;

/**
 * Операция останавливает процесс, если во время выполнения процесса произошли 
 * ошибки (например, ошибки валидации атрибутов)
 *
 * <AUTHOR>
 */
@Component
public class StopProccessWithErrorsOperation extends AtomOperationBase<IBOContext>
{
    @Override
    public void perform(AtomOperationContext<IBOContext> ctx) throws OperationException
    {
        if (!ctx.getContext().getErrors().isEmpty())
        {
            ctx.getProcess().stop();
        }
    }
}
