package ru.naumen.bcp.server.operations.context;

import java.util.Set;

import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.ClassFqn;

import java.util.HashSet;

/**
 * Класс предназначен для отслеживания отредактированных объектов в процессе изменения значения атрибута
 * прямой или обратной ссылки.
 * <p>Редактирование таких атрибутов вызывает редактирование связанных атрибутов.
 * Последнее необходимо для корректного выполнения сопутствующих операций (например запись в лог об изменении
 * атрибута или переиндексация полей объекта для полнотекстового поиска).</p>
 * Цель - предотвратить зацикливание процессов редактирования, а также исключить повторное редактирование одних и тех-же
 * объектов.
 *
 * <AUTHOR>
 * @since: 22.01.2013
 */
public class HasObjectBOContextPreventCycling<T extends IUUIDIdentifiable> extends HasObjectBOContext<T>
{
    private Set<String> processed;

    public HasObjectBOContextPreventCycling(T object, ClassFqn fqn, IBOContext parentContext)
    {
        super(object, fqn, null, parentContext);
    }

    public void addProcessed(IUUIDIdentifiable obj)
    {
        addProcessed(obj.getUUID());
    }

    public void addProcessed(String uuid)
    {
        if (getParentContext() instanceof HasObjectBOContextPreventCycling)
        {
            ((HasObjectBOContextPreventCycling<?>)getParentContext()).addProcessed(uuid);
            return;
        }
        if (processed == null)
        {
            processed = new HashSet<>();
        }
        processed.add(uuid);
    }

    public boolean isProcessed(IUUIDIdentifiable obj)
    {
        return isProcessed(obj.getUUID());
    }

    /**
     * Метод предназначен для проверки того, что объект с указанным uuid'ом уже был обработан в рамках процессов
     * с целью, для которой был создан данный класс
     * @param uuid UUID объекта
     * @return
     */
    public boolean isProcessed(String uuid)
    {
        boolean res = getParentContext() instanceof HasObjectBOContextPreventCycling
                      && ((HasObjectBOContextPreventCycling<?>)getParentContext()).isProcessed(uuid);
        return res || processed != null && processed.contains(uuid);
    }
}
