package ru.naumen.core.client.content.sccase;

import static ru.naumen.core.shared.Constants.Association.AGREEMENT;
import static ru.naumen.core.shared.Constants.Association.SERVICE;
import static ru.naumen.core.shared.settings.AgreementServiceSetting.Agreement;
import static ru.naumen.core.shared.settings.AgreementServiceSetting.Both;
import static ru.naumen.core.shared.settings.AgreementServiceSetting.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.google.common.collect.Sets;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.rpc.StatusCodeException;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.inject.Provider;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.BatchAction;
import net.customware.gwt.dispatch.shared.BatchAction.OnException;
import net.customware.gwt.dispatch.shared.BatchResult;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.CurrentBrowserTabIdentifier;
import ru.naumen.core.client.FormContextHolder;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.attr.presentation.PresentationFactoryEdit;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.sccase.agrserv.context.AgreementServiceContext;
import ru.naumen.core.client.content.sccase.agrserv.context.AgreementServiceContextHelper;
import ru.naumen.core.client.content.sccase.agrserv.property.AgreementServiceHierarchicalTreePropertyProvider;
import ru.naumen.core.client.content.sccase.agrserv.property.AgreementServicePropertyFoldersTreeProvider;
import ru.naumen.core.client.content.sccase.agrserv.property.AgreementServicePropertyListProvider;
import ru.naumen.core.client.content.sccase.agrserv.property.AgreementServicePropertyProvider;
import ru.naumen.core.client.content.sccase.agrserv.property.AgreementServicePropertyTreeListProvider;
import ru.naumen.core.client.contextvariables.OriginProvider;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.HasEnabledSynchronization;
import ru.naumen.core.client.utils.FormUtils;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasPopup;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.ValueToSelectItemConverter;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.SlmService;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.HasReadyState.SynchronizationCallbackRegistration;
import ru.naumen.core.shared.ISTitled;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dispatch.GetCasesWithClientAgsAction;
import ru.naumen.core.shared.dispatch.GetDtObjectAction;
import ru.naumen.core.shared.dispatch.GetDtObjectResponse;
import ru.naumen.core.shared.dispatch.GetPossibleAgreementsAction;
import ru.naumen.core.shared.dispatch.GetPossibleAgreementsChildrenAction;
import ru.naumen.core.shared.dispatch.GetPossibleAgreementsListAction;
import ru.naumen.core.shared.dispatch.GetPossibleAgreementsListResponse;
import ru.naumen.core.shared.dispatch.GetPossibleAgreementsTreeListAction;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.sccase.agrserv.item.AgreementServiceItem;
import ru.naumen.core.shared.sccase.agrserv.item.IAgreementService;
import ru.naumen.core.shared.settings.AgreementServiceSetting;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.settings.ScCaseFieldsOrderSettings;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.MetaClassLite.Status;

/**
 * Общаяя часть формы предназначеная для выбора типа запроса в связке
 * с {@link ru.naumen.core.server.bo.service.SlmService Услугами} и
 * {@link ru.naumen.core.server.bo.agreement.Agreement Соглашениями}.
 * <p>
 * Класс реализует логику этих связей. Предназначен для агрегации Презентерами
 * форм.
 * </p>
 *
 * <AUTHOR>
 */
public class SelectScCaseFormPart
{
    /**
     * Интерфейс обработчика изменения значений Соглашения/Услуги и Типа запроса
     * (нужен для того, чтобы в логику обработки изменения можно было внести специфику конкретного модуля)
     */
    public interface ValuesChangeHandler
    {
        void onValuesChanged(@Nullable DtObject agreement, @Nullable DtObject service, ClassFqn objCase);
    }

    private class AgreementServiceValueChangeHandler implements ValueChangeHandler<SelectItem>
    {
        @Override
        public void onValueChange(ValueChangeEvent<SelectItem> event)
        {
            IAgreementService value = event.getValue() instanceof IAgreementService
                    ? (IAgreementService)event.getValue()
                    : SelectItemValueExtractor.<IAgreementService> extract(event.getValue());
            onAgreementServiceChanged(value, true);
            TabOrderHelper.setFocusDeffered(agreementServiceProperty.getValueWidget(), true);
        }
    }

    private class CaseValueChangeHandler implements ValueChangeHandler<DtObject>
    {
        @Override
        public void onValueChange(ValueChangeEvent<DtObject> event)
        {
            if (event.getValue() != null)
            {
                formContextHolder.setAddFormOpenedCount(formContextHolder.getAddFormOpenedCount() - 1);
            }
            ClassFqn currentFqn = event.getValue() != null ? event.getValue().getMetainfo() : null;
            if (currentFqn == null)
            {
                currentFqn = getCurrentFqn() != null ? getCurrentFqn().fqnOfClass() : null;
            }
            setCurrentFqn(currentFqn);
            onCaseOrAssociationChanged(currentFqn);

            if (agreementServiceContext != null)
            {
                agreementServiceContext.setCurrentCase(currentFqn);
            }

        }
    }

    public static final String REFRESH_AGREEMENT_SERVICE = "refreshAgreementServices";

    public static final String REFRESH_CASE = "refreshCase";
    public static final String REFRESH_PROPERTIES = "refreshProperties";
    private final static Ordering<ISTitled> order = Ordering.from(ITitled.COMPARATOR);

    @Inject
    protected DispatchAsync dispatch;
    @Inject
    protected CommonMessages messages;
    @Inject
    protected WidgetResources widgetResources;
    @Inject
    protected RegistrationContainer handlerRegistrationContainer;
    @Inject
    protected MetainfoServiceAsync metainfoService;
    @Inject
    protected MetainfoServiceSync metainfoServiceSync;
    @Inject
    protected ValueToSelectItemConverter<IAgreementService> transformer;
    @Inject
    protected AgreementServiceContextHelper helper;
    @Inject
    private OriginProvider originProvider;

    protected AgreementServicePropertyProvider agreementServicePP;
    protected Property<SelectItem> agreementServiceProperty;
    protected PropertyRegistration<SelectItem> agreementServicePR;
    protected SynchronizationCallbackRegistration agreementServiceCR;
    protected PropertyRegistration<DtObject> casePR;
    protected SynchronizationCallbackRegistration caseCR;
    protected HasProperties display;
    protected Context context;
    @CheckForNull
    protected Processor validation;
    private Iterable<MetaClassLite> possibleCases;
    private Set<ClassFqn> possibleCasesFqns;
    /**
     * Типы запросов, которые впринципе могут быть зарегистрированы (есть ags)
     */
    protected Set<ClassFqn> casesWithClientAgs = new HashSet<>();
    private ValuesChangeHandler valuesChangeHandler;
    protected IProperties properties;
    protected DtObject agreement;
    protected DtObject service;
    protected ClassFqn currentFqn;
    private ClassFqn initialCaseCode;
    protected Property<DtObject> caseProperty;
    private final ReadyState casePropertyRS = new ReadyState(caseProperty);
    @Inject
    protected EventBus eventBus;
    protected String editPrsCode;
    protected boolean adminInterface = false;
    private boolean isCasePropertyVisible = false;
    protected String agreementServiceDescription = null;
    protected boolean showPropertyDescriptions = false;
    /**
     * clientUUID null, если на форме изменения привязки в поле "Клиент" выбрать "не указано"
     */
    @CheckForNull
    protected String clientUUID;
    protected AgreementServiceContext agreementServiceContext;
    protected SCParameters scParameters;
    @Inject
    private NotNullValidator<SelectItem> notNullSelectItemValidator;
    @Inject
    private NotNullValidator<DtObject> notNullValidatorCase;
    @Inject
    private Provider<AgreementServicePropertyFoldersTreeProvider> foldersTreeProvider;
    @Inject
    private Provider<AgreementServicePropertyListProvider> listProvider;
    @Inject
    private Provider<AgreementServicePropertyTreeListProvider> treeListProvider;
    @Inject
    private Provider<AgreementServiceHierarchicalTreePropertyProvider> hierarchicalTreeProvider;
    @Inject
    private PresentationFactories prsFactories;
    @Inject
    private CurrentBrowserTabIdentifier tabIdetifier;
    @Inject
    private FormContextHolder formContextHolder;
    @Inject
    private FormUtils formUtils;
    @Inject
    private PropertyCreator propertyCreator;
    // Значения по умолчанию
    private IAgreementService defaultItem = new AgreementServiceItem();
    private ClassFqn defaultFqn = null;

    public void bindProperties()
    {
        bindProperties(new BasicCallback<Void>());
    }

    public void bindProperties(final AsyncCallback<Void> callback)
    {
        agreementServiceContext = new AgreementServiceContext()
                .setPossibleCases(possibleCasesFqns);
        agreementServiceContext.setScParameters(scParameters);

        agreementServiceContext.getReadyState().registerSynchronization(getContext().getReadyState());

        initAgreementServiceContext();

        agreementServiceContext.getReadyState().ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                onAgreementServiceContextReady();
                bindProperties(agreementServiceContext, callback);
            }
        });
    }

    /**
     * @return соглашение из свойства Соглашение/Услуга
     */
    public DtObject getAgreement()
    {
        Property<SelectItem> property = getAgreementServiceProperty();
        if (property == null)
        {
            return agreement;
        }
        IAgreementService item = SelectListPropertyValueExtractor.getValue(property);
        return item != null ? item.getAgreement() : null;
    }

    public AgreementServiceContext getAgreementServiceContext()
    {
        return agreementServiceContext;
    }

    /**
     * @return свойство Соглашение/Услуга
     */
    public Property<SelectItem> getAgreementServiceProperty()
    {
        return agreementServiceProperty;
    }

    /**
     * Возвращает свойство Тип запроса, после создания.
     */
    public void getCaseProperty(final AsyncCallback<Property<DtObject>> callback)
    {
        casePropertyRS.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                callback.onSuccess(caseProperty);
            }
        });
    }

    public Context getContext()
    {
        return context;
    }

    /**
     * @return fqn типа запрсоа из свойства Тип запроса
     */
    public ClassFqn getMetaClassFqn()
    {
        return currentFqn;
    }

    public ScCaseFieldsOrderSettings getOrderSettings()
    {
        return agreementServiceContext.getScParameters().getOrderScSetting();
    }

    /**
     * @return множество {@link ClassFqn} допустимых для формы значений типа запроса
     */
    public Set<ClassFqn> getPossibleCasesFqns()
    {
        return possibleCasesFqns;
    }

    protected ClassFqn getInitialCaseCode()
    {
        return initialCaseCode;
    }

    public ReadyState getReadyState()
    {
        return agreementServiceContext.getReadyState();
    }

    /**
     * @return услуга из свойства Соглашение/Услуга
     */
    public DtObject getService()
    {
        Property<SelectItem> property = getAgreementServiceProperty();
        if (property == null)
        {
            return service;
        }
        IAgreementService item = SelectListPropertyValueExtractor.getValue(property);
        return item != null ? item.getService() : null;
    }

    /**
     * Производит асинхронный запрос на сервер, перезаписывает данные виджета в кэше
     * и в случае, если элемент currentItem остается доступным для выбора возвращает его,
     * в противном случае возвращает null
     * @param currentItem
     * @param callback
     */
    public void getToSelect(final IAgreementService currentItem, final AsyncCallback<IAgreementService> callback)
    {
        dispatch.execute(createValidateSelectionAction(currentItem),
                new BasicCallback<GetPossibleAgreementsListResponse>(getReadyState())
                {
                    @Override
                    public void handleSuccess(GetPossibleAgreementsListResponse response)
                    {
                        if (response.includesCurrentValue())
                        {
                            for (IAgreementService item : response.getItems())
                            {
                                // При использовании значений по умолчанию свойство CALL_CASES оказывается не заполнено
                                // Берем данные из ответа сервера
                                if (item.getUUID().equals(currentItem.getUUID()) && null != currentItem.getService()
                                    && null == currentItem.getCallCases() && null != item.getCallCases())
                                {
                                    currentItem.getService().setProperty(Constants.SlmService.CALL_CASES,
                                            item.getCallCases());
                                }
                            }
                            if (!response.getCurrentItemCallCases().isEmpty())
                            {
                                currentItem.getService().setProperty(Constants.SlmService.CALL_CASES,
                                        response.getCurrentItemCallCases());
                            }
                            callback.onSuccess(currentItem);
                        }
                        else
                        {
                            callback.onSuccess(checkDefaultItem(response.getItems()));
                        }
                    }

                    @Override
                    protected void handleFailure(Throwable t)
                    {
                        super.handleFailure(t);
                        if (t instanceof StatusCodeException && ((StatusCodeException)t).getStatusCode() == 0)
                        {
                            callback.onFailure(new StatusCodeException(0, extractFailureMessage(t)));
                        }
                    }
                });
    }

    public ValuesChangeHandler getValuesChangeHandler()
    {
        return valuesChangeHandler;
    }

    //@formatter:off
    public void init(HasProperties display,
            Context context,
            Processor validation,
            Iterable<MetaClassLite> possibleCases,
            IProperties properties,
            boolean isCasePropertyVisible)
    //@formatter:on
    {
        this.display = display;
        this.context = context;
        this.validation = validation;
        setPossibleCases(possibleCases != null
                ? order.sortedCopy(possibleCases)
                : Collections.<MetaClassLite> emptySet());
        this.properties = properties;
        this.agreement = properties.getProperty(Constants.Association.AGREEMENT);
        this.service = properties.getProperty(Constants.Association.SERVICE);
        this.currentFqn = properties.getProperty(Constants.AbstractBO.METACLASS);
        Attribute caseAttr = context.getMetainfo().getAttribute(Constants.AbstractBO.METACLASS);
        this.editPrsCode = properties.getProperty(Constants.CASE_PRESENT_CODE,
                caseAttr.getEditPresentation().getCode());
        this.initialCaseCode = properties.getProperty(Constants.AbstractBO.METACLASS);
        this.isCasePropertyVisible = isCasePropertyVisible;
    }

    /**
     * Инициализация контекста (получаем доступные Соглашения/Услуги, настройки запросов и услугу, если она указана)
     */
    public void initAgreementServiceContext()
    {
        updateAgreementServiceContext();

        final ArrayList<Action<?>> actions = getActions();

        dispatch.execute(new BatchAction(OnException.ROLLBACK, actions),
                new BasicCallback<BatchResult>(agreementServiceContext.getReadyState())
                {
                    @Override
                    protected void handleSuccess(BatchResult result)
                    {

                        @SuppressWarnings("unchecked")
                        SimpleResult<HashSet<ClassFqn>> r1 = (SimpleResult<HashSet<ClassFqn>>)result.getResult(0);
                        casesWithClientAgs = r1.get();

                        if (actions.size() >= 2 && result.getResult(1) instanceof GetDtObjectResponse)
                        {
                            GetDtObjectResponse r2 = (GetDtObjectResponse)result.getResult(1);
                            service = r2.getObj();
                        }

                        initAgreementServiceContext(result);
                    }
                });
    }

    public void initScParameters(SCParameters scParameters)
    {
        this.scParameters = scParameters;
        setAgreementServiceDescription(scParameters.getAgreementServiceSetting());
    }

    /**
     * Метод, который вызывается при инициализации или изменении свойства Соглашение/Услуга
     */
    public void onAgreementServiceChanged(IAgreementService item, boolean manualSelection)
    {
        onAgreementServiceChanged(getSelectableCases(item), manualSelection);
    }

    /**
     * Метод, который вызывается при инициализации или изменении свойства Соглашение/Услуга
     */
    public void onAgreementServiceChanged(Set<ClassFqn> selectableCases, boolean manualSelection)
    {
        ClassFqn newFqn = getNewCaseOnAgsChanged(currentFqn, selectableCases);
        if (newFqn == null && manualSelection)
        {
            ClassFqn initClassCode = initialCaseCode != null ? initialCaseCode.fqnOfClass() : null;
            newFqn = getCurrentFqn() != null ? getCurrentFqn().fqnOfClass() : initClassCode;
        }
        onCaseOrAssociationChanged(newFqn);
    }

    /**
     * Обновить свойства в зависимости от типа обновления
     * @param typeRefresh (обновить оба совйства вместе или только определенное свойство)
     */
    public void onRefresh(String typeRefresh)
    {
        onRefresh(typeRefresh, getAgreement(), getService(), getCurrentFqn());
    }

    /**
     * Обновить свойства в зависимости от типа обновления
     * @param typeRefresh (обновить оба свойства вместе или только определенное свойство)
     * @param service   - услуга для обновления
     * @param agreement - соглашение для обновления
     * @param metaclass - тип для обновления
     */
    public void onRefresh(String typeRefresh, DtObject agreement, DtObject service, ClassFqn metaclass)
    {
        if (typeRefresh == null)
        {
            return;
        }
        switch (typeRefresh) //NOPMD нет дефолтного значения
        {
            case SelectScCaseFormPart.REFRESH_PROPERTIES:
                refreshProperties(agreement, service, metaclass);
                break;
            case SelectScCaseFormPart.REFRESH_AGREEMENT_SERVICE:
                refreshAgreementServiceProperty();
                break;
            case SelectScCaseFormPart.REFRESH_CASE:
                refreshCase(agreementServicePP.getValue(agreement, service), getCurrentFqn());
                break;
        }
    }

    /**
     * Принудительное обновление формы смены соглашения/услуги
     * @see #refreshAgreementServiceProperty(AsyncCallback)
     * После пересоздания свойства устанавливается старое значение, регистрирует изменения
     */
    public void refreshAgreementServiceProperty()
    {
        if (agreementServiceProperty == null)
        {
            bindProperties();
            return;
        }
        final IAgreementService oldValue = SelectListPropertyValueExtractor.getValue(agreementServiceProperty);
        agreementServiceContext.setCurrentCase(getCurrentFqn());
        agreementServiceContext.setClientUuid(clientUUID);
        agreementServiceContext.setNoElementsToSelect(false);

        getToSelect(oldValue, new BasicCallback<IAgreementService>()
        {
            @Override
            public void handleSuccess(final IAgreementService toSelect)
            {
                refreshAgreementServiceProperty(new BasicCallback<Property<SelectItem>>(context.getReadyState())
                {
                    @Override
                    public void handleSuccess(final Property<SelectItem> property)
                    {
                        property.setValue(transformer.transform(toSelect));
                        if (oldValue != toSelect)
                        {
                            Set<ClassFqn> selectableCases = getSelectableCases(toSelect);
                            onAgreementServiceChanged(toSelect, !selectableCases.contains(getCurrentFqn()));
                        }
                        eventBus.fireEvent(new UpdateTabOrderEvent(false));
                    }
                });
            }
        });
    }

    public void setCurrentFqn(ClassFqn currentFqn)
    {
        this.currentFqn = currentFqn;
    }

    public void setDefaultFqn(ClassFqn fqn)
    {
        this.defaultFqn = fqn;
    }

    public void setDefaultItem(IAgreementService item)
    {
        this.defaultItem = item;
    }

    public void setPossibleCases(Iterable<MetaClassLite> possibleCases)
    {
        this.possibleCases = possibleCases;
        this.possibleCasesFqns = Sets.newHashSet(Iterables.transform(possibleCases, MetaClassLite.FQN_EXTRACTOR));
        if (agreementServiceContext != null)
        {
            agreementServiceContext.setPossibleCases(possibleCasesFqns);
        }
    }

    public void setShowAgreementServiceDescription(boolean showAgreementServiceDescription)
    {
        this.showPropertyDescriptions = showAgreementServiceDescription;
    }

    public void setValuesChangeHandler(ValuesChangeHandler valuesChangeHandler)
    {
        this.valuesChangeHandler = valuesChangeHandler;
    }

    public void unbind()
    {
        handlerRegistrationContainer.unbindProperties();
        handlerRegistrationContainer.removeAll();
    }

    /**
     * Определяет что форма строится в интерфейсе администратра
     */
    public void usedAdminInterface()
    {
        adminInterface = true;
    }

    /**
     * метод для установки дополнительных свойств для поля типа в потомках
     */
    protected void afterBindCaseProperty()
    {
    }

    /**
     * Метод, который вызывается после обновления свойства изменения типа, из него можно обращаться к виджету
     * напрямую т.к. он уже пересоздан
     * @param oldValue - значения свойства до обновления
     */
    protected void afterRefreshCase(DtObject oldValue)
    {
    }

    /**
     * @param agreementServiceContext контекст, хранящий в себе необходимую информацию о доступных Соглашениях/Услугах
     *
     * Метод создает свойство Соглашение/Услуга, добавляет его в display
     */
    protected void bindAgreementServiceProperty(AgreementServiceContext agreementServiceContext,
            AsyncCallback<Property<SelectItem>> callback)
    {
        agreementServicePP = selectAgreementServicePP(agreementServiceContext);
        agreementServicePP.createProperty(agreementServiceContext,
                new CallbackDecorator<Property<SelectItem>, Property<SelectItem>>(callback)
                {
                    @Override
                    protected Property<SelectItem> apply(Property<SelectItem> property)
                    {
                        unbindAgreementServiceProperty();
                        agreementServiceProperty = property;
                        if (showPropertyDescriptions && !StringUtilities.isEmpty(agreementServiceDescription))
                        {
                            property.setDescription(agreementServiceDescription);
                        }
                        agreementServiceProperty.setCaption(formUtils.getAgreementServiceCaption(getContext(), false));

                        agreementServiceProperty.addValueChangeHandler(new AgreementServiceValueChangeHandler());
                        DebugIdBuilder.ensureDebugId(agreementServiceProperty, "agreementServiceProperty");

                        if (display instanceof PropertyDialogDisplay
                            && agreementServiceContext.getScParameters().getOrderScSetting().isCaseMain()
                            && null != casePR)
                        {
                            agreementServicePR = ((PropertyDialogDisplay)display)
                                    .addPropertyAfter(agreementServiceProperty, casePR);
                        }
                        else
                        {
                            agreementServicePR = display.add(agreementServiceProperty);
                        }

                        if (validation != null)
                        {
                            agreementServiceProperty.setValidationMarker(true);
                            validation.validate(agreementServiceProperty, notNullSelectItemValidator);
                        }
                        if (caseProperty != null && caseProperty.getValueWidget() instanceof SingleSelectCellList<?>)
                        {
                            caseProperty.<SingleSelectCellList<?>> getValueWidget().setHasSearchLite(true);
                        }
                        agreementServiceCR = context.getReadyState()
                                .registerSynchronization(new HasEnabledSynchronization(this, agreementServiceProperty));
                        handlerRegistrationContainer.register(agreementServicePR);

                        bindAgreementServicePropertyStyles();
                        return agreementServiceProperty;
                    }
                });
    }

    /**
     * Метод для установки дополнительных стилей для свойства в потомках
     */
    protected void bindAgreementServicePropertyStyles()
    {
        agreementServiceProperty.getCaptionWidget().asWidget().addStyleName(widgetResources.form().paddedBlock());
        agreementServiceProperty.getValueWidget().asWidget().addStyleName(widgetResources.form().paddedBlock());
    }

    /**
     * Создает свойство Тип запроса, добавляет его в display, метод синхронный, так как и свойство будет создано
     * синхронно
     */
    protected void bindCaseProperty(final AgreementServiceContext agreementServiceContext,
            final AsyncCallback<Void> callback)
    {
        final String objTypeTitle = getContext().getMetainfo() == null ? messages.objectType()
                : getContext().getMetainfo().getAttribute(Constants.AbstractBO.METACLASS).getTitle();

        Attribute caseAttr = context.getMetainfo().getAttribute(Constants.AbstractBO.METACLASS);
        PresentationFactoryEdit<DtObject> editPrsFactory = prsFactories.getEditPresentationFactory(editPrsCode);
        PresentationContext presentContext = new PresentationContext(caseAttr);
        presentContext.setPermittedTypeFqns(
                getSelectableCases(SelectListPropertyValueExtractor.getValue(agreementServiceProperty)));
        presentContext.setParentContext(context);
        context.setContextProperty(Constants.CASE_PRESENT_VALUE, getCurrentFqn());
        presentContext.put(Constants.ADMIN_ALIAS, adminInterface);
        presentContext.setSelectEmptyOption(true);
        editPrsFactory.createWidget(presentContext, new BasicCallback<HasValueOrThrow<DtObject>>(casePropertyRS)
        {
            @Override
            protected void handleSuccess(HasValueOrThrow<DtObject> widget)
            {
                unbindCaseProperty();
                caseProperty = propertyCreator.create(messages.metaCase(), widget);
                if (caseProperty.getValueWidget() instanceof SingleSelectCellList<?>)
                {
                    caseProperty.<SingleSelectCellList<?>> getValueWidget().setHasSearchLite(true);
                }
                String title = caseAttr.isHiddenAttrCaption() ? StringUtilities.EMPTY : objTypeTitle;
                caseProperty.setCaption(title);
                if (showPropertyDescriptions && !StringUtilities.isEmpty(caseAttr.getDescription()))
                {
                    caseProperty.setDescription(caseAttr.getDescription());
                }
                caseProperty.addValueChangeHandler(new CaseValueChangeHandler());
                caseProperty.setValue(caseProperty.getValue(), false);
                DebugIdBuilder.ensureDebugId(caseProperty, "caseProperty");
                if (validation != null)
                {
                    //@formatter:off
                    caseProperty.setValidationMarker(true);
                    validation.validate(caseProperty, notNullValidatorCase);
                    //@formatter:on
                }
                bindCasePropertyStyles();
                afterBindCaseProperty();

                if (display instanceof PropertyDialogDisplay
                    && agreementServiceContext.getScParameters().getOrderScSetting().isAgsMain()
                    && null != agreementServicePR)
                {
                    casePR = ((PropertyDialogDisplay)display).addPropertyAfter(caseProperty, agreementServicePR);
                }
                else
                {
                    int index = agreementServiceContext.getScParameters().getOrderScSetting().getCaseIndex();
                    casePR = display.addProperty(caseProperty, Math.max(display.getPropertiesCount() - 1 + index, 0));
                }

                caseCR = context.getReadyState()
                        .registerSynchronization(new HasEnabledSynchronization(this, caseProperty));
                handlerRegistrationContainer.register(casePR);

                eventBus.fireEvent(new UpdateTabOrderEvent(true));

                callback.onSuccess(null);
            }

            @Override
            protected void handleFailure(Throwable t)
            {
                super.handleFailure(t);
                if (t instanceof StatusCodeException && ((StatusCodeException)t).getStatusCode() == 0)
                {
                    callback.onFailure(new StatusCodeException(0, extractFailureMessage(t)));
                }
            }
        });
    }

    /**
     * Метод для установки дополнительных стилей для свойства в потомках
     */
    protected void bindCasePropertyStyles()
    {
        caseProperty.getCaptionWidget().asWidget().addStyleName(widgetResources.form().paddedBlock());
        caseProperty.getValueWidget().asWidget().addStyleName(widgetResources.form().paddedBlock());
    }

    /**
     * Создание свойств, все необходимые параметры хранятся в контексте
     */
    protected void bindProperties(final AgreementServiceContext agreementServiceContext,
            final AsyncCallback<Void> callback)
    {
        getToSelect(getCurrentItem(), new BasicCallback<IAgreementService>()
        {
            @Override
            public void handleSuccess(final IAgreementService toSelect)
            {
                bindAgreementServiceProperty(agreementServiceContext, new BasicCallback<Property<SelectItem>>()
                {
                    @Override
                    protected void handleSuccess(Property<SelectItem> property)
                    {
                        IAgreementService singleValue = agreementServiceContext.getSingleValue();
                        IAgreementService initialValue = singleValue != null ? singleValue :
                                agreementServicePP.getValue(agreement, service);
                        IAgreementService actualToSelect = toSelect;
                        if (toSelect != null)
                        {
                            actualToSelect = initialValue;
                        }
                        initAgreementServicePropertyValue(initialValue, actualToSelect);
                        bindCaseProperty(agreementServiceContext, callback);
                        initAgreementServiceAndCase(
                                SelectListPropertyValueExtractor.getValue(agreementServiceProperty));

                        eventBus.fireEvent(new UpdateTabOrderEvent(true));
                    }
                });
            }
        });
    }

    protected void checkCurrentValueConform(IAgreementService current)
    {
        if (validation != null || !(display instanceof PropertyDialogDisplay))
        {
            return;
        }

        String msg1 = checkUnconformValue(current);
        String msg2 = checkEmptyMainField(agreement, getCurrentFqn());
        String msg = StringUtilities.join(Lists.newArrayList(msg1, msg2));
        if (StringUtilities.isNotEmpty(msg))
        {
            msg = messages.mcDefParamsUncomformSCParamsWarnMessage(msg);
            PropertyDialogDisplay pdDisplay = (PropertyDialogDisplay)display;
            pdDisplay.addAttentionMessage(msg);
        }
    }

    protected ArrayList<Action<?>> getActions()
    {
        GetCasesWithClientAgsAction getCasesWithClientAgsAction = new GetCasesWithClientAgsAction(clientUUID,
                isFastCreate(), adminInterface, isCasePropertyVisible, properties);
        getCasesWithClientAgsAction.setFormCode(originProvider.getFormCode(context));

        final ArrayList<Action<?>> actions = Lists.newArrayList(getCasesWithClientAgsAction);
        if (service != null)
        {
            //получим услугу с сервера, в ней будут заполнены все необходимые свойства (типы запросов)
            GetDtObjectAction action = new GetDtObjectAction(service.getUUID());
            action.setProperties(DtoProperties.systemProperties().add(SlmService.CALL_CASES))
                    .setCheckAttrPermissions(false);
            actions.add(action);
        }

        return actions;
    }

    protected ClassFqn getCurrentFqn()
    {
        return currentFqn;
    }

    /**
     * @return текущий объект Соглашение/Услуга
     */
    protected IAgreementService getCurrentItem()
    {
        if (agreement == null)
        {
            return null;
        }
        else if (service == null)
        {
            return new AgreementServiceItem(agreement);
        }
        return new AgreementServiceItem(agreement, service);
    }

    /**
     * @return набор свойств для фильтрации Соглашений/Услуг
     * (нужен для фильтрации скриптом на стороне сервера)
     */
    protected IProperties getFiltrationProperties()
    {
        return (validation != null) ? properties : null;
    }

    protected ClassFqn getNewCaseOnAgsChanged(ClassFqn fqn, Set<ClassFqn> selectableCases)
    {
        if (selectableCases.contains(fqn))
        {
            return fqn;
        }

        if (selectableCases.size() == 1 && formContextHolder.needSelectSingleValueForAttribute(true))
        {
            return selectableCases.iterator().next();
        }

        return null;
    }

    /**
     * @return коды типов, доступных для конкретного экземпляра Соглашения/Услуги
     * (для соглашения доступны все типы, для услуги доступные задаются в атрибуте)
     */
    protected Set<ClassFqn> getSelectableCases(IAgreementService agreementServiceItem)
    {
        Set<ClassFqn> selectableCases = new HashSet<>();

        if (getOrderSettings().isAgsMain() && agreementServiceItem == null)
        {
            return selectableCases;
        }

        Collection<ClassFqn> cases = agreementServiceItem != null ? agreementServiceItem.getCallCases() : null;

        for (MetaClassLite mc : possibleCases)
        {
            if (!Status.REMOVED.equals(mc.getStatus())
                && (getOrderSettings().isCaseMain() || null == cases || cases.contains(mc.getFqn())))
            {
                selectableCases.add(mc.getFqn());
            }
        }
        return Sets.newHashSet(Sets.intersection(selectableCases, casesWithClientAgs));
    }

    /**
     * Метод, который вызывается при инициализации или изменении свойства Соглашение/Услуга
     */
    protected void initAgreementServiceAndCase(@Nullable IAgreementService item)
    {
        Set<ClassFqn> selectableCases = item != null
                ? getSelectableCases(item)
                : getPossibleCasesFqns();
        if (initialCaseCode == null || selectableCases.contains(initialCaseCode))
        {
            onCaseOrAssociationChanged(initialCaseCode);
        }
    }

    /**
     * Доинициализация контекста в потомках, после получения результата обращения к серверу
     */
    protected void initAgreementServiceContext(BatchResult result)
    {

    }

    /**
     * Метод заполняет значения свойств при инициализации
     */
    protected void initAgreementServicePropertyValue(IAgreementService oldItem, IAgreementService itemToSet)
    {
        checkCurrentValueConform(oldItem);
        if (itemToSet != null)
        {
            agreementServiceProperty.setValue(transformer.transform(itemToSet));
        }
    }

    protected boolean isFastCreate()
    {
        return false;
    }

    protected boolean needSaveCase(ClassFqn fqn, IAgreementService item)
    {
        return possibleCasesFqns.contains(fqn) && getSelectableCases(item).contains(fqn);
    }

    /**
     * Метод - обработчик окончания инициализации контекста
     */
    protected void onAgreementServiceContextReady()
    {

    }

    protected void onCaseOrAssociationChanged(@Nullable ClassFqn newCase)
    {
        if (getValuesChangeHandler() != null)
        {
            IAgreementService item = SelectListPropertyValueExtractor.getValue(agreementServiceProperty);
            DtObject agreement = item != null ? item.getAgreement() : null;
            DtObject service = item != null ? item.getService() : null;
            if ((getOrderSettings().isAgsMain() && item == null
                 || caseProperty != null && getOrderSettings().isCaseMain() && caseProperty.getValue() == null)
                && (newCase != null && newCase.isCase()))
            {
                newCase = null;
            }
            getValuesChangeHandler().onValuesChanged(agreement, service, newCase);
        }
    }

    /**
     * Метод выполняет переинициализацию свойства Соглашение/Услуга
     * (нужно, например, для фильтрации скриптом при изменении свойства на форме, которое используется в скрипте -
     * показалось проще пересоздать свойство, чем научиться очищать дерево и добавлять в него элементы -
     * элементы дерева кэшируются в gwt)
     * После пересоздания свойства управление передается в callback
     */
    protected void refreshAgreementServiceProperty(final AsyncCallback<Property<SelectItem>> callback)
    {
        GetCasesWithClientAgsAction action = new GetCasesWithClientAgsAction(clientUUID, isFastCreate(), adminInterface,
                isCasePropertyVisible, properties);
        action.setFormCode(originProvider.getFormCode(context));
        dispatch.execute(action,
                new BasicCallback<SimpleResult<HashSet<ClassFqn>>>(getReadyState())
                {
                    @Override
                    public void handleSuccess(SimpleResult<HashSet<ClassFqn>> result)
                    {
                        casesWithClientAgs = result.get();
                        bindAgreementServiceProperty(agreementServiceContext, callback);
                    }

                    @Override
                    protected void handleFailure(Throwable t)
                    {
                        super.handleFailure(t);
                        if (t instanceof StatusCodeException && ((StatusCodeException)t).getStatusCode() == 0)
                        {
                            callback.onFailure(new StatusCodeException(0, extractFailureMessage(t)));
                        }
                    }
                });
    }

    /**
     *  Принудительное обновление формы смены типа запроса, форма будет обновлена после её окончательного создания,
     *  после пересоздания формы установится старое значение, регистрирует изменения
     *  @parama newItem - альтернативное значение соглашения/услуги
     *  @parama fqn - альтернативный тип вместо старого значения
     */
    protected void refreshCase(final IAgreementService newItem, final ClassFqn fqn)
    {
        getCaseProperty(new BasicCallback<Property<DtObject>>()
        {
            @Override
            public void handleSuccess(Property<DtObject> property)
            {
                //Кейс: создалось agreementServiceProperty, в нем автоматически выбралось единственное значение -
                // попало в pendingState в SelectionModel
                //Потом создается caseProperty, и в нем делается agreementServiceProperty.getValue, что вызывает
                // resolveChanges в SelectionModel,
                //что в свою очередь триггерит agreementServiceProperty VCH, и вызывает refreshCase и биндит
                // caseProperty повторно посередине первого биндинга
                if (casePR == null)
                {
                    return;
                }
                final DtObject oldValue = property != null ? property.getValue() : null;
                final DtObject returnValue = fqn != null
                        ? new SimpleDtObject(fqn.asString(), metainfoService.getMetaClassTitle(fqn), fqn)
                        : null;

                bindCaseProperty(agreementServiceContext, new BasicCallback<Void>());

                if (property == null)
                {
                    return;
                }

                getCaseProperty(new BasicCallback<Property<DtObject>>()
                {
                    @Override
                    public void handleSuccess(Property<DtObject> property)
                    {
                        Attribute attr = context.getMetainfo().getAttribute(Constants.AbstractBO.METACLASS);
                        boolean singleValueHasBeenChosen = formContextHolder
                                                                   .needSelectSingleValueForAttribute(attr)
                                                           && property.getValue() != null
                                                           && !ObjectUtils.equals(property.getValue().getMetaClass(),
                                getCurrentFqn());
                        if ((returnValue == null && !singleValueHasBeenChosen)
                            || (returnValue != null && needSaveCase(returnValue.getMetainfo(),
                                newItem == null ? getCurrentItem() : newItem)))
                        {
                            property.setValue(ValueToSelectItemConverter.convert(returnValue), true);
                        }
                        ClassFqn currentFqn = property.getValue() != null ? property.getValue().getMetainfo() : null;
                        setCurrentFqn(currentFqn);
                        afterRefreshCase(oldValue);
                        eventBus.fireEvent(new UpdateTabOrderEvent(false));
                    }
                });
            }
        });
    }

    /**
     * Принудительное обновление формы смены соглашения/услуги и типа, регистрирует изменения,
     * после пересоздания формы установится значение из контекста
     */
    protected void refreshProperties()
    {
        refreshProperties(getAgreement(), getService(), getCurrentFqn());
    }

    /**
     * Принудительное обновление формы смены соглашения/услуги и типа, регистрирует изменения,
     * после пересоздания формы установится значение из контекста
     * Предполагает альтернативные значения полей после обновления.
     */
    protected void refreshProperties(final DtObject agreement, final DtObject service, final ClassFqn fqn)
    {
        if (agreementServiceProperty == null)
        {
            bindProperties();
            return;
        }
        final IAgreementService oldValue = agreementServicePP.getValue(agreement, service);
        ClassFqn currentFqn = fqn;
        setCurrentFqn(currentFqn);
        agreementServiceContext.setCurrentCase(currentFqn);
        agreementServiceContext.setClientUuid(clientUUID);
        agreementServiceContext.setNoElementsToSelect(false);

        getToSelect(oldValue, new BasicCallback<IAgreementService>(context.getReadyState())
        {
            @Override
            public void handleSuccess(final IAgreementService toSelect)
            {
                refreshAgreementServiceProperty(new BasicCallback<Property<SelectItem>>(context.getReadyState())
                {
                    @Override
                    public void handleSuccess(final Property<SelectItem> property)
                    {
                        if (property.getValue() == null)
                        {
                            if (oldValue == null || oldValue.equals(toSelect))
                            {
                                property.setValue(transformer.transform(oldValue));
                                Set<ClassFqn> selectableCases = getSelectableCases(oldValue);
                                if (getCurrentFqn() != null && !selectableCases.contains(getCurrentFqn()))
                                {
                                    onAgreementServiceChanged(selectableCases, toSelect == null);
                                }
                            }
                            else
                            {
                                property.setValue(transformer.transform(toSelect));
                                Set<ClassFqn> selectableCases = getSelectableCases(toSelect);
                                onAgreementServiceChanged(toSelect, !selectableCases.contains(getCurrentFqn()));
                            }
                        }

                        refreshCase(SelectListPropertyValueExtractor.getValue(property), getCurrentFqn());
                    }
                });
            }
        });
    }

    protected void registerHandler(HandlerRegistration handlerRegistration)
    {
        handlerRegistrationContainer.registerHandler(handlerRegistration);
    }

    protected void unbindAgreementServiceProperty()
    {
        if (null != validation)
        {
            validation.unvalidate(agreementServiceProperty);
        }
        if (agreementServicePR != null)
        {
            agreementServicePR.unregister();
            IsWidget agreementServiceWidget = agreementServiceProperty.getValueWidget();
            if (agreementServiceWidget instanceof HasPopup)
            {
                ((HasPopup)agreementServiceWidget).hidePopup();
            }
        }
        if (agreementServiceCR != null)
        {
            agreementServiceCR.unregister();
        }
    }

    protected void unbindCaseProperty()
    {
        if (validation != null && caseProperty != null)
        {
            validation.unvalidate(caseProperty);
        }
        if (casePR != null)
        {
            casePR.unregister();
            IsWidget caseWidget = caseProperty.getValueWidget();
            if (caseWidget instanceof HasPopup)
            {
                ((HasPopup)caseWidget).hidePopup();
            }
        }
        if (caseCR != null)
        {
            caseCR.unregister();
        }
    }

    protected void updateAgreementServiceContext()
    {
        agreementServiceContext.setClientUuid(clientUUID);
        agreementServiceContext.setCurrentCase(getCurrentFqn());
        agreementServiceContext.setFiltrationProperties(getFiltrationProperties());
        agreementServiceContext.setCaseMain(getOrderSettings().isCaseMain());
    }

    /**
     * Метод необходим для обновления значения свойства на форме при его изменении.
     */
    @SuppressWarnings("rawtypes")
    protected void updateProperty(String name, Object value)
    {
        Object newValue = (value instanceof PropertyBase ? ((PropertyBase)value).getValue() : value);
        this.properties.setProperty(name, newValue);
    }

    /**
     * Установка значений по умолчанию если предыдущие значения не подходят новому контрагенту
     * @param items
     * @return значение по умолчанию
     */
    private IAgreementService checkDefaultItem(List<IAgreementService> items)
    {
        for (IAgreementService item : items)
        {
            if (ObjectUtils.equals(defaultItem.getAgreement(), item.getAgreement())
                && ObjectUtils.equals(defaultItem.getService(), item.getService()))
            {
                if (null != item.getCallCases() && item.getCallCases().contains(defaultFqn))
                {
                    setCurrentFqn(defaultFqn);
                }
                return item;
            }
        }
        if (items.isEmpty() && !(null == defaultItem.getAgreement() && null == defaultItem.getService()))
        {
            setCurrentFqn(defaultFqn);
            return defaultItem;
        }
        if (formContextHolder.needSelectSingleValueForAttribute(true) && items.size() == 1)
        {
            return items.get(0);
        }
        return null;
    }

    private String checkEmptyMainField(DtObject currentAgreement, ClassFqn currentCase)
    {
        ScCaseFieldsOrderSettings setting = agreementServiceContext.getScParameters().getOrderScSetting();

        if (setting.isCaseMain() && currentAgreement != null && currentCase == null)
        {
            return messages.mcDefParamsUncomformSCParamsWarnMessageOrder(messages.metaCase());
        }
        else if (setting.isAgsMain() && currentCase != null && currentAgreement == null)
        {
            return messages
                    .mcDefParamsUncomformSCParamsWarnMessageOrder(formUtils.getAgreementServiceCaption(getContext(),
                            true));
        }
        return "";
    }

    private String checkUnconformValue(IAgreementService current)
    {
        if (!helper.isConform(agreementServiceContext, current))
        {
            //@formatter:off
            return current.getService() != null
                    ? messages.mcDefParamsUncomformSCParamsWarnMessageAgreement()
                    : messages.mcDefParamsUncomformSCParamsWarnMessageService();
            //@formatter:on
        }
        return "";
    }

    private GetPossibleAgreementsAction<GetPossibleAgreementsListResponse> createValidateSelectionAction(
            IAgreementService currentItem)
    {
        IProperties filtrationProperties = agreementServiceContext.getFiltrationProperties();
        List<ClassFqn> possibleCases = agreementServiceContext.getPossibleCases();
        String actionUuid = tabIdetifier.getTabId();
        GetPossibleAgreementsAction<GetPossibleAgreementsListResponse> action;
        switch (agreementServiceContext.getScParameters().getAgreementServiceEditPrs())
        {
            case TreeList:
            case ServiceTreeList, HierarchicalTreeList, HierarchicalServiceTreeList :
                action = new GetPossibleAgreementsTreeListAction(clientUUID, filtrationProperties, possibleCases,
                        actionUuid,
                        currentItem);
                break;
            case List:
                action = new GetPossibleAgreementsListAction(clientUUID, filtrationProperties, possibleCases,
                        actionUuid,
                        currentItem);
                break;
            case FoldersTree:
                action = new GetPossibleAgreementsChildrenAction(clientUUID, filtrationProperties, possibleCases,
                        actionUuid,
                        currentItem);
                break;
        default:
            throw new IllegalArgumentException("SelectScCaseFormPart.createValidateSelectionAction");
        }
        action.setOrigin(originProvider.getOrigin(getContext()));
        action.setFormCode(originProvider.getFormCode(getContext()));
        return action;
    }

    private AgreementServicePropertyProvider<?> selectAgreementServicePP(
            AgreementServiceContext agreementServiceContext)
    {
        switch (agreementServiceContext.getScParameters().getAgreementServiceEditPrs())
        {
        case TreeList:
        case ServiceTreeList:
            return treeListProvider.get();
        case List:
            return listProvider.get();
        case FoldersTree:
            return foldersTreeProvider.get();
        case HierarchicalServiceTreeList, HierarchicalTreeList:
            return hierarchicalTreeProvider.get();
        default:
            throw new IllegalArgumentException("SelectScCaseFormPart.selectAgreementServicePP");
        }
    }

    private void setAgreementServiceDescription(AgreementServiceSetting serviceSetting)
    {
        if (serviceSetting == Agreement)
        {
            this.agreementServiceDescription = getContext().getMetainfo().getAttribute(AGREEMENT).getDescription();
        }
        else if (serviceSetting == Service)
        {
            this.agreementServiceDescription = getContext().getMetainfo().getAttribute(SERVICE).getDescription();
        }
        else if (serviceSetting == Both)
        {
            if (!StringUtilities.isEmpty(getContext().getMetainfo().getAttribute(AGREEMENT).getDescription())
                && !StringUtilities.isEmpty(getContext().getMetainfo().getAttribute(SERVICE).getDescription()))
            {
                this.agreementServiceDescription = getContext().getMetainfo().getAttribute(AGREEMENT).getDescription()
                                                   + " / " + getContext().getMetainfo()
                                                           .getAttribute(SERVICE)
                                                           .getDescription();
            }
            else if (!StringUtilities.isEmpty(getContext().getMetainfo().getAttribute(SERVICE).getDescription()))
            {
                this.agreementServiceDescription = getContext().getMetainfo().getAttribute(SERVICE).getDescription();
            }
            else if (!StringUtilities.isEmpty(getContext().getMetainfo().getAttribute(AGREEMENT).getDescription()))
            {
                this.agreementServiceDescription = getContext().getMetainfo().getAttribute(AGREEMENT).getDescription();
            }
        }
    }
}
