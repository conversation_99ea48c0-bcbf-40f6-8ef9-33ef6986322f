package ru.naumen.core.client.content.sccase.agrserv.property;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.content.sccase.agrserv.context.AgreementServiceContext;
import ru.naumen.core.client.content.sccase.agrserv.property.tree.AgreementServiceTreeGinModule.AgreementServiceTree;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.tree.dto.DtoTreeFactorySplitPoint;
import ru.naumen.core.client.tree.view.TreeFactory;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.sccase.agrserv.item.AgreementServiceItem;
import ru.naumen.core.shared.sccase.agrserv.item.IAgreementService;

/**
 * <AUTHOR>
 * @since 13.08.2025
 */
public class AgreementServiceHierarchicalTreePropertyProvider implements AgreementServicePropertyProvider<IAgreementService>
{

    @Inject
    private SplitPointService splitPointService;

//    @Override
//    public void createProperty(AgreementServiceContext context, AsyncCallback callback)
//    {
//        SimpleSelectCellListBuilder<IAgreementService> listBuilder = selectCellListBuilderProvider
//                .get();
//        listBuilder.setMultiSelect(false);
//        listBuilder.setDataProvider(dataProviderFactory.create(context));
//        listBuilder.withEmptyOption();
//        listBuilder.setHasSearch(true);
//        listBuilder.setSelectSingleValue(formContextHolder.needSelectSingleValueForAttribute(true));
//        SingleSelectCellList<IAgreementService> w = listBuilder.build();
//
//        Predicate<SelectItem> selectableFilter = value -> Constants.EMPTY.equals(value.getCode())
//                || SelectItemValueExtractor.<IAgreementService> extract(value)
//                .isSelectable();
//
//        listBuilder.getCell().setSelectableFilter(selectableFilter);
//
//        w.setSelectableFilter(selectableFilter);
//
//        w.setStyleName(resources.form().formSelectSelected());
//        styleUpdater.setFocusAndBlurHandlers(WidgetTypeCode.SIMPLE, w);
//        styleUpdater.setValidationHandler(WidgetTypeCode.SIMPLE, w);
//
//        callback.onSuccess(new PropertyBase<SelectItem, SingleSelectCellList<IAgreementService>>(w));
//    }

    @Override
    public void createProperty(AgreementServiceContext context, AsyncCallback<Property<IAgreementService>> callback)
    {
        DtoTreeFactorySplitPoint dtoTreeFactories = splitPointService.get(DtoTreeFactorySplitPoint.class);

        TreeFactory<IAgreementService, AgreementServiceTree, AgreementServiceContext> treeFactory = dtoTreeFactories
                .getAgreementServiceTreeFactory();

        treeFactory.createTree(context,
                new CallbackDecorator<HasValueOrThrow<IAgreementService>, Property<IAgreementService>>(callback)
                {
                    @Override
                    protected Property<IAgreementService> apply(HasValueOrThrow<IAgreementService> tree)
                    {
                        return new PropertyBase<IAgreementService, HasValueOrThrow<IAgreementService>>(tree);
                    }
                });
    }

    @Override
    public IAgreementService getValue(@Nullable DtObject agreement, DtObject service)
    {
        return agreement == null ? null : new AgreementServiceItem(agreement, service);
    }
}