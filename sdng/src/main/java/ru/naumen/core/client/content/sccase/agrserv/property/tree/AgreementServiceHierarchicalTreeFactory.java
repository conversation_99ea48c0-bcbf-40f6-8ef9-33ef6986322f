package ru.naumen.core.client.content.sccase.agrserv.property.tree;

import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.view.client.SingleSelectionModel;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.sccase.agrserv.context.AgreementServiceContext;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.tree.dto.DtoTreeFactorySplitPoint;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.AgreementServiceHierarchicalTree;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactoryContext;
import ru.naumen.core.client.tree.view.TreeFactory;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants.AttributeLink;
import ru.naumen.core.shared.dto.DtObject;

/**
 * <AUTHOR>
 * @since 21.08.2025
 */
@Singleton
public class AgreementServiceHierarchicalTreeFactory implements TreeFactory<DtObject,
        AgreementServiceHierarchicalTree, DtoTreeFactoryContext>
{
    private final SplitPointService splitPointService;
    private final CommonMessages messages;

    @Inject
    public AgreementServiceHierarchicalTreeFactory(SplitPointService splitPointService, CommonMessages messages)
    {
        this.splitPointService = splitPointService;
        this.messages = messages;
    }

    @Override
    public HasValueOrThrow<DtObject> createTree(DtoTreeFactoryContext context)
    {
        return null;
    }

    @Override
    public void createTree(DtoTreeFactoryContext context, AsyncCallback<HasValueOrThrow<DtObject>> callback)
    {
        DtoTreeFactorySplitPoint dtoTreeFactories = splitPointService.get(DtoTreeFactorySplitPoint.class);
                //AgreementServiceContext factoryContext = new DtoTreeFactoryContext().setPrsContext(context);
                dtoTreeFactories.getAgreementServiceHierarchicalTreeFactory().createTree(context,
                        new CallbackDecorator<HasValueOrThrow<DtObject>, HasValueOrThrow<DtObject>>(callback)
                        {
                            @Override
                            protected HasValueOrThrow<DtObject> apply(HasValueOrThrow<DtObject> from)
                            {
                                ((PopupValueCellTree<DtObject, DtObject, SingleSelectionModel<DtObject>>)from)
                                        .setFormatter(dto ->
                                        {
                                            if (dto == null)
                                            {
                                                return messages.brackets(messages.empty());
                                            }
                                            List<String> path = dto.getProperty(AttributeLink.ATTRIBUTE_PATH_PROPERTY);
                                            if (!CollectionUtils.isEmpty(path))
                                            {
                                                return StringUtilities.join(path, AttributeLink.CHAIN_PATH_DELIMITER);
                                            }
                                            return dto.getTitle();
                                        });
                                return from;
                            }
                        });
    }

    //    @Override
//    public HasValueOrThrow<DtObject> createTree(AgreementServiceContext context)
//    {
//        return null;
//    }
//
//    @Override
//    public void createTree(AgreementServiceContext context, AsyncCallback<HasValueOrThrow<DtObject>> callback)
//    {
//        DtoTreeFactorySplitPoint dtoTreeFactories = splitPointService.get(DtoTreeFactorySplitPoint.class);
    //        //AgreementServiceContext factoryContext = new DtoTreeFactoryContext().setPrsContext(context);
    //        dtoTreeFactories.getAgreementServiceHierarchicalTreeFactory().createTree(context,
    //                new CallbackDecorator<HasValueOrThrow<DtObject>, HasValueOrThrow<DtObject>>(callback)
    //                {
    //                    @Override
    //                    protected HasValueOrThrow<DtObject> apply(HasValueOrThrow<DtObject> from)
    //                    {
    //                        ((PopupValueCellTree<DtObject, DtObject, SingleSelectionModel<DtObject>>)from)
    //                                .setFormatter(dto ->
    //                                {
    //                                    if (dto == null)
    //                                    {
    //                                        return messages.brackets(messages.empty());
    //                                    }
    //                                    List<String> path = dto.getProperty(AttributeLink.ATTRIBUTE_PATH_PROPERTY);
    //                                    if (!CollectionUtils.isEmpty(path))
    //                                    {
    //                                        return StringUtilities.join(path, AttributeLink.CHAIN_PATH_DELIMITER);
    //                                    }
    //                                    return dto.getTitle();
    //                                });
    //                        return from;
    //                    }
//                });
//    }
}