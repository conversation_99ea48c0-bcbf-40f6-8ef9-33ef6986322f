package ru.naumen.core.client.content.sccase.agrserv.property.tree;

import ru.naumen.core.client.tree.dto.DtoTreeGinModule;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactoryContext;
import ru.naumen.core.client.tree.dto.view.DtoTreeFilteredViewModelFactoryImpl;
import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Генератор модели дерева объектов для свойства "Соглашение/Услуга"
 *
 * <AUTHOR>
 * @since 15.08.2025
 */
public class AgreementServiceHierarchicalTreeViewModelFactory extends
        DtoTreeFilteredViewModelFactoryImpl<DtoTreeGinModule.AgreementServiceHierarchicalTree, DtoTreeGinModule.WithoutFolders,
                FilteredSingleSelectionModel<DtObject>, DtoTreeFactoryContext>
{

}