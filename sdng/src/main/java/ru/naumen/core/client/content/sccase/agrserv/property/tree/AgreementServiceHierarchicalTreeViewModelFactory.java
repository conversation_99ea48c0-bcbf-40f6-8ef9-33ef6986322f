package ru.naumen.core.client.content.sccase.agrserv.property.tree;

import jakarta.inject.Inject;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.AgreementServiceHierarchicalTree;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactoryContext;
import ru.naumen.core.client.tree.dto.view.DtoTreeFilteredViewModelFactoryImpl;
import ru.naumen.core.client.tree.dto.view.IDtoTreeViewModel;
import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.shared.dto.DtObject;
import com.google.inject.Provider;
import com.google.inject.Singleton;

/**
 * Генератор модели дерева объектов для свойства "Соглашение/Услуга"
 *
 * <AUTHOR>
 * @since 15.08.2025
 */
@Singleton
public class AgreementServiceHierarchicalTreeViewModelFactory extends
        DtoTreeFilteredViewModelFactoryImpl<DtoTreeGinModule.AgreementServiceHierarchicalTree, DtoTreeGinModule.WithoutFolders,
                FilteredSingleSelectionModel<DtObject>, DtoTreeFactoryContext>
{
    @Inject
    private Provider<AgreementServiceHierarchicalTreeViewModelImpl> ASJHTVMFactory;

    protected AgreementServiceHierarchicalTreeViewModelImpl<AgreementServiceHierarchicalTree, FilteredSingleSelectionModel<DtObject>> buildTreeViewModel(
            DtoTreeFactoryContext context)
    {
        AgreementServiceHierarchicalTreeViewModelImpl result = ASJHTVMFactory.get();
        return super.buildTreeViewModel(context);
    }
}