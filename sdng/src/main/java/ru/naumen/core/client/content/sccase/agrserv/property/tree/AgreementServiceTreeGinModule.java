package ru.naumen.core.client.content.sccase.agrserv.property.tree;

import ru.naumen.core.client.content.sccase.agrserv.context.AgreementServiceContext;
import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.tree.IsFolderPredicate;
import ru.naumen.core.client.tree.TreeFactoryGinModule;
import ru.naumen.core.client.tree.TreeValueGinModule;
import ru.naumen.core.client.tree.adapter.TreeValueAdapterGinModule;
import ru.naumen.core.client.tree.cell.ContentFilteredWithFoldersTreeCell;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.AgreementServiceHierarchicalTree;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.TreeType;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithFolders;
import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.client.tree.view.TreeViewModelContext;
import ru.naumen.core.client.widgets.tree.PopupSingleValueCellTree;
import ru.naumen.core.client.widgets.tree.ValueCellTreeSingleGinModule;
import ru.naumen.core.client.widgets.tree.cell.WidgetTreeCellGinModule.WithoutRemoved;
import ru.naumen.core.client.widgets.tree.formatters.TreeSingleValueFormatter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.sccase.agrserv.item.FolderItem;
import ru.naumen.core.shared.sccase.agrserv.item.IAgreementService;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;

/**
 * <AUTHOR>
 * @since 05.02.2013
 */
public class AgreementServiceTreeGinModule extends AbstractGinModule
{
    //@formatter:off
    public static class AgreementServiceTree extends TreeType{}
    //@formatter:on

    static class AgreementServiceRootProvider implements Provider<IAgreementService>
    {
        @Override
        public IAgreementService get()
        {
            return new FolderItem("root", "root");
        }
    }

    @Override
    protected void configure()
    {
        //@formatter:off
        install(ValueCellTreeSingleGinModule.create(IAgreementService.class, new TypeLiteral<IAgreementService>(){}, new TypeLiteral<AgreementServiceSingleSelectionModel>(){}, WithFolders.class, WithoutRemoved.class)
                .setPopupValueCellTree(new TypeLiteral<PopupSingleValueCellTree<IAgreementService, AgreementServiceSingleSelectionModel>>(){})
                .setValueCell(new TypeLiteral<ContentFilteredWithFoldersTreeCell<IAgreementService, AgreementServiceSingleSelectionModel>>(){}));

        install(TreeFactoryGinModule.create(AgreementServiceTree.class,
                    IAgreementService.class,
                    new TypeLiteral<AgreementServiceSingleSelectionModel>(){},
                    new TypeLiteral<TreeViewModelContext<IAgreementService, AgreementServiceSingleSelectionModel>>(){},
                    new TypeLiteral<AgreementServiceTreeViewModel>(){},
                    AgreementServiceContext.class)
               .setTreeFactory(new TypeLiteral<AgreementServiceTreeFactory>(){})
               .setTreeViewModelFactory(new TypeLiteral<AgreementServiceTreeViewModelFactory>(){})
               .setDataSource(new TypeLiteral<AgreementServiceTreeDataSource>(){}));

        install(TreeFactoryGinModule.create(AgreementServiceHierarchicalTree.class,
                        DtObject.class,
                        new TypeLiteral<FilteredSingleSelectionModel>(){},
                        new TypeLiteral<TreeViewModelContext<DtObject, FilteredSingleSelectionModel>>(){},
                        new TypeLiteral<AgreementServiceHierarchicalTreeViewModelImpl>(){},
                        AgreementServiceContext.class)
                .setTreeFactory(new TypeLiteral<AgreementServiceHierarchicalTreeFactory>(){})
                .setTreeViewModelFactory(new TypeLiteral<AgreementServiceHierarchicalTreeViewModelFactory>(){})
                .setDataSource(new TypeLiteral<AgreementServiceHierarchicalTreeDataSource>(){}));
        install(TreeValueAdapterGinModule.single(new TypeLiteral<AgreementServiceSingleSelectionModel>(){}, IAgreementService.class));
        install(TreeValueGinModule.create(IAgreementService.class)
                .setTreeSingleValueFormatter(new TypeLiteral<TreeSingleValueFormatter<IAgreementService>>(){})
                .setRootNodeProvider(AgreementServiceRootProvider.class));
        
        
        install(Gin.bindSingleton(new TypeLiteral<IsFolderPredicate<IAgreementService>>(){},
                                  new TypeLiteral<AgreementServiceFolderPredicate>(){}));
        //@formatter:on
    }
}
