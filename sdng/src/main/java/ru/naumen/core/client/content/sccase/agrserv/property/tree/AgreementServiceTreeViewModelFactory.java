/**
 *
 */
package ru.naumen.core.client.content.sccase.agrserv.property.tree;

import jakarta.inject.Inject;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.content.sccase.agrserv.context.AgreementServiceContext;
import ru.naumen.core.client.tree.cell.ContentTreeCellFactory;
import ru.naumen.core.client.tree.datasource.ITreeDataSource;
import ru.naumen.core.client.tree.datasource.TreeDataSourceFactory;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithFolders;
import ru.naumen.core.client.tree.view.TreeViewModelContext;
import ru.naumen.core.client.tree.view.TreeViewModelFactory;
import ru.naumen.core.client.widgets.tree.cell.WidgetTreeCellGinModule.WithoutRemoved;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.sccase.agrserv.item.IAgreementService;

import com.google.gwt.cell.client.Cell;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.inject.Provider;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 25 авг. 2014 г.
 *
 */
@Singleton
public class AgreementServiceTreeViewModelFactory
        implements
        TreeViewModelFactory<IAgreementService, AgreementServiceSingleSelectionModel, AgreementServiceContext,
                AgreementServiceTreeViewModel>
{
    @Inject
    private TreeDataSourceFactory<IAgreementService, AgreementServiceContext> dataSourceFactory;
    @Inject
    private Provider<AgreementServiceSingleSelectionModel> selectionModelProvider;
    @Inject
    private ContentTreeCellFactory<IAgreementService, AgreementServiceSingleSelectionModel, WithFolders,
            WithoutRemoved> cellFactory;
    @Inject
    private Provider<AgreementServiceTreeViewModel> ASVMFactory;

    @Override
    public AgreementServiceTreeViewModel createTreeViewModel(AgreementServiceContext context)
    {
        throw new UnsupportedOperationException();
    }

    @Override
    public void createTreeViewModel(AgreementServiceContext context,
            AsyncCallback<AgreementServiceTreeViewModel> callback)
    {
        ITreeDataSource<IAgreementService> dataSource = dataSourceFactory.create(context);
        AgreementServiceSingleSelectionModel selectionModel = selectionModelProvider.get();
        Cell<IAgreementService> cell = cellFactory.create(Container
                .<AgreementServiceSingleSelectionModel> create(selectionModel));
        final AgreementServiceTreeViewModel result = ASVMFactory.get();
        result.init(new TreeViewModelContext<IAgreementService, AgreementServiceSingleSelectionModel>(dataSource,
                selectionModel, cell, DefaultSelectionEventManager.<IAgreementService> createDefaultManager()));
        result.init(new CallbackDecorator<Void, AgreementServiceTreeViewModel>(callback)
        {
            @Override
            protected AgreementServiceTreeViewModel apply(Void nothing)
            {
                return result;
            }
        });
    }
}