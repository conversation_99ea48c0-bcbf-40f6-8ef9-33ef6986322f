package ru.naumen.core.client.tree.dto;

import java.util.Collection;
import java.util.Collections;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;

import ru.naumen.core.client.content.sccase.agrserv.context.AgreementServiceContext;
import ru.naumen.core.client.content.sccase.agrserv.property.tree.AgreementServiceTreeGinModule.AgreementServiceTree;
import ru.naumen.core.client.inject.splitpoint.SplitPoint;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.Aggregate;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.AggregateFiltered;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.BoTree;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.CatalogItems;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.FavoritesTree;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.FiltrationAttributeTree;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.Folders;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.IndeterminatedTree;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.MetaClass;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.ResponsibleTree;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.TreeType;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithFolders;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithoutFolders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactory;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactoryContext;
import ru.naumen.core.client.tree.dto.impl.aggregate.DtoTreeFactoryAggregateContext;
import ru.naumen.core.client.tree.dto.impl.fastselection.FastSelectionDtoTreeFactoryContext;
import ru.naumen.core.client.tree.dto.impl.metaclass.MetaClassTreeFactoryContext;
import ru.naumen.core.client.tree.dto.impl.responsible.DtoTreeFactoryResponsibleContext;
import ru.naumen.core.client.tree.view.TreeFactory;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.sccase.agrserv.item.IAgreementService;

/**
 * Модуль хранит в себе код для деревьев.
 *
 * <AUTHOR>
 * @since Dec 21, 2015
 */
@Singleton
public class DtoTreeFactorySplitPoint implements SplitPoint
{
    @Inject
    private Provider<DtoTreeFactory<Collection<DtObject>, MetaClass, WithoutFolders, MetaClassTreeFactoryContext>> caseTreeProvider;
    @Inject
    private Provider<DtoTreeFactory<DtObject, MetaClass, WithoutFolders, MetaClassTreeFactoryContext>> metaClassTreeProvider;
    @Inject
    private Provider<DtoTreeFactory<Collection<DtObject>, IndeterminatedTree, WithoutFolders,
            FastSelectionDtoTreeFactoryContext>> fastSelectionProvider;

    @Inject
    private Provider<DtoTreeFactory<DtObject, CatalogItems, WithoutFolders, DtoTreeFactoryContext>> catalogItemProvider;

    @Inject
    private Provider<DtoTreeFactory<Collection<DtObject>, CatalogItems, WithoutFolders, DtoTreeFactoryContext>> catalogItemsProvider;

    @Inject
    private Provider<DtoTreeFactory<DtObject, BoTree, WithoutFolders, DtoTreeFactoryContext>> boSingleWithoutFolders;
    @Inject
    private Provider<DtoTreeFactory<DtObject, BoTree, WithFolders, DtoTreeFactoryContext>> boSingleWithFolders;

    @Inject
    private Provider<DtoTreeFactory<Collection<DtObject>, BoTree, WithoutFolders, DtoTreeFactoryContext>> boMultiWithoutFolders;
    @Inject
    private Provider<DtoTreeFactory<Collection<DtObject>, BoTree, WithFolders, DtoTreeFactoryContext>> boMultiWithFolders;
    @Inject
    private Provider<DtoTreeFactory<DtObject, Aggregate, WithoutFolders, DtoTreeFactoryAggregateContext>> aggregateSingleWithoutFolders;
    @Inject
    private Provider<DtoTreeFactory<DtObject, Aggregate, WithFolders, DtoTreeFactoryAggregateContext>> aggregateSingleWithFolders;

    @Inject
    private Provider<DtoTreeFactory<Collection<DtObject>, Aggregate, WithoutFolders, DtoTreeFactoryAggregateContext>> aggregateMultiWithoutFolders;
    @Inject
    private Provider<DtoTreeFactory<Collection<DtObject>, Aggregate, WithFolders, DtoTreeFactoryAggregateContext>> aggregateMultiWithFolders;
    @Inject
    private Provider<DtoTreeFactory<DtObject, AggregateFiltered, WithoutFolders, DtoTreeFactoryAggregateContext>> aggregateFilteredSingleWithoutFolders;
    @Inject
    private Provider<DtoTreeFactory<DtObject, AggregateFiltered, WithFolders, DtoTreeFactoryAggregateContext>> aggregateFilteredSingleWithFolders;

    @Inject
    private Provider<DtoTreeFactory<Collection<DtObject>, AggregateFiltered, WithoutFolders,
            DtoTreeFactoryAggregateContext>> aggregateFilteredMultiWithoutFolders;
    @Inject
    private Provider<DtoTreeFactory<Collection<DtObject>, AggregateFiltered, WithFolders,
            DtoTreeFactoryAggregateContext>> aggregateFilteredMultiWithFolders;
    @Inject
    private Provider<DtoTreeFactory<DtObject, ResponsibleTree, WithFolders, DtoTreeFactoryResponsibleContext>> responsibleWithFolders;
    @Inject
    private Provider<DtoTreeFactory<DtObject, ResponsibleTree, WithoutFolders, DtoTreeFactoryResponsibleContext>> responsibleWithoutFolders;
    @Inject
    private Provider<DtoTreeFactory<DtObject, FiltrationAttributeTree, WithoutFolders,
            DtoTreeFactoryContext>> attributeFilterFactoryProvider;

    @Inject
    private Provider<TreeFactory<IAgreementService, AgreementServiceTree, AgreementServiceContext>> agreementServiceTreeFactory;
    @Inject
    private Provider<TreeFactory<DtObject, AgreementServiceTree, AgreementServiceContext>> agreementServiceHierarchicalTreeFactory;

    public TreeFactory<IAgreementService, AgreementServiceTree, AgreementServiceContext> getAgreementServiceTreeFactory()
    {
        return agreementServiceTreeFactory.get();
    }

    public TreeFactory<DtObject, AgreementServiceTree, AgreementServiceContext> getAgreementServiceHierarchicalTreeFactory()
    {
        return agreementServiceHierarchicalTreeFactory.get();
    }

    public DtoTreeFactory<DtObject, FiltrationAttributeTree, WithoutFolders, DtoTreeFactoryContext> getAttributeFilterTreeFactory()
    {
        return attributeFilterFactoryProvider.get();
    }

    public DtoTreeFactory<Collection<DtObject>, MetaClass, WithoutFolders, MetaClassTreeFactoryContext> getCaseTreeFactory()
    {
        return caseTreeProvider.get();
    }

    @Override
    public Collection<Class<?>> getDependencies()
    {
        return Collections.emptyList();
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    public <V, T extends TreeType, F extends Folders, C extends DtoTreeFactoryContext> DtoTreeFactory<V, T, F, C> getFactory(
            V value, T treeType, F folders, C context)
    {
        boolean single = value instanceof DtObject;
        boolean withFolders = folders instanceof WithFolders;

        DtoTreeFactory dtoTreeFactory = null;
        if (treeType instanceof ResponsibleTree)
        {
            dtoTreeFactory = withFolders ? responsibleWithFolders.get() : responsibleWithoutFolders.get();
        }
        else if (treeType instanceof AggregateFiltered)
        {
            //@formatter:off
            dtoTreeFactory = single &&  withFolders ? aggregateFilteredSingleWithFolders.get() :
                             single && !withFolders ? aggregateFilteredSingleWithoutFolders.get() :
                            !single &&  withFolders ? aggregateFilteredMultiWithFolders.get() :
                            !single && !withFolders ? aggregateFilteredMultiWithoutFolders.get() : null;
            //@formatter:on
        }
        else if (treeType instanceof Aggregate)
        {
            //@formatter:off
            dtoTreeFactory = single &&  withFolders ? aggregateSingleWithFolders.get() :
                             single && !withFolders ? aggregateSingleWithoutFolders.get() :
                            !single &&  withFolders ? aggregateMultiWithFolders.get() :
                            !single && !withFolders ? aggregateMultiWithoutFolders.get() : null;
            //@formatter:on
        }
        else if (treeType instanceof CatalogItems)
        {
            dtoTreeFactory = single ? catalogItemProvider.get() : catalogItemsProvider.get();
        }
        else if (treeType instanceof BoTree)
        {
            //@formatter:off
            dtoTreeFactory = single &&  withFolders ? boSingleWithFolders.get() :
                             single && !withFolders ? boSingleWithoutFolders.get() :
                            !single &&  withFolders ? boMultiWithFolders.get() :
                            !single && !withFolders ? boMultiWithoutFolders.get() : null;
            //@formatter:on
        }
        else if (treeType instanceof FiltrationAttributeTree)
        {
            dtoTreeFactory = attributeFilterFactoryProvider.get();
        }
        else if (treeType instanceof FavoritesTree)
        {
            dtoTreeFactory = boSingleWithFolders.get();
        }
        return dtoTreeFactory;
    }

    public DtoTreeFactory<Collection<DtObject>, IndeterminatedTree, WithoutFolders,
            FastSelectionDtoTreeFactoryContext> getFastSelectionFactory()
    {
        return fastSelectionProvider.get();
    }

    public DtoTreeFactory<DtObject, MetaClass, WithoutFolders, MetaClassTreeFactoryContext> getMetaClassTreeFactory()
    {
        return metaClassTreeProvider.get();
    }
}
