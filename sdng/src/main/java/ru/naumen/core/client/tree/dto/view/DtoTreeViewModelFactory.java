package ru.naumen.core.client.tree.dto.view;

import ru.naumen.core.client.tree.dto.DtoTreeGinModule.DtoTree;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.Folders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactoryContext;
import ru.naumen.core.client.tree.selection.FilteredSelectionModel;
import ru.naumen.core.client.tree.view.TreeViewModelFactory;
import ru.naumen.core.shared.dto.DtObject;

/**
 * <AUTHOR>
 * @since 15.01.2013
 */
public interface DtoTreeViewModelFactory<T extends DtoTree, F extends Folders,
        M extends FilteredSelectionModel<DtObject>, C extends DtoTreeFactoryContext>
        extends TreeViewModelFactory<DtObject, M, C, IDtoTreeViewModel<T, M>>
{
}