package ru.naumen.core.client.widgets;

import jakarta.annotation.Nullable;

import com.google.common.base.Strings;
import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.SpanElement;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactoryImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.MessagesResources.MessagesStyle;

/**
 * Информационное сообщение в виде плашки
 * - с возможностью скрытия,
 * - имеет панель для добавления дополнительных кнопок
 *
 * <AUTHOR>
 * @since 15.08.2011
 */
public abstract class AbstractMessageWidget extends Composite implements Attention, HasEnabled
{
    interface AttentionWidgetUiBinder extends UiBinder<HTMLPanel, AbstractMessageWidget>
    {
    }

    private static AttentionWidgetUiBinder uiBinder = GWT.create(AttentionWidgetUiBinder.class);

    @UiField
    protected HTMLPanel innerPanel;
    @UiField
    protected SpanElement title;
    @UiField
    protected SpanElement text;
    @UiField(provided = true)
    protected Widget close;
    @UiField
    protected HTMLPanel featurePanel; //Панель для реализации различных фич(добавление кнопок)

    protected MessagesResources resources;

    private boolean enabled = true;

    protected AbstractMessageWidget()
    {
        this(null);
    }

    protected AbstractMessageWidget(@Nullable String debugId)
    {
        resources = GWT.create(MessagesResources.class);
        close = FontIconFactoryImpl.createIcon(IconCodes.CLOSE, false);
        initWidget(uiBinder.createAndBindUi(this));

        MessagesStyle style = resources.style();
        style.ensureInjected();

        if (debugId != null)
        {
            ensureDebugId(debugId);
        }

        innerPanel.addStyleName(style.messageBox());
        title.addClassName(style.title());
        close.getElement().addClassName(style.close());
        close.addDomHandler(event ->
        {
            if (enabled)
            {
                setVisible(false);
            }
        }, ClickEvent.getType());
        setVisible(false);

        featurePanel.ensureDebugId("featurePanel");
    }

    @Override
    public void addClickHandler(ClickHandler handler)
    {
        close.addDomHandler(handler, ClickEvent.getType());
    }

    @Override
    public void clear()
    {
        setHTML("");
        setVisible(false);
    }

    public Widget getCloseIcon()
    {
        return close;
    }

    public HTMLPanel getFeaturePanel()
    {
        return featurePanel;
    }

    @Override
    public String getHTML()
    {
        return this.text.getInnerHTML();
    }

    @Override
    public String getText()
    {
        return this.text.getInnerText();
    }

    @Override
    public boolean isEnabled()
    {
        return enabled;
    }

    @Override
    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }

    @Override
    public void setHTML(String html)
    {
        setVisible(!Strings.isNullOrEmpty(html));
        this.text.setInnerHTML(html); // NOPMD NSDPRD-28509 unsafe html
    }

    @Override
    public void setText(String text)
    {
        boolean emptyText = Strings.isNullOrEmpty(text);
        setVisible(!emptyText);
        this.text.setInnerText(text);
        if (!emptyText)
        {
            getElement().scrollIntoView();
        }
    }

    @Override
    public void setTitle(String title)
    {
        this.title.setInnerText(title);
    }

    public void setHint(String hint)
    {
        this.innerPanel.setTitle(hint);
    }
}
