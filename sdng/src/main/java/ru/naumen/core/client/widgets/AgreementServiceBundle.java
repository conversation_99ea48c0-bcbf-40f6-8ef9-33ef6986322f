package ru.naumen.core.client.widgets;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Пара соглашение+услуга - элемент ServiceSelectList: 
 * <AUTHOR>
 */
public class AgreementServiceBundle
{

    private final DtObject agreement;
    private final DtObject service;

    public AgreementServiceBundle(DtObject agreement, DtObject service)
    {
        this.agreement = agreement;
        this.service = service;
    }

    @Override
    public boolean equals(Object o)
    {
        if (!(o instanceof AgreementServiceBundle))
        {
            return false;
        }
        AgreementServiceBundle element = (AgreementServiceBundle)o;
        return ObjectUtils.equalsByUuid(agreement, element.agreement)
               && ObjectUtils.equalsByUuid(service, element.service);
    }

    public DtObject getAgreement()
    {
        return agreement;
    }

    public DtObject getService()
    {
        return service;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(agreement, service);
    }
}
