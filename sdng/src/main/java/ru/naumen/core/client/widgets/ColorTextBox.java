package ru.naumen.core.client.widgets;

import java.text.ParseException;

import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.Color;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.validation.ValidationMessages;
import ru.naumen.core.client.widgets.WidgetStyleUpdater.WidgetTypeCode;
import ru.naumen.metainfo.shared.Constants.ColorAttributeType;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.Document;
import com.google.gwt.dom.client.Element;
import com.google.gwt.text.shared.AbstractRenderer;
import com.google.gwt.text.shared.Parser;
import com.google.gwt.user.client.DOM;
import com.google.gwt.user.client.Event;

/**
 * Виджет - поле ввода текста, предназначеное для ввода 6 
 * шестнадцатеричных цифр цвета в модели RGB.
 * По мере ввода обновляется цветовое представление содержимого
 * текстового поля: Введённая строка преобразуется в объект цвета.
 * При успешном преобразовании указанный цвет становится цветом фона поля ввода,
 * цвет текста подбирается с помощью метода {@link Color#guessContrastColor()}.
 * Если указанная строка не может быть преобразована в объект цвета,
 * ко всему полю ввода применяется CSS стиль, имя которого по умолчанию
 * равно "gwt-ColorTextBox-fail".
 * Имя CSS стиля в пространстве "gwt" для данного элемента (текстовое поле ввода)
 * равно "gwt-ColorTextBox"
 *
 * <AUTHOR>
 * @since 26.01.2011
 */
public class ColorTextBox extends NauValueBox<Color>
{
    public static class ColorRendererParser extends AbstractRenderer<Color> implements Parser<Color>
    {
        @Inject
        ValidationMessages messages;

        @Override
        public Color parse(CharSequence text) throws ParseException
        {
            try
            {
                if (StringUtilities.isEmpty(text))
                {
                    return null;
                }
                Color color = Color.valueOf(text.toString());
                if (color == null)
                {
                    throw new ParseException("The entered value cannot be converted to color", 0);
                }
                return color;
            }
            catch (NumberFormatException e)
            {
                throw new ParseException(e.getMessage(), 0);
            }
        }

        @Override
        public String render(Color color)
        {
            return color == null ? "" : color.getString();
        }
    }

    public static final ColorRendererParser crpInstance = new ColorRendererParser();

    /**
     * Имя CSS стиля, применяемое к элементу при ошибочном вводе цвета
     */
    private final String failStyleName;

    WidgetStyleUpdater styleUpdater;

    /**
     * Конструктор по умолчанию
     */
    public ColorTextBox()
    {
        this("gwt-ColorTextBox-fail");
    }

    /**
     *
     * @param failColor Имя CSS стиля, применяемое к элементу при ошибочном вводе цвета
     */
    public ColorTextBox(String failStyleName)
    {
        this(Document.get().createTextInputElement(), "gwt-ColorTextBox", failStyleName);
    }

    /**
     * Package-private конструктор
     * @param element HTML элемент - текстовое поле ввода
     * @param styleName имя стиля элемента ввода
     * @param failStyleName имя стиля применяемого при неудачном вводе
     */
    ColorTextBox(Element element, String styleName, String failStyleName)
    {
        super(element, crpInstance, crpInstance);
        this.failStyleName = failStyleName != null ? failStyleName : null;
        sinkEvents(Event.ONKEYUP);
        setMaxLength(ColorAttributeType.COLOR_MAX_LENGTH);
        if (styleName != null)
        {
            setStyleName(styleName);
        }
        addStyleName(WidgetResources.INSTANCE.form().formTextField());
        styleUpdater = GWT.create(WidgetStyleUpdaterImpl.class);
        styleUpdater.setFocusAndBlurHandlers(WidgetTypeCode.SIMPLE, this);
        styleUpdater.setValidationHandler(WidgetTypeCode.SIMPLE, this);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void onBrowserEvent(Event event)
    {
        super.onBrowserEvent(event);
        if (DOM.eventGetType(event) != Event.ONKEYUP)
        {
            return;
        }
        Color c;
        try
        {
            c = getValueOrThrow();
            apply(c);
            if (this.failStyleName != null)
            {
                removeStyleName(failStyleName);
            }
        }
        catch (ParseException e)
        {
            apply(null);
            if (this.failStyleName != null)
            {
                addStyleName(failStyleName);
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void setValue(Color value)
    {
        apply(value);
        super.setValue(value);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void setValue(Color value, boolean fireEvents)
    {
        apply(value);
        super.setValue(value, fireEvents);
    }

    protected void apply(Color c)
    {
        getElement().getStyle().setBackgroundColor(c != null ? c.html() : "");
        getElement().getStyle().setColor(c != null ? c.guessContrastColor().html() : "");
    }
}
