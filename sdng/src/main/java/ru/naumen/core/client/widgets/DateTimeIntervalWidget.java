package ru.naumen.core.client.widgets;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;

import com.google.common.collect.Lists;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Panel;
import com.google.gwt.user.client.ui.Widget;
import com.google.inject.Inject;
import com.google.inject.Provider;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.forms.HasTabOrder;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.validation.ValidationEvent;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;

/**
 * Виджет для введения значения временного интервала. Состоит из поля ввода и выпадающего списка с набором временных
 * интервалов.
 * <AUTHOR>
 *
 */
public class DateTimeIntervalWidget extends Composite
        implements HasValueOrThrow<DateTimeInterval>, HasEnabled, HasTabOrder, Focusable, HasPopupPanelGlass,
        HasMassEditState
{
    public static class IntervalParseException extends ParseException
    {
        private static final long serialVersionUID = 961964103977810236L;

        public IntervalParseException(String s, int errorOffset)
        {
            super(s, errorOffset);
        }
    }

    public static class LengthParseException extends ParseException
    {
        private static final long serialVersionUID = 7374060815055543799L;

        public LengthParseException(String s, int errorOffset)
        {
            super(s, errorOffset);
        }
    }

    private class ValueChangeHandlerImpl implements ChangeHandler, ValueChangeHandler<SelectItem>
    {
        @Override
        public void onChange(ChangeEvent event)
        {
            ValueChangeEvent.fire(DateTimeIntervalWidget.this, getValue());
        }

        @Override
        public void onValueChange(ValueChangeEvent<SelectItem> event)
        {
            ValueChangeEvent.fire(DateTimeIntervalWidget.this, getValue());
        }
    }

    private IntegerTextBox lengthTextBox;
    private SingleSelectCellList<Interval> intervalList;

    private Collection<String> intervalAvailableUnits;
    private boolean isDefaultValueWidget = false;
    private boolean needStoreUnits = false;
    private int baseTabIndex = 1;

    @Inject
    public DateTimeIntervalWidget(Provider<SimpleSelectCellListBuilder<Interval>> intervalListBuilderProvider,
            IntegerTextBox integerTextBox)
    {
        this.intervalList = intervalListBuilderProvider.get().setHasSearch(false).setCellGlobalPaddingLeft(0).build();
        intervalList.setHasSearchLite(true);
        intervalList.addItems(Lists.newArrayList(Interval.values()));

        lengthTextBox = integerTextBox;
        lengthTextBox.addChangeHandler(new ValueChangeHandlerImpl());
        lengthTextBox.addDropHandler(new NauDropHandler<DateTimeInterval>(this));
        lengthTextBox.addStyleName(WidgetResources.INSTANCE.select().dateTimeIntervalInput());

        initWidget(buildPanel());
        intervalList.ensureDebugId("dateTimeInterval");
        addHandler(this::onValidationEvent, ValidationEvent.getType());
    }

    private void onValidationEvent(ValidationEvent event) //NOPMD
    {
        if (event.isValid())
        {
            lengthTextBox.removeStyleName(WidgetResources.INSTANCE.form().formTextFieldError());
            intervalList.getValueWidget().removeStyleName(WidgetResources.INSTANCE.form().formTextFieldError());
        }
        else
        {
            if (intervalList.getValue() == null
                || (intervalAvailableUnits != null && !intervalAvailableUnits.contains(extractValueName())))
            {
                intervalList.getValueWidget()
                        .addStyleName(WidgetResources.INSTANCE.form().formTextFieldError());
            }
            else
            {
                lengthTextBox.addStyleName(WidgetResources.INSTANCE.form().formTextFieldError());
            }
        }
    }

    private String extractValueName()
    {
        Interval value = SelectListPropertyValueExtractor.getValue(intervalList);
        return value == null ? "" : value.name();
    }

    @Override
    public HandlerRegistration addValueChangeHandler(ValueChangeHandler<DateTimeInterval> handler)
    {
        return this.addHandler(handler, ValueChangeEvent.getType());
    }

    @Override
    public int getBaseTabIndex()
    {
        return baseTabIndex;
    }

    @Override
    public Focusable getFirstFocusElement()
    {
        return lengthTextBox;
    }

    public SingleSelectCellList<Interval> getIntervalList()
    {
        return intervalList;
    }

    @Override
    public MassEditState getMassEditState()
    {
        return lengthTextBox.getMassEditState();
    }

    @Override
    public int getNextBaseTabIndex()
    {
        return baseTabIndex + 2;
    }

    @Override
    public int getTabIndex()
    {
        return lengthTextBox.getTabIndex();
    }

    @Override
    public DateTimeInterval getValue()
    {
        return ParseExceptionsUtils.getValueSafe(this);
    }

    @Override
    public DateTimeInterval getValueOrThrow() throws ParseException
    {
        Long length;
        Interval interval;
        try
        {
            length = lengthTextBox.getValueOrThrow();
        }
        catch (ParseException e)
        {
            throw new LengthParseException(e.getMessage(), e.getErrorOffset());
        }
        try
        {
            interval = SelectItemValueExtractor.<Interval> extract(intervalList.getValueOrThrow());
        }
        catch (ParseException e)
        {
            throw new IntervalParseException(e.getMessage(), e.getErrorOffset());
        }
        if (interval == null)
        {
            return null;
        }
        if (length == null)
        {
            if (isDefaultValueWidget || needStoreUnits)
            {
                return new DateTimeInterval(interval);
            }
            return null;
        }
        return new DateTimeInterval(length, interval);
    }

    public boolean isDefaultValueWidget()
    {
        return isDefaultValueWidget;
    }

    @Override
    public boolean isEnabled()
    {
        return lengthTextBox.isEnabled() && intervalList.isEnabled();
    }

    @Override
    public boolean isPopupGlassEnabled()
    {
        return intervalList.isPopupGlassEnabled();
    }

    public void refillIntervalList(Collection<String> intervalAvailableUnits,
            @Nullable DateTimeInterval value, boolean hasEmptyOption)
    {
        this.intervalAvailableUnits = intervalAvailableUnits;
        ArrayList<Interval> intervalUnits = new ArrayList<>();
        for (Interval interval : Interval.values())
        {
            if (intervalAvailableUnits.contains(interval.toString()))
            {
                intervalUnits.add(interval);
            }
        }
        if (value != null && !intervalAvailableUnits.contains(value.getIntervalName()))
        {
            intervalUnits.add(Interval.valueOf(value.getIntervalName()));
        }
        intervalList.clear();
        intervalList.setHasEmptyOption(hasEmptyOption);
        intervalList.addItems(intervalUnits);
        intervalList.setHasSearchLite(true);
    }

    @Override
    public void resetTabOrder()
    {
        TabOrderHelper.resetTabIndex(lengthTextBox);
        TabOrderHelper.resetTabIndex(intervalList);
    }

    @Override
    public void setAccessKey(char key)
    {
        lengthTextBox.setAccessKey(key);
    }

    @Override
    public void setBaseTabIndex(int baseTabIndex)
    {
        this.baseTabIndex = baseTabIndex;
    }

    @Override
    public void setEnabled(boolean enabled)
    {
        lengthTextBox.setEnabled(enabled);
        intervalList.setEnabled(enabled);
    }

    @Override
    public void setFocus(boolean focused)
    {
        if (focused)
        {
            lengthTextBox.setFocus(true);
        }
        else
        {
            lengthTextBox.setFocus(false);
            intervalList.setFocus(false);
        }
    }

    public void setIsDefaultValueWidget(boolean isDefaultValueWidget)
    {
        this.isDefaultValueWidget = isDefaultValueWidget;
    }

    public boolean isNeedStoreUnits()
    {
        return needStoreUnits;
    }

    public void setNeedStoreUnits(boolean needStoreUnits)
    {
        this.needStoreUnits = needStoreUnits;
    }

    @Override
    public void setMassEditState(MassEditState state)
    {
        lengthTextBox.setMassEditState(state);
    }

    @Override
    public void setPopupGlassEnabled(boolean popupGlassEnabled)
    {
        intervalList.setPopupGlassEnabled(popupGlassEnabled);
    }

    @Override
    public void setTabIndex(int index)
    {
        lengthTextBox.setTabIndex(index);
        intervalList.setTabIndex(index + 1);
    }

    @Override
    public void setValue(DateTimeInterval value)
    {
        setValue(value, false);
    }

    @Override
    public void setValue(DateTimeInterval value, boolean fireEvents)
    {
        final Long length = value == null ? null : value.getLength();
        final Interval interval = value == null ? null : value.getInterval();
        lengthTextBox.setValue(length, fireEvents);
        if (value != null)
        {
            intervalList.setObjValue(interval, fireEvents);
        }
    }

    @Override
    public void updateTabOrder()
    {
        int nextTabIndex = baseTabIndex;
        nextTabIndex = TabOrderHelper.setTabIndex(lengthTextBox, nextTabIndex);
        TabOrderHelper.setTabIndex(intervalList, nextTabIndex);
    }

    private Widget buildIntervalList()
    {
        intervalList.addValueChangeHandler(new ValueChangeHandlerImpl());
        intervalList.addStyleName(WidgetResources.INSTANCE.select().dateTimeintervalSelect());
        return intervalList;
    }

    private Panel buildPanel()
    {
        Panel result = new HorizontalPanel();
        result.add(lengthTextBox);
        result.add(buildIntervalList());
        result.addStyleName(WidgetResources.INSTANCE.select().dateTimeIntervalPanel());
        return result;
    }
}
