package ru.naumen.core.client.widgets;

import com.google.gwt.event.shared.GwtEvent;
import com.google.gwt.user.client.ui.IsWidget;

/**
 * Событие на схлопывание/разворачивание чего-либо
 * <AUTHOR>
 */
public class ExpandCollapseEvent extends GwtEvent<ExpandCollapseEventHandler>
{
    public static final GwtEvent.Type<ExpandCollapseEventHandler> TYPE = new GwtEvent.Type<>();

    private final boolean expand;
    private final IsWidget content;

    public ExpandCollapseEvent(boolean expand)
    {
        this(expand, null);
    }

    public ExpandCollapseEvent(boolean expand, IsWidget content)
    {
        this.expand = expand;
        this.content = content;
    }

    @Override
    public Type<ExpandCollapseEventHandler> getAssociatedType()
    {
        return TYPE;
    }

    public boolean isExpand()
    {
        return expand;
    }

    public IsWidget getContent()
    {
        return content;
    }

    @Override
    protected void dispatch(ExpandCollapseEventHandler handler)
    {
        handler.onExpandCollapse(this);
    }
}
