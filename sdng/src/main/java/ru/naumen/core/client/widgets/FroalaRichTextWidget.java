package ru.naumen.core.client.widgets;

import static ru.naumen.core.client.jsinterop.InteropUtils.getProperty;
import static ru.naumen.core.client.jsinterop.JQuery.$;
import static ru.naumen.core.client.widgets.FroalaEditorConstants.FR_IFRAME;
import static ru.naumen.core.shared.Constants.ImageFileExtension.RASTER_IMAGE_FILE_EXTENSIONS;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.JavaScriptObject;
import com.google.gwt.core.client.JsArrayString;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.DivElement;
import com.google.gwt.dom.client.Document;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NodeList;
import com.google.gwt.dom.client.VideoElement;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.user.client.ui.DecoratedPopupPanel;
import com.google.gwt.user.client.ui.FocusWidget;
import com.google.gwt.user.client.ui.RootPanel;
import com.google.gwt.user.client.ui.Widget;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jsinterop.annotations.JsFunction;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.common.client.settings.SharedSettingsClientServiceImpl;
import ru.naumen.commons.shared.utils.BooleanUtils;
import ru.naumen.commons.shared.utils.FileUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.JSUtils;
import ru.naumen.core.client.ModuleHolder;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.jsinterop.JQueryEvent;
import ru.naumen.core.client.jsinterop.JsObject;
import ru.naumen.core.client.jsinterop.codemirror.CodeMirror;
import ru.naumen.core.client.jsinterop.codemirror.CodeMirrorEventHandler;
import ru.naumen.core.client.jsinterop.froala.FroalaEditor;
import ru.naumen.core.client.jsinterop.froala.FroalaEditorBridge;
import ru.naumen.core.client.jsinterop.froala.editor.methods.FroalaPopups;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.validation.ValidationEvent; //NOPMD
import ru.naumen.core.client.widgets.WidgetStyleUpdater.WidgetTypeCode;
import ru.naumen.core.client.widgets.events.MinimizeFormEvent;
import ru.naumen.core.client.widgets.events.MinimizeFormHandler;
import ru.naumen.core.client.widgets.events.RestoreFormEvent;
import ru.naumen.core.client.widgets.events.RestoreFormHandler;
import ru.naumen.core.client.widgets.properties.ImageWidgetMessages;
import ru.naumen.core.client.widgets.richtext.FroalaInitializer;
import ru.naumen.core.client.widgets.richtext.FroalaRichTextArea;
import ru.naumen.core.client.widgets.richtext.ImageResizeSupport;
import ru.naumen.core.client.widgets.richtext.mentions.EditorMentionContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.HtmlUtils;
import ru.naumen.core.shared.utils.ReadyState;

/**
 * Виджет редактирования текста RTF на основе редактора Froala
 *
 * <AUTHOR>
 * @since 21.12.17
 */
@SuppressWarnings("java:S6201")
public class FroalaRichTextWidget extends RichTextWidgetBase<FroalaRichTextArea> implements HasMassEditState,
        MinimizeFormHandler, RestoreFormHandler
{
    private static final Logger LOG = Logger.getLogger(FroalaRichTextWidget.class.getName());
    private static final ImageWidgetMessages messages = GWT.create(ImageWidgetMessages.class);
    private static final WidgetMessages widgetMessages = GWT.create(WidgetMessages.class);
    private static final CommonMessages cmessages = GWT.create(CommonMessages.class);
    private static final SharedSettingsClientService settingsClientService =
            GWT.create(SharedSettingsClientServiceImpl.class);
    private static final int MAX_SIZE_FOR_CODE_VIEWER = 100_000;

    /**
     * Интервал между повторениями синхронизации размеров iframe
     */
    private static final int REPEAT_SYNC_INTERVAL = 100;

    @Inject
    private Dialogs dialog;
    @Inject
    private ModuleHolder moduleHolder;
    @Inject
    private FroalaEditorInitializer initializer;
    @Inject
    private FroalaInitializer froalaInitializer;

    private final ReadyState editorInitialized = new ReadyState(this);

    /**
     * Значение в виджет может быть установлено до того, как проинициализировался
     * редактор Froala. Поэтому до инициализации сохраним значение в переменной
     */
    private String tempWidgetValue;

    /**
     * Состояние редактора. Может быть изменено до инициализации
     */
    private boolean isEnabled = true;

    /**
     * Фикс не закрывающегося всплывающего окна изменения вставленной картинки/видео, при клике вне froala
     */
    private final JsEventHandler fixImageEditPopupHandler = (self, event) ->
    {
        FroalaEditor froala = getEditor();
        if (froala == null)
        {
            return;
        }
        FroalaPopups popups = froala.popups;
        if (popups == null)
        {
            return;
        }
        boolean isEditImageVisible = froala.popups.areVisible();
        if (isEditImageVisible)
        {
            froala.events.enableBlur();
            froala.events.trigger("blur", new Object[0], true);
        }
    };

    /**
     * Выполнить действие по завершению инициализации
     * @param runnable действие
     */
    protected void onEditorReady(Runnable runnable)
    {
        editorInitialized.onReady(() ->
        {
            if (getEditor() != null)
            {
                runnable.run();
            }
        });
    }

    /**
     * Измененная высота виджета. Сохраняется в переменной, так как может быть выставлена извне до момента инициализации
     */
    private Integer modifiedHeight;
    private EditorMentionContext editorMentionContext;
    private MassEditState massEditState;
    private boolean compactToolbar = false;
    private final Map<String, Object> options = new HashMap<>();

    /**
     * Значение при установке фокуса в виджет.
     * Необходимо для определения наличия изменений при потере фокуса.
     */
    private String valueOnSetFocus;
    protected final RegistrationContainer registrationContainer = new RegistrationContainer();

    public FroalaRichTextWidget()
    {
        // Старт загрузки редактора
        editorInitialized.notReady();
        onEditorReady(this::init);
    }

    protected void init()
    {
        if (!isIgnoreOwaspInSafeHtml())
        {
            area.writeSecureHtml();
        }
        if (!area.isEnabled())
        {
            area.setFocus(true);
            area.setEnabled(false);
        }

        addHandler(event ->
        {
            Element areaElement = area.getAreaElement();
            if (areaElement != null)
            {
                if (event.isValid())
                {
                    areaElement.removeClassName(WidgetResources.INSTANCE.form().formTextFieldError());
                }
                else
                {
                    areaElement.addClassName(WidgetResources.INSTANCE.form().formTextFieldError());
                }
            }
        }, ValidationEvent.getType());

        styleUpdater.setFocusAndBlurHandlers(WidgetTypeCode.SIMPLE, areaAsFocusWidget(), area.getAreaElement());
        styleUpdater.addFormWideClass(this);
    }

    @Override
    public void enableTools(boolean enabled)
    {
        onEditorReady(() -> visibleTools(enabled));
    }

    @Nullable
    public FroalaEditor getEditor()
    {
        return area.getEditor();
    }

    @Nullable
    public EditorMentionContext getMentionContext()
    {
        return editorMentionContext;
    }

    @Override
    public MassEditState getMassEditState()
    {
        return massEditState;
    }

    @Override
    public String getValue()
    {
        if (!editorInitialized.isReady() && tempWidgetValue != null)
        {
            return tempWidgetValue;
        }
        else
        {
            return super.getValue();
        }
    }

    @Override
    public String getValueOrThrow() throws ParseException
    {
        return processReplaces(super.getValueOrThrow());
    }

    public Map<String, Object> getOptions()
    {
        return options;
    }

    /**
     * Устранить несанкционированные замены символов, выполненные редактором,
     * а также выполнить некоторые другие технически необходимые замены
     */
    private static String processReplaces(String value)
    {
        String prepared = value.replace("&#39;", "'")
                .replace("&quot;", "\"")
                .replace("uuid=file%24", "uuid=file$")
                .replace("id=\"isPasted\"", "")
                .replace("fr-original-style=\"\"", StringUtilities.EMPTY);
        // Нужно, чтоб не ломался синтаксис font-family при вставке произвольного текста в редактор
        return HtmlUtils.unquoteFonts(prepared);
    }

    @Override
    public boolean isEnabled()
    {
        return isEnabled;
    }

    @Override
    public void processOnBlurActions()
    {
        handleContentChanged();
        valueOnSetFocus = null;
    }

    @Override
    public void setAccessKey(char key)
    {
        // Ничего не делаем
    }

    @Override
    public void setAreaHeight(Integer height)
    {
        modifiedHeight = height;
    }

    public void setCompactMode()
    {
        compactToolbar = true;
    }

    @Override
    public void setEnabled(boolean enabled)
    {
        isEnabled = enabled;

        final boolean isDelayRequired = !enabled && !editorInitialized.isReady();
        onEditorReady(() ->
        {
            if (isDelayRequired)
            {
                // Редактор можно задизейблить только через некоторое время после его инициализации
                Scheduler.get().scheduleFixedDelay(() ->
                {
                    area.setEditEnabled(isEnabled);
                    area.setToolbarEnabled(isEnabled);
                    return false;
                }, 50);
            }
            else
            {
                area.setEditEnabled(isEnabled);
                area.setToolbarEnabled(isEnabled);
            }
        });
    }

    @Override
    public void setFocus(boolean focused)
    {
        onEditorReady(() -> super.setFocus(focused));
    }

    @Override
    public void setMassEditState(MassEditState state)
    {
        massEditState = state;
        onEditorReady(() ->
        {
            if (MassEditState.DIFFERENT_VALUES == massEditState)
            {
                area.setPlaceholderText(cmessages.brackets(cmessages.selectedDifferentValues()));
            }
            else if (MassEditState.NOT_SET == massEditState)
            {
                area.setPlaceholderText(cmessages.brackets(cmessages.valueNotSet()));
            }
            else
            {
                area.setPlaceholderText(StringUtilities.EMPTY);
            }
        });
    }

    @Override
    public void setMentionContext(EditorMentionContext mentionContext)
    {
        this.editorMentionContext = mentionContext;
    }

    @Override
    public void setTabIndex(int index)
    {
        onEditorReady(() -> super.setTabIndex(index));
    }

    @Override
    public void setValue(String value)
    {
        if (!editorInitialized.isReady())
        {
            tempWidgetValue = prepareValueToSet(value);
        }

        onEditorReady(() ->
        {
            super.setValue(value);
            syncIframeWithRepeat();
        });
    }

    @Override
    public void setValue(String value, boolean fireEvents)
    {
        if (!editorInitialized.isReady())
        {
            tempWidgetValue = prepareValueToSet(value);
        }

        onEditorReady(() ->
        {
            try
            {
                super.setValue(value, fireEvents);
                syncIframeWithRepeat();
            }
            catch (Exception e)
            {
                LOG.warning("Error during set value: " + e.getMessage());
                dialog.error(widgetMessages.openRtfError(), new DialogCallback()); //NOPMD
            }
        });
    }

    /**
     * Установить определенную опциию фроала для данного виджета.
     * Передавать опции можно только до момента появления виджета в DOM.
     */
    public void setOption(String optionName, Object value)
    {
        options.put(optionName, value);
    }

    public void visibleToolbar(boolean visible)
    {
        onEditorReady(() -> area.setToolbarVisible(visible));
    }

    private void visibleTools(boolean visible)
    {
        onEditorReady(() -> area.setToolbarEnabled(visible));
    }

    @Override
    protected FocusWidget areaAsFocusWidget()
    {
        return area;
    }

    @Override
    protected Widget buildPanel()
    {
        area = GWT.create(FroalaRichTextArea.class);
        return area;
    }

    @Override
    protected void onAttach()
    {
        JSUtils.preparingWidgetsInc();
        super.onAttach();
        registrationContainer.registerHandler(DialogEventBus.INSTANCE.addHandler(MinimizeFormEvent.getType(), this));
        registrationContainer.registerHandler(DialogEventBus.INSTANCE.addHandler(RestoreFormEvent.getType(), this));

        froalaInitializer.onComplete(() -> Scheduler.get().scheduleFinally(this::onFroalaInitialized));
    }

    private void onFroalaInitialized()
    {
        if (!isAttached())
        {
            editorInitialized.ready();
            return;
        }
        List<String> aliases = new ArrayList<>();
        if (editorMentionContext != null && moduleHolder.getModule().isOperator())
        {
            editorMentionContext.getAvailableFastLinkSettings()
                    .forEach(dto -> aliases.add(dto.getProperty(Constants.MENTION_ALIAS)));
        }

        if (editorInitialized.isReady())
        {
            editorInitialized.notReady();
        }
        Scheduler.get().scheduleDeferred(() ->
        {
            FroalaEditor editor = initializer.initEditor(this, modifiedHeight, aliases, compactToolbar);
            editor.setMentionContext(editorMentionContext);
            area.setEditor(editor);
        });
        bindFixImageEditPopup();
    }

    private void bindFixImageEditPopup()
    {
        if (BooleanUtils.isTrue(settingsClientService.isUseImageEditorCloseFix()))
        {
            $(Document.get().getDocumentElement()).on("mousedown", fixImageEditPopupHandler);
        }
    }

    private void unbindFixImageEditPopup()
    {
        if (BooleanUtils.isTrue(settingsClientService.isUseImageEditorCloseFix()))
        {
            $(Document.get().getDocumentElement()).off("mousedown", fixImageEditPopupHandler);
        }
    }

    @Override
    protected void onDetach()
    {
        super.onDetach();
        registrationContainer.removeAll();
        if (area != null)
        {
            FroalaEditor editor = getEditor();
            if (editor != null)
            {
                editor.destroy();
                editorInitialized.notReady();
                unbindFixImageEditPopup();
            }
            area.setEditor(null);
        }
    }

    @Override
    protected void processOnFocusActions()
    {
        if (null == valueOnSetFocus)
        {
            valueOnSetFocus = getValue();
        }
        Element areaElement = area.getAreaElement();
        if (areaElement != null)
        {
            areaElement.addClassName(WidgetResources.INSTANCE.form().formTextFieldBlue());
        }
    }

    /**
     * Используется в JSNI
     */
    boolean checkImageFormat(String fileName)
    {
        if (StringUtilities.isEmpty(fileName) || !fileName.contains("."))
        {
            return true;
        }

        if (!RASTER_IMAGE_FILE_EXTENSIONS.contains(FileUtils.getFileExtension(fileName).toLowerCase()))
        {
            dialog.error(messages.isNotImageForRtf(fileName), new DialogCallback());
            return false;
        }
        return true;
    }

    void handleBlur()
    {
        onEditorReady(() ->
        {
            processOnBlurActions();
            Element areaElement = area.getAreaElement();
            if (areaElement != null)
            {
                areaElement.removeClassName(WidgetResources.INSTANCE.form().formTextFieldBlue());
            }
        });
    }

    /**
     * Обработка ввода текста в редакторе
     */
    void handleContentChanged()
    {
        String currentValue = getValue();
        if (null != valueOnSetFocus && valueOnSetFocus.equals(currentValue))
        {
            return;
        }
        valueOnSetFocus = currentValue;
        removeEmptyTRTag();
        ValueChangeEvent.fire(this, getValue());
    }

    private void removeEmptyTRTag()
    {
        DivElement container = (DivElement)area.getContainerElement();
        if (container == null)
        {
            return;
        }
        NodeList<Element> nodeList = container.getElementsByTagName("tr");

        for (int i = 0; i < nodeList.getLength(); ++i)
        {
            if (nodeList.getItem(i).getInnerHTML().matches("\\W*"))
            {
                nodeList.getItem(i).removeFromParent();
            }
        }
    }

    /**
     * Обработка окончания инициализации редактора
     */
    protected void handleEditorCreation()
    {
        area.getAreaElement().addClassName(WidgetResources.INSTANCE.form().formTextAreaField());
        editorInitialized.ready();
        JSUtils.preparingWidgetsDec();
    }

    /**
     * Обработка события 'focus' на редакторе - выделяем редактор голубой рамочкой
     */
    void handleFocus()
    {
        onEditorReady(this::processOnFocusActions);
    }

    /**
     * После вставки картинок осуществляется их масштабирование
     * Используется в JSNI
     */
    void handleImagePaste()
    {
        ImageResizeSupport.scaleImages(area, new BasicCallback<Void>()
        {
            @Override
            protected void handleSuccess(Void response)
            {
                // После масштабирования картинки изменения в редакторе сразу не записываем,
                // т.к. при этом теряется часть внутренней логики Фроала
                // Изменения будут записаны при любом следующем действии, или blur
                // handleContentChanged();
                area.triggerFocusEvent();
            }
        });
    }

    /**
     * По клику на iframe редактора необходимо закрыть все выпадающие списки
     */
    static void handleMouseDown()
    {
        RootPanel rootPanel = RootPanel.get();
        if (rootPanel == null)
        {
            rootPanel = RootPanel.getBody();
        }

        for (Widget widget : rootPanel)
        {
            if (widget instanceof FastScrollPopupPanel)
            {
                FastScrollPopupPanel popup = (FastScrollPopupPanel)widget;
                if (popup.isShowing())
                {
                    popup.hide();
                    break;
                }
            }
        }
    }

    /**
     * Переопределение перехода в режим просмотра html ввиду
     * необходимости разбить длинные строки в base64 представлении картинок на подстроки
     */
    void handleOpenCodeView()
    {
        if (getEditor() == null)
        {
            return;
        }
        String initialValue = getValue();

        if (StringUtilities.length(initialValue) > MAX_SIZE_FOR_CODE_VIEWER)
        {
            dialog.warning(widgetMessages.froalaCodeViewForbidden(MAX_SIZE_FOR_CODE_VIEWER));
            return;
        }

        area.setHTML(normalizeBase64Images(initialValue, true));
        area.enableCodeView(normalizeBase64Images(initialValue, false));

        final Object codeMirrorObject = getProperty(getEditor().box().find(".CodeMirror").get(0), "CodeMirror");
        if (codeMirrorObject instanceof CodeMirror)
        {
            final CodeMirror codeMirror = (CodeMirror)codeMirrorObject;
            codeMirror.on("focus", (CodeMirrorEventHandler)(me, instance, args) -> handleFocus());
            codeMirror.on("blur", (CodeMirrorEventHandler)(me, instance, args) -> handleBlur());
        }
        handleFocus();
    }

    /**
     * Открытие окна справки
     */
    void handleShowHelp(FroalaEditor editor)
    {
        final JsArrayString mentionAliases = (JsArrayString)JavaScriptObject.createArray();
        EditorMentionContext mentionContext = editor.getMentionContext();
        if (mentionContext != null)
        {
            mentionContext.getAvailableFastLinkSettings()
                    .stream()
                    .limit(10)
                    .forEach(dto -> mentionAliases.push(dto.getTitle()
                                                        + " - <b>"
                                                        + dto.getProperty(Constants.MENTION_ALIAS)
                                                        + "</b>"));
        }

        String caption = editor.language.translate("Shortcuts");
        Dialog helpDialog = dialog.info(caption, area.getHelpBody(mentionAliases));
        helpDialog.asWidget().getElement().removeClassName("b-lightbox-form__inner");
        helpDialog.asWidget().getElement().addClassName("b-lightbox-form__inner_for_help");
        ((DecoratedPopupPanel)helpDialog).center();
    }

    /**
     * Обработка вставки видео
     */
    void handleVideoPaste()
    {
        //Выбираем все <video> элементы содержащиеся в поле
        DivElement container = (DivElement)area.getContainerElement();
        NodeList<Element> nodeList = container.getElementsByTagName("video");

        for (int i = 0; i < nodeList.getLength(); ++i)
        {
            VideoElement video = (VideoElement)nodeList.getItem(i);
            video.setSrc(FroalaEditorBridge.sanitizeInternalLink(video.getSrc()));
        }
    }

    /**
     * Синхронизирует размеры iframe с его содержимым
     */
    private void syncIframe()
    {
        onEditorReady(area::syncIframe);
    }

    /**
     * Синхронизирует размеры iframe с его содержимым пока его высота не станет не нулевой
     */
    private void syncIframeWithRepeat()
    {
        Scheduler.get().scheduleFixedDelay(() ->
        {
            FroalaEditor froala = getEditor();
            Number height = froala != null ? froala.box().find(FR_IFRAME).height() : null;
            if (height == null || height.intValue() == 0)
            {
                area.syncIframe();
                return true;
            }
            return false;
        }, REPEAT_SYNC_INTERVAL);
    }

    @Override
    public void onMinimizeForm(MinimizeFormEvent event)
    {
        if (belongsForm(event.getForm()))
        {
            setFocus(false);
            setEnabled(false);
        }
    }

    @Override
    public void onRestoreForm(RestoreFormEvent event)
    {
        if (belongsForm(event.getForm()))
        {
            setFocus(true);
            setEnabled(true);
            syncIframe();
        }
    }

    /**
     * Опция стилей для редактора фроалы (зависит от виджета)
     */
    public String getIframeStyleOption()
    {
        return ".fr-view blockquote {color: #999999} .fr-view {font-family: \"Arial\";  font-size: "
               + JSUtils.getUserFontSize() + "; overflow-y: hidden; overflow-x: auto;} "
               + ".fr-view {padding: 8px !important; padding-right: 14px;}"
               + ".fr-box.fr-basic blockquote {color: rgb(153, 153, 153) !important}";
    }

    @JsFunction
    private interface JsEventHandler
    {
        void onEvent(JsObject self, JQueryEvent event);
    }

    private boolean belongsForm(Widget form)
    {
        Widget widget = getParent();
        while (widget != null)
        {
            if (widget.equals(form))
            {
                return true;
            }
            widget = widget.getParent();
        }
        return false;
    }
}
