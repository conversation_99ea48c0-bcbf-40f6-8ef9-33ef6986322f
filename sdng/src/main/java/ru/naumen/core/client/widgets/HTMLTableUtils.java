package ru.naumen.core.client.widgets;

import com.google.gwt.user.client.ui.HTMLTable;
import com.google.gwt.user.client.ui.Label;

/**
 * Методы для работы с {@link com.google.gwt.user.client.ui.HTMLTableUtils}
 * <AUTHOR>
 * @since 05.10.2011
 *
 */
public class HTMLTableUtils
{
    /**
     * Добавляем текст в ячейку через div
     * @param row
     * @param column
     * @param text
     */
    public static void setText(HTMLTable table, int row, int column, String text)
    {
        table.setWidget(row, column, new Label(text));
    }
}
