package ru.naumen.core.client.widgets;

import java.text.ParseException;

import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.HasValue;
import com.google.gwt.user.client.ui.IsWidget;

/**
 * Можно определить, является ли текущее значение элемента пустым
 * <AUTHOR>
 * @since 06.10.2011
 *
 */
public interface HasValueOrThrow<T> extends HasValue<T>, IsWidget, HasEnabled
{
    default void clearValue()
    {
        setValue(null, true);
    }

    /**
     * Возвращает преобразованное значение, или  null если поле пусто
     * Не рекомендуется использовать getValue, иначе может произойти зацикливание, если getValue использует
     * getValueOrThrow
     * @throws ParseException если значение не может быть преобразовано
     */
    T getValueOrThrow() throws ParseException;

    default boolean isValueEmpty()
    {
        return null == ParseExceptionsUtils.getValueSafe(this);
    }
}
