package ru.naumen.core.client.widgets;

import jakarta.inject.Singleton;

import com.google.gwt.resources.client.ClientBundle;
import com.google.gwt.resources.client.CssResource;

/**
 * Ресурсы для различных сообщений (attention, error) на карточках, формах, в списках
 * <AUTHOR>
 *
 */
@Singleton
public interface MessagesResources extends ClientBundle
{
    @Source({ "messages.css" })
    MessagesStyle style();

    interface MessagesStyle extends CssResource
    {
        String close();

        String messageBox();

        String text();

        String title();

        String inlineButton();

        String attentionMessage();

        String restoreValuesMessage();

        String errorMessage();

        String infoMessage();

        String errorMessageNoBorder();
    }
}
