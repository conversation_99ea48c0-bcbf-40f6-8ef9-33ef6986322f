package ru.naumen.core.client.widgets;

import static ru.naumen.core.client.DocumentUtils.CSS_MAX_HEIGHT_PROPERTY;
import static ru.naumen.core.client.widgets.clselect.PopupCellList.POPUP_WIDTH_DIFF;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import com.google.common.collect.Sets;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.RepeatingCommand;
import com.google.gwt.dom.client.Document;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.dom.client.Style.Visibility;
import com.google.gwt.event.dom.client.MouseDownEvent;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.DOM;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.Event.NativePreviewEvent;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.Panel;
import com.google.gwt.user.client.ui.RootPanel;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.DocumentUtils;
import ru.naumen.core.client.widgets.caption.FormWindowCommandsCaption;

/**
 * Модальная форма, имеющая заголовок и содержимое.
 * (Кнопки внизу формы определяются именно в содержимом, а не в этой форме.)
 *
 * Класс предназначен для переопределения поведения модального
 * диалогового блока {@link DialogBox} при скролле, нажатии F5 и нажатии Ctrl+C.
 * Эти события обрабатываются стандартным образом.
 * <AUTHOR>
 */
public class NDialogBox extends DialogBox
{
    private static final int F5_CODE = 116;
    private static final int MAX_DIALOG_FORM_HEIGHT = 600; // примерное значение

    /**
     * Константа-допуск на разницу в измерениях высоты формы.
     * Понадобилась после обновления хрома на версию 91.0.4472.77 -
     * Т.к. округление чисел изменилось, высота формы начала бесконечно пересчитываться,
     * а сама форма начала дрожать.
     */
    private static final int ANTI_RECURSIVE_RECALC_OFFSET = 1;

    /*
     * Клавиши, нажимаемые с Ctrl, которые должны быть обработаны стандартным образом
     *
     * Добавлен 0 для корректной обработки в FF.
     * При нажатии на Ctrl+c в FF дополнительно отсылается событие keypress c нажатым
     * ctrl и кодом клавиши 0. Если это событие не разрешить обрабатывать, то выделенный текст в FF
     * не скопируется в буфер.
     */
    private static final Set<Integer> CTRL_KEYS = Sets.newHashSet(67, 0);

    private static final int DIALOG_CONTENT_TOTAL_MARGIN = 128;

    /**
     * Разрешить стандартную обработку для определенных событий
     * @param element
     */
    public static final void ensureConsumeEvent(NativePreviewEvent event, com.google.gwt.user.client.Element element)
    {
        //Разрешаем обработку F5 и Ctrl+c
        Event ne = Event.as(event.getNativeEvent());
        int type = ne.getTypeInt();
        if ((Event.KEYEVENTS & type) != 0
            && (F5_CODE == ne.getKeyCode() || ne.getCtrlKey() && CTRL_KEYS.contains(ne.getKeyCode())))
        {
            event.consume();
            return;
        }
        //Разрешаем обработку скролла
        if (Event.ONMOUSEWHEEL == type || Event.ONSCROLL == type)
        {
            event.consume();
            return;
        }

        if (isResizingTable(element) && (Event.ONMOUSEMOVE == type || Event.ONMOUSEUP == type))
        {
            event.consume();
            return;
        }

        //В Chrome и ie9 не работает скролл основной страницы, если открыто всплывающее окно
        //Баг GWT - https://code.google.com/p/google-web-toolkit/issues/detail?id=399
        if ((DOM.eventGetType(ne) & Event.MOUSEEVENTS) != 0)
        {
            int clientX = ne.getClientX();
            int clientY = ne.getClientY();
            if (clientX > Window.getClientWidth() || clientY > Window.getClientHeight())
            {
                event.consume();
            }
        }
    }

    private native static boolean isResizingTable(Element elem)
    /*-{
        if ($wnd.$ != null) {
            var draggingGlassController = $wnd.$('#DraggingGlassController')[0];
            if (draggingGlassController != null) {
                var className = $wnd.$(draggingGlassController).attr('class');
                return className !== '' && className.indexOf('resizing') !== -1;
                }
        }
        return false;
    }-*/;

    private boolean isScrollMonitoringStarted = false;
    private Element previousScrollableRootPanelElement;
    private static final WindowLayoutHolder layoutHolder = WindowLayoutHolder.INSTANCE;
    private final List<HandlerRegistration> handlers = new ArrayList<>();
    private IsWidget previousRootPanelOwner;

    protected boolean fixed = true;
    protected int contentHeight = 0;
    protected DialogResizeMode dialogResizeMode = DialogResizeMode.ALWAYS_CENTER;

    protected boolean currentScrollState = false;

    /**
     * Creates an empty dialog box. It should not be shown until its child widget
     * has been added using {@link #add(com.google.gwt.user.client.ui.Widget.Widget)}.
     */
    public NDialogBox()
    {
        super();
    }

    /**
     * Creates an empty dialog box specifying its "auto-hide" property. It should
     * not be shown until its child widget has been added using
     * {@link #add(com.google.gwt.user.client.ui.Widget.Widget)}.
     *
     * @param autoHide <code>true</code> if the dialog should be automatically
     *          hidden when the user clicks outside of it
     */
    public NDialogBox(boolean autoHide)
    {
        super(autoHide);
    }

    /**
     * Creates an empty dialog box specifying its "auto-hide" and "modal"
     * properties. It should not be shown until its child widget has been added
     * using {@link #add(com.google.gwt.user.client.ui.Widget.Widget)}.
     *
     * @param autoHide <code>true</code> if the dialog should be automatically
     *          hidden when the user clicks outside of it
     * @param modal <code>true</code> if keyboard and mouse events for widgets not
     *          contained by the dialog should be ignored
     */
    public NDialogBox(boolean autoHide, boolean modal)
    {
        super(autoHide, modal);
    }

    /**
     *
     * Creates an empty dialog box specifying its "auto-hide", "modal" properties
     * and an implementation a custom {@link Caption}. It should not be shown
     * until its child widget has been added using {@link #add(com.google.gwt.user.client.ui.Widget.Widget)}.
     *
     * @param autoHide <code>true</code> if the dialog should be automatically
     *          hidden when the user clicks outside of it
     * @param modal <code>true</code> if keyboard and mouse events for widgets not
     *          contained by the dialog should be ignored
     * @param captionWidget the widget that is the DialogBox's header.
     */
    public NDialogBox(boolean autoHide, boolean modal, Caption captionWidget)
    {
        super(autoHide, modal, captionWidget);
        if (captionWidget instanceof FormWindowCommandsCaption)
        {
            ((FormWindowCommandsCaption)captionWidget).setFormDisplay(this);
        }
    }

    /**
     * Creates an empty dialog box specifying its {@link Caption}. It should not
     * be shown until its child widget has been added using {@link #add(com.google.gwt.user.client.ui.Widget.Widget)}.
     *
     * @param captionWidget the widget that is the DialogBox's header.
     */
    public NDialogBox(Caption captionWidget)
    {
        super(captionWidget);
        if (captionWidget instanceof FormWindowCommandsCaption)
        {
            ((FormWindowCommandsCaption)captionWidget).setFormDisplay(this);
        }
    }

    public DialogResizeMode getDialogResizeMode()
    {
        return dialogResizeMode;
    }

    public boolean getFixed()
    {
        return this.fixed;
    }

    @Override
    public void hide(boolean autoClosed)
    {
        handlers.forEach(HandlerRegistration::removeHandler);
        handlers.clear();
        RootPanel.setScrollableRootPanel(null, this, false);
        super.hide(autoClosed);
        RootPanel.setScrollableRootPanel(previousScrollableRootPanelElement, previousRootPanelOwner, false);
    }

    public int repositionVertically()
    {
        Element element = getElement();
        int oldTop = DocumentUtils.getAbsoluteTop(element);
        int topLimit = Window.getScrollTop() + Window.getClientHeight() - layoutHolder.getStatusPanelHeight();
        if (DialogResizeMode.CENTER_ON_OVERFLOW == getDialogResizeMode() && oldTop + contentHeight < topLimit)
        {
            return 0;
        }
        element.getStyle().setTop(0, Unit.PX);
        int top = Window.getScrollTop()
                  + (Window.getClientHeight() - (layoutHolder.getStatusPanelHeight() + contentHeight) >> 1);
        if (isGlassEnabled())
        {
            int glassHeight = getGlassElement().getOffsetHeight();
            int delta = top + contentHeight - glassHeight;
            if (delta > 0)
            {
                top -= delta;
            }
        }
        element.getStyle().setTop(top, Unit.PX);
        return top - oldTop;
    }

    public void setDialogResizeMode(DialogResizeMode dialogResizeMode)
    {
        this.dialogResizeMode = dialogResizeMode;
    }

    public void setFixed(boolean fixed)
    {
        this.fixed = fixed;
    }

    public void setWidth(double value, Unit unit)
    {
        getElement().getStyle().setWidth(value, unit);
    }

    @Override
    public void show()
    {
        previousScrollableRootPanelElement = RootPanel.getScrollableRootPanelElement();
        previousRootPanelOwner = RootPanel.getRootPanelOwner();
        RootPanel.setScrollableRootPanel(null, this, false);

        super.show();
        DialogEventBus.INSTANCE.fireEvent(new ShowDialogBoxEvent());
    }

    @Override
    protected void onAttach()
    {
        super.onAttach();
        // если действия запрещены, но форму открыли - значит, ей можно. Навешиваем соответствующий стиль.
        if (Document.get().getBody().hasClassName(WidgetResources.INSTANCE.all().actionsDisabled()))
        {
            getContainerElement().addClassName(WidgetResources.INSTANCE.all().actionsForceEnabled());
        }
    }

    @Override
    protected void onPreviewNativeEvent(NativePreviewEvent npe)
    {
        if (getCaption() instanceof FormWindowCommandsCaption)
        {
            ((FormWindowCommandsCaption)getCaption()).prePreviewNativeEvent(npe);
            if (npe.isConsumed() || npe.isCanceled())
            {
                return;
            }
        }
        ensureConsumeEvent(npe, getElement());
        super.onPreviewNativeEvent(npe);
    }

    protected void setPosition(Boolean fixed)
    {
        int winHeight = Window.getClientHeight();
        int winWidth = Window.getClientWidth();
        if (getOffsetHeight() > winHeight && getOffsetWidth() > winWidth)
        {
            setPopupPosition(Window.getScrollLeft(), Window.getScrollTop());
            return;
        }
        if (getOffsetHeight() > winHeight)
        {
            int left = winWidth - getOffsetWidth() >> 1;
            setPopupPosition(Math.max(Window.getScrollLeft() + left, 0), Math.max(Window.getScrollTop(), 0));
            return;
        }
        if (getOffsetWidth() > winWidth)
        {
            int top = winHeight - getOffsetHeight() >> 1;
            setPopupPosition(Math.max(Window.getScrollLeft(), 0), Math.max(Window.getScrollTop() + top, 0));
            return;
        }
        int dialogFormTopOffset = winHeight > MAX_DIALOG_FORM_HEIGHT ? winHeight - MAX_DIALOG_FORM_HEIGHT >> 1 : 0;
        int top = Window.getScrollTop() + dialogFormTopOffset;
        int left = winWidth - getOffsetWidth() >> 1;
        setPopupPosition(Math.max(Window.getScrollLeft() + left, 0), Math.max(top, dialogFormTopOffset));
        if (DialogResizeMode.NONE != getDialogResizeMode())
        {
            repositionVertically();
        }
    }

    /**
     * Отслеживает появление скрола внутри диалога.
     * Если скрол появился - данный диалог становится RootPanel,
     * чтобы все выпадашки рисовались внутри него
     */
    protected void startScrollMonitoring(Panel panel)
    {
        if (DialogResizeMode.NONE != getDialogResizeMode())
        {
            recalcMaxHeight(panel);
            handlers.add(DialogEventBus.INSTANCE.addHandler(WindowLayoutChangeEvent.TYPE,
                    event -> recalcMaxHeight(panel)));
        }
        if (!isScrollMonitoringStarted)
        {
            Scheduler.get().scheduleFinally(new RepeatingCommand()
            {
                @Override
                public boolean execute()
                {
                    if (!isShowing())
                    {
                        isScrollMonitoringStarted = false;
                        return false;
                    }

                    if (DialogResizeMode.NONE != getDialogResizeMode())
                    {
                        checkContentHeightChanged();
                    }

                    if (!RootPanel.isRootPanelOwner(NDialogBox.this))
                    {
                        return true;
                    }

                    Element element = panel.getElement();

                    // Отслеживаем только вертикальный скролл
                    currentScrollState = element.getClientHeight() + 2 < element.getScrollHeight();

                    if (currentScrollState && RootPanel.getScrollableRootPanelElement() == null)
                    {
                        RootPanel.setScrollableRootPanel(element, NDialogBox.this, true);
                        redrawOpenedPopup(RootPanel.getBody(), panel);
                    }
                    else if (!currentScrollState && RootPanel.getScrollableRootPanelElement() != null)
                    {
                        RootPanel from = RootPanel.get();
                        RootPanel.setScrollableRootPanel(null, NDialogBox.this, true);
                        redrawOpenedPopup(from, RootPanel.getBody());
                    }

                    return true;
                }
            });
            isScrollMonitoringStarted = true;
        }
    }

    private void checkContentHeightChanged()
    {
        int currentHeight = getOffsetHeight();
        if (Math.abs(contentHeight - currentHeight) > ANTI_RECURSIVE_RECALC_OFFSET)
        {
            contentHeight = currentHeight;
            int offset = repositionVertically();
            if (!currentScrollState)
            {
                moveRootPopup(0, offset);
            }
        }
    }

    private void moveRootPopup(int offsetX, int offsetY)
    {
        for (Widget widget : RootPanel.getBody())
        {
            if (widget instanceof FastScrollPopupPanel)
            {
                FastScrollPopupPanel popup = (FastScrollPopupPanel)widget;
                if (popup.isShowing())
                {
                    int left = DocumentUtils.getAbsoluteLeft(popup.getElement()) + offsetX;
                    int top = DocumentUtils.getAbsoluteTop(popup.getElement()) + offsetY;
                    popup.setPopupPosition(left, top);
                    break;
                }
            }
        }
    }

    private void recalcMaxHeight(Panel container)
    {
        int height = Window.getClientHeight() - DIALOG_CONTENT_TOTAL_MARGIN;
        height -= layoutHolder.getStatusPanelHeight();

        container.getElement().getStyle().setProperty(CSS_MAX_HEIGHT_PROPERTY, height, Unit.PX);
    }

    private void redrawOpenedPopup(Panel panelFrom, Panel panelTo)
    {
        if (null == panelFrom || null == panelTo)
        {
            return;
        }

        for (Widget widget : panelFrom)
        {
            if (widget instanceof FastScrollPopupPanel)
            {
                FastScrollPopupPanel popup = (FastScrollPopupPanel)widget;
                if (popup.isShowing())
                {
                    popup.hide();
                    popup.show();
                    Widget partner = popup.getPartnerWidget();
                    popup.setWidth(partner.getOffsetWidth() - POPUP_WIDTH_DIFF + Unit.PX.getType());
                    popup.setPopupPosition(partner.getAbsoluteLeft(), partner.getAbsoluteTop()
                                                                      + partner.getOffsetHeight() - 1);
                    break;
                }
            }
        }
    }

    @Override
    protected void beginDragging(MouseDownEvent event)
    {
        if (getCaption() instanceof FormWindowCommandsCaption &&
            !((FormWindowCommandsCaption)getCaption()).isDraggingEnabled(event))
        {
            // запрет перемещения свёрнутого модального окна
            event.preventDefault();
        }
        else
        {
            super.beginDragging(event);
        }
    }

    public void setGlassVisibility(boolean visible)
    {
        getGlassElement().getStyle().setVisibility(visible ? Visibility.VISIBLE : Visibility.HIDDEN);
    }
}
