package ru.naumen.core.client.widgets;

import com.google.gwt.event.dom.client.DropEvent;
import com.google.gwt.event.dom.client.DropHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.user.client.Timer;

/**
 * Обработчик DropEvent для текстовых полей
 *
 * <AUTHOR>
 * @since 11 сент. 2015 г.
 */
public class NauDropHandler<T> implements DropHandler
{
    /**
     * После реакции на dropEvent js не может мгновенно высчитать inputValue, тем более, если до этого оно было не
     * пусто.
     * Для этого требуется времени до 0.2 сек. (взято с запасом, все работает и при 0.1)
     */
    private static final int DROP_UPDATE_TIME = 200;

    HasValueOrThrow<T> widget;

    Timer valueChanger = new Timer()
    {
        @Override
        public void run()
        {
            ValueChangeEvent.fire(widget, widget.getValue());
        }

        ;
    };

    public NauDropHandler(HasValueOrThrow<T> w)
    {
        widget = w;
    }

    @Override
    public void onDrop(DropEvent event)
    {
        // Событие на изменение значения запускаем с задержкой
        valueChanger.schedule(DROP_UPDATE_TIME);
    }
}
