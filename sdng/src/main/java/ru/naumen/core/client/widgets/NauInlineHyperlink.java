package ru.naumen.core.client.widgets;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.user.client.ui.InlineHyperlink;

/**
 * Исправляет генерацию id ссылки.<br>
 * {@link InlineHyperlink} исключает использование div'а в качестве обёртки ссылки, однако не меняет обратно назначение
 * {@link com.google.gwt.user.client.ui.UIObject#ensureDebugId} из-за чего к атрибуту id ссылки добавляется ненужная
 * строка "-wrapper".<br>
 * Поиск по id элемента производится в автотестах и возникает ошибка из-за некорректного id.
 *
 * <AUTHOR>
 * @since 2013-12-09
 */
public class NauInlineHyperlink extends InlineHyperlink
{
    /**
     * Creates an empty hyperlink.
     */
    public NauInlineHyperlink()
    {
        super();
    }

    /**
     * Creates a hyperlink with its html and target history token specified.
     *
     * @param html the hyperlink's html
     * @param targetHistoryToken the history token to which it will link
     * @see #setTargetHistoryToken
     */
    public NauInlineHyperlink(SafeHtml html, String targetHistoryToken)
    {
        super(html.asString(), true, targetHistoryToken);
    }

    /**
     * Creates a hyperlink with its text and target history token specified.
     *
     * @param text the hyperlink's text
     * @param targetHistoryToken the history token to which it will link
     */
    public NauInlineHyperlink(String text, String targetHistoryToken)
    {
        super(text, false, targetHistoryToken);
    }

    /**
     * Исключает добавление "-wrapper" к атрибуту 'id' элемента <code>&lt;a&gt;</code>
     */
    @Override
    protected void onEnsureDebugId(String baseID)
    {
        ensureDebugId(getElement(), "", baseID);
    }
}
