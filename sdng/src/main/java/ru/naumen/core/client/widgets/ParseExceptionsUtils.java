package ru.naumen.core.client.widgets;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Класс обработки исключений
 * Служит для избежания дублирования кода
 *
 * <AUTHOR>
 */
public class ParseExceptionsUtils
{
    private static final Logger logger = Logger.getLogger(ParseExceptionsUtils.class.getName());

    public static <T> T getValueSafe(HasValueOrThrow<T> object)
    {
        try
        {
            return object.getValueOrThrow();
        }
        catch (Exception e)
        {
            if (logger.isLoggable(Level.FINE))
            {
                logger.log(Level.FINE, e.getMessage(), e);
            }
            return null;
        }
    }
}
