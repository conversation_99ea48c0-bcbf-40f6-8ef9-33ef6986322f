package ru.naumen.core.client.widgets;

/*
 * This software is published under the Apchae 2.0 licenses.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Author: <PERSON>
 * Web: http://blog.elitecoderz.net
 */

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;

import com.google.common.collect.Lists;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.event.dom.client.MouseUpEvent;
import com.google.gwt.event.dom.client.MouseUpHandler;
import com.google.gwt.resources.client.ClientBundle;
import com.google.gwt.resources.client.ImageResource;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.ComplexPanel;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FocusWidget;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.PushButton;
import com.google.gwt.user.client.ui.RichTextArea;
import com.google.gwt.user.client.ui.RichTextArea.FontSize;
import com.google.gwt.user.client.ui.RichTextArea.Formatter;
import com.google.gwt.user.client.ui.ToggleButton;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.richtext.NauRichTextArea;

public class RichTextToolbar extends Composite
{

    public interface Resources extends ClientBundle
    {
        @Source("richTextToolbar.gif")
        ImageResource richTextToolbar();
    }

    /** Click Handler of the Toolbar **/
    private class EventHandler implements ClickHandler, KeyUpHandler, ChangeHandler, MouseUpHandler
    {
        @Override
        public void onChange(ChangeEvent event)
        {
            if (event.getSource().equals(fontlist))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle("<span style=\"font-family: " + fontlist.getValue(fontlist.getSelectedIndex())
                                    + ";\">", HTML_STYLE_CLOSE_SPAN);
                }
                else
                {
                    if (!rtWidget.area.getSelection().text.equals("\n"))
                    {
                        styleTextFormatter.setFontName(fontlist.getValue(fontlist.getSelectedIndex()));
                    }
                }
            }
            else if (event.getSource().equals(fontsizelist))
            {
                RichTextArea.FontSize fontSize = GUI_FONTSIZELIST.get(fontsizelist.getValue(fontsizelist
                        .getSelectedIndex()));
                if (fontSize != null)
                {
                    if (isHTMLMode())
                    {
                        changeHtmlStyle("<span style=\"font: " + fontSize.getNumber() + ";\">", HTML_STYLE_CLOSE_SPAN);
                    }
                    else
                    {
                        if (!rtWidget.area.getSelection().text.equals("\n"))
                        {
                            styleTextFormatter.setFontSize(fontSize);
                        }
                    }
                }
            }
            else if (event.getSource().equals(colorlist))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(
                            "<span style=\"color: " + colorlist.getValue(colorlist.getSelectedIndex()) + ";\">",
                            HTML_STYLE_CLOSE_SPAN);
                }
                else
                {
                    if (!rtWidget.area.getSelection().text.equals("\n"))
                    {
                        styleTextFormatter.setForeColor(colorlist.getValue(colorlist.getSelectedIndex()));
                    }
                }
            }
        }

        @Override
        public void onClick(ClickEvent event)
        {
            if (event.getSource().equals(bold))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_BOLD, HTML_STYLE_CLOSE_SPAN);
                }
                else
                {
                    styleTextFormatter.toggleBold();
                }
            }
            else if (event.getSource().equals(italic))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_ITALIC, HTML_STYLE_CLOSE_SPAN);
                }
                else
                {
                    styleTextFormatter.toggleItalic();
                }
            }
            else if (event.getSource().equals(underline))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_UNDERLINE, HTML_STYLE_CLOSE_SPAN);
                }
                else
                {
                    styleTextFormatter.toggleUnderline();
                }
            }
            else if (event.getSource().equals(stroke))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_LINETHROUGH, HTML_STYLE_CLOSE_SPAN);
                }
                else
                {
                    styleTextFormatter.toggleStrikethrough();
                }
            }
            else if (event.getSource().equals(subscript))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_SUBSCRIPT, HTML_STYLE_CLOSE_SUBSCRIPT);
                }
                else
                {
                    styleTextFormatter.toggleSubscript();
                }
            }
            else if (event.getSource().equals(superscript))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_SUPERSCRIPT, HTML_STYLE_CLOSE_SUPERSCRIPT);
                }
                else
                {
                    styleTextFormatter.toggleSuperscript();
                }
            }
            else if (event.getSource().equals(alignleft))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_ALIGNLEFT, HTML_STYLE_CLOSE_DIV);
                }
                else
                {
                    styleTextFormatter.setJustification(RichTextArea.Justification.LEFT);
                }
            }
            else if (event.getSource().equals(alignmiddle))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_ALIGNCENTER, HTML_STYLE_CLOSE_DIV);
                }
                else
                {
                    styleTextFormatter.setJustification(RichTextArea.Justification.CENTER);
                }
            }
            else if (event.getSource().equals(alignright))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_ALIGNRIGHT, HTML_STYLE_CLOSE_DIV);
                }
                else
                {
                    styleTextFormatter.setJustification(RichTextArea.Justification.RIGHT);
                }
            }
            else if (event.getSource().equals(orderlist))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_ORDERLIST, HTML_STYLE_CLOSE_ORDERLIST);
                }
                else
                {
                    styleTextFormatter.insertOrderedList();
                }
            }
            else if (event.getSource().equals(unorderlist))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_UNORDERLIST, HTML_STYLE_CLOSE_UNORDERLIST);
                }
                else
                {
                    styleTextFormatter.insertUnorderedList();
                }
            }
            else if (event.getSource().equals(indentright))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_OPEN_INDENTRIGHT, HTML_STYLE_CLOSE_DIV);
                }
                else
                {
                    styleTextFormatter.rightIndent();
                }
            }
            else if (event.getSource().equals(indentleft))
            {
                if (!isHTMLMode())
                {
                    styleTextFormatter.leftIndent();
                }
            }
            else if (event.getSource().equals(generatelink))
            {
                String url = Window.prompt(GUI_DIALOG_INSERTURL, "http://");
                if (url != null)
                {
                    createLink(url);
                }
            }
            else if (event.getSource().equals(breaklink))
            {
                if (!isHTMLMode())
                {
                    styleTextFormatter.removeLink();
                }
                //TODO else nothing can be done here at the moment
            }
            else if (event.getSource().equals(insertimage))
            {
                String url = Window.prompt(GUI_DIALOG_IMAGEURL, "http://");
                if (url != null)
                {
                    rtWidget.area.insertImage(url);
                }
            }
            else if (event.getSource().equals(insertline))
            {
                if (isHTMLMode())
                {
                    changeHtmlStyle(HTML_STYLE_HLINE, "");
                }
                else
                {
                    styleTextFormatter.insertHorizontalRule();
                }
            }
            else if (event.getSource().equals(removeformatting))
            {
                if (!isHTMLMode())
                {
                    styleTextFormatter.removeFormat();
                    // Для корректной работы в Chrome
                    styleTextFormatter.setFontName("Arial");
                    styleTextFormatter.setFontSize(FontSize.X_SMALL);

                }
                //TODO else nothing can be done here at the moment
            }
            else if (event.getSource().equals(texthtml))
            {
                if (texthtml.isDown())
                {
                    rtWidget.area.setHtmlSourceMode(true);
                    String html = rtWidget.area.getHTML();
                    html = rtWidget.area.normalizeFonts(html);
                    html = rtWidget.decodeHrefs(html);
                    // Необходимо разбить длинную строку Base64-представления изображения на несколько,
                    // поскольку слишком длинные строки серьезно подвешивают Chrome.
                    html = rtWidget.normalizeBase64Images(html, false);
                    rtWidget.area.setText(html);
                }
                else
                {
                    rtWidget.area.setHtmlSourceMode(false);
                    String html = rtWidget.area.getText();
                    //Удаляем лишние пробельные символы, потому что они заменяются
                    //на &nbsp; и поганят вёрстку.
                    html = StringUtilities.removeHtmlWhitespaceCharacters(html);
                    html = rtWidget.sanitize(html);
                    //ipestov: Так как в режиме редактирования HTML можно ввести "пустые" ссылки,
                    // то при переходе обратно в визуальный режим необходимо привести их к нормальному виду.
                    html = NauRichTextArea.normalizeLinks(html);
                    rtWidget.area.setHTML(html); // NOPMD NSDPRD-28509 unsafe html
                }
            }
            updateStatus();
        }

        @Override
        public void onKeyUp(KeyUpEvent event)
        {
            if (event.getSource().equals(rtWidget.area) && rtWidget.area.isEnabled())
            {
                updateToolbar();
            }
        }

        @Override
        public void onMouseUp(MouseUpEvent event)
        {
            if (event.getSource().equals(rtWidget.area))
            {
                updateToolbar();
            }
        }
    }

    private static CommonMessages messages;
    private static Resources resources;

    private static final String CSS_ROOT_NAME = "RichTextToolbar";

    //Fontlists - First Value (key) is the Name to display, Second Value (value) is the HTML-Definition
    public final static HashMap<String, String> GUI_FONTLIST = new HashMap<>();

    static
    {
        GUI_FONTLIST.put("Times New Roman", "Times New Roman, Times");
        GUI_FONTLIST.put("Arial", "Arial");
        GUI_FONTLIST.put("Courier New", "Courier New");
        GUI_FONTLIST.put("Georgia", "Georgia");
        GUI_FONTLIST.put("Trebuchet", "Trebuchet");
        GUI_FONTLIST.put("Verdana", "Verdana");
    }

    public final static HashMap<String, RichTextArea.FontSize> GUI_FONTSIZELIST = new HashMap<>();

    static
    {
        GUI_FONTSIZELIST.put("xxsmall", RichTextArea.FontSize.XX_SMALL);
        GUI_FONTSIZELIST.put("xsmall", RichTextArea.FontSize.X_SMALL);
        GUI_FONTSIZELIST.put("small", RichTextArea.FontSize.SMALL);
        GUI_FONTSIZELIST.put("medium", RichTextArea.FontSize.MEDIUM);
        GUI_FONTSIZELIST.put("large", RichTextArea.FontSize.LARGE);
        GUI_FONTSIZELIST.put("xlarge", RichTextArea.FontSize.X_LARGE);
        GUI_FONTSIZELIST.put("xxlarge", RichTextArea.FontSize.XX_LARGE);
    }

    //HTML Related (styles made by SPAN and DIV)
    private static final String HTML_STYLE_CLOSE_SPAN = "</span>";

    private static final String HTML_STYLE_CLOSE_DIV = "</div>";
    private static final String HTML_STYLE_OPEN_BOLD = "<span style=\"font-weight: bold;\">";
    private static final String HTML_STYLE_OPEN_ITALIC = "<span style=\"font-style: italic;\">";
    private static final String HTML_STYLE_OPEN_UNDERLINE = "<span style=\"text-decoration: underline;\">";
    private static final String HTML_STYLE_OPEN_LINETHROUGH = "<span style=\"text-decoration: line-through;\">";
    private static final String HTML_STYLE_OPEN_ALIGNLEFT = "<div style=\"text-align: left;\">";
    private static final String HTML_STYLE_OPEN_ALIGNCENTER = "<div style=\"text-align: center;\">";
    private static final String HTML_STYLE_OPEN_ALIGNRIGHT = "<div style=\"text-align: right;\">";
    private static final String HTML_STYLE_OPEN_INDENTRIGHT = "<div style=\"margin-left: 40px;\">";

    //HTML Related (styles made by custom HTML-Tags)
    private static final String HTML_STYLE_OPEN_SUBSCRIPT = "<sub>";
    private static final String HTML_STYLE_CLOSE_SUBSCRIPT = "</sub>";
    private static final String HTML_STYLE_OPEN_SUPERSCRIPT = "<sup>";
    private static final String HTML_STYLE_CLOSE_SUPERSCRIPT = "</sup>";
    private static final String HTML_STYLE_OPEN_ORDERLIST = "<ol><li>";
    private static final String HTML_STYLE_CLOSE_ORDERLIST = "</ol></li>";
    private static final String HTML_STYLE_OPEN_UNORDERLIST = "<ul><li>";
    private static final String HTML_STYLE_CLOSE_UNORDERLIST = "</ul></li>";

    //HTML Related (styles without closing Tag)
    private static final String HTML_STYLE_HLINE = "<hr style=\"width: 100%; height: 2px;\">";

    //GUI Related stuff
    private static final String GUI_DIALOG_INSERTURL = "Enter a link URL:";
    private static final String GUI_DIALOG_IMAGEURL = "Enter an image URL:";

    private static final String GUI_HOVERTEXT_SWITCHVIEW = "Switch View HTML/Source";

    private static final String GUI_HOVERTEXT_REMOVEFORMAT = "Remove Formatting";
    private static final String GUI_HOVERTEXT_IMAGE = "Insert Image";
    private static final String GUI_HOVERTEXT_HLINE = "Insert Horizontal Line";
    private static final String GUI_HOVERTEXT_BREAKLINK = "Break Link";
    private static final String GUI_HOVERTEXT_LINK = "Generate Link";
    private static final String GUI_HOVERTEXT_IDENTLEFT = "Ident Left";
    private static final String GUI_HOVERTEXT_IDENTRIGHT = "Ident Right";
    private static final String GUI_HOVERTEXT_UNORDERLIST = "Unordered List";
    private static final String GUI_HOVERTEXT_ORDERLIST = "Ordered List";
    private static final String GUI_HOVERTEXT_ALIGNRIGHT = "Align Right";
    private static final String GUI_HOVERTEXT_ALIGNCENTER = "Align Center";
    private static final String GUI_HOVERTEXT_ALIGNLEFT = "Align Left";
    private static final String GUI_HOVERTEXT_SUPERSCRIPT = "Superscript";
    private static final String GUI_HOVERTEXT_SUBSCRIPT = "Subscript";
    private static final String GUI_HOVERTEXT_STROKE = "Stroke";
    private static final String GUI_HOVERTEXT_UNDERLINE = "Underline";
    private static final String GUI_HOVERTEXT_ITALIC = "Italic";
    private static final String GUI_HOVERTEXT_BOLD = "Bold";
    private ArrayList<String> fontRGBList;
    //The main (Vertical)-Panel and the two inner (Horizontal)-Panels
    protected final HorizontalPanel topPanel;
    protected final HorizontalPanel bottomPanel;
    protected VerticalPanel outer;
    protected ToggleButton subscript;
    protected ToggleButton superscript;

    /** Private Variables **/
    private final GwtRichTextWidget rtWidget;
    private final Formatter styleTextFormatter;
    //We use an internal class of the ClickHandler and the KeyUpHandler to be private to others with these events
    private final EventHandler evHandler;
    //The Buttons of the Menubar
    private ToggleButton bold;
    private ToggleButton italic;
    private ToggleButton underline;
    private ToggleButton stroke;
    private PushButton alignleft;
    private PushButton alignmiddle;
    private PushButton alignright;
    private PushButton orderlist;
    private PushButton unorderlist;
    private PushButton indentleft;
    private PushButton indentright;
    private PushButton generatelink;
    private PushButton breaklink;
    private PushButton insertline;

    private PushButton insertimage;
    private PushButton removeformatting;

    private ToggleButton texthtml;
    private ListBox fontlist;
    private ListBox colorlist;
    private ListBox fontsizelist;

    private boolean enabled;

    public RichTextToolbar(GwtRichTextWidget rtfWidget)
    {
        if (null == messages)
        {
            messages = GWT.create(CommonMessages.class);
        }
        if (resources == null)
        {
            resources = GWT.create(Resources.class);
        }
        outer = new VerticalPanel();

        topPanel = new HorizontalPanel();
        bottomPanel = new HorizontalPanel();
        topPanel.setStyleName(CSS_ROOT_NAME);
        bottomPanel.setStyleName(CSS_ROOT_NAME);

        this.rtWidget = rtfWidget;
        styleTextFormatter = rtWidget.area.getFormatter();

        topPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_LEFT);
        bottomPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_LEFT);

        outer.add(topPanel);
        outer.add(bottomPanel);

        outer.setWidth("100%");
        outer.setStyleName(CSS_ROOT_NAME);
        initWidget(outer);

        evHandler = new EventHandler();

        rtWidget.area.addKeyUpHandler(evHandler);
        rtWidget.area.addMouseUpHandler(evHandler);

        buildTools();
    }

    /**
     * Преобразует размер шрифта из px в номер из диапазона xxsmall - xxlarge (1-7)
     * @param input размер шрифта в px
     * @return номер размера шрифта из диапазона xxsmall - xxlarge
     */
    public native String getFontSizeFromPx(double input)
    /*-{
        var basePxSize = 16;
        var fontSizes = [0.625, 0.82, 1, 1.125, 1.5, 2, 3]
        var emSize = input / basePxSize;
        var difference = fontSizes.map(function(e){return Math.abs(emSize - e)});
        return difference.reduce(function(lowest, next, index) {
                   return next < difference[lowest] ? index : lowest; 
        },0) + 1;
    }-*/;

    public void updateToolbar()
    {
        if (!isHTMLMode())
        {
            updateToolbarNoHtmlMode();
        }
        else
        {
            clearToolbarValues();
        }
    }

    protected boolean isEnabled()
    {
        return enabled;
    }

    protected void setChildrenEnabled(ComplexPanel complexPanel, boolean enabled)
    {
        for (Iterator<Widget> iter = complexPanel.iterator(); iter.hasNext(); )
        {
            Widget child = iter.next();
            if (child instanceof FocusWidget)
            {
                ((FocusWidget)child).setEnabled(enabled);
                //чтобы кнопки при дизабле визуально изменялись
                child.getElement().getStyle().setOpacity(enabled ? 1 : 0.5);
            }
        }
    }

    protected void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
        setChildrenEnabled(topPanel, enabled);
        setChildrenEnabled(bottomPanel, enabled);
    }

    /** Method with a more understandable name to get if HTML mode is on or not **/
    boolean isHTMLMode()
    {
        return texthtml.isDown();
    }

    /** Initialize the options on the toolbar **/
    private void buildTools()
    {
        topPanel.add(bold = createToggleButton(resources.richTextToolbar(), 0, 0, 20, 20, GUI_HOVERTEXT_BOLD));
        topPanel.add(italic = createToggleButton(resources.richTextToolbar(), 0, 60, 20, 20, GUI_HOVERTEXT_ITALIC));
        topPanel.add(underline = createToggleButton(resources.richTextToolbar(), 0, 140, 20, 20,
                GUI_HOVERTEXT_UNDERLINE));
        topPanel.add(stroke = createToggleButton(resources.richTextToolbar(), 0, 120, 20, 20, GUI_HOVERTEXT_STROKE));
        topPanel.add(new HTML("&nbsp;")); // NOPMD safe html
        topPanel.add(subscript = createToggleButton(resources.richTextToolbar(), 0, 600, 20, 20,
                GUI_HOVERTEXT_SUBSCRIPT));
        topPanel.add(superscript = createToggleButton(resources.richTextToolbar(), 0, 620, 20, 20,
                GUI_HOVERTEXT_SUPERSCRIPT));
        topPanel.add(new HTML("&nbsp;")); // NOPMD safe html
        topPanel.add(
                alignleft = createPushButton(resources.richTextToolbar(), 0, 460, 20, 20, GUI_HOVERTEXT_ALIGNLEFT));
        topPanel.add(alignmiddle = createPushButton(resources.richTextToolbar(), 0, 420, 20, 20,
                GUI_HOVERTEXT_ALIGNCENTER));
        topPanel.add(alignright = createPushButton(resources.richTextToolbar(), 0, 480, 20, 20,
                GUI_HOVERTEXT_ALIGNRIGHT));
        topPanel.add(new HTML("&nbsp;")); // NOPMD safe html
        topPanel.add(orderlist = createPushButton(resources.richTextToolbar(), 0, 80, 20, 20, GUI_HOVERTEXT_ORDERLIST));
        topPanel.add(unorderlist = createPushButton(resources.richTextToolbar(), 0, 20, 20, 20,
                GUI_HOVERTEXT_UNORDERLIST));
        topPanel.add(indentright = createPushButton(resources.richTextToolbar(), 0, 400, 20, 20,
                GUI_HOVERTEXT_IDENTRIGHT));
        topPanel.add(
                indentleft = createPushButton(resources.richTextToolbar(), 0, 540, 20, 20, GUI_HOVERTEXT_IDENTLEFT));
        topPanel.add(new HTML("&nbsp;")); // NOPMD safe html
        topPanel.add(generatelink = createPushButton(resources.richTextToolbar(), 0, 500, 20, 20, GUI_HOVERTEXT_LINK));
        topPanel.add(
                breaklink = createPushButton(resources.richTextToolbar(), 0, 640, 20, 20, GUI_HOVERTEXT_BREAKLINK));
        topPanel.add(new HTML("&nbsp;")); // NOPMD safe html
        topPanel.add(insertline = createPushButton(resources.richTextToolbar(), 0, 360, 20, 20, GUI_HOVERTEXT_HLINE));
        topPanel.add(insertimage = createPushButton(resources.richTextToolbar(), 0, 380, 20, 20, GUI_HOVERTEXT_IMAGE));
        topPanel.add(new HTML("&nbsp;")); // NOPMD safe html
        topPanel.add(removeformatting = createPushButton(resources.richTextToolbar(), 20, 460, 20, 20,
                GUI_HOVERTEXT_REMOVEFORMAT));
        topPanel.add(new HTML("&nbsp;")); // NOPMD safe html
        topPanel.add(texthtml = createToggleButton(resources.richTextToolbar(), 0, 260, 20, 20,
                GUI_HOVERTEXT_SWITCHVIEW));

        bottomPanel.add(fontlist = createFontList());
        bottomPanel.add(new HTML("&nbsp;")); // NOPMD safe html
        bottomPanel.add(fontsizelist = createFontSizeList());
        bottomPanel.add(new HTML("&nbsp;")); // NOPMD safe html
        bottomPanel.add(colorlist = createColorList());

        ensureDebugId();
    }

    /** Method called to toggle the style in HTML-Mode **/
    private void changeHtmlStyle(String startTag, String stopTag)
    {
        rtWidget.area.insertOuterHtml(startTag, stopTag);
    }

    private void clearToolbarValues()
    {
        fontlist.setSelectedIndex(0);
        fontsizelist.setSelectedIndex(0);
        colorlist.setSelectedIndex(0);
    }

    /** Method to create the colorlist for the toolbar **/
    private ListBox createColorList()
    {
        ListBox mylistBox = new ListBox();
        mylistBox.addChangeHandler(evHandler);
        mylistBox.setVisibleItemCount(1);

        mylistBox.addItem(messages.color());
        mylistBox.addItem(messages.white(), "#ffffff");
        mylistBox.addItem(messages.black(), "#000000");
        mylistBox.addItem(messages.red(), "#ff0000");
        mylistBox.addItem(messages.green(), "#008000");
        mylistBox.addItem(messages.yellow(), "#ffff00");
        mylistBox.addItem(messages.blue(), "#0000ff");
        initFontRGBList();

        return mylistBox;
    }

    /** Method to create the fontlist for the toolbar **/
    private ListBox createFontList()
    {
        ListBox mylistBox = new ListBox();
        mylistBox.addChangeHandler(evHandler);
        mylistBox.setVisibleItemCount(1);

        mylistBox.addItem(messages.font());
        for (String name : GUI_FONTLIST.keySet())
        {
            mylistBox.addItem(name, GUI_FONTLIST.get(name));
        }

        return mylistBox;
    }

    /** Method to create the fontsizelist for the toolbar **/
    private ListBox createFontSizeList()
    {
        ListBox mylistBox = new ListBox();

        mylistBox.addChangeHandler(evHandler);
        mylistBox.setVisibleItemCount(1);

        mylistBox.addItem(messages.fontSize());
        for (String name : GUI_FONTSIZELIST.keySet())
        {
            mylistBox.addItem(name);
        }

        return mylistBox;
    }

    /**
     * Create link on text selection.
     * <i>Note:</i> If text selection is empty then link text will be set to given URL.
     * @param url - link URL
     */
    private void createLink(String url)
    {
        NauRichTextArea.Selection selection = rtWidget.area.getSelection();

        if (isHTMLMode())
        {
            if (selection.isEmpty())
            {
                rtWidget.area.insertOuterHtml("<a href=\"" + url + "\">" + url, "</a>");
            }
            else
            {
                rtWidget.area.insertOuterHtml("<a href=\"" + url + "\">", "</a>");
            }
        }
        else
        {
            //для выделенных изображений, например, selectedText=="", a isEmpty == false
            if (selection.isEmpty())
            {
                //в редакторе ничего не выделено: втавить ссылку на место курсора.
                //workaround https://groups.google.com/forum/#!searchin/google-web-toolkit/createLink/google-web-toolkit/cu_AVJYrgRk/7t_41Arddh8J
                //styleTextFormatter.insertHTML("<a href=\"" + url + "\">" + url + "</a>");
                rtWidget.area.insertHtml("<a href=\"" + url + "\">" + url + "</a>");
            }
            else
            {
                styleTextFormatter.createLink(url);
            }
        }
    }

    /** Method to create a Push button for the toolbar **/
    private PushButton createPushButton(ImageResource image, int top, int left, int width, int height, String tip)
    {
        final Image extract = new Image(image.getSafeUri().asString(), left, top, width, height);
        final PushButton tb = new PushButton(extract);
        tb.setHeight(height + "px");
        tb.setWidth(width + "px");
        tb.addClickHandler(evHandler);
        if (tip != null)
        {
            tb.setTitle(tip);
        }
        return tb;
    }

    /** Method to create a Toggle button for the toolbar **/
    private ToggleButton createToggleButton(ImageResource image, int top, int left, int width, int height,
            final String tip)
    {
        final Image extract = new Image(image.getSafeUri().asString(), left, top, width, height);
        final ToggleButton tb = new ToggleButton(extract)
        {
            @Override
            public void setDown(boolean down)
            {
                super.setDown(down);
                showPushed(extract);
            }

            @Override
            protected void onClick()
            {
                super.onClick();
                showPushed(extract);
            }

            private void showPushed(Image extract)
            {
                extract.getElement().getStyle().setBackgroundColor(isDown() ? "#eee" : "transparent");
            }
        };

        tb.setHeight(height + "px");
        tb.setWidth(width + "px");
        tb.addClickHandler(evHandler);
        if (tip != null)
        {
            tb.setTitle(tip);
        }
        return tb;
    }

    private void ensureDebugId()
    {
        fontlist.ensureDebugId("fontList");
        fontsizelist.ensureDebugId("fontSizeList");
        colorlist.ensureDebugId("colorList");
    }

    private void initFontRGBList()
    {
        fontRGBList = new ArrayList<>();
        fontRGBList.add("rgb(255, 255, 255)");
        fontRGBList.add("rgb(0, 0, 0)");
        fontRGBList.add("rgb(255, 0, 0)");
        fontRGBList.add("rgb(0, 128, 0)");
        fontRGBList.add("rgb(255, 255, 0)");
        fontRGBList.add("rgb(0, 0, 255)");
    }

    /** Private method to set the toggle buttons and disable/enable buttons which do not work in html-mode **/
    private void updateStatus()
    {
        if (styleTextFormatter != null)
        {
            bold.setDown(styleTextFormatter.isBold());
            italic.setDown(styleTextFormatter.isItalic());
            underline.setDown(styleTextFormatter.isUnderlined());
            subscript.setDown(styleTextFormatter.isSubscript());
            superscript.setDown(styleTextFormatter.isSuperscript());
            stroke.setDown(styleTextFormatter.isStrikethrough());
        }

        if (isHTMLMode())
        {
            removeformatting.setEnabled(false);
            indentleft.setEnabled(false);
            breaklink.setEnabled(false);
        }
        else
        {
            removeformatting.setEnabled(true);
            indentleft.setEnabled(true);
            breaklink.setEnabled(true);
        }
    }

    /**
     * Обновление панели инструментов виджета RTF
     */
    private void updateToolbarNoHtmlMode()
    {
        if (StringUtilities.isEmpty(rtWidget.area.getSelection().text))
        {
            String fontName = rtWidget.area.getFontName();
            String fontSize = rtWidget.area.getFontSize();
            String foreColor = rtWidget.area.getForeColor();
            if (fontSize == null)
            {
                fontSize = rtWidget.area.getSelectionFontSize();
            }
            if (fontSize.contains("px"))
            {
                fontSize = getFontSizeFromPx(Double.parseDouble(fontSize.replace("px", "")));
            }
            int indexOfFont = Lists.newArrayList(GUI_FONTLIST.values()).indexOf(fontName);
            if (indexOfFont < 0)
            {
                //Для случая, когда шрифт указан не как Times, а как Times New Roman
                indexOfFont = Lists.newArrayList(GUI_FONTLIST.keySet()).indexOf(fontName);
            }
            fontlist.setSelectedIndex(indexOfFont + 1);
            fontsizelist.setSelectedIndex(Integer.parseInt(fontSize));
            colorlist.setSelectedIndex(fontRGBList.indexOf(foreColor) + 1);
        }
        else
        {
            clearToolbarValues();
        }
    }
}
