package ru.naumen.core.client.widgets;

import static ru.naumen.core.client.DocumentUtils.getContentDocument;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.ArrayList;
import java.util.HashSet;

import com.google.gwt.core.client.JavaScriptObject;
import com.google.gwt.core.client.JsArray;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.RepeatingCommand;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.core.shared.GWT;
import com.google.gwt.dom.client.AnchorElement;
import com.google.gwt.dom.client.AreaElement;
import com.google.gwt.dom.client.DivElement;
import com.google.gwt.dom.client.Document;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.IFrameElement;
import com.google.gwt.dom.client.ImageElement;
import com.google.gwt.dom.client.NodeList;
import com.google.gwt.dom.client.ScriptElement;
import com.google.gwt.dom.client.Style.Overflow;
import com.google.gwt.dom.client.Style.Position;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.dom.client.StyleElement;
import com.google.gwt.event.logical.shared.ResizeEvent;
import com.google.gwt.event.logical.shared.ResizeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.inject.client.AsyncProvider;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.RootPanel;

import ru.naumen.common.client.utils.HtmlFactoryUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.ClientUtils;
import ru.naumen.core.client.DocumentUtils;
import ru.naumen.core.client.JSUtils;
import ru.naumen.core.client.MainContentDisplayImpl;
import ru.naumen.core.client.ModuleHolder;
import ru.naumen.core.client.attr.presentation.factories.view.html.AttributeRichTextHtmlFactoryImpl;
import ru.naumen.core.client.menu.ShowNavTreeEvent;
import ru.naumen.core.client.menu.ShowNavTreeEventHandler;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.preview.FilePreviewPresenterFactory;
import ru.naumen.core.client.preview.RtfImageInitContext;
import ru.naumen.core.client.utils.WindowScrollController;
import ru.naumen.core.client.widgets.Constants.Tags;
import ru.naumen.core.client.widgets.Constants.Target;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.utils.HtmlUtils;

/**
 * Класс предназначен для обслуживания процессов заполнения и ресайза отображения содержимого типа
 * "Текст RTF" внутри IFRAME, когда такое содержимое формируется рендерингом HTML. См.
 * {@link AttributeRichTextHtmlFactoryImpl}
 * Для этого при рендеринге нужно вызвать {@link #enshureInstalled()}, а затем установить содержимое
 * с помощью метода {@link #setContent(String, String)}.
 *
 * <AUTHOR>
 * @since 12 марта 2014 г.
 */
@Singleton
public class RichTextView implements ExpandCollapseEventHandler, ShowNavTreeEventHandler
{
    /**
     * Реализация подстройки размера контейнера RTF
     */
    public static class ResizeRTFImpl
    {
        public boolean resize(Element iframe, boolean isOperator)
        {
            return resize(iframe, null, isOperator);
        }

        /**
         * @param el элемент контейнер RTF
         * @param lastDimensions предыдущие размеры
         * @param isOperator признак того что находимся в операторе
         * @return <code>true</code> если было проведено изменение размера
         *         <code>false</code> если размер не изменился
         */
        public boolean resize(Element el, Map<String, Dimension> lastDimensions, boolean isOperator)
        {
            Element doc = el, body = el;
            if (IFrameElement.is(el))
            {
                Document iframeDoc = getContentDocument(IFrameElement.as(el));
                if (iframeDoc == null)
                {
                    LOG.info("resizeIFrame: doc == null");
                    return false;
                }
                doc = iframeDoc.getDocumentElement();
                body = iframeDoc.getBody();
                if (body == null)
                {
                    LOG.info("resizeIFrame: body == null");
                    return false;
                }
            }
            Dimension lastDimension = lastDimensions != null ? lastDimensions.get(el.getId()) : null;
            String lastWidth = lastDimension == null ? getProperty(el, "width") : lastDimension.width;
            String lastHeight = lastDimension == null ? getProperty(el, "height") : lastDimension.height;

            NodeList<Element> images = doc.getElementsByTagName("img");
            for (int i = 0; i < images.getLength(); ++i)
            {
                ImageElement img = ImageElement.as(images.getItem(i));
                String imageDesiredWidth = img.getAttribute("width");
                if (StringUtilities.isEmpty(imageDesiredWidth))
                {
                    continue;
                }
                String imageRealWidth = Integer.toString(img.getWidth());
                if (!StringUtilities.equalsIgnoreCase(imageDesiredWidth, imageRealWidth))
                {
                    // Для пропорционального сжатия картинок
                    img.setAttribute("height", "auto");
                }
            }

            int verticalScrollWidth = ClientUtils.getPageVerticalScrollWidth();

            // Убрать ширину
            setProperty(el, "width", "auto");
            // Вычислить реальную ширину
            int width = calculateWidth(el, doc, body, isOperator);
            String widthStr = getWidthStr(width);
            setProperty(el, "width", widthStr);
            // Подогнать высоту под новую ширину
            String heightStr = calculateHeightStr(doc, body);
            setProperty(el, "height", heightStr);

            if (!isAdvlistColumn(el))
            {
                int scrollWidthDiff = ClientUtils.getPageVerticalScrollWidth() - verticalScrollWidth;
                // Если после установки высоты в контейнере появился скролл, нужно пересчитать ширину элемента
                if (scrollWidthDiff > 0 && width > scrollWidthDiff)
                {
                    width -= scrollWidthDiff;
                    widthStr = String.valueOf(width);
                    setProperty(el, "width", String.valueOf(width));
                    setProperty(el, "height", calculateHeightStr(doc, body));
                }
            }

            if (heightStr.equals(lastHeight) && getWidthStr(width).equals(lastWidth))
            {
                LOG.info("element '" + el.getId() + "' size: " + widthStr + " x " + heightStr + " unchanged");
                return false;
            }

            LOG.info("Set element '" + el.getId() + "' size = " + widthStr + " x " + heightStr);

            if (lastDimensions != null)
            {
                widthStr = calculateWidthStr(el, doc, body, isOperator);
                heightStr = calculateHeightStr(doc, body);
                lastDimensions.put(el.getId(), new Dimension(widthStr, heightStr));
            }
            return true;
        }

        private static String calculateHeightStr(Element doc, Element body)
        {
            int actualHeight = Math.min(body.getScrollHeight(), doc.getScrollHeight());
            return String.valueOf(actualHeight);
        }

        /**
         * Вычисляет ширину контейнера RTF.
         */
        private static int calculateWidth(Element el, Element doc, Element body, boolean isOperator)
        {
            int bodyWidth = isAdvlistColumn(el)
                    ? body.getOffsetWidth()
                    : Math.max(doc.getScrollWidth(), body.getScrollWidth());

            Element parent = getParentElement(el.getParentElement(), isOperator);
            int elParentWidth = getClientWidth(parent);
            if (elParentWidth > bodyWidth)
            {
                bodyWidth = elParentWidth;
            }

            int result = bodyWidth;

            if (isAdvlistColumn(el))
            {
                if (StringUtilities.isEmptyTrim(body.getInnerText()))
                {
                    result = -1;
                }
                else if (bodyWidth <= MIN_ADVLIST_IFRAME_WIDTH)
                {
                    result = MIN_ADVLIST_IFRAME_WIDTH;
                }
                else
                {
                    result = Math.min(bodyWidth, DEFAULT_IFRAME_WIDTH);
                }
            }
            return result;
        }

        private static Element getParentElement(Element parentElement, boolean isOperator)
        {
            if (isOperator && parentElement.getClassName().contains(INLINE_BLOCK))
            {
                parentElement = parentElement.getParentElement();
            }
            return parentElement;
        }

        private static String calculateWidthStr(Element el, Element doc, Element body, boolean isOperator)
        {
            int width = calculateWidth(el, doc, body, isOperator);
            return getWidthStr(width);
        }

        private static String getWidthStr(int width)
        {
            return width > 0 ? String.valueOf(width) : "100%";
        }

        private static String getProperty(Element el, String property)
        {
            return IFrameElement.is(el) ? IFrameElement.as(el).getAttribute(property)
                    : el.getStyle().getProperty(property);
        }

        private void setProperty(Element el, String property, String value)
        {
            if (IFrameElement.is(el))
            {
                IFrameElement.as(el).setAttribute(property, value);
            }
            else
            {
                value = value.indexOf('%') < 0 ? value + "px" : value;
                el.getStyle().setProperty(property, value);
            }
        }
    }

    static class Dimension
    {
        String width;
        String height;

        public Dimension(String width, String height)
        {
            this.width = width;
            this.height = height;
        }
    }

    /**
     * Класс предназначен для проведения подстройки размера контейнера RTF под содержимое
     * Его реализация предусматривает итерационное выполнение подстройки для {@value #RESIZE_COUNT}
     * за итерацию. Это необходимо чтобы обеспечить отзывчивость браузера при ресайзе 100 IFRAME'ов
     * например при выводе их в списках
     */
    class ResizeRTFCommand implements RepeatingCommand
    {
        /**
         * Кол-во IFRAME обрабатываемых за одну итерацию {@link ResizeRTFCommand}
         */
        static final int RESIZE_COUNT = 5;
        private final boolean isOperator;

        public ResizeRTFCommand(boolean isOperator)
        {
            this.isOperator = isOperator;
        }

        private Set<String> ids = new HashSet<>();

        @Override
        public boolean execute()
        {
            if (isVideoFullScreenMode)
            {
                this.ids.clear();
            }

            Iterator<String> it = this.ids.iterator();
            for (int i = 0; i < RESIZE_COUNT && it.hasNext(); ++i)
            {
                try
                {
                    String id = it.next();
                    Element el = Document.get().getElementById(id);
                    if (el != null && !slowResize(id, el, isOperator))
                    {
                        resizeImpl.resize(el, isOperator);
                    }
                    it.remove();
                }
                catch (Throwable t)
                {
                    LOG.info("resize error: " + t.getMessage());
                }
            }
            // Если все ID закончились останавливаем итерационное выполнение команды
            resizeCommand = it.hasNext() ? resizeCommand : null;
            return resizeCommand != null;
        }

        void ensureId(String id)
        {
            this.ids.add(id);
        }

        boolean slowResize(String id, Element el, boolean isOperator)
        {
            boolean firstTime = slowResizeRTFCommand == null;
            /*
             * Если бы isEnabled был статическим, то всё было бы проще, но поскольку реализация метода зависит от
             * браузера,
             * то приходится для проверки создавать новый экземпляр и в случае неуспеха очищать переменную
             * */
            if (firstTime)
            {
                slowResizeRTFCommand = GWT.create(SlowResizeRTFCommand.class);
            }
            if (!slowResizeRTFCommand.isEnabled(el))
            {
                if (firstTime)
                {
                    slowResizeRTFCommand = null;
                }
                return false;
            }
            slowResizeRTFCommand.setOperator(isOperator);
            slowResizeRTFCommand.ensureId(id);
            if (firstTime)
            {
                Scheduler.get().scheduleFixedDelay(slowResizeRTFCommand, SLOW_RESIZE_RTF_INTERVAL);
            }
            return true;
        }
    }

    /**
     * Класс предназначен для выполнения отложенной подстройки размеров контейнера RTF.
     * Для адвлистов нужна отложенная подстройка размеров
     */
    static class SlowResizeRTFCommand implements RepeatingCommand
    {
        /**
         * Кол-во IFRAME обрабатываемых за одну итерацию
         */
        static final int RESIZE_COUNT = 50;

        private Map<String, Long> ids = new HashMap<>();
        private Map<String, Dimension> oldDimensions = new HashMap<>();
        private boolean isOperator;

        @Override
        public boolean execute()
        {
            Iterator<Entry<String, Long>> it = this.ids.entrySet().iterator();
            for (int i = 0; i < RESIZE_COUNT && it.hasNext(); ++i)
            {
                try
                {
                    Entry<String, Long> e = it.next();
                    Element el = Document.get().getElementById(e.getKey());
                    if (el != null)
                    {
                        long diff = System.currentTimeMillis() - e.getValue();
                        if (diff < SLOW_RESIZE_RTF_INTERVAL)
                        {
                            continue;
                        }
                        if (resizeImpl.resize(el, oldDimensions, isOperator))
                        {
                            e.setValue(System.currentTimeMillis());
                            continue;
                        }
                    }
                    it.remove();
                    oldDimensions.remove(e.getKey());
                }
                catch (Throwable t)
                {
                    LOG.info("resize error: " + t.getMessage());
                }
            }
            if (this.ids.isEmpty())
            {
                slowResizeRTFCommand = null;
            }
            return !this.ids.isEmpty();
        }

        public void setOperator(boolean isOperator)
        {
            this.isOperator = isOperator;
        }

        void ensureId(String id)
        {
            this.ids.put(id, System.currentTimeMillis());
        }

        boolean isEnabled(Element el)
        {
            return isAdvlistColumn(el);
        }
    }

    /**
     * Команда записи содержимого IFRAME
     */
    class WriteIFrameCommand implements ScheduledCommand
    {
        private Map<String, String> content = new HashMap<>();
        private int x, y;

        @Override
        public void execute()
        {
            //В хроме и ИЕ при закрытии документа в iframe document.close после записи в него контента,
            // происходит скролл основной страницы. Чтобы это обойти - запоминаем начальное положение, потом скролим
            // обратно.
            // http://sd-jira.naumen.ru/browse/NSDPRD-5255
            x = RootPanel.getScrollLeft();
            y = RootPanel.getScrollTop();

            for (Entry<String, String> e : this.content.entrySet())
            {
                try
                {
                    Element el = Document.get().getElementById(e.getKey());
                    if (IFrameElement.is(el))
                    {
                        if (isAdvlistColumn(el))
                        {
                            int parentClientWidth = getClientWidth(el.getParentElement());
                            IFrameElement.as(el).setAttribute("width",
                                    String.valueOf(parentClientWidth > DEFAULT_IFRAME_WIDTH ? DEFAULT_IFRAME_WIDTH
                                            : parentClientWidth));
                        }
                        Document doc = getContentDocument(IFrameElement.as(el));
                        if (doc != null)
                        {
                            writeHtml(doc, preprocess(e.getValue()));
                        }
                    }
                }
                catch (Throwable t)
                {
                    LOG.info("write html error: " + t.getMessage());
                }
            }
            writeCommand = null;
            // если в процессе записи страница проскролилась - возвращаем ее обратно.
            Scheduler.get().scheduleDeferred(new ScheduledCommand()
            {
                @Override
                public void execute()
                {
                    RootPanel.scrollTo(x, y);
                }
            });
        }

        void ensure(String id, String value)
        {
            this.content.put(id, value);
        }
    }

    private static final int MIN_ADVLIST_IFRAME_WIDTH = 150;

    private static SlowResizeRTFCommand slowResizeRTFCommand;

    static ResizeRTFImpl resizeImpl = GWT.create(ResizeRTFImpl.class);

    /**
     * Время в мс, между выполнением команды постройки размера IFRAME'ов
     * необходимо, чтобы браузер мог реагировать на события
     */
    static final int BROWSER_TIME_MS = 10;

    /**
     * Период времени для медленной подстройки размеров контейнеров RTF
     */
    static final int SLOW_RESIZE_RTF_INTERVAL = 1000;

    /**
     * Период для очистки id-шников
     */
    static final int CLEAR_ID_INTERVAL = 5 * 60 * 1000;

    static Logger LOG = Logger.getLogger(RichTextView.class.getName());

    public static final int DEFAULT_IFRAME_WIDTH = 300;

    private static final int MAX_SET_HANDLER_RETRY_COUNT = 5;

    private static final String INLINE_BLOCK = "inline-block";

    private static int getClientWidth(Element parent)
    {
        //Получаем ширину в которую должен вписаться iframe. Отнимаем один пиксель для того
        //чтобы подстраховаться от ошибок при округлении размеров элементов в случае если в
        //браузере установлен масштаб менее 100%
        return parent.getClientWidth() - 1;
    }

    /**
     * Признак того, что в iframe находится значение атрибута, отображаемого в колонке списка
     */
    private static boolean isAdvlistColumn(Element element)
    {
        return element.hasAttribute("__isadvl");
    }

    private Map<String, String> contents = new HashMap<>();

    private HandlerRegistration resizeMainWindowHR = null;

    private ResizeRTFCommand resizeCommand = null;

    private WriteIFrameCommand writeCommand = null;

    private boolean isVideoFullScreenMode = false;
    private Map<String, Integer> initialResizeQueue = new HashMap<>();
    private Map<String, Runnable> initialActionQueue = new HashMap<>();

    @Inject
    private AsyncProvider<FilePreviewPresenterFactory<RtfImageInitContext>> previewPresenterFactoryProvider;
    @Inject
    private WindowScrollController scrollController;
    @Inject
    private HtmlFactoryUtils htmlFactoryUtils;
    @Inject
    private ModuleHolder moduleHolder;

    private Timer cleanTimer = new Timer()
    {
        @Override
        public void run()
        {
            onWindowResize();
        }
    };

    public void defferedHandlersSetting(final String id)
    {
        Scheduler.get().scheduleFixedDelay(new RepeatingCommand()
        {
            private int repeated = 0;

            @Override
            public boolean execute()
            {
                Element elem = Document.get().getElementById(id);
                if (elem == null)
                {
                    if (repeated < MAX_SET_HANDLER_RETRY_COUNT)
                    {
                        ++repeated;
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                LOG.info("Adding show original handler...");
                addShowOriginalOnClickHandler(elem);
                LOG.info("Handler is ready");
                return false;
            }
        }, BROWSER_TIME_MS);
    }

    public void defferedIFrameResize(String id)
    {
        if (resizeCommand == null)
        {
            resizeCommand = new ResizeRTFCommand(moduleHolder.getModule().isOperator());
            Scheduler.get().scheduleFixedDelay(resizeCommand, BROWSER_TIME_MS);
        }
        resizeCommand.ensureId(id);
    }

    public void enshureInstalled()
    {
        if (!isInstalled())
        {
            initWindowHandler();
            cleanTimer.scheduleRepeating(CLEAR_ID_INTERVAL);
        }
        if (resizeMainWindowHR == null)
        {
            resizeMainWindowHR = Window.addResizeHandler(new ResizeHandler()
            {
                @Override
                public void onResize(ResizeEvent event)
                {
                    LOG.info("Window onResize");
                    onWindowResize();
                }
            });

            initFullScreenChangeHanders();
        }
    }

    public void onImageClicked(String url, Element doc)
    {
        String currentFileUuid = extractUuid(url);
        if (currentFileUuid == null)
        {
            return;
        }

        List<String> fileUuids = new ArrayList<>();

        NodeList<Element> imgs = doc.getElementsByTagName("img");
        for (int i = 0; i < imgs.getLength(); ++i)
        {
            Element img = imgs.getItem(i);
            String uuid = extractUuid(img.getAttribute("src"));
            if (uuid != null)
            {
                fileUuids.add(uuid);
            }
        }
        final RtfImageInitContext context = new RtfImageInitContext(fileUuids, currentFileUuid);
        previewPresenterFactoryProvider.get(new BasicCallback<FilePreviewPresenterFactory<RtfImageInitContext>>()
        {

            @Override
            protected void handleSuccess(FilePreviewPresenterFactory<RtfImageInitContext> presenterFactory)
            {
                presenterFactory.create(context).bind();
            }
        });
    }

    public static String preprocess(String input)
    {
        DivElement element = Document.get().createDivElement();
        element.setInnerHTML(input); // NOPMD NSDPRD-28509 unsafe html
        NodeList<Element> lst = element.getElementsByTagName(Tags.SCRIPT);
        for (int i = 0; i < lst.getLength(); ++i)
        {
            lst.getItem(i).removeFromParent();
        }
        lst = element.getElementsByTagName(Tags.A);
        for (int i = 0; i < lst.getLength(); ++i)
        {
            AnchorElement anchor = AnchorElement.as(lst.getItem(i));
            if (!Constants.Target.BLANK.equals(anchor.getTarget()))
            {
                setTargetForElement(lst, anchor);
            }
        }
        lst = element.getElementsByTagName(Tags.AREA);
        for (int i = 0; i < lst.getLength(); ++i)
        {
            setTargetForElement(lst, AreaElement.as(lst.getItem(i)));
        }
        return JSUtils.isFirefox()
                ? HtmlUtils.wrapHtmlBodyForFireFox(element.getInnerHTML())
                : HtmlUtils.wrapHtmlBody(element.getInnerHTML());
    }

    /**
     * Устанавливает значение атрибута target="_top" при соблюдении условий
     * @param elements список, аналогичных по тегу, элементов
     * @param element элемент для проверки и для последующей установки атрибута
     */
    private static void setTargetForElement(NodeList<Element> elements, Element element)
    {
        String hash = element.getAttribute(Constants.Href.HREF);
        if (hash.isEmpty() || !hash.startsWith(Constants.Href.HASH) || isLinkMatchHref(elements, element))
        {
            if (element instanceof AnchorElement)
            {
                ((AnchorElement)element).setTarget(Target.TOP);
            }
            else if (element instanceof AreaElement)
            {
                ((AreaElement)element).setTarget(Target.TOP);
            }
        }
    }

    private static boolean isLinkMatchHref(NodeList<Element> elements, Element element)
    {
        String href = element.getAttribute(Constants.Href.HREF).replaceFirst(Constants.Href.HASH, "");
        for (int i = 0; i < elements.getLength(); ++i)
        {
            Element el = elements.getItem(i);
            if (href.equals(el.getAttribute(Constants.Href.NAME)) || href.equals(el.getId()))
            {
                return false;
            }
        }
        return true;
    }

    public void disposeRichText(Element parent)
    {
        NodeList<Element> iframes = parent.getElementsByTagName(IFrameElement.TAG);
        for (int i = 0; i < iframes.getLength(); ++i)
        {
            IFrameElement iframe = IFrameElement.as(iframes.getItem(i));
            Document innerDocument = getContentDocument(iframe);
            if (null != innerDocument)
            {
                innerDocument.removeAllChildren();
            }
            iframe.setSrc("about:blank");
            contents.remove(iframe.getId());
        }
    }

    @Override
    public void onShowNavTree(ShowNavTreeEvent event)
    {
        onChangeContainerDimensions(null);
    }

    public void scheduleInitialAction(String id, Runnable action)
    {
        initialActionQueue.put(id, action);
    }

    public String setContent(String id, String content)
    {
        return contents.put(id, content);
    }

    public SafeHtml toPlainHtml(@Nullable String value)
    {
        if (StringUtilities.isEmpty(value))
        {
            return SafeHtmlUtils.EMPTY_SAFE_HTML;
        }
        return htmlFactoryUtils.normalizeHtml(new SafeHtmlBuilder()
                .appendHtmlConstant("<div class=\"no-whitespace-collapse\">")
                .appendEscaped(htmlAsString(value))
                .appendHtmlConstant("</div>")
                .toSafeHtml());
    }

    public static String htmlAsString(String value)
    {
        DivElement container = Document.get().createDivElement();
        container.getStyle().setPosition(Position.ABSOLUTE);
        container.getStyle().setTop(-10, Unit.PX);
        container.getStyle().setPropertyPx("maxHeight", 1);
        container.getStyle().setOverflow(Overflow.HIDDEN);
        container.setInnerHTML(value); // NOPMD NSDPRD-28509 unsafe html
        DocumentUtils.forEachElement(container, StyleElement.TAG, Element::removeFromParent);
        DocumentUtils.forEachElement(container, ScriptElement.TAG, Element::removeFromParent);
        Document.get().getBody().appendChild(container);
        String text = DocumentUtils.extractInnerText(container).trim();
        container.removeFromParent();
        container.setInnerHTML(StringUtilities.EMPTY); // NOPMD safe html
        return text;
    }

    public void updateIFrameWidth(String id, int width)
    {
        if (initialResizeQueue.isEmpty())
        {
            Scheduler.get().scheduleFixedDelay(() ->
            {
                try
                {
                    initialResizeQueue.forEach(this::setInitialIFrameWidth);
                }
                finally
                {
                    initialResizeQueue.clear();
                }
                return false;
            }, SLOW_RESIZE_RTF_INTERVAL);
        }

        initialResizeQueue.put(id, width);
    }

    public native void writeHtml(Document doc, String html)
    /*-{
        doc.open('text/html', 'replace');
        if (!$wnd.isIgnoreOwaspInSafeHtml)
        {
            var metaTag = @ru.naumen.core.shared.utils.OwaspUtils::POLICY;
            if (@ru.naumen.core.client.JSUtils::isFirefox()())
            {
                var baseurl = $wnd.location.origin;
                metaTag = metaTag.replace(/'self'/g, baseurl);
            }
            var fullHtml = '<html><head><meta http-equiv="Content-Security-Policy" content="' + metaTag +
            '"/></head><body>' + html + '</body></html>'
            doc.write(fullHtml);
        }
        else
        {
            if (@ru.naumen.core.client.JSUtils::isFirefox()())
            {
                doc.write(@ru.naumen.core.shared.utils.HtmlUtils::prepareHtmlForFireFox(*)(html));
            }
            else
            {
                doc.write(html);
            }
        }

        doc.close();
        <EMAIL>::setBodyStyle(*)(doc);
    }-*/;

    private void defferedIFrameWrite(String id, String value)
    {
        if (writeCommand == null)
        {
            writeCommand = new WriteIFrameCommand();
            Scheduler.get().scheduleDeferred(writeCommand);
        }
        writeCommand.ensure(id, value);
    }

    private native void initFullScreenChangeHanders()
    /*-{
        var that = this;

        var handler = $entry(function(e) {
           <EMAIL>::toggleVideoFullscreen(*)();
        });

        if($wnd.document.addEventListener)
        {
          $wnd.document.addEventListener('webkitfullscreenchange', handler, false);
          $wnd.document.addEventListener('mozfullscreenchange', handler, false);
          $wnd.document.addEventListener('fullscreenchange', handler, false);
          $wnd.document.addEventListener('MSFullscreenChange', handler, false);
        }

    }-*/;

    /**
     * Метод вызывается при обработке события onload IFRAME
     * @param object
     */
    protected void onLoadRtf(JavaScriptObject object)
    {
        if (!Element.is(object))
        {
            return;
        }
        Element el = Element.as(object);
        String id = el.getId();
        if (IFrameElement.is(object))
        {
            IFrameElement ifrel = IFrameElement.as(el);
            LOG.info("onload iframe '" + id + '\'');
            String value = contents.put(id, null);
            if (null != value)
            {
                defferedIFrameWrite(id, value);
            }
            else
            {
                defferedIFrameResize(id);
            }
            adjustIFrameStyles(ifrel);
            Runnable action = initialActionQueue.remove(id);
            if (null != action)
            {
                Scheduler.get().scheduleDeferred(action::run);
            }
            Document doc = getContentDocument(ifrel);
            if (doc != null)
            {
                LOG.info("Adding show original handler...");
                addShowOriginalOnClickHandler(doc.getBody());
                LOG.info("Handler is ready");

                addClickPreventer(doc.getBody(), Document.get());
            }
        }
        else
        {
            LOG.info("onload element '" + id + '\'');
            defferedIFrameResize(id);
        }
    }

    /**
     * Метод реализует обработку события изменения окна браузера
     */
    private void onWindowResize()
    {
        refreshIframes(contents.keySet());
    }

    /**
     * Пересчет iframe'ов, указанных в списке
     */
    private void refreshIframes(Set<String> ids)
    {
        for (Iterator<String> it = ids.iterator(); it.hasNext(); )
        {
            String id = it.next();
            Element el = Document.get().getElementById(id);
            if (el != null)
            {
                defferedIFrameResize(id);
            }
            else
            {
                it.remove();
            }
        }
    }

    protected void toggleVideoFullscreen()
    {
        if (isVideoFullScreenMode)
        {
            Scheduler.get().scheduleFixedDelay(new RepeatingCommand()
            {
                @Override
                public boolean execute()
                {
                    isVideoFullScreenMode = !isVideoFullScreenMode;
                    return false;
                }
            }, 50);
        }
        else
        {
            isVideoFullScreenMode = !isVideoFullScreenMode;
        }
    }

    @Inject
    void initExpandCollapseHandler(EventBus eventBus)
    {
        eventBus.addHandler(ExpandCollapseEvent.TYPE, this);
        eventBus.addHandler(ShowNavTreeEvent.getType(), this);
    }

    /**
     * Устанавливает handler, открывающий оригинальное изображение
     *
     * @param wnd
     */
    private native void addShowOriginalOnClickHandler(Element wnd)
    /*-{
    var that = this;
        var handler = function(event) {
            var element = event.target;
            var url = element.getAttribute('src');

            <EMAIL>::onImageClicked(*)(url, wnd);
        };
        if (wnd.addEventListener) {
            wnd.addEventListener('click', handler, true);
        } else {
            wnd.attachEvent('onclick', handler);
        }
    }-*/;

    private native void addClickPreventer(Element iframeBody, Document superDocument)
    /*-{
         var superBody = superDocument.body;
         var handler = function(event) {
            if (superBody.classList.contains("actionsDisabled")) {
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();
            }
        };
        if (iframeBody.addEventListener) {
           iframeBody.addEventListener('click', handler, true);
        } else {
           iframeBody.attachEvent('onclick', handler);
        }
    }-*/;

    private void adjustIFrameStyles(IFrameElement el)
    {
        Document doc = getContentDocument(el);
        if (doc != null)
        {
            // TODO установка шрифта (будет сделана далее)
            setBodyStyle(doc);
        }
    }

    private String extractUuid(String url)
    {
        if (url == null)
        {
            return null;
        }
        int index = url.lastIndexOf('=');
        if (index == -1 && index + 1 > url.length())
        {
            return null;
        }
        String uuid = url.substring(index + 1);
        if (!uuid.startsWith(File.CLASS_ID + "$"))
        {
            return null;
        }
        return uuid;
    }

    private native void initWindowHandler()
    /*-{
        var that = this;
        $wnd.onloadRtf = $entry(function(param) {
            <EMAIL>::onLoadRtf(*)(param);
        });
    }-*/;

    private native boolean isInstalled()
    /*-{
        if ($wnd.onloadRtf)
            return true;
        return false;
    }-*/;

    private native void setBodyStyle(Document doc)
    /*-{
        doc.body.style.backgroundColor = "transparent";
        doc.body.style.margin = "0";
        doc.body.style.display = "inline-block";
        doc.body.style.verticalAlign = "top";
        doc.body.style.maxWidth = '100%';
        doc.body.style.wordWrap = 'break-word';
        doc.body.style.fontSize = @ru.naumen.core.client.JSUtils::getUserFontSize(*)();

        var constants = @ru.naumen.core.client.widgets.WidgetResourcesInitializer::getConstants(*)();
        doc.body.style.color = <EMAIL>::textColor(*)();

        doc.body.style.fontFamily = "Roboto, Arial, sans-serif";
    }-*/;

    private void setInitialIFrameWidth(String id, int width)
    {
        Element element = Document.get().getElementById(id);
        if (IFrameElement.is(element))
        {
            int scrollLeft = scrollController.getScrollLeft();
            int scrollTop = scrollController.getScrollTop();
            element.setAttribute("width", Integer.toString(width));
            resizeImpl.resize(element, moduleHolder.getModule().isOperator());
            element.setAttribute("height", "0");
            resizeImpl.resize(element, moduleHolder.getModule().isOperator());
            scrollController.scrollTo(scrollLeft, scrollTop);
        }
    }

    @Override
    public void onExpandCollapse(ExpandCollapseEvent event)
    {
        onChangeContainerDimensions(event.getContent());
    }

    /**
     * т.к. в свернутом контенте iframe и его body имеют нулевые высоту и ширину,
     * то при разворачивании контента их нужно пересчитать
     */
    private void onChangeContainerDimensions(@Nullable IsWidget container)
    {
        Element containerElement = container == null
                ? ClientUtils.getElementByDebugId(MainContentDisplayImpl.MAIN_CONTENT_CONTAINER_ID)
                : container.asWidget().getElement();

        if (containerElement == null)
        {
            return;
        }

        Set<Element> elements = cast(getInnerIframes(containerElement));
        if (elements.size() > 1 && !isAdvlistColumn(elements.iterator().next()))
        {
            // Если элементов больше одного(в контенте несколько RTF) - необходимо сбросить их ширины,
            // иначе ширина значений не уменьшится при сворачивании контента
            elements.forEach(el -> el.setAttribute("width", "auto"));
        }
        Set<String> ids = elements.stream().map(Element::getId).collect(Collectors.toSet());
        refreshIframes(ids);
    }

    /**
     * Каст jsArray к javaSet, чтоб работал iterator()
     */
    private static Set<Element> cast(JsArray<Element> jsArray)
    {
        Set<Element> set = new HashSet<>();
        for (int i = 0; i < jsArray.length(); i++)
        {
            set.add(jsArray.get(i));
        }
        return set;
    }

    /**
     * Получение внутренних iframe
     * @param container элемент, внутри которого искать
     */
    private native JsArray<Element> getInnerIframes(Element container)
    /*-{
        return Array.prototype.slice.call(container.querySelectorAll('iframe'));
    }-*/;
}
