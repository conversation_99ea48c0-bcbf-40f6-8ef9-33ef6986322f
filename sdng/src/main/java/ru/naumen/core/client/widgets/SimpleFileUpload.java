package ru.naumen.core.client.widgets;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.JavaScriptException;
import com.google.gwt.core.client.JsArrayString;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.uibinder.client.UiConstructor;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FileUpload;
import com.google.gwt.user.client.ui.FocusPanel;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.FormPanel;
import com.google.gwt.user.client.ui.FormPanel.SubmitCompleteEvent;
import com.google.gwt.user.client.ui.FormPanel.SubmitCompleteHandler;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Hidden;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Panel;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.xhr.client.XMLHttpRequest;

import jakarta.annotation.Nullable;
import ru.naumen.commons.shared.utils.FileUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.impl.DialogsImpl;
import ru.naumen.core.client.events.UploadCompleteEvent;
import ru.naumen.core.client.events.UploadCompleteHandler;
import ru.naumen.core.client.forms.HasTabOrder;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.utils.CsrfTokenUtils;
import ru.naumen.core.client.utils.upload.FileUploadUtils;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.web.servlets.UploadServlet;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.HasReadyState;
import ru.naumen.core.shared.UploadConstants;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.ReadyState;

/**
 * Widget позволяет загружать файлы на сервер через {@link UploadServlet}. Далее на стороне сервера можно
 * получить загруженный файл через {@link UploadService}
 *
 * <AUTHOR>
 *
 */
public class SimpleFileUpload extends Composite
        implements HasValueOrThrow<Collection<DtObject>>, HasReadyState, HasTabOrder, HasMassEditState
{
    /**
     * Функция, выполняющаяся при удалении выбранных файлов (При нажатии на кнопку (X) )
     */
    public interface OnRemoveFilesFunction
    {
        void onRemoveFiles();
    }

    private static CommonMessages messages;
    private static Dialogs dialogs;

    private Panel container;
    private final Panel uploadedCnt;
    private final Map<String, DtObject> uploadedUuids;
    private final ReadyState readyState;
    private boolean multiple;
    private FileUpload file;
    private FormPanel uploadForm;
    private Set<String> validFileExtensions = new HashSet<>();
    private OnRemoveFilesFunction onRemoveFilesFunction;
    private FocusPanel focusPanel;
    private MassEditState massEditState = MassEditState.SINGLE_VALUE;

    private FileUploadUtils utils;

    private int baseTabIndex = 1;

    private Map<String, Button> clearButtons;
    Map<String, String> errorFiles;
    List<String> fileNames;

    public SimpleFileUpload()
    {
        this(true);
    }

    @UiConstructor
    public SimpleFileUpload(boolean multiple)
    {
        if (messages == null)
        {
            messages = GWT.create(CommonMessages.class);
        }

        this.multiple = multiple;
        readyState = new ReadyState(this);
        uploadedUuids = new HashMap<>();
        clearButtons = new HashMap<>();
        uploadedCnt = new VerticalPanel();
        container = new VerticalPanel();
        container.add(uploadedCnt);

        container.add(uploadedCnt);
        initWidget(container);
        DebugIdBuilder.ensureDebugId(this, "SimpleFileUpload");
        WidgetResources.INSTANCE.form().ensureInjected();

        initUpload();
    }

    public void addOnRemoveFilesFunction(OnRemoveFilesFunction onRemoveFilesFunction)
    {
        this.onRemoveFilesFunction = onRemoveFilesFunction;
    }

    public HandlerRegistration addUploadCompleteHandler(UploadCompleteHandler handler)
    {
        return this.addHandler(handler, UploadCompleteEvent.getType());
    }

    @Override
    public HandlerRegistration addValueChangeHandler(ValueChangeHandler<Collection<DtObject>> handler)
    {
        return addHandler(handler, ValueChangeEvent.getType());
    }

    @Override
    public int getBaseTabIndex()
    {
        return baseTabIndex;
    }

    public String getFilename()
    {
        return file.getFilename();
    }

    @Override
    public Focusable getFirstFocusElement()
    {
        if (focusPanel.isAttached())
        {
            return focusPanel;
        }
        return TabOrderHelper.findFirstFocusElement(getWidget());
    }

    @Override
    public MassEditState getMassEditState()
    {
        return massEditState;
    }

    @Override
    public int getNextBaseTabIndex()
    {
        return multiple ? baseTabIndex + 20 : baseTabIndex + 2;
    }

    @Override
    public Collection<DtObject> getValue()
    {
        return ParseExceptionsUtils.getValueSafe(this);
    }

    @Override
    public Collection<DtObject> getValueOrThrow() throws ParseException
    {
        if (uploadedUuids.isEmpty())
        {
            return null;
        }
        return Lists.newArrayList(uploadedUuids.values());
    }

    @Override
    public boolean isEnabled()
    {
        return file.isEnabled();
    }

    public boolean isMultiple()
    {
        return multiple;
    }

    @Override
    public void ready(IReadyCallback callback)
    {
        readyState.ready(callback);
    }

    @Override
    public SynchronizationCallbackRegistration registerSynchronization(SynchronizationCallback callback)
    {
        return readyState.registerSynchronization(callback);
    }

    public void removeFile(String uuid)
    {
        Button clrButton = clearButtons.get(uuid);
        if (clrButton != null)
        {
            clrButton.click();
        }
    }

    @Override
    public void resetTabOrder()
    {
        TabOrderHelper.resetTabIndex(focusPanel);
        TabOrderHelper.resetTabIndex(uploadedCnt);
    }

    @Override
    public void setBaseTabIndex(int baseTabIndex)
    {
        this.baseTabIndex = baseTabIndex;
    }

    @Override
    public void setEnabled(boolean enabled)
    {
        file.setEnabled(enabled);
    }

    public SimpleFileUpload setFileUploadUtils(FileUploadUtils utils)
    {
        this.utils = utils;
        return this;
    }

    @Override
    public void setMassEditState(MassEditState state)
    {
        massEditState = state;
        if (MassEditState.DIFFERENT_VALUES == massEditState)
        {
            setTitle(messages.selectedDifferentValues());
        }
        else if (MassEditState.NOT_SET == massEditState)
        {
            setTitle(messages.valueNotSet());
        }
        else
        {
            setTitle(StringUtilities.EMPTY);
        }
        if (MassEditState.SINGLE_VALUE != massEditState)
        {
            addStyleName(WidgetResources.INSTANCE.form().disabledLabel());
        }
        else
        {
            removeStyleName(WidgetResources.INSTANCE.form().disabledLabel());
        }
    }

    public void setMimeTypes(String... mimeTypes)
    {
        file.getElement().setAttribute("accept", Joiner.on(",").join(mimeTypes));
    }

    public void setMultiple(boolean multiple)
    {
        this.multiple = multiple;
    }

    public void setValidFileExtension(Set<String> validFileExtensions)
    {
        this.validFileExtensions = validFileExtensions;
        if (dialogs == null)
        {
            dialogs = GWT.create(DialogsImpl.class);
        }
    }

    @Override
    public void setValue(Collection<DtObject> value)
    {
        clearValues();
        if (null != value)
        {
            for (DtObject v : value)
            {
                addValue(v);
            }
        }
    }

    @Override
    public void setValue(Collection<DtObject> value, boolean fireEvents)
    {
        setValue(value);
        if (fireEvents)
        {
            ValueChangeEvent.fire(this, getValue());
        }
    }

    @Override
    public void updateTabOrder()
    {
        int nextIndex = baseTabIndex;
        nextIndex = TabOrderHelper.setTabIndex((Focusable)focusPanel, nextIndex);
        TabOrderHelper.setTabIndex(uploadedCnt, nextIndex);
    }

    protected void addUploaded(DtObject dto)
    {
        uploadedUuids.put(dto.getUUID(), dto);
        final String fileUuid = dto.getUUID();
        String fileName = dto.getTitle();

        final FormPanel form = new FormPanel();
        form.setAction("upload");
        form.setMethod(FormPanel.METHOD_GET);

        Panel iPanel = new HorizontalPanel();
        form.add(iPanel);

        Hidden uuid = new Hidden();
        uuid.setName(UploadConstants.UPLOADED_UUID);
        uuid.setValue(fileUuid);
        iPanel.add(uuid);

        final Button clrBtn = new Button("x");
        clearButtons.put(fileUuid, clrBtn);

        DebugIdBuilder.ensureDebugId(clrBtn, "clearButton." + fileUuid);
        clrBtn.addClickHandler(new ClickHandler()
        {
            @Override
            public void onClick(ClickEvent event)
            {
                clrBtn.setEnabled(false);
                readyState.notReady();
                form.submit();
                if (onRemoveFilesFunction != null)
                {
                    onRemoveFilesFunction.onRemoveFiles();
                }
            }
        });

        form.addSubmitCompleteHandler(new SubmitCompleteHandler()
        {
            @Override
            public void onSubmitComplete(SubmitCompleteEvent event)
            {
                form.removeFromParent();
                uploadedUuids.remove(fileUuid);
                readyState.ready();
                if (!multiple)
                {
                    initUpload();
                }
                ValueChangeEvent.fire(SimpleFileUpload.this, getValue());
            }
        });

        HTML fileNameHtml = new HTML(fileName); // NOPMD NSDPRD-28509 unsafe html
        fileNameHtml.addStyleName(WidgetResources.INSTANCE.form().fileNameField());
        iPanel.add(fileNameHtml);
        iPanel.add(clrBtn);
        uploadedCnt.add(form);
        resetTabOrder();
        updateTabOrder();
    }

    protected void addValue(Object v)
    {
        if (v instanceof DtObject)
        {
            DtObject obj = (DtObject)v;
            addUploaded(obj);

            if (!multiple && null != uploadForm)
            {
                uploadForm.removeFromParent();
                uploadForm = null;
            }
        }
    }

    protected void clearValues()
    {
        uploadedCnt.clear();
        uploadedUuids.clear();
    }

    protected List<String> getFileNames(FileUpload file)
    {
        JsArrayString jsArray = getFileNames(file.getElement());
        List<String> result = new ArrayList<>();
        for (int i = 0; i < jsArray.length(); ++i)
        {
            result.add(jsArray.get(i));
        }
        return result;
    }

    protected void initUpload()
    {
        file = new FileUpload();
        file.setName(UploadConstants.FILE);
        if (multiple)
        {
            file.getElement().setPropertyString("multiple", "multiple");
        }

        final FormPanel form = createForm();
        file.addChangeHandler(event ->
        {
            errorFiles = new HashMap<>();
            fileNames = new ArrayList<>();
            checkFileExtension();
            checkEmptyFiles();
            fillFileNamesList();

            form.setVisible(false);
            readyState.notReady();
            sendFiles(form);
        });

        container.add(form);

        if (null != uploadForm)
        {
            uploadForm.removeFromParent();
            uploadForm = null;
        }
        uploadForm = form;
        resetTabOrder();
        updateTabOrder();
    }

    protected void onFilesSubmitted(String response)
    {
        Map<String, String> responseResult = utils.parseResultWithNames(response,
                UploadConstants.FILE_NAME_RESULT_DELIMITER);
        if (responseResult.isEmpty())
        {
            onFileUploadFailure(response);
            return;
        }
        for (String fName : fileNames)
        {
            String fileUploadResult = responseResult.get(fName);

            if (fileUploadResult.startsWith(UploadConstants.ERROR_MESSAGE_PREFIX))
            {
                onFileSubmitted(null, fName, fileUploadResult.substring(UploadConstants.ERROR_MESSAGE_PREFIX
                        .length()));
            }
            else
            {
                onFileSubmitted(fileUploadResult, fName, null);
            }
        }
    }

    protected void onFileSubmitted(@Nullable final String fileUuid, @Nullable String fileName,
            @Nullable String errorMessage)
    {
        if (null == errorMessage && null != fileUuid && checkFileExtension(FileUtils.getFileExtension(fileName)))
        {
            addUploaded(fileUuid, fileName);
        }
        else
        {
            initUpload();
        }

        this.fireEvent(new UploadCompleteEvent(fileUuid, fileName, errorMessage));
        ValueChangeEvent.fire(this, getValue());
        readyState.ready();

        if (multiple)
        {
            initUpload();
        }
    }

    protected void onFileUploadFailure(@Nullable String response)
    {
        String possibleErrors = utils.extractPossibleErrorsFromResponse(response);
        if (StringUtilities.isEmpty(possibleErrors))
        {
            possibleErrors = response;
        }
        else
        {
            possibleErrors = Constants.File.SD_FILE_UPLOAD_ERROR_PREFIX + possibleErrors;
        }
        utils.prepareErrorMessage(possibleErrors, new BasicCallback<SimpleResult<String>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<String> value)
            {
                SimpleFileUpload.this.onFileSubmitted(null, null, value.get());
            }
        });
    }

    private void addUploaded(String fileUuid, String fileName)
    {
        addUploaded(new SimpleDtObject(fileUuid, fileName, Constants.File.FQN));
    }

    /**
     * Проверить наличие загружаемых пустых файлов
     * при их наличии возвращает текст ошибки, иначе пустую строку
     */
    private void checkEmptyFiles()
    {
        List<String> emptyFiles = getEmptyFileNames(file);
        for (String fileName : emptyFiles)
        {
            errorFiles.put(fileName, messages.emptyFile());
        }
    }

    private void checkFileExtension()
    {
        List<String> fileNames = getFileNames(file);
        for (String fileName : fileNames)
        {
            if (!checkFileExtension(FileUtils.getFileExtension(fileName)))
            {
                errorFiles.put(fileName, messages.fileUploadError(
                        messages.incorrectFileExtensions(StringUtilities.join(validFileExtensions, ", "))));

            }
        }
    }

    private boolean checkFileExtension(String extension)
    {
        return validFileExtensions.isEmpty()
               || file.getFilename().contains(".") && validFileExtensions.contains(extension.toLowerCase());
    }

    /**
     * Возвращает форму для отправки файлов 
     */
    private FormPanel createForm()
    {
        final FormPanel form = new FormPanel();
        form.setAction(getUploadAddress());
        form.setEncoding(FormPanel.ENCODING_MULTIPART);
        form.setMethod(FormPanel.METHOD_POST);
        SubmitCompleteHandler submitHandler = new SubmitCompleteHandler()
        {
            @Override
            public void onSubmitComplete(SubmitCompleteEvent event)
            {
                submitComplete(form, event.getResults());
            }
        };
        form.addSubmitCompleteHandler(submitHandler);
        initFocusPanel();
        form.setWidget(focusPanel);
        return form;
    }

    private void fillFileNamesList()
    {
        for (String fileName : getFileNames(file))
        {
            if (errorFiles.containsKey(fileName))
            {
                continue;
            }

            fileNames.add(fileName);
        }
    }

    /**
     * Метод извлекает названия пустых файлов из input'а на странице
     */
    private native JsArrayString getEmptyFileNames(final Element fileInput)
    /*-{
        var names = [];
        var files = fileInput.files;
        if (!files){
            return names;
        }
        for (var i = 0; i < files.length; i++){
            if(files[i].size == 0){
                names.push(files[i].name);
            }
        }
        return names;
    }-*/;

    /**
     * Метод возвращает список названий пустых файлов из input'а на странице
     */
    private List<String> getEmptyFileNames(FileUpload file)
    {
        JsArrayString jsArray = getEmptyFileNames(file.getElement());
        List<String> result = new ArrayList<>();
        for (int i = 0; i < jsArray.length(); ++i)
        {
            result.add(jsArray.get(i));
        }
        return result;
    }

    /**
     * Метод извлекает названия загруженных файлов из input'а на странице
     *
     * @param fileInput
     * @return
     */
    private native JsArrayString getFileNames(Element fileInput)
    /*-{
        var names = [];
        var files = fileInput.files;
        if (!files) {
            files = [];
        }
    
        for (var i = 0; i < files.length; i++){
            names.push(files[i].name);
        }
    
        return names;
    }-*/;

    /**
     * Возвращает адрес для загрузки файлов
     */
    private String getUploadAddress()
    {
        String csrfToken = CsrfTokenUtils.getCSRFToken();
        String address = "upload";
        if (!StringUtilities.isEmptyTrim(csrfToken))
        {
            address += "?_csrf=" + csrfToken;
        }
        return address;
    }

    private void initFocusPanel()
    {
        focusPanel = new FocusPanel(file);
        focusPanel.addFocusHandler(event -> file.addStyleName(WidgetResources.INSTANCE.form()
                .simpleFocused()));
        focusPanel.addBlurHandler(event -> file.removeStyleName(WidgetResources.INSTANCE.form()
                .simpleFocused()));
    }

    private native boolean isSupportFormData()
    /*-{
        return !! window.FormData;
    }-*/;

    /**
     * Отправить файлы
     * если нет ошибок валидации, то отправляется через FormPanel
     * если ошибки есть, пытается отправить через XMLHttpRequest
     */
    private void sendFiles(FormPanel form)
    {
        if (errorFiles.isEmpty())
        {
            try
            {
                form.submit();
            }
            catch (JavaScriptException e)
            {
                onFileUploadFailure(null);
            }
            return;
        }
        String errorMessage = String.join("<br>", errorFiles.values());
        if (!isSupportFormData())
        {
            onFileSubmitted(null, null, errorMessage);
            return;
        }
        List<String> files = getFileNames(file);
        files.removeAll(errorFiles.keySet());
        if (files.isEmpty())
        {
            onFileSubmitted(null, null, errorMessage);
            return;
        }
        try
        {
            sendFilesByXMLHttpRequest(form);
        }
        catch (JavaScriptException e)
        {
            onFileUploadFailure(null);
        }
    }

    private native Element sendFilesByXMLHttpRequest(Element fileInput, Element request, Object[] errorFiles)
    /*-{
        var formData = new FormData();
        for(var i = 0; i < fileInput.files.length; i++)
        {
            var nonError = true;
            for(var j = 0; j < errorFiles.length; j++)
            {
                if(errorFiles[j] === fileInput.files[i].name)
                {
                    nonError = false;
                    break;
                }
            }
            if(nonError)
            {
                formData.append('file', fileInput.files[i]);
            }
        }
        request.send(formData);
    }-*/;

    /**
     * Отправить файлы через XMLHttpRequest
     * Используется FormData(), которая не поддерживается IE9
     */
    private void sendFilesByXMLHttpRequest(FormPanel form)
    {
        if (!isSupportFormData())
        {
            return;
        }
        XMLHttpRequest xmlHttpRequest = XMLHttpRequest.create();
        xmlHttpRequest.open(FormPanel.METHOD_POST, getUploadAddress());
        Element xmlHttpRequestElement = xmlHttpRequest.cast();
        Event.sinkEvents(xmlHttpRequestElement, Event.ONLOAD);
        Event.setEventListener(xmlHttpRequestElement, event ->
        {
            String response = ((XMLHttpRequest)event.getCurrentEventTarget().cast()).getResponseText();
            submitComplete(form, response);
        });
        sendFilesByXMLHttpRequest(file.getElement(), xmlHttpRequestElement, errorFiles.keySet().toArray());
    }

    /**
     * Действия, при корректной отправке 
     */
    private void submitComplete(FormPanel form, String response)
    {
        form.removeFromParent();
        onFilesSubmitted(response);
    }
}
