package ru.naumen.core.client.widgets;

import java.util.Date;

import com.google.gwt.dom.client.Element;

import ru.naumen.core.client.common.RendererParser;
import ru.naumen.core.client.widgets.datepicker.HasTimeRoundingMode;

/**
 * Базовый виджет для ввода времени
 * <AUTHOR>
 * @since 23.11.2017
 */
public class TimeTextBoxBase extends NauValueBox<Date>
{
    private final HasTimeRoundingMode.SecondsMillisMode secondsMillisRoundingMode;

    protected TimeTextBoxBase(Element element, RendererParser<Date> rendererParser,
            HasTimeRoundingMode.SecondsMillisMode secondsMillisRoundingMode)
    {
        super(element, rendererParser, rendererParser);
        this.secondsMillisRoundingMode = secondsMillisRoundingMode;
    }

    public HasTimeRoundingMode.SecondsMillisMode getSecondsMillisRoundingMode()
    {
        return secondsMillisRoundingMode;
    }
}
