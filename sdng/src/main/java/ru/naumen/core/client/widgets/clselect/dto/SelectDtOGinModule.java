package ru.naumen.core.client.widgets.clselect.dto;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.SelectWidgetContextImpl;
import ru.naumen.core.client.content.sccase.agrserv.context.AgreementServiceContext;
import ru.naumen.core.client.content.sccase.agrserv.property.AgreementServiceHierarchicalTreePropertyProvider;
import ru.naumen.core.client.tree.view.TreeFactoryContext;
import ru.naumen.core.client.utils.AttrDtOAsyncDataProvider;
import ru.naumen.core.client.widgets.select.AgreementServiceListDataProvider;
import ru.naumen.core.client.widgets.select.AgreementServiceTreeListDataProvider;
import ru.naumen.core.client.widgets.select.SelectResponsibleListDataProvider;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.gwt.view.client.AbstractDataProvider;
import com.google.inject.assistedinject.Assisted;

/**
 *
 * <AUTHOR>
 */
public class SelectDtOGinModule extends AbstractGinModule
{
    public interface AgreementServiceListDataProviderFactory
    {
        AgreementServiceListDataProvider create(AgreementServiceContext context);
    }

    public interface AgreementServiceTreeListDataProviderFactory
    {
        AgreementServiceTreeListDataProvider create(AgreementServiceContext context);
    }

    public interface AgreementServiceHierarchicalTreeDataProviderFactory
    {
        AgreementServiceHierarchicalTreePropertyProvider create(TreeFactoryContext context);
    }

    public interface AttrDtOAsyncDataProviderFactory
    {
        AttrDtOAsyncDataProvider create(PresentationContext context, @Assisted SelectWidgetContextImpl selectContext);
    }

    public interface SelectResponsibleListDataProviderFactory
    {
        SelectResponsibleListDataProvider create(PresentationContext context);
    }

    @Override
    protected void configure()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(AbstractDataProvider.class, AttrDtOAsyncDataProvider.class)
            .build(AttrDtOAsyncDataProviderFactory.class));
        
        install(new GinFactoryModuleBuilder()
            .implement(SelectResponsibleListDataProvider.class, SelectResponsibleListDataProvider.class)
            .build(SelectResponsibleListDataProviderFactory.class));
        
        install(new GinFactoryModuleBuilder()
            .implement(AbstractDataProvider.class, AgreementServiceListDataProvider.class)
            .build(AgreementServiceListDataProviderFactory.class));
        
        install(new GinFactoryModuleBuilder()
            .implement(AbstractDataProvider.class, AgreementServiceTreeListDataProvider.class)
            .build(AgreementServiceTreeListDataProviderFactory.class));
        //@formatter:on
    }
}
