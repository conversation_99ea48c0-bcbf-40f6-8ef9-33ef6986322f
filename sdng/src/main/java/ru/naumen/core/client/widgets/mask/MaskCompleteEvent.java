package ru.naumen.core.client.widgets.mask;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Cобытие посылается, когда маска в поле ввода была полностью заполнена
 * <AUTHOR>
 *
 */
public class MaskCompleteEvent extends GwtEvent<MaskCompleteHandler>
{
    public static Type<MaskCompleteHandler> TYPE = new Type<MaskCompleteHandler>();

    @Override
    public Type<MaskCompleteHandler> getAssociatedType()
    {
        return TYPE;
    }

    @Override
    protected void dispatch(MaskCompleteHandler handler)
    {
        handler.onMaskComplete(this);
    }
}