package ru.naumen.core.server;

import java.util.List;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementCallback;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import ru.naumen.core.server.hibernate.DataBaseInfo;

/**
 * Кластерный сервис, выполняющий основной функционал {@link JdbcTemplate}
 * Нужен, чтобы не создавать каждый раз jdbcTemplate и логировать запросы
 * Используется для прямых операций с бд в кластерном окружении
 * <AUTHOR>
 * @since 29.01.15
 */
@Component
@DependsOn("dataSourceForClusterJdbc")
public class ClusterJdbcTemplateHelper implements InitializingBean
{
    private static final Logger LOG = LoggerFactory.getLogger(ClusterJdbcTemplateHelper.class);

    /**
     * Вернет sql функцию запроса нового элемента из последовательности в зависимости от типа бд
     * @param dataBaseInfo информация о бд
     * @return функция получения нового элемента из последовательности
     */
    private static String getNextValFunction(DataBaseInfo dataBaseInfo)
    {
        if (dataBaseInfo.isOracle())
        {
            return "objectid_sequence_slow.nextval";
        }
        else if (dataBaseInfo.isMssql())
        {
            return "next value for \"objectid_sequence_slow\"";
        }
        return "nextval('objectid_sequence_slow')";
    }

    private final DataSource dataSource;
    private final PlatformTransactionManager transactionManager;
    private JdbcTemplate jdbcTemplate;
    private final String nextValFunction;

    @Inject
    public ClusterJdbcTemplateHelper(
            @Named("dataSourceForClusterJdbc") final DataSource dataSource,
            final PlatformTransactionManager transactionManager,
            final DataBaseInfo dataBaseInfo)
    {
        this.dataSource = dataSource;
        this.transactionManager = transactionManager;
        nextValFunction = getNextValFunction(dataBaseInfo);
    }

    @PostConstruct
    @Override
    public void afterPropertiesSet() throws Exception
    {
        jdbcTemplate = new JdbcTemplate(dataSource);
    }

    public void execute(final String sqlQuery)
    {
        LOG.debug(sqlQuery);
        executeTransaction((TransactionCallback<Void>)status ->
        {
            jdbcTemplate.execute(sqlQuery);
            return null;
        });
    }

    public <T> T execute(final String sqlQuery, final PreparedStatementCallback<T> preparedStatementCallback)
    {
        LOG.debug(sqlQuery);
        return executeTransaction(status -> jdbcTemplate.execute(sqlQuery, preparedStatementCallback));
    }

    public void update(final String sqlQuery, final PreparedStatementSetter preparedStatementSetter)
    {
        LOG.debug(sqlQuery);
        executeTransaction((TransactionCallback<Void>)status ->
        {
            jdbcTemplate.update(sqlQuery, preparedStatementSetter);
            return null;
        });
    }

    public <T> List<T> query(final String sqlQuery, final RowMapper<T> rowMapper) throws DataAccessException
    {
        LOG.debug(sqlQuery);
        return executeTransaction(status ->
                jdbcTemplate.query(sqlQuery, rowMapper));
    }

    public String getNextValFunction()
    {
        return nextValFunction;
    }

    private <T> T executeTransaction(TransactionCallback<T> callback)
    {
        final TransactionTemplate tt = new TransactionTemplate(transactionManager);
        tt.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        return tt.execute(callback);
    }
}