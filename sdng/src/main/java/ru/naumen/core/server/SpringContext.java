package ru.naumen.core.server;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.Advised;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.Map;

/**
 * Контекст текущего приложения.
 * <p>
 * Содержит утилитарные методы для получения Spring бинов.
 *
 * <AUTHOR>
 *
 */
@Component
public class SpringContext
{
    private static final Logger LOG = LoggerFactory.getLogger(SpringContext.class);
    private static SpringContext INSTANCE;

    public static SpringContext getInstance()
    {
        return INSTANCE;
    }

    /**
     * Попытаться распроксировать бин, распроксируется только <code>первый</code> уровень прокси
     * Если бин не прокси, то заданный бин возвращается как есть
     * Если распроксировать не вышло то заданный бин возвращается как есть
     * @param proxiedBean бин, который нужно распроксировать
     * @param <T> тип бина
     * @return распроксированный бин, если получилось распроксировать, иначе бин как есть
     */
    @SuppressWarnings("unchecked")
    public static <T> T tryUnproxyBean(T proxiedBean)
    {
        if (AopUtils.isAopProxy(proxiedBean) && proxiedBean instanceof Advised)
        {
            try
            {
                return (T)((Advised)proxiedBean).getTargetSource().getTarget();
            }
            catch (Exception e)
            {
                LOG.warn("Failed to unproxy bean of class {}", proxiedBean.getClass());//NOPMD
                LOG.debug(e.toString(), e);
                return proxiedBean;
            }
        }
        LOG.debug("Object of type {} is not a valid proxy instance", proxiedBean.getClass());
        return proxiedBean;
    }

    @Inject
    private AutowireCapableBeanFactory factory;
    @Inject
    private ConfigurableApplicationContext applicationContext;

    /**
     * Add a new ApplicationListener that will be notified on context events
     * such as context refresh and context shutdown.
     * <p>Note that any ApplicationListener registered here will be applied
     * on refresh if the context is not active yet, or on the fly with the
     * current event multicaster in case of a context that is already active.
     * @param listener the ApplicationListener to register
     * @see org.springframework.context.event.ContextRefreshedEvent
     * @see org.springframework.context.event.ContextClosedEvent
     */
    public void addApplicationListener(ApplicationListener<?> listener)
    {
        applicationContext.addApplicationListener(listener);
    }

    /**
     * @see AutowireCapableBeanFactory#autowireBean(Object)
     *
     * @param existingBean бин, который нужно заинжектить
     */
    public void autowireBean(Object existingBean) throws BeansException
    {
        getBeanFactory().autowireBean(existingBean);
    }

    public boolean containsBean(String beanName)
    {
        return getBeanFactory().containsBean(beanName);
    }

    /**
     * @see AutowireCapableBeanFactory#createBean(Class)
     *
     * @param requiredType класс, по которому нужно создать бин
     */
    public <T> T createBean(Class<T> requiredType) throws BeansException
    {
        return getBeanFactory().createBean(requiredType);
    }

    public <T> T evaluateExpression(String expression, Class<? extends T> clazz)
    {
        final StandardEvaluationContext evalCtx = new StandardEvaluationContext();
        evalCtx.setBeanResolver(new BeanFactoryResolver(factory));

        return new SpelExpressionParser().parseExpression(expression).getValue(evalCtx, clazz);
    }

    /**
     * Поднятие бина по классу
     * Метод синхронизирован внутри, поэтому по возможности
     * нужно минимизировать количество его вызовов.
     * Если не хотите делать приведение типов нужно использовать 
     * метод {@link #getBean(String, Class)}.
     * @see AutowireCapableBeanFactory#getBean(Class)
     *
     * @param requiredType класс, по которому нужно найти бин
     */
    public <T> T getBean(Class<T> requiredType) throws BeansException
    {
        return getBeanFactory().getBean(requiredType);
    }

    public void publishEvent(ApplicationEvent applicationEvent)
    {
        this.applicationContext.publishEvent(applicationEvent);
    }

    /**
     * @see AutowireCapableBeanFactory#getBean(String)
     *
     * @param name имя бина, по которому будет осуществлен поиск
     */
    public Object getBean(String name) throws BeansException
    {
        return getBeanFactory().getBean(name);
    }

    /**
     * @see AutowireCapableBeanFactory#getBean(String, Class)
     *
     * @param name имя бина, по которому будет осуществлен поиск
     * @param requiredType класс бина, по которому будет осуществлен поиск
     */
    public <T> T getBean(String name, Class<T> requiredType) throws BeansException
    {
        return getBeanFactory().getBean(name, requiredType);
    }

    /**
     * @return {@link BeanFactory}
     */
    public AutowireCapableBeanFactory getBeanFactory()
    {
        final WebApplicationContext currentWebApplicationContext = ContextLoader.getCurrentWebApplicationContext();
        return null == currentWebApplicationContext ? factory
                : currentWebApplicationContext.getAutowireCapableBeanFactory();
    }

    /**
     * Возвращает все бины, реализующие интерфейс (класс)
     *
     * @param requiredType класс бинов, которые нужно получить
     */
    public <T> Map<String, T> getBeans(Class<T> requiredType)
    {
        return ((DefaultListableBeanFactory)getBeanFactory()).getBeansOfType(requiredType);
    }

    @PostConstruct
    public void init()
    {
        INSTANCE = this;
    }

    /**
     * @see AutowireCapableBeanFactory#initializeBean(Object, String)
     */
    public void initializeBean(Object existingBean, String beanName)
    {
        getBeanFactory().initializeBean(existingBean, beanName);
    }

    public void isTypeMatch(String name, Class<?> targetType) throws NoSuchBeanDefinitionException
    {
        getBeanFactory().isTypeMatch(name, targetType);
    }
}