package ru.naumen.core.server.bo;

import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Transient;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.UUIDIdentifiableBase;
import ru.naumen.core.server.flex.HasFlexes;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.server.annotations.Attribute;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.server.annotations.RequireType;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.Accessors;

/**
 * Базовая реализация системных объектов. Используется, например для объектов "Привязка"
 * <AUTHOR>
 * @since 05.04.2011
 *
 */
@MappedSuperclass
@Metaclass(id = Constants.AbstractSystemObject.CLASS_ID, title = { @LStr(value = "Служебный класс"),
        @LStr(lang = "en", value = "Secondary class") }, withCase = false)
public abstract class AbstractSystemObject extends UUIDIdentifiableBase implements ISimpleBO, HasFlexes
{
    @Transient
    IProperties flexes;

    @Override
    public IProperties getFlexes()
    {
        if (null == flexes)
        {
            flexes = new MapProperties();
        }
        return flexes;
    }

    @Override
    @Attribute(title = { @LStr(value = "Тип объекта"), @LStr(lang = "en", value = "Object type") }, example = {
            @LStr(value = "Запрос") }, required = RequireType.SYSTEM_REQUIRED, editable = false, accessor =
            Accessors.METACLASS)
    public abstract ClassFqn getMetaClass();

    @Override
    public String toString()
    {
        return null == getUUID() ? getMetaClass().toString() : getUUID();
    }
}
