package ru.naumen.core.server.bo;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.mapper.MappingContext;
import ru.naumen.core.shared.dto.AbstractDtObject;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Расширение для процесса подготовки объекта к передаче на сторону клиента (маппинг в DTO).
 * Позволяет дополнить логику преобразования значений и проверки прав.
 * <AUTHOR>
 * @since Aug 08, 2022
 */
public interface AttributeValueMappingExtension
{
    /**
     * Функция преобразования значений по умолчанию.
     */
    @FunctionalInterface
    interface DefaultAttributeValueDtoConverter
    {
        @Nullable
        Object convert(Attribute attribute, @Nullable Object value);
    }

    /**
     * Позволяет наложить дополнительное ограничение при проверке прав на атрибут.
     * @param attribute атрибут
     * @param edit <code>true</code>, если проверяется право на редактирование, иначе <code>false</code>
     * @return <code>false</code>, если права на атрибут безусловно отсутствуют, иначе <code>true</code>, при этом права
     * будут определяться другими проверками
     */
    boolean hasPermission(Attribute attribute, boolean edit);

    /**
     * Преобразует значение атрибута.
     *
     * @param attribute атрибут
     * @param value исходное значение
     * @param mappingContext контекст преобразования
     * @param defaultConverter стандартная функция конвертирования значений
     * @return преобразованное значение атрибута
     */
    @Nullable
    Object transformValue(Attribute attribute, @Nullable Object value, MappingContext mappingContext,
            DefaultAttributeValueDtoConverter defaultConverter);

    /**
     * Преобразует значение атрибута для использования в скриптах.
     * @param attribute атрибут
     * @param value исходное значение
     * @param defaultConverter стандартная функция конвертирования значений
     * @return преобразованное значение атрибута
     */
    @Nullable
    Object transformValueForScript(Attribute attribute, @Nullable Object value,
            DefaultAttributeValueDtoConverter defaultConverter);

    /**
     * При необходимости обновляет объект после преобразования значения атрибута.
     * @param object объект (DTO)
     * @param attribute атрибут, значение которого было преобразовано
     */
    void updateObject(AbstractDtObject object, Attribute attribute);

    /**
     * При необходимости обновляет информацию о правах после преобразования значения атрибута.
     * @param object объект (DTO)
     * @param attribute атрибут, значение которого было преобразовано
     * @param checkPermissionUuid UUID объекта контекста для генерации токенов доступа (при необходимости)
     */
    void updatePermissions(AbstractDtObject object, Attribute attribute, @Nullable String checkPermissionUuid);
}
