/**
 *
 */
package ru.naumen.core.server.bo;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.mail.Mail;
import ru.naumen.core.server.mapper.MappingContext;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * <AUTHOR>
 * @since Apr 10, 2015
 */
@Component
public class MailDtoMapper extends MailDtoMapperBase<Mail>
{
    public MailDtoMapper()
    {
        super(Mail.class);
    }

    public MailDtoMapper(Class<Mail> from)
    {
        super(from);
    }

    @Override
    protected Object convertValue0(Mail from, Attribute attr, Object value, DtoProperties properties,
            MappingContext mappingContext)
    {
        return convertValueForMail(from, attr, value, properties, mappingContext);
    }
}
