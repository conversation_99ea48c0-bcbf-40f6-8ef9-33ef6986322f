/**
 *
 */
package ru.naumen.core.server.bo;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.mail.MailLogRecord;
import ru.naumen.core.server.mapper.MappingContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * <AUTHOR>
 * @since 30 апр. 2015 г.
 */
@Component
public class MailLogRecordDtoMapper extends MailDtoMapperBase<MailLogRecord>
{
    public MailLogRecordDtoMapper()
    {
        super(MailLogRecord.class);
    }

    public MailLogRecordDtoMapper(Class<MailLogRecord> from)
    {
        super(from);
    }

    @Override
    protected Object convertValue0(MailLogRecord from, Attribute attr, Object value, DtoProperties properties,
            MappingContext mappingContext)
    {
        return convertValueForMail(from, attr, value, properties, mappingContext);
    }

    @Override
    protected Object transformAttrValue(MailLogRecord from, Attribute attr, Object value, MappingContext mappingContext)
    {
        value = super.transformAttrValue(from, attr, value, mappingContext);

        if (value == null)
        {
            return null;
        }

        if (Constants.MailLogRecord.EVENT_TYPE.equals(attr.getCode()))
        {
            return new SimpleDtObject((String)value, messages.getMessage("MailLogRecord." + (String)value));
        }

        return value;
    }

}
