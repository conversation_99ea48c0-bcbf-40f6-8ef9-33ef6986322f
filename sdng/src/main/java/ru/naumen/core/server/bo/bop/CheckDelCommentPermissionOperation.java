package ru.naumen.core.server.bo.bop;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.autorize.GrantedPermission;
import ru.naumen.core.server.comment.CommentImpl;
import ru.naumen.core.shared.SecConstants.AbstractBO;

/**
 * Операция проверки прав удаления комментария
 *
 * <AUTHOR>
 *
 */
@Component
public class CheckDelCommentPermissionOperation extends AbstractSourceCheckPermissionOperation<CommentImpl>
{
    public CheckDelCommentPermissionOperation()
    {
        super(ru.naumen.core.shared.SecConstants.CommentList.DEL);
    }

    @Override
    public void perform(AtomOperationContext<IHasObjectBOContext<CommentImpl>> ctx) throws OperationException
    {
        super.perform(ctx);
        GrantedPermission gp = new GrantedPermission(ctx.getContext().getObject().getMetaClass())
                .permit(AbstractBO.DELETE_OBJECT);
        ctx.getProcess().grantOperationsPermission(gp, DeleteOperation.ID);
    }
}
