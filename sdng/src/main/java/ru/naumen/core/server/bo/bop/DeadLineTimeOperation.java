package ru.naumen.core.server.bo.bop;

import java.util.Date;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.SetAttrValueOperation;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.core.server.bo.servicecall.ServiceCall;
import ru.naumen.core.server.timing.calculate.ITimingCalculator;

/**
 * Операция вычисления {@link ServiceCall#getDeadLineTime() Регламентное время закрытия запроса}
 *
 * <AUTHOR>
 *
 */
@Component
public class DeadLineTimeOperation extends SetAttrValueOperation<ServiceCall, Date>
{
    private static final Logger LOG = LoggerFactory.getLogger(DeadLineTimeOperation.class);

    @Inject
    OperationUtils operationUtils;

    @Override
    protected Date getNewValue(AtomOperationContext<IHasObjectBOContext<ServiceCall>> ctx)
    {
        Date newValue = super.getNewValue(ctx);
        if (newValue == null)
        {
            final boolean debugEnabled = LOG.isDebugEnabled();

            ServiceCall serviceCall = ctx.getContext().getObject();
            DateTimeInterval resolutionTime = serviceCall.getResolutionTime();
            if (resolutionTime == null || resolutionTime.toMiliseconds() == null)
            {
                LOG.info("Resolution time not set. Skip.");
                return null;
            }
            if (debugEnabled)
            {
                LOG.debug("StartTime is " + serviceCall.getStartTime());
                LOG.debug("ResolutionTime is " + resolutionTime);
            }
            ITimingCalculator calculator = operationUtils.getServicetimeCalculator(serviceCall);
            calculator.setStartDate(serviceCall.getStartTime());
            calculator.setServiceTime(resolutionTime.toMiliseconds());
            Date result = calculator.getServiceEndDate();
            if (debugEnabled)
            {
                LOG.debug("DeadLineTime is " + result);
            }
            return result;
        }
        return newValue;
    }

    @Override
    protected void validate(AtomOperationContext<IHasObjectBOContext<ServiceCall>> ctx, Date oldValue, Date newValue)
    {
        super.validate(ctx, oldValue, newValue);
        validator.validateSetAttrOperation(ctx.getContext(), ctx.getId(), newValue);
    }
}
