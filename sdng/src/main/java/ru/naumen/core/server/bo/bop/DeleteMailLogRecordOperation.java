package ru.naumen.core.server.bo.bop;

import java.util.Locale;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.core.server.mail.MailLogRecord;
import ru.naumen.core.server.util.MessageFacade;

/**
 * Операция удаления объекта {@link MailLogRecord}
 * <AUTHOR>
 * @since Oct 4, 2019
 */
@Component
public class DeleteMailLogRecordOperation extends DeleteOperation<MailLogRecord>
{
    private final MessageFacade messageFacade;

    private final LocaleUtils localeUtils;

    @Inject
    public DeleteMailLogRecordOperation(
            MessageFacade messageFacade,
            LocaleUtils localeUtils)
    {
        this.messageFacade = messageFacade;
        this.localeUtils = localeUtils;
    }

    @Override
    public void perform(AtomOperationContext<IHasObjectBOContext<MailLogRecord>> ctx)
    {
        final Locale locale;
        if (ctx.hasProperty(Constants.OPERATION_MESSAGE_LOCALE_OVERRIDE))
        {
            locale = ctx.getProperty(Constants.OPERATION_MESSAGE_LOCALE_OVERRIDE);
        }
        else
        {
            locale = localeUtils.getCurrentUserLocale();
        }

        throw new OperationException(messageFacade.getMessage("MailLogOperation.objectCannotBeDeleted", locale),
                true);
    }
}
