/**
 *
 */
package ru.naumen.core.server.bo.bop;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.ObjectOperationBase;
import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.events.Constants.Categories;
import ru.naumen.core.server.wf.HasResponsible;
import ru.naumen.core.server.wf.HasState;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * Операция сохранения события об ошибке изменения ответственного после сохранения объекта
 * <AUTHOR>
 * @since 11 февр. 2015 г.
 *
 */
@Component
public class SaveSetResponsibleEvent<T extends AbstractBO & HasState & HasResponsible> extends ObjectOperationBase<T>
{
    @Override
    public void perform(AtomOperationContext<IHasObjectBOContext<T>> ctx) throws OperationException
    {
        String errorMessage = ctx.getProperty(SetResponsibleOperation.RESPONBSIBLE_ERROR_EVENT_MESSAGE);
        if (errorMessage == null)
        {
            return;
        }
        IUUIDIdentifiable eventParent = operationHelper.getObjectParent(ctx.getContext().getObject());
        eventService.event(Categories.RESPONSIBLE_STRATEGY_ERROR, ctx.getContext().getObject(), eventParent,
                errorMessage);
    }
}