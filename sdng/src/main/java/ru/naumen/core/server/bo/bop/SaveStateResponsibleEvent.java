package ru.naumen.core.server.bo.bop;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.ObjectOperationBase;
import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.events.AbstractStateResponsibleEvent;
import ru.naumen.core.server.wf.HasResponsible;
import ru.naumen.core.server.wf.HasState;

/**
 * Операция сохранять событие смены ответственного/состояния в случае необходимости
 *
 * <AUTHOR>
 *
 * @param <T>
 */
@Component
public class SaveStateResponsibleEvent<T extends AbstractBO & HasState & HasResponsible> extends ObjectOperationBase<T>
{
    @Override
    public void perform(AtomOperationContext<IHasObjectBOContext<T>> ctx) throws OperationException
    {
        AbstractStateResponsibleEvent<T> event = ctx.getProperty(InitStateResponsibleEvent.STATE_RESPONBSIBLE_EVENT);

        if (null == event)
        {
            return;
        }
        if (event.isResponsibleChanged() || event.isStateChanged())
        {
            // сбрасываем не нужные значения
            if (!event.isResponsibleChanged())
            {
                event.setResponsibleStartTime(null);
                event.setResponsibleElapsed(0);
                event.setResponsibleEmployee(null);
                event.setResponsibleTeam(null);
                event.setNewResponsibleEmployee(null);
                event.setNewResponsibleTeam(null);
            }
            else if (!event.isStateChanged())
            {
                event.setStateStartTime(null);
                event.setStateElapsed(0);
                event.setStateCode(null);
                event.setNewStateCode(null);
            }

            getSession().persist(event);
        }
    }
}
