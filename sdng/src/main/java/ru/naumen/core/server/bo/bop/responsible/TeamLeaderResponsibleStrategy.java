package ru.naumen.core.server.bo.bop.responsible;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.team.Team;
import ru.naumen.core.server.wf.HasResponsible;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.elements.wf.State;

/**
 * Лидер команды текущего ответственного
 * <p>
 * Лидер команды, ответственной за запрос, либо команды, в рамках которой ответственный отвечает за запрос
 * <p>
 * Ответственный(сотрудник):лидер команды из атрибута "Ответственный(команда)"
 * Ответственный(команда):не меняется
 *
 * <AUTHOR>
 *
 */
@Component(TeamLeaderResponsibleStrategy.ID)
public class TeamLeaderResponsibleStrategy<T extends HasResponsible & IUUIDIdentifiable> extends
        ResponsibleStrategyImpl<T>
{
    public static final String ID = "teamLeaderResponsibleStrategy";

    public TeamLeaderResponsibleStrategy()
    {
        super(ID);
    }

    @Override
    public Pair<Team, Employee> getNewResponsible(IHasObjectBOContext<T> context, State state)
    {
        T serviceCall = context.getObject();
        Team team = serviceCall.getResponsibleTeam();
        if (null == team)
        {
            Employee employee = serviceCall.getResponsibleEmployee();
            team = responsibleUtils.getEmployeeTeam(employee);
        }
        if (null == team)
        {
            responsibleUtils
                    .throwResponsibleStrategyException("responsibleStrategy.teamLeaderResponsibleStrategy.teamError");
        }
        Employee teamLeader = team.getLeader();
        if (null == teamLeader)
        {
            responsibleUtils
                    .throwResponsibleStrategyException("responsibleStrategy.teamLeaderResponsibleStrategy.leaderError");
        }
        checkPerformer(teamLeader, serviceCall.getMetaClass());

        return Pair.create(team, teamLeader);
    }
}
