package ru.naumen.core.server.cluster.license;

import static ru.naumen.core.shared.Constants.BaseLevel.SERVER_ENTERPRISE;
import static ru.naumen.core.shared.maintenance.MaintenanceMode.LOGIN_AND_BACKGROUND_TASKS_BLOCKING;

import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.background.BackgroundManager;
import ru.naumen.core.server.configuration.beanconditions.cluster.AnyClusterCondition;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.license.events.LicenseChangedEvent;
import ru.naumen.core.server.maintenance.services.MaintenanceManipulatingService;

/**
 * Реализция сервиса {@link SecurityClusterService}
 * Слушает события об изменении лицензионного файла и переключает, при необходимости, флажок допустимости кластера
 * <AUTHOR>
 * @since 28.10.2021
 */
@Conditional(AnyClusterCondition.class)
@Component
class SecurityClusterServiceImpl implements SecurityClusterService, ApplicationListener<LicenseChangedEvent>
{
    private final BackgroundManager backgroundManager;
    private final LicensingService licensingService;
    private final ClusterInfoService clusterInfoService;
    private final MaintenanceManipulatingService maintenanceManipulatingService;

    private volatile boolean clusterEnabled;

    @Inject
    SecurityClusterServiceImpl(
            @Lazy final MaintenanceManipulatingService maintenanceManipulatingService,
            @Lazy final BackgroundManager backgroundManager,
            final LicensingService licensingService,
            final ClusterInfoService clusterInfoService)
    {
        this.maintenanceManipulatingService = maintenanceManipulatingService;
        this.backgroundManager = backgroundManager;
        this.licensingService = licensingService;
        this.clusterInfoService = clusterInfoService;
    }

    @Override
    public void onApplicationEvent(final LicenseChangedEvent event)
    {
        clusterEnabled = licensingService.getBaseLevels().contains(SERVER_ENTERPRISE);
        startBackgroundProcesses();
    }

    /**
     * Запустить фоновую активность на той ноде, где загрузили корректный лицензионый файл
     * на остальных нодах стартует после синхронизации автоматически
     */
    private void startBackgroundProcesses()
    {
        if (clusterEnabled && !clusterInfoService.isSyncStarted()
            && (backgroundManager.isStopped() &&
                !maintenanceManipulatingService.isMaintenanceModeEnabledWithMode(LOGIN_AND_BACKGROUND_TASKS_BLOCKING)))
        {
            backgroundManager.start();
        }
    }

    @Override
    public boolean isClusterEnabled()
    {
        return clusterEnabled;
    }
}