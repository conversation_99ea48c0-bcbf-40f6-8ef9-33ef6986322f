package ru.naumen.core.server.cluster.synchronization.bus.invalidation.events;

import ru.naumen.core.server.cluster.external.NodeRole;

/**
 * Событие об изменении состава кластера на "Полный" {@link ClusterState#COMPLETE}
 * или на "Частичный" {@link ClusterState#PARTIAL}
 * <AUTHOR>
 * @since 24.03.2025
 */
final class ClusterChangeStateEvent extends ClusterStateEvent
{
    ClusterChangeStateEvent(NodeRole thisRole, ClusterState clusterState,
            ClusterMembership clusterMembership,
            boolean thisNodeIsCoordinator, ClusterMembership previousClusterMembership)
    {
        super(thisRole, clusterState, clusterMembership, thisNodeIsCoordinator, previousClusterMembership);
    }
}