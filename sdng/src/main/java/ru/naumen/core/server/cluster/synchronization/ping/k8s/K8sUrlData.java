package ru.naumen.core.server.cluster.synchronization.ping.k8s;

/**
 * Параметры подключения к kubernetes мастеру
 *
 * @param masterProtocol    http  протокол
 * @param apiVersion        версия API
 * @param connectTimeout    таймаут подключения
 * @param readTimeout       таймаут получения ответа от мастера
 * @param operationAttempts кол-во попыток подключений
 * @param operationSleep    ожидание между попытками подключений
 * <AUTHOR>
 * @since 11.06.2024
 */
public record K8sUrlData(String masterProtocol, String apiVersion, int connectTimeout, int readTimeout,
                         int operationAttempts, long operationSleep)
{
}
