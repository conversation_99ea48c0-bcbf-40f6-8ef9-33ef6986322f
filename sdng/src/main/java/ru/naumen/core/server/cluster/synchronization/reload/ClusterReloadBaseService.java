package ru.naumen.core.server.cluster.synchronization.reload;

import static ru.naumen.core.server.license.Constants.LICENSE;
import static ru.naumen.core.server.script.storage.ScriptStorageConfiguration.SCRIPT_TYPE;
import static ru.naumen.metainfo.server.Constants.METACLASS;

import java.util.Map;
import java.util.Set;

import ru.naumen.core.server.flex.spi.ReloadableSessionFactory;
import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.libraries.LibrariesService;
import ru.naumen.core.server.script.modules.storage.ScriptModulesStorageServiceImpl;
import ru.naumen.core.server.script.storage.ScriptStorageServiceBean;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;

/**
 * Базовая логика для синхронизации кэша метаинформации в кластере
 * По запуску счетчика для синхронной синхронизации или же после получения эвента
 * об асинхронной сбрасываем кэш (если есть поддержка у региона для точечного сброса - то точечно)
 * у изменившихся регионов метаинформации, проводим корректную инициализацию и снова наполняем кэш
 * из бд актуальными данными.
 * <AUTHOR>
 * @since 22.05.2024
 */
public abstract class ClusterReloadBaseService
{
    private final MetaStorageChangesService metaStorageChangesService;
    private final ScriptStorageServiceBean scriptStorageServiceBean;
    private final ScriptModulesStorageServiceImpl scriptModulesStorageService;
    private final ScriptService scriptService;
    private final ClusterCacheReloader clusterCacheReloader;
    private final ReloadableSessionFactory sessionFactory;
    private final LicensingService licensingService;
    private final LibrariesService librariesService;

    protected ClusterReloadBaseService(
            ScriptStorageServiceBean scriptStorageServiceBean,
            ScriptModulesStorageServiceImpl scriptModulesStorageService,
            ScriptService scriptService,
            ClusterCacheReloader clusterCacheReloader,
            ReloadableSessionFactory sessionFactory,
            LicensingService licensingService,
            LibrariesService librariesService,
            MetaStorageChangesService metaStorageChangesService)
    {
        this.metaStorageChangesService = metaStorageChangesService;
        this.scriptStorageServiceBean = scriptStorageServiceBean;
        this.scriptModulesStorageService = scriptModulesStorageService;
        this.scriptService = scriptService;
        this.clusterCacheReloader = clusterCacheReloader;
        this.sessionFactory = sessionFactory;
        this.licensingService = licensingService;
        this.librariesService = librariesService;
    }

    /**
     * Запуск механизма синхронизации кэшей в кластере
     * @param changedCacheRegions изменившиеся регионы метаинформации
     * @param changedScriptModules коды, изменившихся скриптовых модулей
     */
    protected void makeReload(Set<String> changedCacheRegions, Set<String> changedScriptModules)
    {
        final boolean licenseChanged = changedCacheRegions.contains(LICENSE);
        if (changedCacheRegions.size() == 1 && (!changedScriptModules.isEmpty() || licenseChanged))
        {
            metaStorageChangesService.reInit();
            if (licenseChanged)
            {
                licensingService.reload();
            }
            reloadOnlyScriptModules(changedScriptModules);
        }
        else
        {
            final boolean hadResetScripts = beforeReload(changedCacheRegions, changedScriptModules);
            reloadProcess(changedCacheRegions);
            afterReload(hadResetScripts, changedScriptModules, changedCacheRegions);
        }
    }

    /**
     * Перезагрузка только модулей скриптов
     * @param changedScriptModules изменившиеся модули
     */
    private void reloadOnlyScriptModules(Set<String> changedScriptModules)
    {
        if (!changedScriptModules.isEmpty())
        {
            scriptModulesStorageService.reinitCache(changedScriptModules);
            scriptService.reloadModules(changedScriptModules);
        }
    }

    /**
     * Действия перед основным процессом синхронизации
     * @param changedCacheRegions изменившиеся регионы меты
     * @param changedScriptModules коды изменившихся модулей
     * @return нужно ли перезагрузить после основного процесса места использования скриптов/модули
     */
    private boolean beforeReload(Set<String> changedCacheRegions, Set<String> changedScriptModules)
    {
        // нужно рефрешнуть до рефреша скриптов/модулей
        if (changedCacheRegions.contains(ScriptLibrary.META_TYPE))
        {
            changedCacheRegions.remove(ScriptLibrary.META_TYPE);
            librariesService.reloadCluster();
        }
        if (!changedScriptModules.isEmpty() || ClusterReloadUtils.isNeedReloadScriptUsage(changedCacheRegions))
        {
            // удалим из измененных скрипт, чтобы не было попыток найти его сервис позже
            changedCacheRegions.remove(SCRIPT_TYPE);
            // Необходимо пересобрать кэш скриптов в мете до того, как
            // в ходе переинициализации метаклассов будет их обработка
            scriptStorageServiceBean.reinitCache();
            scriptModulesStorageService.reinitCache(changedScriptModules);
            return true;
        }
        return false;
    }

    /**
     * Основной процесс синхронизации ноды кластера
     * @param changedCacheRegions изменившиеся регионы метаинформации
     */
    private void reloadProcess(Set<String> changedCacheRegions)
    {
        final boolean needReloadSessionFactory = ClusterReloadUtils.isNeedReloadSessionFactory(changedCacheRegions);
        // если это не метакласс, но что-то из того, что требует полной синхронизации, надо
        // добавить в список регион метакласса, чтобы механизм по базовой синхронизации выполнил
        // синхронизацию в MetainfoService, который сейчас и выполняет всю работу в случае полной
        // синхронизации
        if (needReloadSessionFactory)
        {
            changedCacheRegions.add(METACLASS);
        }

        // Соберем регион + список кодов элементов, что были изменены в нем
        // из changedCacheRegions такой регион будет удален, тк синхронизируется отдельно
        final Map<String, Set<String>> regionsWithChangedKeys = getRegionsWithChangedKeys(changedCacheRegions);

        clusterCacheReloader.clearPointedCache(regionsWithChangedKeys);
        clusterCacheReloader.clearFullCache(changedCacheRegions);
        if (needReloadSessionFactory)
        {
            sessionFactory.reloadCluster();
        }
        metaStorageChangesService.reInit();
        clusterCacheReloader.reInitPointedCache(regionsWithChangedKeys);
        clusterCacheReloader.reInitFullCache(changedCacheRegions);
    }

    /**
     * Действия после основного процесса синхронизации
     * @param hadResetScripts  признак, что были перезагружены скрипты/изменены регионы, где скрипты могли быть
     *  изменены, поэтому нужно перезаписать места их использования/сбросить кэш изменившихся модулей
     * @param changedScriptModules изменившиеся модули скриптов
     * @param changedCacheRegions изменившиеся регионы меты
     */
    private void afterReload(boolean hadResetScripts, Set<String> changedScriptModules, Set<String> changedCacheRegions)
    {
        if (hadResetScripts)
        {
            // После того, как метаинформация приведена в актуальное состояние можно перезаписать
            // всю сопутствующую инфу по скриптам ScriptUsagePoint
            scriptStorageServiceBean.resetScriptUsageCache();
            if (!changedScriptModules.isEmpty())
            {
                // Если изменились модули - сбрасываем кэш точечно
                scriptService.reloadModules(changedScriptModules);
            }
        }
        // лицензию нужно обновить только после инкремента версии меты
        if (changedCacheRegions.contains(LICENSE))
        {
            licensingService.reload();
        }
    }

    /**
     * Получим список регионов с кодами изменившихся элементов
     * @param changedCacheRegions все изменившиеся регионы
     */
    protected abstract Map<String, Set<String>> getRegionsWithChangedKeys(Set<String> changedCacheRegions);
}