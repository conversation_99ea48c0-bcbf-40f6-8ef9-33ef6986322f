package ru.naumen.core.server.configuration;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Абстрактный контейнер конфиг-параметров.
 * <AUTHOR>
 */
public abstract class PropertyContainer
{
    public abstract void applyValuesAndSave(String prefix, List<ReflectedPropertyValue> values);

    public abstract String getName();

    public abstract Map<String, String> getParameters();

    public boolean hasKeyWithPrefix(String prefix)
    {
        for (String key : getParameters().keySet())
        {
            if (key.startsWith(prefix))
            {
                return true;
            }
        }
        return false;
    }

    public Collection<String> listKeysWithPrefix(String prefix)
    {
        List<String> result = new ArrayList<String>();
        for (String key : getParameters().keySet())
        {
            if (key.startsWith(prefix))
            {
                result.add(key);
            }
        }
        return result;
    }
}
