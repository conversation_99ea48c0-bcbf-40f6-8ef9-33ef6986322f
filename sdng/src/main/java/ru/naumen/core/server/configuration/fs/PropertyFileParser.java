package ru.naumen.core.server.configuration.fs;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import ru.naumen.commons.shared.FxException;

/**
 * Парсер конфиг-файлов.<br>
 * Формат файла:<ul>
 * <li> Первая строка может указывать кодировку файла в формате ### UTF-8
 * <li> Комментариями являются строки, начинающиеся с #
 * <li> Формат параметра: name = value <br> Пробелы в начале и в конце name и value обрезаются
 * <li> В значениях распознаются escape-последовательности (\r, \n, \t, \\uXXXX)
 * </ul>
 * <AUTHOR>
 */
public class PropertyFileParser
{
    private static final String ENCODING_PREFIX = "###";
    public static final String DEFAULT_ENCODING = "cp1251";

    @edu.umd.cs.findbugs.annotations.SuppressWarnings("RCN_REDUNDANT_NULLCHECK_OF_NULL_VALUE")
    public static PropertyFile parse(File file) throws IOException
    {
        boolean hadEncodingLine = true;
        String encoding = getEncoding(file);
        if (encoding == null)
        {
            encoding = DEFAULT_ENCODING;
            hadEncodingLine = false;
        }
        try (BufferedReader rdr = new BufferedReader(new InputStreamReader(new FileInputStream(file), encoding)))
        {
            if (hadEncodingLine)
            {
                rdr.readLine();
            }
            PropertyFileParser parser = new PropertyFileParser(rdr, file);
            List<PropertyFileEntry> entries = parser.parse();
            return new PropertyFile(entries, file, encoding);
        }
    }

    private static String getEncoding(File file) throws IOException
    {
        try (BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(file),
                StandardCharsets.UTF_8)))
        {
            String firstLine = br.readLine();
            if (firstLine == null)
            {
                return null;
            }
            firstLine = firstLine.trim();
            if (firstLine.startsWith(ENCODING_PREFIX) && firstLine.length() > ENCODING_PREFIX.length())
            {
                String encoding = firstLine.substring(ENCODING_PREFIX.length()).trim();
                if (encoding.startsWith("#"))
                {
                    return null;
                }
                else
                {
                    return encoding;
                }
            }
            else
            {
                return null;
            }
        }
    }

    private BufferedReader _reader;
    private int _lineNumber;

    private File _file;

    protected PropertyFileParser(BufferedReader reader, File file)
    {
        _reader = reader;
        _file = file;
    }

    private String formatPosition()
    {
        return _file.getName() + ":" + _lineNumber;
    }

    private List<PropertyFileEntry> parse() throws IOException
    {
        List<PropertyFileEntry> result = new ArrayList<PropertyFileEntry>();
        PropertyFileEntry entry;
        while ((entry = parseEntry()) != null)
        {
            result.add(entry);
        }
        return result;
    }

    private PropertyFileEntry parseEntry() throws IOException
    {
        String line = _reader.readLine();
        _lineNumber++;
        if (line == null)
        {
            return null;
        }
        String trimmed = line.trim();
        if (trimmed.isEmpty() || trimmed.startsWith("#") || trimmed.startsWith("="))
        {
            return new PropertyFileRawLine(line);
        }
        else
        {
            return parseParameter(trimmed);
        }
    }

    private PropertyFileEntry parseParameter(String line)
    {
        int eqPos = line.indexOf('=');
        if (eqPos < 0 || eqPos == line.length() - 1)
        {
            if (eqPos >= 0)
            {
                line = line.substring(0, eqPos).trim();
            }

            return new PropertyFileValue(line.trim(), null);
        }
        else
        {
            String name = line.substring(0, eqPos);
            String value = line.substring(eqPos + 1, line.length());
            name = unescape(name.trim());
            value = value.trim();
            if (value.isEmpty())
            {
                value = null;
            }
            else
            {
                value = unescape(value);
            }

            //_log.debug("Parameter parsed: " + name + " = " + value);
            return new PropertyFileValue(name, value);
        }
    }

    private String unescape(String value)
    {
        int index = 0;
        int len = value.length();
        StringBuilder result = new StringBuilder(len);

        while (index < len)
        {
            char ch = value.charAt(index);
            if (ch == '\\' && index < len - 1)
            {
                char esc = value.charAt(index + 1);
                switch (esc)
                {
                    case 'u':
                        result.append(unescapeHex(index, value));
                        index += 6;
                        break;
                    case 'r':
                        result.append('\r');
                        index += 2;
                        break;
                    case 'n':
                        result.append('\n');
                        index += 2;
                        break;
                    case 't':
                        result.append('\t');
                        index += 2;
                        break;
                    default:
                        result.append(esc);
                        index += 2;
                        break;
                }
            }
            else
            {
                result.append(ch);
                index++;
            }
        }

        return result.toString();
    }

    private char unescapeHex(int index, String value)
    {
        if (index > value.length() - 6)
        {
            throw new FxException(
                    "Unicode escape sequence \\u" + value.substring(index) + " is too short at "
                    + formatPosition());
        }
        String hexCode = value.substring(index + 2, index + 6);
        int code;
        try
        {
            code = Integer.parseInt(hexCode, 16);
        }
        catch (NumberFormatException e)
        {
            throw new FxException("Invalid unicode escape sequence \\u" + hexCode + " at "
                                  + formatPosition());
        }
        return (char)code;
    }
}
