package ru.naumen.core.server.export.byset.transfer;

import ru.naumen.metainfo.shared.elements.HasElementId;

/**
 * Конфигурация стратегии слияния дочерних элементов.
 * <AUTHOR>
 * @since Jul 15, 2024
 */
public class ChildrenMergeConfiguration<S extends HasElementId, C extends HasElementId>
        extends ChildrenConfiguration<S, C>
{
    public enum MergeStrategy
    {
        /**
         * Обычное слияние настроек.
         */
        MERGE,
        /**
         * Слияние настроек с возможностью удаления.
         */
        MERGE_WITH_REMOVAL,
        /**
         * Слияние настроек с возможностью удаления родительского элемента, если все дочерние были помечены на удаление.
         */
        MERGE_WITH_RECURSIVE_REMOVAL
    }

    private final MergeStrategy mergeStrategy;

    public ChildrenMergeConfiguration(
            ChildrenConfiguration.ChildrenProvider<S, C> childrenProvider,
            ChildrenConfiguration.ChildrenUpdater<S, C> childrenUpdater,
            MergeStrategy mergeStrategy)
    {
        super(childrenProvider, childrenUpdater);
        this.mergeStrategy = mergeStrategy;
    }

    public MergeStrategy getMergeStrategy()
    {
        return mergeStrategy;
    }
}
