package ru.naumen.core.server.export.byset.transfer;

import jakarta.annotation.Nullable;
import ru.naumen.metainfo.server.spi.MetainfoContainer;
import ru.naumen.metainfo.shared.elements.HasElementId;

/**
 * Обработчик дополнительных событий импорта элемента.
 * @param <S> тип элемента
 * <AUTHOR>
 * @since Aug 20, 2024
 */
public interface ElementImportHandler<S extends HasElementId>
{
    default void afterMerge(@Nullable S element, @Nullable S existingElement, S destinationElement,
            MetainfoContainer destinationContainer)
    {
    }

    default boolean isCreationAllowedAnyway(S element)
    {
        return false;
    }
}
