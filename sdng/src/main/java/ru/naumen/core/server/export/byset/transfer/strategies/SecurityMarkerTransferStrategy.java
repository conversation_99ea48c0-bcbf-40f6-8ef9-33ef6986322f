package ru.naumen.core.server.export.byset.transfer.strategies;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.export.byset.transfer.ElementImportHandler;
import ru.naumen.metainfo.server.spi.MetainfoContainer;
import ru.naumen.metainfo.server.spi.store.sec.Marker;

/**
 * Стратегия загрузки и выгрузки маркера прав.
 * <AUTHOR>
 * @since Aug 13, 2024
 */
@Component
public class SecurityMarkerTransferStrategy implements ElementImportHandler<Marker>
{
    @Override
    public void afterMerge(@Nullable Marker element, @Nullable Marker existingElement, Marker destinationElement,
            MetainfoContainer destinationContainer)
    {
        if (element == null || existingElement == null || !destinationElement.isAttributesMarker())
        {
            return;
        }

        destinationElement.getAddedAttributes().clear();
        destinationElement.getAddedAttributes().addAll(element.getAddedAttributes());
        destinationElement.getAddedAttributes().addAll(existingElement.getAddedAttributes());
    }
}
