package ru.naumen.core.server.export.byset.transfer.wrappers;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map.Entry;

import ru.naumen.core.server.export.byset.transfer.MultiMapEntryWrapper;
import ru.naumen.metainfo.shared.elements.HasElementId;

/**
 * Абстрактная базовая реализация обёртки-адаптера для мультимапы.
 * <AUTHOR>
 * @since Aug 09, 2024
 * @param <K> тип ключа
 * @param <S> тип элемента списка из значения
 */
public abstract class AbstractMultiMapEntryWrapper<K, S extends HasElementId> implements MultiMapEntryWrapper<K, S>
{
    private final Entry<K, Collection<S>> entry;

    protected AbstractMultiMapEntryWrapper(Entry<K, Collection<S>> entry)
    {
        this.entry = entry;
    }

    @Override
    public K getKey()
    {
        return entry.getKey();
    }

    @Override
    public List<S> getChildren()
    {
        return new ArrayList<>(entry.getValue());
    }

    @Override
    public String getElementType()
    {
        return "multiMapEntry";
    }

    @Override
    public String getElementCode()
    {
        return getStringKey(entry.getKey());
    }

    protected abstract String getStringKey(K key);
}
