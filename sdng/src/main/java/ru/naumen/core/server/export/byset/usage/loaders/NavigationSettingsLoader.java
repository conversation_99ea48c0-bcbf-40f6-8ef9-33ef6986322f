package ru.naumen.core.server.export.byset.usage.loaders;

import java.util.Collection;
import java.util.List;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.navigationsettings.NavigationSettingsValue;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;

/**
 * Загрузчик настроек навигации из кеша метаинформации
 *
 * <AUTHOR>
 * @since 28.08.2024
 */
@Component
public class NavigationSettingsLoader implements MetaInfoCacheElementLoader<NavigationSettingsValue>
{
    private final MetainfoServiceBean metainfoServiceBean;

    @Inject
    public NavigationSettingsLoader(MetainfoServiceBean metainfoServiceBean)
    {
        this.metainfoServiceBean = metainfoServiceBean;
    }

    @Override
    public Collection<NavigationSettingsValue> loadFromCache()
    {
        return List.of(metainfoServiceBean.getNavigationSettings());
    }
}
