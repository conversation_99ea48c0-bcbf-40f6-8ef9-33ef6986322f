package ru.naumen.core.server.export.byset.usage.loaders;

import java.util.Collection;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.script.modules.storage.ScriptModule;
import ru.naumen.core.server.script.modules.storage.ScriptModulesStorageService;

/**
 * Загрузчик скриптовых модулей из кеша метаинформации
 *
 * <AUTHOR>
 * @since 28.08.2024
 */
@Component
public class ScriptModuleLoader implements MetaInfoCacheElementLoader<ScriptModule>
{
    private final ScriptModulesStorageService scriptModulesStorageService;

    @Inject
    public ScriptModuleLoader(ScriptModulesStorageService scriptModulesStorageService)
    {
        this.scriptModulesStorageService = scriptModulesStorageService;
    }

    @Override
    public Collection<ScriptModule> loadFromCache()
    {
        return scriptModulesStorageService.getAllModules();
    }
}
