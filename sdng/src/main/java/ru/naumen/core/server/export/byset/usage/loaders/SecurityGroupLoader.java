package ru.naumen.core.server.export.byset.usage.loaders;

import java.util.Collection;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.sec.GroupImpl;

/**
 * Загрузчик списка групп пользователей из кеша метаинформации
 *
 * <AUTHOR>
 * @since 28.08.2024
 */
@Component
public class SecurityGroupLoader implements MetaInfoCacheElementLoader<GroupImpl>
{
    private final SecurityServiceBean securityService;

    @Inject
    public SecurityGroupLoader(SecurityServiceBean securityService)
    {
        this.securityService = securityService;
    }

    @Override
    public Collection<GroupImpl> loadFromCache()
    {
        return securityService.getGroups();
    }
}
