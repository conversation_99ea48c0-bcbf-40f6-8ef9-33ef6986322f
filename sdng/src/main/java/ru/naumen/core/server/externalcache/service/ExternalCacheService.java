package ru.naumen.core.server.externalcache.service;

import java.util.Collection;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;

/**
 * Сервис для работы с внешним кэшем
 * <AUTHOR>
 * @since 25.06.2020
 **/
public interface ExternalCacheService<K, V>
{
    /**
     *  Подключен ли внешний кэш
     */
    boolean isCacheConnected();

    /**
     * Положить элемент по ключу в регион кэша, если региона нет - он будет создан
     *
     * @param key ключ
     * @param value значение
     * @param cacheRegion регион кэша
     */
    void put(@NotNull K key, @NotNull V value, @NotNull String cacheRegion);

    /**
     * Получить элемент по ключу из региона внешнего кэша
     *
     * @param key ключ, по которому требуется найти значение
     * @param cacheRegion регион кэша, если нет - будет exception
     * @return значение по ключу из указанного региона
     */
    @Nullable
    V get(@NotNull K key, @NotNull String cacheRegion);

    /**
     * Очистить регион внешнего кэша
     *
     * @param cacheRegion регион кэша, который требуется очистить
     */
    void clearCache(@NotNull String cacheRegion);

    /**
     * Возвращает множество адресов серверов, где хранится кеш
     * @return список адресов
     */
    default Collection<String> getAddresses()
    {
        throw new UnsupportedOperationException();
    }

    /**
     * Добавляет переданные адреса серверов, к которым необходимо подключится для работы с кешем
     */
    default void addAddresses(String... addresses)
    {
        throw new UnsupportedOperationException();
    }

    /**
     * Позволяет удалить определенные адреса серверов, где хранится кеш
     */
    default void removeAddresses(String... addresses)
    {
        throw new UnsupportedOperationException();
    }

    /**
     * Устанавливаем соединение с внешним кешем
     */
    boolean connectCache();

    /**
     * Разрываем соединение с внешним кешем
     */
    boolean disconnectCache();

    /**
     * Осуществляем переподключение к внешнему кешу
     */
    boolean reconnectCache();
}