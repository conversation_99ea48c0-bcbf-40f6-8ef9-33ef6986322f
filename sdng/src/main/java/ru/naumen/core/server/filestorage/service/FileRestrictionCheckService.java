package ru.naumen.core.server.filestorage.service;

import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.filestorage.CoreFileRestrictionCheckService;
import ru.naumen.core.server.filestorage.File;

/**
 * Сервис проверок файлов {@link File}.
 * Расширение сервиса {@link CoreFileRestrictionCheckService}
 *
 * <AUTHOR>
 * @since 10.02.2025
 */
public interface FileRestrictionCheckService extends CoreFileRestrictionCheckService
{
    /**
     * Проверка, что загружаемый файл удовлетворяет необходимым условиям.
     * @param fileName имя файла
     * @param contentType mime-тип файла
     * @param content содержимое файла
     */
    void checkUploadedFile(String fileName, String contentType, byte[] content);

    /**
     * Проверка, превышает ли размер загружаемого файла допустимую величину.
     * @param fileSize - размер файла в байтах
     * @throws FxException если размер загружаемого файла превышает допустимую величину.
     */
    void checkFileSize(long fileSize);

    /**
     * Проверка, превышает ли размер загружаемого файла/файлов допустимую величину.
     * @param allFilesSize - суммарный размер файлов в байтах
     * @throws FxException если размер загружаемого файла/файлов превышает допустимую величину.
     */
    void checkFilesSize(long allFilesSize);

    /**
     * Проверка, превышает ли размер загружаемого файла допустимую величину.
     * @param bytes массив байт загружаемого файла
     * @throws FxException если размер загружаемого файла/файлов превышает допустимую величину.
     */
    void checkFileSize(@Nullable byte[] bytes);

    /**
     * Проверка, превышает ли размер загружаемого файла/файлов допустимую величину.
     * @param request запрос {@link HttpServletRequest}
     * @throws FxException если размер загружаемого файла/файлов превышает допустимую величину.
     */
    void checkFilesSize(HttpServletRequest request);
}
