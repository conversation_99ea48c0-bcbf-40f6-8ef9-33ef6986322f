package ru.naumen.core.server.flex.codegen.attributes.object.back.single;

import static javassist.bytecode.AnnotationsAttribute.visibleTag;
import static ru.naumen.core.server.flex.codegen.JavassistHelper.*;
import static ru.naumen.core.server.flex.codegen.attributes.object.back.single.BackSinglePropertiesUtils.getColumnName;

import javassist.CtClass;
import javassist.CtMethod;
import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.ConstPool;
import javassist.bytecode.annotation.Annotation;
import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.flex.codegen.GenContext;
import ru.naumen.core.server.flex.codegen.attributes.MethodAnnotationsGenerator;
import ru.naumen.core.server.flex.codegen.persisters.ReadOnlyOneToManyCollectionPersister;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.Constants.Accessors;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Генерирует аннотации для методов атрибута единичной обратной ссылки на БО.
 *
 * <AUTHOR>
 * @since 18.12.2023
 */
class BackSingleAnnotationsGenerator implements MethodAnnotationsGenerator
{
    /**
     * Проверяет что прямая ссылка находится в системном классе и системном атрибуте.
     *
     * @param relatedAttribute атрибут
     * @param relatedMetaClass метакласс
     * @return true или false
     */
    private static boolean isDirectLinkFromSystemClass(Attribute relatedAttribute, MetaClass relatedMetaClass)
    {
        return relatedAttribute.isHardcoded()
               && (relatedMetaClass.getFqn().isClass() || relatedAttribute.getDeclaredMetaClass().isClass());
    }

    private final FlexHelper flexHelper;
    private final MetainfoService metainfoService;
    private final String mappingName;

    /**
     * Генерирует аннотации для методов атрибута единичной обратной ссылки на БО.
     *
     * @param flexHelper вспомогательные методы для работы с flex-атрибутами;
     * @param metainfoService сервис для работы с метаинформацией;
     */
    public BackSingleAnnotationsGenerator(FlexHelper flexHelper, MetainfoService metainfoService, String mappingName)
    {
        this.flexHelper = flexHelper;
        this.metainfoService = metainfoService;
        this.mappingName = mappingName;
    }

    @Override
    public void generate(Attribute attribute, CtMethod method, CtClass persistentClass, GenContext context)
    {
        final Attribute relatedAttribute = flexHelper.getBackLinkRelatedAttribute(attribute);
        final MetaClass relatedMetaClass = relatedAttribute.getMetaClass();

        final String relatedClassName = isDirectLinkFromSystemClass(relatedAttribute, relatedMetaClass)
                ? metainfoService.getJavaClassName(relatedMetaClass.getFqn())
                : metainfoService.getEntityJavaClassName(relatedMetaClass.getFqn());
        final String relatedAttributeCode = relatedAttribute.getCode();

        final ConstPool constPool = getConstPool(persistentClass);
        final AnnotationsAttribute annotationsAttribute = new AnnotationsAttribute(constPool, visibleTag);
        final Annotation accessAnnot = createPropertyAccessAnnotation(constPool);
        final Annotation mappingAnnot = createMappingNameAnnotation(constPool, mappingName);

        annotationsAttribute.addAnnotation(accessAnnot);
        annotationsAttribute.addAnnotation(mappingAnnot);

        // маппинг обратных ссылок построен через mappedBy свойство для всех атрибутов связи
        // кроме агрегирующего, так как для него не строится hibernate маппинг. Поэтому
        // обратную связь определяем как однонаправленную ReadOnly через JoinColumn аннотацию
        if (relatedAttribute.getType().getCode().equals(AggregateAttributeType.CODE)
            || attribute.getAccessor().equals(Accessors.ATTRIBUTE_OF_RELATED_OBJECT))
        {
            final Annotation o2mAnnot = createOneToManyAnnotation(constPool, relatedClassName);
            final Annotation joinColumnAnnot = createJoinColumnAnnotationNoForeignKey(constPool,
                    getColumnName(relatedAttribute), false, false, true);
            final Annotation persisterAnnot = createPersisterAnnotation(constPool,
                    ReadOnlyOneToManyCollectionPersister.class);
            annotationsAttribute.addAnnotation(o2mAnnot);
            annotationsAttribute.addAnnotation(joinColumnAnnot);
            annotationsAttribute.addAnnotation(persisterAnnot);
        }
        else
        {
            final Annotation o2mAnnot = createOneToManyBackAnnotation(
                    constPool, relatedAttributeCode, relatedClassName);
            annotationsAttribute.addAnnotation(o2mAnnot);
        }

        addAttribute(method, annotationsAttribute);
    }
}
