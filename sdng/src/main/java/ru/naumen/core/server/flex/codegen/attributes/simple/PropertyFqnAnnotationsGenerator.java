package ru.naumen.core.server.flex.codegen.attributes.simple;

import static javassist.bytecode.AnnotationsAttribute.visibleTag;
import static org.hibernate.annotations.OptimisticLockType.NONE;
import static ru.naumen.core.server.flex.codegen.JavassistHelper.*;
import static ru.naumen.core.server.flex.codegen.attributes.simple.SimpleGeneratorHelper.getGeneratedAnnotationColumnLength;

import javassist.CtClass;
import javassist.CtMethod;
import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.ConstPool;
import javassist.bytecode.annotation.Annotation;
import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.flex.codegen.GenContext;
import ru.naumen.core.server.flex.codegen.TypeAnnotationGeneratorHelper.TypeAnnotationGenerator;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Генератор аннотаций для методов доступа, для flex-атрибутов простых типов.
 *
 * <AUTHOR>
 * @since 18.12.2023
 */
public class PropertyFqnAnnotationsGenerator extends PropertyAnnotationsGenerator
{

    public PropertyFqnAnnotationsGenerator(TypeAnnotationGenerator typeAnnotationGenerator)
    {
        super(typeAnnotationGenerator);
    }

    @Override
    public void generate(Attribute attribute, CtMethod method, CtClass persistentClass, GenContext context)
    {
        final ConstPool constPool = getConstPool(persistentClass);
        final Integer maxLength = getGeneratedAnnotationColumnLength(attribute);
        final Annotation columnAnnotation = createColumnAnnotation(constPool, FlexHelper.getColumnName(attribute),
                true, false, false, maxLength);
        final Annotation optimisticLockAnnotation = createOptimisticLockingAnnotation(constPool, NONE);
        final Annotation accesssAnnotation = createPropertyAccessAnnotation(constPool);
        final Annotation mappinngAnnotation = createMappingNameAnnotation(constPool, attribute.getPropertyFqn());

        final AnnotationsAttribute annotationsAttribute = new AnnotationsAttribute(constPool, visibleTag);
        annotationsAttribute.addAnnotation(columnAnnotation);
        annotationsAttribute.addAnnotation(optimisticLockAnnotation);
        annotationsAttribute.addAnnotation(accesssAnnotation);
        annotationsAttribute.addAnnotation(mappinngAnnotation);
        typeAnnotationGenerator.generate(constPool).forEach(annotationsAttribute::addAnnotation);

        addAttribute(method, annotationsAttribute);
    }
}
