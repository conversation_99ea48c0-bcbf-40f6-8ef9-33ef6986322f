package ru.naumen.core.server.flex.codegen.attributes.simple.specific;

import jakarta.inject.Inject;
import ru.naumen.common.server.hibernate.ColorUserType;
import ru.naumen.common.shared.utils.Color;
import ru.naumen.core.server.flex.attr.AttrStrategyComponent;
import ru.naumen.core.server.flex.codegen.TypeAnnotationGeneratorHelper.UserTypeAnnotationGenerator;
import ru.naumen.core.server.flex.codegen.attributes.simple.SimpleAttributeGenerator;
import ru.naumen.metainfo.shared.Constants;

/**
 * Генерирует методы и необходимые аннотации для атрибута типа Цвет.
 *
 * <AUTHOR>
 * @since 18.12.2023
 */
@AttrStrategyComponent(types = { Constants.ColorAttributeType.CODE })
public class ColorAttributeGenerator extends SimpleAttributeBaseGenerator
{
    @Inject
    public ColorAttributeGenerator()
    {
        super(new SimpleAttributeGenerator(Color.class, new UserTypeAnnotationGenerator(ColorUserType.class), true));
    }
}