package ru.naumen.core.server.flex.codegen.attributes.simple.specific;

import static ru.naumen.core.server.flex.codegen.TypeAnnotationGeneratorHelper.STRING_GENERATOR;

import jakarta.inject.Inject;
import ru.naumen.core.server.flex.attr.AttrStrategyComponent;
import ru.naumen.core.server.flex.codegen.attributes.simple.SimpleAttributeGenerator;
import ru.naumen.metainfo.shared.Constants;

/**
 * Генерирует методы и необходимые аннотации для атрибута типа Статус.
 *
 * <AUTHOR>
 * @since 18.12.2023
 */
@AttrStrategyComponent(types = { Constants.StateAttributeType.CODE })
public class StateAttributeGenerator extends SimpleAttributeBaseGenerator
{
    @Inject
    public StateAttributeGenerator()
    {
        super(new SimpleAttributeGenerator(String.class, STRING_GENERATOR, false));
    }
}