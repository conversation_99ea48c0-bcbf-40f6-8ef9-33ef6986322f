package ru.naumen.core.server.flex.codegen.attributes.simple.specific;

import static ru.naumen.core.server.flex.codegen.TypeAnnotationGeneratorHelper.EMPTY_GENERATOR;

import jakarta.inject.Inject;
import ru.naumen.core.server.flex.attr.AttrStrategyComponent;
import ru.naumen.core.server.flex.codegen.attributes.simple.SimpleAttributeGenerator;
import ru.naumen.metainfo.shared.Constants;

/**
 * Генерирует методы и необходимые аннотации для атрибута типа Текст.
 *
 * <AUTHOR>
 * @since 18.12.2023
 */
@AttrStrategyComponent(types = { Constants.TextAttributeType.CODE })
public class TextAttributeGenerator extends SimpleAttributeBaseGenerator
{
    @Inject
    public TextAttributeGenerator()
    {
        super(new SimpleAttributeGenerator(String.class, EMPTY_GENERATOR, true));
    }
}
