package ru.naumen.core.server.flex.spi;

import static ru.naumen.core.server.hibernate.PlannedVersionDDLTool.getPlannedVersionIdentifierName;
import static ru.naumen.core.server.hibernate.PlannedVersionDDLTool.isVersionedIdentifier;

import java.util.Properties;

import ru.naumen.core.server.flex.FlexHelper;

/**
 * Производит создание ограничений объектов для проверки целостности структуры БД
 *
 * <AUTHOR>
 * @since 22.07.2025
 */
public abstract class AbstractConstraintSchemaUpdater implements SchemaUpdater
{
    /**
     * Получить имя ограничения
     * @param tableName имя таблицы
     */
    protected abstract String getConstraintName(String tableName);

    /**
     * Добавление SQL ограничений таблицы для фабрики планируемых версий. Необходимо, чтобы множества SQL запросов
     * 2-х фабрик (обычной и планируемой) не перезаписывали друг друга.
     * @param tableName имя таблицы
     * @param existed текущие SQL запросы в метаиформации
     * @param processed SQL запросы для записи в метаиформацию
     */
    protected void addConstraintIfFromVersionsTable(String tableName, Properties existed,
            Properties processed)
    {
        if (!isVersionedIdentifier(tableName))
        {
            String plannedTable = getPlannedVersionIdentifierName(tableName, FlexHelper.TABLE_PREFIX);
            String constraintName = getConstraintName(plannedTable);
            String sql = (String)existed.remove(constraintName);
            if (sql != null)
            {
                processed.put(constraintName, sql);
            }
        }
    }
}
