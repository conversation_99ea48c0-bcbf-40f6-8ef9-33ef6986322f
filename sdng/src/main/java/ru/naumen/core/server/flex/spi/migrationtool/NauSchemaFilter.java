package ru.naumen.core.server.flex.spi.migrationtool;

import org.hibernate.boot.model.relational.Namespace;
import org.hibernate.boot.model.relational.Sequence;
import org.hibernate.mapping.Table;
import org.hibernate.tool.schema.spi.SchemaFilter;

/**
 * Фильтр мигратора схемы, в котором отключены все локации миграции
 *
 * <AUTHOR>
 * @since 18.03.2024
 */
public final class NauSchemaFilter implements SchemaFilter
{
    public static final NauSchemaFilter INSTANCE = new NauSchemaFilter();

    private NauSchemaFilter()
    {
    }

    @Override
    public boolean includeNamespace(Namespace namespace)
    {
        return false;
    }

    @Override
    public boolean includeTable(Table table)
    {
        return false;
    }

    @Override
    public boolean includeSequence(Sequence sequence)
    {
        return false;
    }
}