package ru.naumen.core.server.hibernate;

import java.io.Serial;

import org.hibernate.type.descriptor.WrapperOptions;
import org.hibernate.type.descriptor.java.AbstractClassJavaType;
import org.hibernate.type.descriptor.java.LongJavaType;

import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UuidHelper;

/**
 * Hibernate тип атрибутов entity используемый для primary key колонок. Особенностью таких колонок является то, что
 * в базе данных они храняться в виде целого числа, а очень часто с ними приходится работать в виде строки - уникального
 * идентификатора в виде {prefix}${id}
 *
 * <AUTHOR>
 */
public class IdJavaType extends AbstractClassJavaType<Object>
{
    @Serial
    private static final long serialVersionUID = -2324256229019282417L;

    private static final Long ZERO = 0L;

    public IdJavaType()
    {
        super(Long.class);
    }

    @Override
    public boolean isInstance(Object value)
    {
        return value instanceof Long
               || value instanceof String
               || value instanceof IUUIDIdentifiable;
    }

    @Override
    public Object fromString(CharSequence string)
    {
        return UuidHelper.toId(string.toString());
    }

    @Override
    public String toString(Object value)
    {
        if (value instanceof String str)
        {
            return str;
        }
        return LongJavaType.INSTANCE.toString((Long)value);
    }

    @Override
    public <X> X unwrap(Object value, Class<X> type, WrapperOptions options)
    {
        if (value instanceof String str)
        {
            return (X)UuidHelper.toId(str);
        }
        if (value instanceof IUUIDIdentifiable iuuidIdentifiable)
        {
            return (X)UuidHelper.toId(iuuidIdentifiable.getUUID());
        }
        return LongJavaType.INSTANCE.unwrap((Long)value, type, options);
    }

    @Override
    public <X> Object wrap(X value, WrapperOptions options)
    {
        if (value instanceof String str)
        {
            return UuidHelper.toId(str);
        }
        if (value instanceof IUUIDIdentifiable iuuidIdentifiable)
        {
            return UuidHelper.toId(iuuidIdentifiable.getUUID());
        }
        return LongJavaType.INSTANCE.wrap(value, options);
    }

    @Override
    public Object getDefaultValue()
    {
        return ZERO;
    }
}