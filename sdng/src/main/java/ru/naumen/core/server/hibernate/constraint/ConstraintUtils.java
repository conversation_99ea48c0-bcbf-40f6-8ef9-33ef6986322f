package ru.naumen.core.server.hibernate.constraint;

import static ru.naumen.commons.server.utils.PredicateUtils.isPropertyEquals;
import static ru.naumen.core.server.hibernate.constraint.Constraint.INLINE;

import com.google.common.base.Predicate;

/**
 * <AUTHOR>
 * @since 18.04.2012
 */
public class ConstraintUtils
{
    public static Predicate<Constraint> isInlineConstraint(boolean inline)
    {
        return isPropertyEquals(Constraint.class, INLINE, inline);
    }
}
