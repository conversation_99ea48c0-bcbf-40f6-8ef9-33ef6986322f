package ru.naumen.core.server.hibernate.dialect.function;

import java.util.List;

import org.hibernate.query.ReturnableType;
import org.hibernate.query.sqm.function.AbstractSqmSelfRenderingFunctionDescriptor;
import org.hibernate.query.sqm.produce.function.StandardFunctionReturnTypeResolvers;
import org.hibernate.sql.ast.SqlAstTranslator;
import org.hibernate.sql.ast.spi.SqlAppender;
import org.hibernate.sql.ast.tree.SqlAstNode;
import org.hibernate.sql.ast.tree.expression.Literal;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.spi.TypeConfiguration;

import ru.naumen.core.server.script.api.criteria.IApiCriteriaColumn;

/**
 * Функция усечения даты, специфичная для Oracle.<br>
 * Преобразует общие значения, описанные в
 * {@link ru.naumen.core.server.script.api.ISelectClauseApi#truncDate(IApiCriteriaColumn, String)},
 * специфичные для Oracle.<br>
 * Позволяет использовать все допустимые значения для oracle, если указывать их явно в поле field.<br>
 * <a href='https://docs.oracle.com/cd/B19306_01/server.102/b14200/functions230.htm#i1002084'>ROUND and TRUNC Date Functions</a>
 *
 * <AUTHOR>
 * @since 15.11.2021
 */
public class OracleTruncDateSQLFunction extends AbstractSqmSelfRenderingFunctionDescriptor
{
    public OracleTruncDateSQLFunction(String name, TypeConfiguration typeConfiguration)
    {
        super(name, null, StandardFunctionReturnTypeResolvers.invariant(
                typeConfiguration.getBasicTypeRegistry().resolve(StandardBasicTypes.TIMESTAMP)), null);
    }

    @Override
    public void render(
            SqlAppender sqlAppender,
            List<? extends SqlAstNode> sqlAstArguments,
            ReturnableType<?> returnType,
            SqlAstTranslator<?> walker)
    {
        sqlAppender.appendSql("trunc(");
        sqlAstArguments.get(0).accept(walker);
        if (sqlAstArguments.size() > 1)
        {
            sqlAppender.appendSql(", ");
            Literal dateField = (Literal)sqlAstArguments.get(1);
            sqlAppender.appendSql(mapDateFieldToOracleDateField((String)dateField.getLiteralValue()));
        }
        sqlAppender.appendSql(")");
    }

    private static String mapDateFieldToOracleDateField(String arg)
    {
        switch (arg)
        {
            case "minute":
                return "'MI'";
            case "hour":
                return "'HH'";
            case "day":
                return "'DD'";
            case "week":
                return "'IW'";
            case "month":
                return "'MM'";
            case "quarter":
                return "'Q'";
            case "year":
                return "'YY'";
            default:
                return arg;
        }
    }
}
