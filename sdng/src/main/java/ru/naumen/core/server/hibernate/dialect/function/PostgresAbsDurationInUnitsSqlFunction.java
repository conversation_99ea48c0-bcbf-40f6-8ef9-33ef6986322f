package ru.naumen.core.server.hibernate.dialect.function;

import org.hibernate.sql.ast.SqlAstTranslator;
import org.hibernate.sql.ast.spi.SqlAppender;
import org.hibernate.sql.ast.tree.SqlAstNode;
import org.hibernate.type.spi.TypeConfiguration;

/**
 * Реализация {@link ru.naumen.core.server.hibernate.HibernateConstants.SQLFunctions#ABS_DURATION_IN_UNITS} для
 * PostgreSQL
 *
 * <AUTHOR>
 * @since 20.01.2021
 */
public class PostgresAbsDurationInUnitsSqlFunction extends AbstractAbsDurationInUnitsSqlFunction
{
    private static final String TIMESTAMP_FRAGMENT = ")::timestamp - (";

    public PostgresAbsDurationInUnitsSqlFunction(String name, TypeConfiguration typeConfiguration)
    {
        super(name, typeConfiguration);
    }

    @Override
    protected void renderInt(SqlAppender sqlAppender, SqlAstNode leftColumn, SqlAstNode rightColumn,
            DateTimeDurationUnits units, SqlAstTranslator<?> walker)
    {
        switch (units)
        {
            case DAY:
                sqlAppender.appendSql("abs(extract(day from ((");
                leftColumn.accept(walker);
                sqlAppender.appendSql(TIMESTAMP_FRAGMENT);
                rightColumn.accept(walker);
                sqlAppender.appendSql(")::timestamp)))::int");
                break;
            case HOUR:
                sqlAppender.appendSql("trunc(abs(extract(epoch from ((");
                leftColumn.accept(walker);
                sqlAppender.appendSql(TIMESTAMP_FRAGMENT);
                rightColumn.accept(walker);
                sqlAppender.appendSql(")::timestamp))) / 3600)::int");
                break;
            case MINUTE:
                sqlAppender.appendSql("trunc(abs(extract(epoch from ((");
                leftColumn.accept(walker);
                sqlAppender.appendSql(TIMESTAMP_FRAGMENT);
                rightColumn.accept(walker);
                sqlAppender.appendSql(")::timestamp))) / 60)::int");
                break;
            case SECOND:
                sqlAppender.appendSql("abs(extract(epoch from ((");
                leftColumn.accept(walker);
                sqlAppender.appendSql(TIMESTAMP_FRAGMENT);
                rightColumn.accept(walker);
                sqlAppender.appendSql(")::timestamp)))::int");
                break;
            case WEEK:
                sqlAppender.appendSql("trunc(abs(extract(day from ((");
                leftColumn.accept(walker);
                sqlAppender.appendSql(TIMESTAMP_FRAGMENT);
                rightColumn.accept(walker);
                sqlAppender.appendSql(")::timestamp))) / 7)::int");
                break;
            default:
                throw new IllegalArgumentException(String.format(UNITS_NOT_SUPPORTED_FMT, units));
        }
    }
}
