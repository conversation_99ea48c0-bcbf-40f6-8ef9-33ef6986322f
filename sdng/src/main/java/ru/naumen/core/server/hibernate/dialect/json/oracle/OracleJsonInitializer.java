package ru.naumen.core.server.hibernate.dialect.json.oracle;

import static ru.naumen.core.server.hibernate.dialect.json.JsonFiledDataType.*;

import org.hibernate.boot.model.FunctionContributions;
import org.hibernate.dialect.Dialect;
import org.hibernate.type.BasicTypeReference;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.descriptor.sql.internal.DdlTypeImpl;
import org.hibernate.type.descriptor.sql.spi.DdlTypeRegistry;

import ru.naumen.core.server.hibernate.HibernateConstants.JsonFunctions;
import ru.naumen.core.server.hibernate.dialect.SQLFunctionTemplate;
import ru.naumen.core.server.hibernate.dialect.json.JsonExpressionTemplateGenerator;
import ru.naumen.core.server.hibernate.dialect.json.JsonFiledDataType;
import ru.naumen.core.server.hibernate.dialect.json.JsonSupportInitializer;
import ru.naumen.core.server.hibernate.type.ExtraSqlTypes;

/**
 * Вспомогательный класс для инициализации средств для работы с JSON в Oracle.
 *
 * <AUTHOR>
 * @since Oct 14, 2021
 */
public class OracleJsonInitializer implements JsonSupportInitializer
{
    private final JsonExpressionTemplateGenerator templateGenerator = new OracleJsonExpressionTemplateGenerator();

    @Override
    public void registerFunctions(FunctionContributions functionContributions)
    {
        register(functionContributions, JsonFunctions.JSON_QUERY, jsonQuery());
        register(functionContributions, JsonFunctions.JSON_GET_BOOL, jsonValue(StandardBasicTypes.BOOLEAN, BOOL));
        register(functionContributions, JsonFunctions.JSON_GET_INTEGER, jsonValue(StandardBasicTypes.INTEGER, INTEGER));
        register(functionContributions, JsonFunctions.JSON_GET_LONG, jsonValue(StandardBasicTypes.LONG, LONG));
        register(functionContributions, JsonFunctions.JSON_GET_DOUBLE, jsonValue(StandardBasicTypes.DOUBLE, DOUBLE));
        register(functionContributions, JsonFunctions.JSON_GET_STRING, jsonValue(StandardBasicTypes.STRING, STRING));

        register(functionContributions, JsonFunctions.JSON_GET_TEXT,
                jsonValue(StandardBasicTypes.MATERIALIZED_CLOB, TEXT));
        register(functionContributions, JsonFunctions.JSON_ARRAY, jsonArray());
        register(functionContributions, JsonFunctions.JSON_HAS_KEY, jsonHasKey());
    }

    @Override
    public void registerColumnType(DdlTypeRegistry registrar, Dialect dialect)
    {
        registrar.addDescriptor(new DdlTypeImpl(ExtraSqlTypes.JSON, "clob", dialect));
    }

    private static void register(FunctionContributions functionContributions, String name, SQLFunctionTemplate function)
    {
        functionContributions.getFunctionRegistry().registerPattern(name, function.template(),
                functionContributions.getTypeConfiguration().getBasicTypeRegistry().resolve(function.type()));
    }

    private static SQLFunctionTemplate jsonQuery()
    {
        return new SQLFunctionTemplate(StandardBasicTypes.STRING, "json_query(?1, ?2)");
    }

    private SQLFunctionTemplate jsonValue(BasicTypeReference<?> returnType, JsonFiledDataType dataType)
    {
        return new SQLFunctionTemplate(returnType,
                String.format(templateGenerator.getExpressionTemplate(dataType), "?1", "?2"));
    }

    private static SQLFunctionTemplate jsonArray()
    {
        return new SQLFunctionTemplate(StandardBasicTypes.LONG,
                "(SELECT value FROM json_table(json_query(?1, ?2), '$[*]' columns (value NUMBER PATH '$')))");
    }

    private static SQLFunctionTemplate jsonHasKey()
    {
        return new SQLFunctionTemplate(StandardBasicTypes.LONG,
                "(SELECT value FROM json_table(?1, '$' columns (value NUMBER exists PATH ?2)))");
    }
}
