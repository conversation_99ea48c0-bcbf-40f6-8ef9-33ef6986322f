package ru.naumen.core.server.hibernate.uuid;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;

import org.hibernate.LazyInitializationException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.internal.CoreMessageLogger;
import org.hibernate.internal.util.ReflectHelper;
import org.hibernate.proxy.ProxyConfiguration;
import org.hibernate.proxy.pojo.BasicLazyInitializer;
import org.hibernate.proxy.pojo.bytebuddy.ByteBuddyInterceptor;
import org.hibernate.proxy.pojo.bytebuddy.SerializableProxy;
import org.hibernate.type.CompositeType;
import org.jboss.logging.Logger;

import com.google.common.collect.Maps;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UuidHelper;

/**
 * Копипаст {@link ByteBuddyInterceptor} для изменения логики получения uuid-а объекта без поднятия инстанса объекта.
 *
 * <AUTHOR>
 * @see ByteBuddyInterceptor
 */
@SuppressWarnings("PMD.AvoidSuppressingThrowablesAndErrorsRule")
public final class UUIDIdentifiableByteBuddyLazyInitializer extends BasicLazyInitializer implements ProxyConfiguration.Interceptor
{
    private static final CoreMessageLogger LOG = Logger.getMessageLogger(CoreMessageLogger.class,
            ByteBuddyInterceptor.class.getName());

    private static final Map<String, String> CLASS_TO_PREFIX_CACHE = Maps.newConcurrentMap();
    private static final Class<?>[] EMPTY_CLASS_ARRAY = new Class<?>[0];

    private Class[] interfaces;

    private String uuid;

    UUIDIdentifiableByteBuddyLazyInitializer( // NOSONAR
            final String entityName,
            final Class<?> persistentClass,
            final Class[] interfaces,
            final Object id,
            final Method getIdentifierMethod,
            final Method setIdentifierMethod,
            final CompositeType componentIdType,
            final SharedSessionContractImplementor session,
            final boolean overridesEquals)
    {
        super(entityName, persistentClass, id, getIdentifierMethod, setIdentifierMethod, componentIdType, session,
                overridesEquals);
        this.interfaces = interfaces;
    }

    @Override
    public Object intercept(Object proxy, Method thisMethod, Object[] args) throws Throwable // NOSONAR
    {
        String methodName = thisMethod.getName();
        Object result;
        try
        {
            try // NOSONAR
            {
                if (0 == args.length)
                {
                    if ("getId".equals(methodName))
                    {
                        return getIdentifier();
                    }
                    // получаем uuid без поднятия объекта из БД
                    if ("getUUID".equals(methodName))
                    {
                        return getUuid();
                    }
                    // стандартная реализацию toString возвращает uuid-объекта
                    if ("toString".equals(methodName))
                    {
                        return getUuid() + (isLoaded() ? " (proxy, loaded)" : " (proxy)");
                    }

                    if ("hashCode".equals(methodName) && (proxy instanceof IUUIDIdentifiable))
                    {
                        return UuidHelper.hashCode((Long)getIdentifier());
                    }
                }
                if (args.length == 1 && "equals".equals(methodName))
                {
                    Object other = args[0];
                    if (null == other)
                    {
                        return false;
                    }
                    if (other instanceof IUUIDIdentifiable iuuidIdentifiable)
                    {
                        return getUuid().equals(iuuidIdentifiable.getUUID());
                    }
                }
            }
            catch (RuntimeException e)
            {
                LOG.debug(e.getMessage(), e);
            }
            result = this.invoke(thisMethod, args, proxy);
        }
        catch (Throwable t)
        {
            throw new Exception(t.getCause()); // NOSONAR
        }
        if (result == INVOKE_IMPLEMENTATION)
        {
            Object target = getTargetObject();
            final Object returnValue;
            try
            {
                if (ReflectHelper.isPublic(persistentClass, thisMethod))
                {
                    if (!thisMethod.getDeclaringClass().isInstance(target))
                    {
                        throw new ClassCastException(target.getClass().getName());
                    }
                    returnValue = thisMethod.invoke(target, args);
                }
                else
                {
                    thisMethod.setAccessible(true); // NOSONAR
                    returnValue = thisMethod.invoke(target, args);
                }

                if (returnValue == target)
                {
                    if (returnValue.getClass().isInstance(proxy))
                    {
                        return proxy;
                    }
                    else
                    {
                        LOG.narrowingProxy(returnValue.getClass());
                    }
                }
                return returnValue;
            }
            catch (InvocationTargetException ite)
            {
                throw ite.getTargetException();
            }
        }
        else
        {
            return result;
        }
    }

    /**
     * Метод подгружает lazy объект. В случае, если получается ошибка загрузки, пробует создать новую сессию (если
     * возникла ситуация, что, например, сессия, в которой объект получили, уже закрыта) и подгрузить объект
     * @return объект
     */
    private Object getTargetObject()
    {
        Session session = null;
        try
        {
            //Первая попытка
            return getImplementation();
        }
        catch (LazyInitializationException e)
        {
            //Логируем ошибку и перезапускаем сессию
            LOG.debug(e.getMessage(), e);
            session = ((SessionFactory)SpringContext.getInstance().getBean("sessionFactory")).openSession();
            setSession((SharedSessionContractImplementor)session);
            return getImplementation();
        }
        finally
        {
            if (session != null)
            {
                session.close();
            }
        }
    }

    public boolean isLoaded()
    {
        return null != getTarget();
    }

    protected Object getUuid() throws NoSuchMethodException, IllegalAccessException, InvocationTargetException
    {
        if (null == uuid)
        {
            String className = persistentClass.getName();
            String prefix = CLASS_TO_PREFIX_CACHE.get(className); // NOSONAR
            if (prefix == null)
            {

                Method method = persistentClass.getMethod(FlexHelper.UUID_STATIC_METHOD, EMPTY_CLASS_ARRAY);
                prefix = (String)method.invoke(null, (Object[])null);
                CLASS_TO_PREFIX_CACHE.put(className, prefix);
            }

            uuid = UuidHelper.toUuid((Long)getIdentifier(), prefix);
        }
        return uuid;
    }

    //@formatter:off
    @Override
    protected Object serializableProxy()
    {
        return new SerializableProxy(
                getEntityName(),
                persistentClass,
                interfaces,
                getIdentifier(),
                isReadOnlySettingAvailable() ? Boolean.valueOf(isReadOnly()) : isReadOnlyBeforeAttachedToSession(), //NOPMD нужно приведение к объекту, иначе если второй тип null будет NPE при анбоксе
                getSessionFactoryUuid(),
                getSessionFactoryName(),
                isAllowLoadOutsideTransaction(),
                getIdentifierMethod,
                setIdentifierMethod,
                componentIdType
        );
    }
    //@formatter:on
}
