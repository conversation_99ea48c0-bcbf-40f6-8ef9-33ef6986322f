/**
 *
 */
package ru.naumen.core.server.inputmask;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.export.ExportSource;
import ru.naumen.core.server.metastorage.MetaStorageException;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.metainfo.server.Constants;

/**
 * Компонент приложения, предназначен для импорта/экспорта расширения настроек плагина inputmask:
 * <AUTHOR>
 * @since 15 дек. 2015 г.
 *
 */
@Component("inputmask")
public class InputmaskExtensionsExportSource implements ExportSource<InputmaskExtension>
{
    @Inject
    private MetaStorageService metaStorageService;

    @Override
    public InputmaskExtension exportObject(String options)
    {
        return metaStorageService.get(Constants.INPUTMASK_EXTENSIONS, Constants.INPUTMASK_EXTENSIONS);
    }

    @Override
    public boolean hasExportObject()
    {
        try
        {
            metaStorageService.get(Constants.INPUTMASK_EXTENSIONS, Constants.INPUTMASK_EXTENSIONS);
            return true;
        }
        catch (MetaStorageException e)
        {
            return false;
        }
    }
}