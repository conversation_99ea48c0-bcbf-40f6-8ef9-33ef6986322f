package ru.naumen.core.server.jta.ds.context;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.jta.ds.DataSourceType;
import ru.naumen.core.server.jta.ds.context.manager.DataSourceBoundInvocationManager;

/**
 * Реализация провайдера информации о режиме Read Only на основе настроек приложения и окружающего контекста.
 * <AUTHOR>
 * @since Dec 15, 2024
 */
@Component
public class ReadOnlyContextProviderImpl implements ReadOnlyContextProvider
{
    private final DataSourceBoundInvocationManager invocationManager;

    @Inject
    public ReadOnlyContextProviderImpl(DataSourceBoundInvocationManager invocationManager)
    {
        this.invocationManager = invocationManager;
    }

    @Override
    public boolean isReadOnly()
    {
        return AppContext.isReadOnly()
               || invocationManager.getCurrentInvocation().getBoundDataSourceType() == DataSourceType.READ_ONLY;
    }
}
