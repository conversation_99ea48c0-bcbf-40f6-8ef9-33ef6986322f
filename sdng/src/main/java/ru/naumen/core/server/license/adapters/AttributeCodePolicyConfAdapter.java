package ru.naumen.core.server.license.adapters;

import static ru.naumen.core.shared.Constants.HasResponsible.RESPONSIBLE;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.license.conf.policy.AttributePolicyConf;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Расширение {@link AttributePolicyConfAdapter}, когда разрешения заданы кодами атрибутов
 *
 * <AUTHOR>
 * @since 01.07.2024
 */
public class AttributeCodePolicyConfAdapter extends AttributePolicyConfAdapter
{
    private final String code;

    public AttributeCodePolicyConfAdapter(String code, @Nullable Boolean viewable, @Nullable Boolean editable,
            @Nullable Boolean hardcoded)
    {
        super(viewable, editable, hardcoded);
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    /**
     * Создаёт конфигурацию политики атрибута
     */
    protected AttributePolicyConf createAttributePolicyConf()
    {
        AttributePolicyConf attributePolicyConf = new AttributePolicyConf();
        attributePolicyConf.setEditable(editable);
        attributePolicyConf.setViewable(viewable);
        attributePolicyConf.setCode(code);
        return attributePolicyConf;
    }

    @Override
    public boolean add(MetaClassPolicyConfAdapter mcConf)
    {
        if (hardcoded == null)
        {
            return false;
        }
        // Системный атрибут "Ответственный" должен быть запрещен для редактирования
        if (code.equals(RESPONSIBLE))
        {
            editable = Boolean.FALSE;
        }
        return hardcoded ? mcConf.addSystemAttribute(this) : mcConf.addUserAttribute(this);
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(code);
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }

        AttributeCodePolicyConfAdapter that = (AttributeCodePolicyConfAdapter)o;
        return code.equals(that.code);
    }
}