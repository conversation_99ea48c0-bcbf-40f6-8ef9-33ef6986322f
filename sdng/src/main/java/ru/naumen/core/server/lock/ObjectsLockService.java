package ru.naumen.core.server.lock;

import java.util.Collection;

/**
 * Сервис для возможности получить доступ только из одного потока
 *
 * <AUTHOR>
 * @since Jan 20, 2014
 */
public interface ObjectsLockService
{
    /**
     * Заблокировать доступ к бизнесс-объектам из других потоков
     * @param objects
     */
    void lock(Collection<String> objects);

    /**
     * Разрешить доступ к бизнесс-объектам из других потоков
     * @param objects
     */
    void unlock(Collection<String> objects);
}
