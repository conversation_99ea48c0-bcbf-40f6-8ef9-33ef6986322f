package ru.naumen.core.server.mail;

import static ru.naumen.commons.shared.utils.CollectionUtils.isEmpty;

import java.util.Date;
import java.util.List;

import org.hibernate.type.StandardBasicTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.bo.DefaultDao;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UuidHelper;

/**
 * Реализация {@link MailLogRecordDao}
 *
 * <AUTHOR>
 *
 */
@Component
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
public class MailLogRecordDaoBean extends DefaultDao<MailLogRecord> implements MailLogRecordDao
{
    private static final Logger LOG = LoggerFactory.getLogger(MailLogRecordDaoBean.class);

    @Inject
    private FastFileCleaner fileCleaner;

    @Value("${ru.naumen.mail.batch_size}")
    private Integer batchSize;

    public MailLogRecordDaoBean()
    {
        super(Constants.MailLogRecord.CLASS_ID);
    }

    @Override
    public void delete(MailLogRecord mailLogRecord)
    {
        getSession().remove(mailLogRecord);
    }

    public void deleteBatch(final List<Long> ids)
    {
        TransactionRunner.run(TransactionType.NEW, () ->
        {
            long start = System.currentTimeMillis();

            //С помощью SQL очишаем таблицы, содержащие коллекции примитивных элементов примитивных типов,
            //связанных с удаляемыми записями.
            getSession().createNativeQuery("delete from tbl_mail_rec_added_comments where mail_rec_id in (:ids)")
                    .addSynchronizedQuerySpace("tbl_mail_rec_added_comments")
                    .setParameterList("ids", ids).executeUpdate();
            getSession().createNativeQuery("delete from tbl_mail_rec_created_objects where mail_rec_id in (:ids)")
                    .addSynchronizedQuerySpace("tbl_mail_rec_created_objects")
                    .setParameterList("ids", ids).executeUpdate();
            getSession().createNativeQuery("delete from tbl_mail_rec_editedobjects where mail_id in (:ids)")
                    .addSynchronizedQuerySpace("tbl_mail_rec_editedobjects")
                    .setParameterList("ids", ids).executeUpdate();
            getSession().createNativeQuery("delete from tbl_mail_rec_edited_comments where mail_rec_id in (:ids)")
                    .addSynchronizedQuerySpace("tbl_mail_rec_edited_comments")
                    .setParameterList("ids", ids).executeUpdate();

            getSession().createNativeQuery(
                            "update tbl_mail_log_record set mailcontentfile_id = null where id in (:ids)")
                    .addSynchronizedEntityClass(MailLogRecord.class)
                    .setParameterList("ids", ids).executeUpdate();

            //Удаляем файлы, связанные с записями
            for (Long id : ids)
            {
                fileCleaner.clearFiles(UuidHelper.toUuid(id, Constants.MailLogRecord.CLASS_ID));
            }

            //С помощью hql удаляем сами записи, которые теперь ничего не держит
            getSession().createQuery("delete MailLogRecord where id in (:ids)").setParameterList("ids", ids)
                    .executeUpdate();

            LOG.debug(String.format("Done(%d):Batch of %d mail log records deleted",
                    System.currentTimeMillis() - start, ids.size()));
        });
    }

    @Override
    public long deleteBatchOlderThan(Date threeshold, long lastId, int batchSize)
    {
        List<Long> toDelete = getBatch(threeshold, lastId);
        long lastIdx = isEmpty(toDelete) ? -1 : toDelete.getLast();
        try
        {
            if (lastIdx > 0)
            {
                deleteBatch(toDelete);
            }
        }
        catch (Exception e)
        {
            LOG.error(e.getMessage(), e);
        }
        return lastIdx;
    }

    @Override
    public void deleteOlderThan(Date threeshold)
    {
        //В переменной хранится id последнего объекта, выбранного для удаления
        //Нужна для обеспечения возможности пропуска пачек в процессе удаления 
        //которых возникла ошибка
        long lastId = 0;
        do
        {
            lastId = deleteBatchOlderThan(threeshold, lastId, batchSize);
        }
        while (lastId > 0);
    }

    @Override
    public void deleteRecordsOfMailWithIds(List<Long> mailIds)
    {
        @SuppressWarnings("unchecked")
        List<Long> ids = getSession()
                .createQuery("select r.id from MailLogRecord r join r.mail m where m.id in (:mailIds)")
                .setParameterList("mailIds", mailIds).list();
        if (isEmpty(ids))
        {
            return;
        }
        deleteBatch(ids);
    }

    @Override
    public void update(MailLogRecord mailLogRecord)
    {
        getSession().merge(mailLogRecord);
    }

    @SuppressWarnings({ "unchecked" })
    private List<Long> getBatch(final Date date, final long lastId)
    {
        return TransactionRunner.call(() -> getSession().createQuery("select id from MailLogRecord "
                                                                     + "where eventDate < :date and id > :lastId "
                                                                     + "order by eventDate")
                .setParameter("date", date)
                .setParameter("lastId", lastId, StandardBasicTypes.LONG)
                .setMaxResults(batchSize)
                .list());
    }
}
