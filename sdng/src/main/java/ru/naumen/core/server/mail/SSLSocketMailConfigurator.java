package ru.naumen.core.server.mail;

import java.security.GeneralSecurityException;
import java.util.Properties;

import jakarta.inject.Inject;

import javax.net.ssl.X509ExtendedTrustManager;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.keystore.KeyStoreService;
import ru.naumen.mailreader.server.receiver.DummyTrustManager;

import org.eclipse.angus.mail.util.MailSSLSocketFactory;

/**
 * Вспомогательный класс для заполнения параметров подключения к почтовому серверу с self-signed certificate
 * <AUTHOR>
 * @since 04.09.2018
 */
@Component
public class SSLSocketMailConfigurator
{
    private static final Logger LOG = LoggerFactory.getLogger(ConnectionValidationService.class);
    private final KeyStoreService keyStoreService;

    @Inject
    public SSLSocketMailConfigurator(KeyStoreService keyStoreService)
    {
        this.keyStoreService = keyStoreService;
    }

    public static void setDummySSLSocketFactoryForProtocol(Properties props, String protocol)
    {
        try
        {
            // Вместо props.setProperty("mail." + protocol + ".ssl.trust", "*")
            MailSSLSocketFactory factory = new MailSSLSocketFactory();
            factory.setTrustManagers(new X509ExtendedTrustManager[] { new DummyTrustManager() });
            props.put("mail." + protocol + ".ssl.socketFactory", factory);
        }
        catch (GeneralSecurityException e)
        {
            LOG.error("Cant create MailSSLSocketFactory for skipCertVerification", e);
        }
    }

    public void setSSLSocketFactoryForProtocolWithLocalKeyStore(Properties props, String protocol)
    {
        try
        {
            MailSSLSocketFactory factory = new MailSSLSocketFactory();
            factory.setTrustManagers(keyStoreService.getAllTrustManagers());
            props.put("mail." + protocol + ".ssl.socketFactory", factory);
        }
        catch (GeneralSecurityException e)
        {
            LOG.error("Cant create MailSSLSocketFactory without skipCertVerification", e);
        }
    }
}
