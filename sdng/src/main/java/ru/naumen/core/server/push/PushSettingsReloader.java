package ru.naumen.core.server.push;

import org.springframework.stereotype.Component;

/**
 * Отвечает за перезагрузку настроек для push-провайдеров
 *
 * <AUTHOR>
 * @since 15.01.2025
 */
public interface PushSettingsReloader
{
    /**
     * Перезагружает настройки для работы с push-провайдером FCM
     */
    void reloadFcmConfig();

    /**
     * Перезагружает настройки для работы с push-провайдером RuStore
     */
    void reloadRuStoreConfig();

    /**
     * Перезагружает настройки для работы с push-провайдером HMS
     */
    void reloadHmsConfig();

    /**
     * На бранч-тестированиях (этап {@code Unit Postgres} с db-тестами) модуль {@code sdng-mobile} с основной
     * реализацией {@link PushSettingsReloader} не подключается, определение бина не находится и контекст не
     * поднимается. Если модуль с основной реализацией не подключится, Spring возьмет эту заглушку и контекст для
     * тестирования поднимется
     *
     * <AUTHOR>
     * @since 14.04.2025
     */
    @Component
    class PushSettingsReloaderImplStub implements PushSettingsReloader
    {
        public static final String MESSAGE_EXCEPTION = "Implementation must be in another module";

        @Override
        public void reloadFcmConfig()
        {
            throw new UnsupportedOperationException(MESSAGE_EXCEPTION);
        }

        @Override
        public void reloadRuStoreConfig()
        {
            throw new UnsupportedOperationException(MESSAGE_EXCEPTION);
        }

        @Override
        public void reloadHmsConfig()
        {
            throw new UnsupportedOperationException(MESSAGE_EXCEPTION);
        }
    }
}