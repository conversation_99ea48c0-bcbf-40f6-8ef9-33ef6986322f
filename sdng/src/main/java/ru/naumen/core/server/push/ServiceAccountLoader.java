package ru.naumen.core.server.push;

import org.springframework.stereotype.Component;

/**
 * Загружает сервисный аккаунт
 *
 * <AUTHOR>
 * @since 16.04.2025
 */
public interface ServiceAccountLoader
{
    /**
     * Перезагружает сервисный аккаунт. Если загрузить аккаунт не удалось, остается старый сервисный аккаунт
     */
    void load();

    /**
     * На бранч-тестированиях (этап {@code Unit Postgres} с db-тестами) модуль {@code sdng-mobile} с основной
     * реализацией {@link ServiceAccountLoader} не подключается, определение бина не находится и контекст не
     * поднимается. Если модуль с основной реализацией не подключится, spring возьмет эту заглушку и контекст для
     * тестирования поднимется
     *
     * <AUTHOR>
     * @since 16.04.2025
     */
    @Component
    class ServiceAccountLoaderImplStub implements ServiceAccountLoader
    {
        @Override
        public void load()
        {
            throw new UnsupportedOperationException("Implementation must be in another module");
        }
    }
}
