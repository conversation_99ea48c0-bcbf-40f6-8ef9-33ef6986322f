package ru.naumen.core.server.scheduler.listener;

import java.util.List;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.listeners.JobListenerSupport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.scheduler.listener.operation.tobe.executed.JobToBeExecutedOperation;
import ru.naumen.core.server.scheduler.listener.operation.veto.TooOftenVetoCheckOperation;
import ru.naumen.core.server.scheduler.service.SystemTaskClassesRegistry;

/**
 * Служит для логирования поведения задач планировщика {@link org.quartz.core.QuartzScheduler}.
 *
 * <AUTHOR>
 */
@Component(value = "jobLogger")
public class JobEventListener extends JobListenerSupport
{
    private static final Logger LOG = LoggerFactory.getLogger(JobEventListener.class);

    private final SystemTaskClassesRegistry systemTaskClassesRegistry;
    /**
     * Операция {@link TooOftenVetoCheckOperation} должна быть последней проверкой на вето среди всех операций, так
     * как задача в работу берется чуть раньше чем указано в дате запуска
     */
    private final List<JobToBeExecutedOperation> preExecuteUserJobOperations;

    @Inject
    public JobEventListener(SystemTaskClassesRegistry systemTaskClassesRegistry,
            List<JobToBeExecutedOperation> preExecuteUserJobOperations)
    {
        this.systemTaskClassesRegistry = systemTaskClassesRegistry;
        this.preExecuteUserJobOperations = preExecuteUserJobOperations;
    }

    @Override
    public String getName()
    {
        return getClass().getSimpleName();
    }

    @Override
    public void jobExecutionVetoed(JobExecutionContext context)
    {
        LOG.debug("The '{}' job execution has been forbidden (\"vetoed\", in terms of Quartz) for some reason.",
                context.getJobDetail().getKey());
    }

    @Override
    public void jobToBeExecuted(JobExecutionContext context)
    {
        LOG.debug("The '{}' job is about to be executed.", context.getJobDetail().getKey());
        String taskCode = context.getJobDetail().getKey().getName();
        if (systemTaskClassesRegistry.getSystemTaskClassesNames().contains(taskCode))
        {
            LOG.debug("Task '{}' is system. Skip custom 'JobToBeExecutedOperation'.", taskCode);
            return;
        }
        preExecuteUserJobOperations.forEach(operation -> operation.preExecute(context));
    }

    @Override
    public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException)
    {
        LOG.debug("The '{}' job has been executed.", context.getJobDetail().getKey());
    }
}
