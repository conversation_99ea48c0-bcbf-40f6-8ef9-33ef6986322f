package ru.naumen.core.server.scheduler.listener;

import java.util.List;

import org.quartz.JobExecutionContext;
import org.quartz.Trigger;
import org.quartz.Trigger.CompletedExecutionInstruction;
import org.quartz.listeners.TriggerListenerSupport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.scheduler.listener.operation.complete.TriggerCompleteOperation;
import ru.naumen.core.server.scheduler.storage.SchedulerUserTaskStorageService;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;

/**
 * Реализация слушателя {@link TriggerListenerSupport} для запуска необходимых операций, при завершении выполнения
 * задачи
 * по определенному триггеру (расписанию).
 *
 * <AUTHOR>
 * @since 10.10.2024
 */
@Component
public class TriggerCompleteListener extends TriggerListenerSupport
{
    private static final Logger LOG = LoggerFactory.getLogger(TriggerCompleteListener.class);

    private final SchedulerUserTaskStorageService schedulerUserTaskStorageService;
    private final TagService tagService;
    private final List<TriggerCompleteOperation> completeOperations;

    @Inject
    public TriggerCompleteListener(
            @Lazy SchedulerUserTaskStorageService schedulerUserTaskStorageService,
            @Lazy TagService tagService,
            List<TriggerCompleteOperation> completeOperations)
    {
        this.schedulerUserTaskStorageService = schedulerUserTaskStorageService;
        this.tagService = tagService;
        this.completeOperations = completeOperations;
    }

    @Override
    public String getName()
    {
        return TriggerCompleteListener.class.getSimpleName();
    }

    @Override
    public void triggerComplete(Trigger trigger, JobExecutionContext context,
            CompletedExecutionInstruction triggerInstructionCode)
    {
        try
        {
            LOG.debug("The '{}' trigger for the '{}' job has been completed.", trigger.getKey(),
                    context.getJobDetail().getKey());
            String taskCode = trigger.getJobKey().getName();
            SchedulerTask task = schedulerUserTaskStorageService.getLightSchedulerTasks(taskCode);
            if (task == null || !tagService.isElementEnabled(task))
            {
                LOG.debug("Task '{}' - not exist or disable!", taskCode);
                return;
            }
            this.completeOperations.forEach(operation -> operation.execute(task, trigger, context));
        }
        catch (Exception e)
        {
            LOG.error("Error run trigger complete operations.", e);
        }
    }
}
