package ru.naumen.core.server.scheduler.listener.operation.complete;

import java.util.Date;

import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.scheduler.manager.QuartzSchedulerManager;
import ru.naumen.metainfo.shared.scheduler.PeriodicTrigger;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfo.shared.scheduler.Trigger.CalculateStrategies;

/**
 * Реализация {@link TriggerCompleteOperation}
 * Выполняет операцию перерасчета всех периодических триггеров (расписаний) задачи, со стратегий
 * <code>"От последнего времени выполнения"</code> (кроме текущего).
 * <br><br>
 * Данные триггеры считают началом своего периода любое выполнение задачи, по любому другому триггеру.
 *
 * <AUTHOR>
 * @since 09.10.2024
 */
@Component
public class RecalculateFromLastExecutionStrategyOperation implements TriggerCompleteOperation
{
    private static final Logger LOG = LoggerFactory.getLogger(RecalculateFromLastExecutionStrategyOperation.class);

    private final QuartzSchedulerManager quartzSchedulerManager;

    @Inject
    public RecalculateFromLastExecutionStrategyOperation(
            @Lazy QuartzSchedulerManager quartzSchedulerManager)
    {
        this.quartzSchedulerManager = quartzSchedulerManager;
    }

    @Override
    public void execute(SchedulerTask task, org.quartz.Trigger trigger, JobExecutionContext context)
    {
        //Для дальнейших операций не должны нарушать целостность поднятой задачи
        String executedTriggerCode = trigger.getKey().getName();
        Date lastExecutionDate = context.getFireTime();
        for (Trigger taskTrigger : task.getTrigger())
        {
            if (!isNeedRecalculate(executedTriggerCode, taskTrigger))
            {
                continue;
            }
            String triggerCode = taskTrigger.getCode();
            try
            {
                quartzSchedulerManager.rescheduleFromLastExecutionDate(triggerCode, task.getCode(),
                        context.getFireTime());
                LOG.debug("Recalculated periodic trigger '{}' from task '{}'. Last execution date: {}",
                        triggerCode, task.getCode(), lastExecutionDate);
            }
            catch (Exception e)
            {
                LOG.atError().setCause(e).log("""
                        Error recalculate periodic trigger '{}' from task '{}' with 'FROM_LAST_EXECUTION' \s
                        strategy.""", triggerCode, task.getCode());
            }
        }
    }

    private static boolean isNeedRecalculate(String executedTriggerCode, Trigger taskTrigger)
    {
        return !executedTriggerCode.equals(taskTrigger.getCode())
               && taskTrigger.isEnabled()
               && taskTrigger instanceof PeriodicTrigger periodicTrigger
               && CalculateStrategies.FROM_LAST_EXECUTION.equals(periodicTrigger.getStrategy());
    }
}
