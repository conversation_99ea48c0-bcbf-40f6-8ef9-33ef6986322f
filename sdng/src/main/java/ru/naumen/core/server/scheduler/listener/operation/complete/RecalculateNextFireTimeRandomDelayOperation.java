package ru.naumen.core.server.scheduler.listener.operation.complete;

import org.quartz.CronTrigger;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.SimpleTrigger;
import org.quartz.Trigger;
import org.quartz.TriggerKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.scheduler.SchedulerProperties;
import ru.naumen.core.server.configuration.beanconditions.scheduler.NextFireTimeRandomDelayEnabledCondition;
import ru.naumen.core.server.scheduler.manager.QuartzSchedulerManager;
import ru.naumen.core.server.scheduler.service.trigger.TriggerCreationService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.scheduler.PeriodicTrigger;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;

/**
 * Реализация {@link TriggerCompleteOperation} <br>
 * Выполняет перевычисление следующей даты выполнения задачи для триггера (расписания), по которому сейчас задача
 * завершила свое выполнение. <br>
 * Расчёт происходит, если включен механизм рандомизации времени запуска задач, при этом он выполняется с учётом
 * случайной задержки в заданном диапазоне. <br>
 *
 * <AUTHOR>
 * @since 08.10.2024
 */
@Component
@Conditional(NextFireTimeRandomDelayEnabledCondition.class)
public class RecalculateNextFireTimeRandomDelayOperation implements TriggerCompleteOperation
{
    private static final Logger LOG = LoggerFactory.getLogger(RecalculateNextFireTimeRandomDelayOperation.class);

    private final QuartzSchedulerManager quartzSchedulerManager;
    private final TriggerCreationService triggerCreationService;
    private final MetainfoUtils metainfoUtils;
    private final MessageFacade messages;
    private final SchedulerProperties schedulerProperties;

    @Inject
    public RecalculateNextFireTimeRandomDelayOperation(
            @Lazy QuartzSchedulerManager quartzSchedulerManager,
            TriggerCreationService triggerCreationService,
            MetainfoUtils metainfoUtils,
            MessageFacade messages,
            SchedulerProperties schedulerProperties)
    {
        this.quartzSchedulerManager = quartzSchedulerManager;
        this.triggerCreationService = triggerCreationService;
        this.metainfoUtils = metainfoUtils;
        this.messages = messages;
        this.schedulerProperties = schedulerProperties;
    }

    /**
     * Перепланирование пользовательской задачи планировщика с периодическим расписанием, при включенном механизме
     * рандомизации запуска задач.<br><br>
     * <b>Для расписаний со стратегией "От даты последнего выполнения" - рандомизация не доступна</b>
     *
     * @param task выполненная задача планировщика
     * @param trigger - старое правило выполнения задачи планировщика, относительно которого происходит перевычисление.
     * @param context контекст выполненной задачи
     */
    @Override
    public void execute(SchedulerTask task, Trigger trigger, JobExecutionContext context)
    {
        //Бизнес процесс предполагает пересчет только включенных периодических расписаний с включенной рандомизацией
        if (trigger instanceof SimpleTrigger simpleTrigger && simpleTrigger.getRepeatCount() == 0
            || !(trigger instanceof CronTrigger))
        {
            return;
        }
        TriggerKey triggerKey = trigger.getKey();
        JobKey jobKey = trigger.getJobKey();
        if (triggerKey == null || jobKey == null)
        {
            return;
        }
        PeriodicTrigger periodicTrigger = (PeriodicTrigger)task.getTrigger(triggerKey.getName());
        if (periodicTrigger == null
            || !periodicTrigger.isEnabled()
            || !periodicTrigger.isRandomizeDelayOn())
        {
            return;
        }
        Trigger newTrigger = triggerCreationService.createUserTaskTrigger(periodicTrigger, triggerKey, jobKey);
        quartzSchedulerManager.rescheduleJob(newTrigger, triggerKey);

        LOG.atInfo().log(() -> messages.getMessage("scheduler.addRandomDelay",
                metainfoUtils.getLocalizedValue(task.getTitle()), task.getCode(),
                schedulerProperties.getRandomDelay()));
    }
}
