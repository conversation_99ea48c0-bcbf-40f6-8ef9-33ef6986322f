package ru.naumen.core.server.scheduler.listener.operation.tobe.executed;

import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.scheduler.storage.SchedulerUserTaskStorageService;
import ru.naumen.metainfo.shared.scheduler.ConcreteDateTrigger;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;

/**
 * Операция отключения триггера с конкретной единичной датой запуска задачи в нашей метаинформации и кеше infinispan
 *
 * <AUTHOR>
 * @since 01.04.2025
 */
@Component
public class DisablingConcreteTriggerOperation implements JobToBeExecutedOperation
{
    private final SchedulerUserTaskStorageService schedulerUserTaskStorageService;

    @Inject
    public DisablingConcreteTriggerOperation(SchedulerUserTaskStorageService schedulerUserTaskStorageService)
    {
        this.schedulerUserTaskStorageService = schedulerUserTaskStorageService;
    }

    @Override
    public void preExecute(JobExecutionContext context)
    {
        disableConcreteTrigger(context);
    }

    private void disableConcreteTrigger(JobExecutionContext context)
    {
        // Если данный триггер - "выполнение в определённое время" - то необходимо сделать его "выключенным".
        // Имеется в виду, выключить саму настройку, которая хранится в метаинформации. Триггер в quartz выключится сам.
        String taskCode = context.getJobDetail().getKey().getName();
        String triggerCode = context.getTrigger().getKey().getName();
        SchedulerTask schedulerTask = schedulerUserTaskStorageService.getSchedulerTaskWithLastExecutionDate(taskCode);
        if (schedulerTask != null
            && schedulerTask.getTrigger(triggerCode) instanceof ConcreteDateTrigger concreteDateTrigger
            && concreteDateTrigger.isEnabled())
        {
            concreteDateTrigger.setEnabled(false);
            schedulerUserTaskStorageService.saveTrigger(concreteDateTrigger);
        }
    }
}
