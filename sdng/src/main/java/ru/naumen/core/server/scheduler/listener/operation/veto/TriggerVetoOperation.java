package ru.naumen.core.server.scheduler.listener.operation.veto;

import org.quartz.JobExecutionContext;
import org.quartz.Trigger;

/**
 * Представление операции по проверке возможности запуска задачи в данный момент.
 * Проверка осуществляется перед непосредственным выполнением тела задачи, сразу после вычисления следующей даты
 * выполнения задачи в core планировщика.
 *
 * <AUTHOR>
 * @since 21.10.2024
 */
@FunctionalInterface
public interface TriggerVetoOperation
{
    /**
     * Проверка необходимости наложения вето на исполнение задачи
     * <br>
     * <b>Важно:</b>Чтобы триггер не застрял в состоянии 'BLOCKED' - обрабатываем все ошибки операции и накладываем вето
     * на исполнение задачи в случае ошибок
     * <br>
     * @param trigger расписание задачи
     * @param context контекст исполняемой задачи
     * @return true, если исполнение задачи не возможно, иначе false
     */
    boolean veto(Trigger trigger, JobExecutionContext context);
}
