package ru.naumen.core.server.scheduler.manager;

import ru.naumen.core.server.util.JsonUtils;

/**
 * Представление расписания задачи планировщика, хранящее значения атрибутов расписания на момент их получения из
 * <code>core</code> планировщика.
 *
 * <AUTHOR>
 * @since 28.01.2025
 */
public class TriggerStatus
{
    private final String group;
    private final String jobName;
    private final String triggerName;
    private final boolean isRunning;
    private final String nextFireTime;
    private final String previousFireTime;
    private final String finalFireTime;

    public TriggerStatus(
            String group,
            String jobName,
            String triggerName,
            boolean isRunning,
            String nextFireTime,
            String previousFireTime,
            String finalFireTime)
    {
        this.group = group;
        this.jobName = jobName;
        this.triggerName = triggerName;
        this.isRunning = isRunning;
        this.nextFireTime = nextFireTime;
        this.previousFireTime = previousFireTime;
        this.finalFireTime = finalFireTime;
    }

    /**
     * @return имя группы задач планировщика, к которой относится текущая задача
     */
    public String getGroupName()
    {
        return group;
    }

    /**
     * @return имя задачи планировщика
     */
    public String getJobName()
    {
        return jobName;
    }

    /**
     * @return имя расписания задачи планировщика
     */
    public String getTriggerName()
    {
        return triggerName;
    }

    /**
     * @return true, если задача планировщика была запущена на момент получения статуса, иначе false
     */
    public boolean isRunning()
    {
        return isRunning;
    }

    /**
     * @return строка со временем следующего запуска задачи
     */
    public String getNextFireTime()
    {
        return nextFireTime;
    }

    /**
     * @return строка со временем прошлого запуска задачи
     */
    public String getPreviousFireTime()
    {
        return previousFireTime;
    }

    /**
     * @return строка со временем завершения задачи
     */
    public String getFinalFireTime()
    {
        return finalFireTime;
    }

    @Override
    public String toString()
    {
        return JsonUtils.toJson(this);
    }
}
