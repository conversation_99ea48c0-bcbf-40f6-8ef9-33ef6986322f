package ru.naumen.core.server.scheduler.service;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

import org.apache.commons.lang3.reflect.MethodUtils;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.scheduler.CronExpressionException;
import ru.naumen.core.server.scheduler.CronUtils;
import ru.naumen.core.server.scheduler.job.annotation.CronExpression;
import ru.naumen.core.server.scheduler.job.annotation.FixedRate;
import ru.naumen.core.server.scheduler.job.annotation.ScheduledJob;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;

/**
 * Утилитарные методы по работе с системными задачами планировщика.
 *
 * <AUTHOR>
 * @since 18.02.2024
 */
public final class SchedulerJobUtils
{
    public static final String JOB_NOT_RESCHEDULED_ERROR_MESSAGE = "Job '{}' is not rescheduled!";
    private static final SpringContext springContext = SpringContext.getInstance();

    private SchedulerJobUtils()
    {
    }

    /**
     * Создаётся {@link JobDetail} - описание задачи планировщика для Quartz - по настройке задачи планировщика
     * {@link SchedulerTask}. Для каждой настройки задачи планировщика в системе существует bean, имя которого строится
     * по схеме {@code task.getType() + "_Runnable"}. Данный bean должен реализовывать интерфейс {@link Job}. По имени
     * bean'а мы определяем класс задачи планировщика - это необходимо для регистрации в планировщике.
     *
     * @param task задача планировщика
     * @return объект, хранящий все важные параметры задачи планировщика
     * @throws IllegalStateException если класс задачи планировщика не найден
     * @throws IllegalArgumentException если класс не считается задачей планировщика
     */
    @SuppressWarnings("unchecked")
    public static JobDetail createJob(SchedulerTask task)
    {
        if (task.getType() == null)
        {
            throw new IllegalStateException("Create runnable task for Scheduler task type " + task.getType());
        }
        DefaultListableBeanFactory factory = (DefaultListableBeanFactory)springContext.getBeanFactory();
        BeanDefinition definition = factory.getBeanDefinition(task.getType() + "_Runnable");
        Class<?> beanClass;
        try
        {
            beanClass = Class.forName(definition.getBeanClassName());
        }
        catch (ClassNotFoundException e)
        {
            throw new IllegalStateException(e.getMessage(), e);
        }
        if (Job.class.isAssignableFrom(beanClass))
        {
            return JobBuilder.newJob((Class<? extends Job>)beanClass)
                    .withIdentity(task.getCode())
                    .storeDurably()
                    .build();
        }
        throw new IllegalArgumentException(
                "Only classes implementing the quartz Job interface can be annotated with @ScheduledJob");
    }

    /**
     * Вычисление cron-расписания задачи
     *
     * @param jobClass класс задачи
     * @param annotation аннотация задачи с ее уникальными параметрами
     * @return cron-расписания задачи
     * @throws CronExpressionException в случае, когда cron выражение не задано, или имеет не корректный вид
     */
    public static String extractCronExpression(Class<? extends Job> jobClass, ScheduledJob annotation)
            throws CronExpressionException
    {
        if (!CronUtils.CRON_EXPRESSION_ANNOTATION.equals(annotation.cronExpression()))
        {
            return annotation.cronExpression();
        }
        String resultCronExpression = annotation.cronExpression();
        Method[] methods = jobClass.getMethods();
        boolean hasCronExpressionAnnotation = false;
        boolean isValidHourValue = true;
        try
        {
            Job job = springContext.getBean(jobClass);
            for (Method method : methods)
            {
                if (method.isAnnotationPresent(CronExpression.class))
                {
                    hasCronExpressionAnnotation = true;
                    if (method.invoke(job) instanceof String result)
                    {
                        resultCronExpression = result;
                    }
                    else
                    {
                        throw new CronExpressionException(
                                "Method %s#%s return not valid value! Job is not scheduled!"
                                        .formatted(jobClass.getName(), method.getName()));
                    }
                }
                if ("getAllowedHoursBegin".equals(method.getName())
                    || "getAllowedHoursEnd".equals(method.getName()))
                {
                    Object hour = method.invoke(job);
                    isValidHourValue = isValidHourValue && hour != null
                                       && CronUtils.isValidHourValue(Integer.parseInt(hour.toString()));
                }
            }
        }
        catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e)
        {
            throw new CronExpressionException(
                    JOB_NOT_RESCHEDULED_ERROR_MESSAGE.replace("{}", jobClass.getName()), e);
        }
        if (!isValidHourValue)
        {
            throw new CronExpressionException("Job" + jobClass.getName()
                                              + " is not scheduled! Hour values is invalid!");
        }
        if (!hasCronExpressionAnnotation)
        {
            throw new CronExpressionException("Class " + jobClass.getName()
                                              + "must have method with CronExpression annotation! Job is not "
                                              + "scheduled!");
        }
        return resultCronExpression;
    }

    public static long extractFixedRate(Class<? extends Job> jobClass, ScheduledJob annotation)
    {
        List<Method> fixedRateMethods = MethodUtils.getMethodsListWithAnnotation(jobClass, FixedRate.class);
        if (!fixedRateMethods.isEmpty())
        {
            Job job = springContext.getBean(jobClass);
            String methodName = fixedRateMethods.getFirst().getName();
            try
            {
                return (long)MethodUtils.invokeMethod(job, methodName);
            }
            catch (Exception e)
            {
                throw new FxException(
                        "Error invocation method '%s' in Job '%s'".formatted(methodName, jobClass.getName()), e);
            }
        }
        return annotation.fixedRate();
    }

    /**
     * Создает описание системной задачи
     *
     * @param jobClass класс задачи
     * @param annotation аннотация, которой помечен класс
     * @return описание созданной задачи
     */
    public static JobDetail createJobDetail(Class<? extends Job> jobClass, ScheduledJob annotation)
    {
        String jobName = annotation.name().isEmpty() ? jobClass.getSimpleName() : annotation.name();
        String jobGroup = annotation.group().isEmpty() ? jobClass.getPackage().getName() : annotation.group();
        return JobBuilder.newJob(jobClass).withIdentity(jobName, jobGroup).build();
    }
}
