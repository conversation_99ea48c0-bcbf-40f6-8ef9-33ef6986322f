package ru.naumen.core.server.scheduler.service.recovery.jobstate;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.scheduler.job.base.InterruptableAbstractJob;

/**
 * Состояние потока задачи планировщика. Хранит стектрейс потока
 * снятый при прошлом обходе watchdog'а планировщика, а также
 * количество обходов при которых поток простоял в одном месте.
 *
 * <AUTHOR>
 * @since 23 янв. 2015 г.
 */
public class JobThreadState implements IJobThreadState
{
    private StackTraceElement[] oldStackTrace;
    private int samePlaceCount = 0;
    private final int maxStackTime;
    private final InterruptableAbstractJob job;

    public JobThreadState(@Nullable InterruptableAbstractJob job, int maxStackTime)
    {
        this.job = job;
        this.oldStackTrace = getExecutionThreadStackTrace();
        this.maxStackTime = maxStackTime;
    }

    private boolean isStuck(StackTraceElement[] stackTrace)
    {
        if (sameStackTrace(stackTrace))
        {
            ++this.samePlaceCount;
            return this.samePlaceCount >= this.maxStackTime;
        }
        this.oldStackTrace = stackTrace;
        this.samePlaceCount = 0;
        return false;
    }

    private boolean sameStackTrace(StackTraceElement[] stackTrace)
    {
        if (stackTrace.length != this.oldStackTrace.length)
        {
            return false;
        }
        for (int i = 0; i < stackTrace.length; ++i)
        {
            if (!stackTrace[i].equals(this.oldStackTrace[i]))
            {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean isStuck()
    {
        return isStuck(getExecutionThreadStackTrace());
    }

    private StackTraceElement[] getExecutionThreadStackTrace()
    {
        return this.job == null || this.job.getExecutionThread() == null
                ? new StackTraceElement[0]
                : this.job.getExecutionThread().getStackTrace();
    }
}
