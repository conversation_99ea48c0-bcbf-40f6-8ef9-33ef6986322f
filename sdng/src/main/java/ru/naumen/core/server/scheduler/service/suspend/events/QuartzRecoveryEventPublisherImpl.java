package ru.naumen.core.server.scheduler.service.suspend.events;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;

/**
 * Реализация {@link QuartzRecoveryEventPublisher}
 *
 * <AUTHOR>
 * @since 15.09.2023
 */
@Component
public class QuartzRecoveryEventPublisherImpl implements QuartzRecoveryEventPublisher
{
    private final ApplicationEventPublisher applicationEventPublisher;

    @Inject
    public QuartzRecoveryEventPublisherImpl(ApplicationEventPublisher applicationEventPublisher)
    {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public void publishResumeRecoveryEvent()
    {
        applicationEventPublisher.publishEvent(new QuartzResumeRecoveryEvent());
    }

    @Override
    public void publishSuspendRecoveryEvent()
    {
        applicationEventPublisher.publishEvent(new QuartzSuspendRecoveryEvent());
    }
}
