package ru.naumen.core.server.scheduler.storage;

import static ru.naumen.core.server.scheduler.Constants.SCHEDULER_TASK;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.SerializationUtils;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.cache.infinispan.ISCacheProvider;
import ru.naumen.core.server.cache.infinispan.plain.ISCacheAdapter;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.metastorage.MetaStorageException;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.scheduler.manager.QuartzSchedulerManager;
import ru.naumen.core.server.scheduler.storage.dao.SchedulerTaskExecutionInfoDao;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.SchedulerTaskContainer;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 * Всё, что касается механизма запуска - остаётся в QuartzSchedulerManager.
 *
 * <AUTHOR>
 * @since 05.07.2024
 */
@Component
public class SchedulerUserTaskStorageServiceImpl implements SchedulerUserTaskStorageService
{
    private static final Logger LOG = LoggerFactory.getLogger(SchedulerUserTaskStorageServiceImpl.class);

    private final MetaStorageService metaStorage;
    private final SchedulerTaskExecutionInfoDao executionInfoDao;
    private final TagService tagService;
    private final ISCacheAdapter cacheAdapter;
    private final QuartzSchedulerManager quartzSchedulerManager;

    @Inject
    public SchedulerUserTaskStorageServiceImpl(
            MetaStorageService metaStorageService,
            SchedulerTaskExecutionInfoDao executionInfoDao,
            TagService tagService,
            ISCacheProvider provider,
            @Lazy QuartzSchedulerManager quartzSchedulerManager)
    {
        this.metaStorage = metaStorageService;
        this.executionInfoDao = executionInfoDao;
        this.tagService = tagService;
        this.cacheAdapter = new ISCacheAdapter(provider);
        this.quartzSchedulerManager = quartzSchedulerManager;
        init();
    }

    private void init()
    {
        TransactionRunner.run(() ->
        {
            cacheAdapter.initCache(SCHEDULER_TASK);
            populateCache();
        });
    }

    @Override
    public void deleteSchedulerTask(SchedulerTask task)
    {
        cacheAdapter.removeProperty(task.getCode());
        executionInfoDao.delete(task);
        metaStorage.remove(SCHEDULER_TASK, task.getCode());
        LOG.info("Scheduler task '{}' is removed.", task.getCode());
    }

    @Override
    public SchedulerTask deleteTrigger(Trigger taskTrigger)
    {
        String taskCode = taskTrigger.getSchTaskCode();
        String triggerCode = taskTrigger.getCode();

        SchedulerTask task = Objects.requireNonNull(getLightSchedulerTasks(taskCode));
        task.deleteTrigger(triggerCode);
        executionInfoDao.fillLastExecutionDate(task);
        executionInfoDao.delete(taskCode, triggerCode);
        saveSchedulerTask(task);
        LOG.info("Trigger '{}' of scheduler task '{}' is removed.", triggerCode, taskCode);
        return task;
    }

    @Nullable
    @Override
    public SchedulerTask getLightSchedulerTasks(String taskCode)
    {
        SchedulerTask task = getTask(taskCode);
        return copySchedulerTask(task);
    }

    @Nullable
    @Override
    public SchedulerTask getSchedulerTaskWithLastExecutionDate(String taskCode)
    {
        SchedulerTask task = getLightSchedulerTasks(taskCode);
        if (task != null)
        {
            executionInfoDao.fillLastExecutionDate(task);
        }
        return task;
    }

    @Override
    public SchedulerTask getReadOnlySchedulerTask(String taskCode)
    {
        return getTask(taskCode);
    }

    /**
     * Возвращает расписание по его идентификатору
     *
     * @param triggerName идентификатор расписания задачи планировщика
     * @return расписание задачи планировщика
     * @throws SchedulerException расписание не найдено
     */
    public Trigger getTrigger(String triggerName) throws SchedulerException
    {
        return getSchedulerTasks().stream()
                .map(task -> task.getTrigger(triggerName))
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(SchedulerException::new);
    }

    @Override
    public Collection<SchedulerTask> getSchedulerTasks()
    {
        Collection<SchedulerTask> schedulerTasks = getAllSchedulerTasks();
        for (SchedulerTask task : schedulerTasks)
        {
            fillPlanExecutionDate(task);
        }
        return schedulerTasks;
    }

    @Override
    @Nullable
    public SchedulerTask getSchedulerTaskWithPlanExecutionDate(String taskCode)
    {
        SchedulerTask task = getSchedulerTaskWithLastExecutionDate(taskCode);
        fillPlanExecutionDate(task);
        return task;
    }

    /**
     * Устанавливает для задачи ближайшую дату планового выполнения задачи и дату последнего выполнения, на основании
     * анализа всех существующих расписаний задачи
     *
     * @param task объектное представление задачи
     */
    private void fillPlanExecutionDate(@Nullable SchedulerTask task)
    {
        if (task == null)
        {
            return;
        }
        JobKey jobKey = new JobKey(task.getCode());
        if (!quartzSchedulerManager.isJobExist(jobKey))
        {
            // Задача не запланирована... не можем знать ничего о триггерах
            return;
        }
        Date nextFireTime = null;
        for (org.quartz.Trigger tr : quartzSchedulerManager.getTaskTriggers(jobKey))
        {
            Date triggerNextFireTime = tr.getNextFireTime();
            LOG.debug("Fill task: '{}', trigger: '{}', nextFireTime: '{}'",
                    task.getCode(), tr.getKey().getName(), triggerNextFireTime);
            nextFireTime = getMinimumNonNullDate(nextFireTime, triggerNextFireTime);
            Trigger trigger = task.getTrigger(tr.getKey().getName());
            if (trigger != null)
            {
                trigger.setPlanExecutionDate(triggerNextFireTime);
            }
        }
        LOG.debug("Fill task: '{}', nextFireTime: '{}'", task.getCode(), nextFireTime);
        task.setPlanDate(nextFireTime);
    }

    @Nullable
    private static Date getMinimumNonNullDate(@Nullable Date date1, @Nullable Date date2)
    {
        if (date1 == null && date2 == null)
        {
            return null;
        }
        if (date1 == null)
        {
            return date2;
        }
        if (date2 == null)
        {
            return date1;
        }
        return date1.before(date2) ? date1 : date2;
    }

    @Override
    public Set<SchedulerTask> getAllSchedulerTasks()
    {
        return cacheAdapter.getValues().stream()
                .map(task -> copySchedulerTask((SchedulerTask)task))
                .filter(Objects::nonNull)
                .peek(executionInfoDao::fillLastExecutionDate) //NOSONAR предлагает заменить peek на foreach
                .collect(Collectors.toSet());
    }

    @Override
    public Set<SchedulerTask> getActiveSchedulerTasks()
    {
        return cacheAdapter.getValues().stream()
                .map(SchedulerTask.class::cast)
                .filter(tagService::isElementEnabled)
                .map(SchedulerUserTaskStorageServiceImpl::copySchedulerTask)
                .filter(Objects::nonNull)
                .peek(executionInfoDao::fillLastExecutionDate) //NOSONAR предлагает заменить peek на foreach
                .collect(Collectors.toSet());
    }

    @Override
    public Trigger saveTrigger(Trigger trigger)
    {
        SchedulerTask schTask = Objects.requireNonNull(getSchedulerTaskWithLastExecutionDate(trigger.getSchTaskCode()));
        schTask.addTrigger(trigger);
        saveSchedulerTask(schTask);
        LOG.info("Trigger '{}' of scheduler task '{}' is saved or updated.", trigger.getCode(), schTask.getCode());
        return trigger;
    }

    @Override
    public boolean saveSchedulerTask(SchedulerTask task)
    {
        cacheAdapter.putProperty(task.getCode(), task);
        boolean isSaved = metaStorage.save(new SchedulerTaskContainer(task), SCHEDULER_TASK, task.getCode());
        if (isSaved)
        {
            LOG.info("Scheduler task '{}' is saved or updated.", task.getCode());
        }
        return isSaved;
    }

    public void saveUserTaskLastExecutionDate(SchedulerTask task, Date lastExecutionDate)
    {
        List<String> changeTriggers = task.getTrigger().stream().map(Trigger::getCode).toList();
        //Сохраняем правила с новой датой последнего выполнения
        executionInfoDao.save(task.getCode(), changeTriggers, lastExecutionDate);
    }

    private static SchedulerTask copySchedulerTask(SchedulerTask task)
    {
        return SerializationUtils.clone(task);
    }

    @Nullable
    private SchedulerTask getTask(String taskCode)
    {
        SchedulerTask taskFromCache = cacheAdapter.getProperty(taskCode);
        if (taskFromCache != null)
        {
            return taskFromCache;
        }
        return getTaskFromMetaStorage(taskCode);
    }

    @Nullable
    private SchedulerTask getTaskFromMetaStorage(String taskCode)
    {
        try
        {
            SchedulerTaskContainer taskFromMetaStorage = metaStorage.get(SCHEDULER_TASK, taskCode);
            if (taskFromMetaStorage != null)
            {
                SchedulerTask schedulerTask = SchedulerTaskContainer.EXTRACTOR.apply(taskFromMetaStorage);
                if (schedulerTask != null)
                {
                    cacheAdapter.putProperty(taskCode, schedulerTask);
                    return schedulerTask;
                }
            }
            return null;
        }
        catch (MetaStorageException mse)
        {
            LOG.debug("No scheduler task with code {}", taskCode);
            return null;
        }
    }

    @Override
    public void reloadCache()
    {
        cacheAdapter.clearCache();
        populateCache();
    }

    private void populateCache()
    {
        metaStorage.<SchedulerTaskContainer> get(SCHEDULER_TASK).stream()
                .map(SchedulerTaskContainer.EXTRACTOR)
                .peek(executionInfoDao::fillLastExecutionDate)  //NOSONAR предлагает заменить peek на foreach
                .forEach(task -> cacheAdapter.putProperty(task.getCode(), task));
    }

    @Override
    public boolean isClearAndReloadInSameOperation()
    {
        return true;
    }

    @Override
    public String getMetaRegion()
    {
        return SCHEDULER_TASK;
    }
}