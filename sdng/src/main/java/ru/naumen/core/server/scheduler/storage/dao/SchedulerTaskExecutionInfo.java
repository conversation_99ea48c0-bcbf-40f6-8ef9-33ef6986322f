package ru.naumen.core.server.scheduler.storage.dao;

import java.util.Date;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import ru.naumen.core.server.UUIDIdentifiableBase;
import ru.naumen.core.server.metastorage.impl.StorageValue;

/**
 * Класс для хранения динамической информации связанной
 * с выполненим задачи планировщика по триггеру
 * время последнего выполнения + время следующего выполнения
 * <AUTHOR>
 * @since 6.11.2012
 */
@Entity
@Table(name = "tbl_sys_task_date", indexes = {
        @jakarta.persistence.Index(name = "idx_task_trigger_taskcode", columnList = "triggercode, schedulertaskcode") })
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
public class SchedulerTaskExecutionInfo extends UUIDIdentifiableBase //NOSONAR Override the "equals" method in this
        // class.
{
    private static final long serialVersionUID = 3335188157639125266L;

    public static final String PREFIX = "taskinfo";
    @Column
    private Date lastExecutionDate;
    @CheckForNull
    @Column
    private String triggerCode;
    @Column(length = StorageValue.MAX_KEY_COLUMN_LENGTH)
    private String schedulerTaskCode;

    public SchedulerTaskExecutionInfo()
    {
    }

    SchedulerTaskExecutionInfo(@Nullable String triggerCode, String schedulerTaskCode)
    {
        this.triggerCode = triggerCode;
        this.schedulerTaskCode = schedulerTaskCode;
    }

    public Date getLastExecutionDate()
    {
        return lastExecutionDate;
    }

    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return null;
    }

    public String getSchedulerTaskCode()
    {
        return schedulerTaskCode;
    }

    @CheckForNull
    public String getTriggerCode()
    {
        return triggerCode;
    }

    public void setLastExecutionDate(Date lastExecutionDate)
    {
        this.lastExecutionDate = lastExecutionDate;
    }

    public void setSchedulerTaskCode(String schedulerTaskCode)
    {
        this.schedulerTaskCode = schedulerTaskCode;
    }

    public void setTriggerCode(@Nullable String triggerCode)
    {
        this.triggerCode = triggerCode;
    }
}
