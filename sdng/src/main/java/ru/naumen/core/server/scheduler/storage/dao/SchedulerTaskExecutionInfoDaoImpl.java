package ru.naumen.core.server.scheduler.storage.dao;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 * Реализация {@link SchedulerTaskExecutionInfoDao}
 *
 * <AUTHOR>
 * @since 14.02.2013
 */
@Component
/*
TODO исправить транзакционность в задаче https://naupp.naumen
 .ru/sd/operator/#uuid:smrmTask$275245201:smrmTask$development
 */
@Transactional
public class SchedulerTaskExecutionInfoDaoImpl implements SchedulerTaskExecutionInfoDao
{
    private static final String TRIGGER_CODE = "triggerCode";
    private static final String SCHEDULER_TASK_CODE = "schedulerTaskCode";

    public static final Logger LOG = LoggerFactory.getLogger(SchedulerTaskExecutionInfoDaoImpl.class);

    private final SessionFactory sessionFactory;

    @Inject
    public SchedulerTaskExecutionInfoDaoImpl(@Named("metastorageSessionFactory") SessionFactory sessionFactory)
    {
        this.sessionFactory = sessionFactory;
    }

    @Override
    public void delete(SchedulerTask task)
    {
        LOG.debug("start delete {}", task.getCode());
        String taskCode = task.getCode();

        for (Trigger trigger : task.getTrigger())
        {
            String triggerCode = trigger.getCode();
            delete(taskCode, triggerCode);
        }

        delete(taskCode, null);
    }

    @Override
    public void delete(String taskCode, @Nullable String triggerCode)
    {
        SchedulerTaskExecutionInfo info = getOrNull(taskCode, triggerCode);
        if (null != info)
        {
            sessionFactory.getCurrentSession().remove(info);
        }
    }

    @Override
    public void fillLastExecutionDate(SchedulerTask task)
    {
        LOG.debug("fill {}", task.getTitle());
        String taskCode = task.getCode();
        for (Trigger trigger : task.getTrigger())
        {
            String triggerCode = trigger.getCode();
            SchedulerTaskExecutionInfo info = getOrNull(taskCode, triggerCode);
            if (null == info)
            {
                continue;
            }
            trigger.setLastExecutionDate(info.getLastExecutionDate());
        }

        SchedulerTaskExecutionInfo info = getOrNull(taskCode, null);
        if (null != info)
        {
            task.setLastExecutionDate(info.getLastExecutionDate());
        }
    }

    @Nullable
    public SchedulerTaskExecutionInfo getOrNull(String taskCode, @Nullable String triggerCode)
    {
        List<SchedulerTaskExecutionInfo> taskExecutionInfos = getTaskExecutionInfos(taskCode, triggerCode);
        if (taskExecutionInfos.size() > 1)
        {
            LOG.warn("A lot of records: {}, {}; used first", taskCode, triggerCode);
        }
        return taskExecutionInfos.isEmpty() ? null : taskExecutionInfos.getFirst();
    }

    private List<SchedulerTaskExecutionInfo> getTaskExecutionInfos(String taskCode, @Nullable String triggerCode)
    {
        CriteriaBuilder builder = sessionFactory.getCurrentSession().getCriteriaBuilder();
        CriteriaQuery<SchedulerTaskExecutionInfo> criteriaQuery = builder.createQuery(SchedulerTaskExecutionInfo.class);
        Root<SchedulerTaskExecutionInfo> root = criteriaQuery.from(SchedulerTaskExecutionInfo.class);

        criteriaQuery.where(
                builder.equal(root.get(SCHEDULER_TASK_CODE), taskCode),
                triggerCode != null
                        ? builder.equal(root.get(TRIGGER_CODE), triggerCode)
                        : builder.isNull(root.get(TRIGGER_CODE))
        );

        return sessionFactory.getCurrentSession().createQuery(criteriaQuery).setCacheable(true).getResultList();
    }

    @Override
    public void save(String taskCode, String triggerCode, Date lastExecDate)
    {
        save(taskCode, List.of(triggerCode), lastExecDate);
    }

    @Override
    public void save(String taskCode, List<String> triggerCodes, Date lastExecDate)
    {
        List<SchedulerTaskExecutionInfo> taskInfoList =
                triggerCodes.stream().map(triggerCode ->
                        getOrCreateTaskInfo(taskCode, triggerCode)).collect(Collectors.toList());
        taskInfoList.add(getOrCreateTaskInfo(taskCode, null));

        Session currentSession = sessionFactory.getCurrentSession();
        taskInfoList.forEach(info ->
        {
            LOG.debug("save trigger {} for task {} (lastExecDate = {})", taskCode, info.getTriggerCode(), lastExecDate);
            info.setLastExecutionDate(lastExecDate);
            if (Objects.isNull(currentSession.find(SchedulerTaskExecutionInfo.class, info.getId())))
            {
                currentSession.persist(info);
            }
            else
            {
                currentSession.merge(info);
            }
        });
        currentSession.flush();
    }

    private SchedulerTaskExecutionInfo getOrCreateTaskInfo(String taskCode, @Nullable String triggerCode)
    {
        SchedulerTaskExecutionInfo taskExecutionInfo = get(taskCode, triggerCode);
        return Objects.requireNonNullElseGet(taskExecutionInfo,
                () -> new SchedulerTaskExecutionInfo(triggerCode, taskCode));
    }

    private SchedulerTaskExecutionInfo get(String taskCode, @Nullable String triggerCode)
    {
        List<SchedulerTaskExecutionInfo> taskExecutionInfos = getTaskExecutionInfos(taskCode, triggerCode);
        if (taskExecutionInfos.isEmpty())
        {
            return new SchedulerTaskExecutionInfo(triggerCode, taskCode);
        }
        return Objects.requireNonNullElseGet(taskExecutionInfos.getFirst(),
                () -> new SchedulerTaskExecutionInfo(triggerCode, taskCode));
    }
}
