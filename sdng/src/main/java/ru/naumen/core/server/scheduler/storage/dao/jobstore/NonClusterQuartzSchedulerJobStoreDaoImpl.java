package ru.naumen.core.server.scheduler.storage.dao.jobstore;

import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerKey;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.scheduler.manager.QuartzSchedulerManager;

/**
 * Реализация для {@link QuartzSchedulerJobStoreDao}
 *
 * <AUTHOR>
 * @since 26.02.2024
 */
@Component
public class NonClusterQuartzSchedulerJobStoreDaoImpl extends AbstractQuartzSchedulerJobStoreDao
        implements QuartzSchedulerJobStoreDao<Trigger>
{
    @Inject
    public NonClusterQuartzSchedulerJobStoreDaoImpl(
            DataBaseInfo dataBaseInfo,
            QuartzSchedulerManager quartzSchedulerManager)
    {
        super(dataBaseInfo, quartzSchedulerManager);
    }

    public Trigger getTriggerState(TriggerKey triggerKey)
    {
        try
        {
            return quartzSchedulerManager.retrieveTrigger(triggerKey);
        }
        catch (SchedulerException e)
        {
            throw new FxException(e);
        }
    }
}
