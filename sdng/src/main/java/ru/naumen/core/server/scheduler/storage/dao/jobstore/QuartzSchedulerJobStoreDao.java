package ru.naumen.core.server.scheduler.storage.dao.jobstore;

import java.util.Set;

import org.quartz.TriggerKey;

import jakarta.annotation.Nullable;

/**
 * Предоставляет методы взаимодействия с ресурсами планировщика {@link org.quartz.impl.StdScheduler} в БД.
 *
 * <AUTHOR>
 * @since 26.02.2024
 */
public interface QuartzSchedulerJobStoreDao<T>
{
    /**
     * Получить набор имен системных задач которые фактически зарегистрированы и выполняются в планировщике
     *
     * @param expectedSystemTaskNames набор имен системных задач, которые потенциально могли быть зарегистрированы в
     *                                планировщике
     * @return набор имен системных задач которые фактически зарегистрированы и выполняются в планировщике
     */
    Set<String> getRegisteredSystemTaskNames(Set<String> expectedSystemTaskNames);

    /**
     * Получить текущее состояние триггера.<br>
     * В кластерной конфигурации важными атрибутами возвращаемого объекта является уникальный идентификатор планировщика
     * в кластере, состояние расписания и состояние процесса исполнения задачи по расписанию.
     *
     * @param triggerKey ключ триггера
     * @return представление текущего состояния триггера, или null, если нет информации об активном процессе исполнения
     * задачи в БД
     */
    @Nullable
    T getTriggerState(TriggerKey triggerKey);
}