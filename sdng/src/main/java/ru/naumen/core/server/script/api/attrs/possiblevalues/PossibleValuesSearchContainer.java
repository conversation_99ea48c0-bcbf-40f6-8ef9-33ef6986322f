package ru.naumen.core.server.script.api.attrs.possiblevalues;

import java.util.Collection;

import ru.naumen.core.server.script.api.possiblevalues.IPossibleValuesContainer;

/**
 * Контейнер для хранения результатов поиска возможных значений.
 *
 * <AUTHOR>
 * @since 03.07.2020
 */
public class PossibleValuesSearchContainer implements IPossibleValuesContainer
{
    private final Collection<?> results;
    private final boolean hasMore;

    public PossibleValuesSearchContainer(Collection<?> results, boolean hasMore)
    {
        this.results = results;
        this.hasMore = hasMore;
    }

    @Override
    public Collection<?> getResults()
    {
        return results;
    }

    /**
     * Определяет, возвращены ли все найденные значения
     */
    public boolean isHasMore()
    {
        return hasMore;
    }

    @Override
    public String toString()
    {
        return "PossibleValuesSearchContainer[results=" + getResults() + ", hasMore=" + hasMore + ']';
    }
}
