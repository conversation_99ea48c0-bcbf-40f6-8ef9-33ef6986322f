package ru.naumen.core.server.script.api.attrs.recalculatevalues;

import java.util.Map;

import ru.naumen.core.server.script.api.recalculatevalues.IRecalculateValuesOnFormContainer;
import ru.naumen.core.server.script.api.recalculatevalues.restrictions.IDateTimeRestrictionsContainer;

/**
 * Контейнер для хранения результата вычисления значений атрибутов на форме
 * @see IRecalculateValuesOnFormContainer
 *
 * <AUTHOR>
 * @since 08.06.2022
 */
public class RecalculateValuesOnFormContainer implements IRecalculateValuesOnFormContainer
{
    private final Map<String, Object> results;
    private final Map<String, IDateTimeRestrictionsContainer> dateTimeRestrictions;

    public RecalculateValuesOnFormContainer(final Map<String, Object> results,
            final Map<String, IDateTimeRestrictionsContainer> dateTimeRestrictions)
    {
        this.results = results;
        this.dateTimeRestrictions = dateTimeRestrictions;
    }

    @Override
    public Map<String, Object> getResults()
    {
        return results;
    }

    @Override
    public Map<String, IDateTimeRestrictionsContainer> getDateTimeRestrictions()
    {
        return dateTimeRestrictions;
    }

    @Override
    public String toString()
    {
        return "RecalculateValuesOnFormContainer[results=" + results.keySet() + "]";
    }
}
