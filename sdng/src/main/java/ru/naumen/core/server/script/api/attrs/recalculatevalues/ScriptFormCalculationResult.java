package ru.naumen.core.server.script.api.attrs.recalculatevalues;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import ru.naumen.core.server.form.calculator.FormCalculationResult;
import ru.naumen.core.shared.dispatch.datetime.Restriction;

/**
 * Результат расчёта формы в скриптовом API
 *
 * <AUTHOR>
 * @since 24.12.2024
 */
public class ScriptFormCalculationResult implements FormCalculationResult
{
    /** Словарь изменившихся значений атрибутов */
    private final Map<String, Object> values = new HashMap<>();
    /**
     * Словарь, сопоставляющий возвращаемым ограничениям тексты ошибок, которые должны быть показаны
     * пользователю, если ограничение не выполняется.
     * */
    private final Map<String, Map<Restriction<?>, String>> dateTimeRestrictions = new HashMap<>();

    /**
     * Добавляет изменившееся значения атрибутов
     */
    void addValues(Map<String, Object> results)
    {
        this.values.putAll(results);
    }

    /**
     * Добавляет ограничения для атрибутов типов "Дата" и "Дата/время"
     */
    void addDateTimeRestrictions(Map<String, Map<Restriction<?>, String>> dateTimeRestrictions)
    {
        this.dateTimeRestrictions.putAll(dateTimeRestrictions);
    }

    /**
     * Возвращает изменившиеся значения атрибутов
     **/
    public Map<String, Object> getValues()
    {
        return Collections.unmodifiableMap(values);
    }

    /**
     * Возвращает ограничения для атрибутов типов "Дата" и "Дата/время"
     */
    @SuppressWarnings("java:S1452") // ? на Object или общего предка не поменять
    public Map<String, Map<Restriction<?>, String>> getDateTimeRestrictions()
    {
        return Collections.unmodifiableMap(dateTimeRestrictions);
    }
}
