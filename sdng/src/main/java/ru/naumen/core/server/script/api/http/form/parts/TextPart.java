package ru.naumen.core.server.script.api.http.form.parts;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;

/**
 * Реализация {@link Part}, позволяющая передать текстовые данные в multipart/form-data.
 *
 * <AUTHOR>
 * @since 14.10.2024
 */
public class TextPart implements Part
{
    private final String name;
    private final String value;
    private final Charset charset;

    public TextPart(String name, String value, Charset charset)
    {
        this.name = name;
        this.value = value;
        this.charset = charset;
    }

    @Override
    public String name()
    {
        return name;
    }

    @Override
    public InputStream open() throws IOException
    {
        return new ByteArrayInputStream(value.getBytes(charset));
    }
}