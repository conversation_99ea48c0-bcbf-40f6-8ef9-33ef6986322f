package ru.naumen.core.server.script.api.maintenance;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.maintenance.domain.Maintenance;
import ru.naumen.core.server.maintenance.services.MaintenanceAccessMode;
import ru.naumen.core.server.maintenance.services.MaintenanceStorageService;
import ru.naumen.core.shared.maintenance.MaintenanceState;

/**
 * Реализация {@link IMaintenanceApi}
 *
 * <AUTHOR>
 * @since 20.11.2024
 */
@Component("maintenance")
public class MaintenanceApi implements IMaintenanceApi
{
    private final MaintenanceStorageService maintenanceStorageService;

    @Inject
    public MaintenanceApi(final MaintenanceStorageService maintenanceStorageService)
    {
        this.maintenanceStorageService = maintenanceStorageService;
    }

    @Override
    @Nullable
    public IMaintenanceInfo getMaintenanceInfo()
    {
        Maintenance maintenance = maintenanceStorageService.getMaintenance(MaintenanceAccessMode.READ);
        if (maintenance.getState() == MaintenanceState.ENDED)
        {
            return null;
        }
        return new MaintenanceInfo(
                maintenance.getStartDate(),
                maintenance.getEndDate(),
                maintenance.getMode().name(),
                maintenance.getState().name(),
                maintenance.getNotificationText(),
                maintenance.getNotificationInterval());
    }
}
