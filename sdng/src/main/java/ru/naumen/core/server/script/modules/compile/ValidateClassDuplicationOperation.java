package ru.naumen.core.server.script.modules.compile;

import java.util.Map;

import org.codehaus.groovy.control.CompilationFailedException;
import org.codehaus.groovy.control.CompilationUnit;
import org.codehaus.groovy.control.CompilationUnit.ISourceUnitOperation;
import org.codehaus.groovy.control.SourceUnit;
import org.codehaus.groovy.syntax.SyntaxException;

/**
 * Операция проверки наличия дублей классов относительно уже скомпилированных ранее модулей.
 * <p>Groovy-компилятор умеет осуществлять проверку дублей классов в разных источниках, но только среди
 * компилируемых в данный момент модулей. У нас есть оптимизации, позволяющие перекомпилировать только изменившиеся
 * модули. В этом случае модули, которые не менялись, не попадают в {@link CompilationUnit}, соответственно
 * компилятор для них проверку не осуществляет. Поэтому и нужна эта дополнительная операция</p>
 *
 * <AUTHOR> vpyzhyanov
 * @since 15.06.2023
 */
public class ValidateClassDuplicationOperation implements ISourceUnitOperation
{
    private final Map<String, String> loadedClassesWithModules;

    /**
     * @param loadedClassesWithModules набор скомпилированных ранее классов и соответствующе им модули
     */
    public ValidateClassDuplicationOperation(Map<String, String> loadedClassesWithModules)
    {
        this.loadedClassesWithModules = loadedClassesWithModules;
    }

    @Override
    public void call(SourceUnit source) throws CompilationFailedException
    {
        source.getAST().getClasses()
                .forEach(clazz ->
                {
                    String className = clazz.getName();
                    if (loadedClassesWithModules.containsKey(className))
                    {
                        String moduleCode = loadedClassesWithModules.get(className);
                        String error = String.format(
                                "Invalid duplicate class definition of class %s : The modules '%s' and '%s' each "
                                + "contain a class with the name %s.\n",
                                className, source.getName(), moduleCode, className);

                        SyntaxException exception = new SyntaxException(error, clazz.getLineNumber(),
                                clazz.getColumnNumber(), clazz.getLastLineNumber(), clazz.getLastColumnNumber());
                        source.addErrorAndContinue(exception);
                    }
                });
    }
}
