package ru.naumen.core.shared.console;

import com.google.common.collect.Lists;

import org.springframework.stereotype.Component;

import jakarta.inject.Singleton;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 17.05.2013
 */
@Component
@Singleton
public class LevelsRegistryImpl implements LevelsRegistry
{
    public static final String ERROR = "ERROR";
    public static final String INFO = "INFO";
    private static final String WARN = "WARN";
    private static final String DEBUG = "DEBUG";
    private static final String TRACE = "TRACE";
    private static final String ALL = "ALL";

    private List<String> levels = new ArrayList<>();

    public LevelsRegistryImpl()
    {
        levels.add(ERROR);
        levels.add(WARN);
        levels.add(INFO);
        levels.add(DEBUG);
        levels.add(TRACE);
        levels.add(ALL);
    }

    @Override
    public List<String> getLevels()
    {
        return levels;
    }

    @Override
    public List<String> getLevels(String level)
    {
        return Lists.newArrayList(levels.subList(0, levels.indexOf(level) + 1));
    }

}
