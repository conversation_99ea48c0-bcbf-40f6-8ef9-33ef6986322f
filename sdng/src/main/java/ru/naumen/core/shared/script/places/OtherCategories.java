package ru.naumen.core.shared.script.places;

/**
 * Прочие категории скриптов.
 */
public enum OtherCategories implements ScriptCategory
{
    /**
     * Правило обработки звонков CTI. 
     */
    CTI,
    /**
     * Консольные скрипты.
     */
    CONSOLE,
    /**
     * Скрипты планировщика задач.
     */
    SCHEDULER_TASK,
    /**
     * Правило обработки почты. 
     */
    MAIL_PROCESSOR_RULE,
    /**
     * Скрипт для встроенного приложения. 
     */
    APPLICATION,
    /**
     * Скрипт системы мониторинга.
     */
    MONITORING,
    /**
     * Скрипты шаблонов отчётов и печатных форм.
     */
    REPORT_TEMPLATE,
    /**
     * Скрипт уточнения прав доступа.
     */
    PERMISSIONS,
    /**
     * Скриптовый модуль. 
     */
    SCRIPT_MODULE,
    /**
     * XML-конфигурация импорта
     */
    ADVIMPORT_CONFIG,
    /**
     * Скрипты для настроек ограничений при фильтрации
     */
    FILTER_RESTRICTION,
    /**
     * Скрипты обработки голосовых данных
     */
    VOICE_PROCESSING,
    /**
     * Без категории
     */
    WITHOUT_CATEGORY;

    @Override
    public String getTitleCode()
    {
        return "scriptcatalog-OtherCategories." + name() + ".title";
    }

    @Override
    public boolean isCatalogCategory()
    {
        return this != ADVIMPORT_CONFIG && this != SCRIPT_MODULE && this != CONSOLE;
    }
}