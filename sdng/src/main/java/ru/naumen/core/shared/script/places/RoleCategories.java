package ru.naumen.core.shared.script.places;

/**
 * Категории скриптов в пределах роли.
 */
public enum RoleCategories implements ScriptCategory
{
    /**
     * Скрипты определения прав доступа.
     */
    ACCESS,
    /**
     * Скрипты определения обладателей роли.
     */
    OWNERS,
    /**
     * Скрипты определения условия отбора объектов доступных обладателю роли при поиске.
     */
    LIST_FILTER,

    /**
     * Скрипты определения условия отбора объектов доступных обладателю роли при упоминании
     */
    FAST_LINK_RIGHTS;

    @Override
    public String getTitleCode()
    {
        return "scriptcatalog-RoleCategories." + name() + ".title";
    }

    @Override
    public boolean isCatalogCategory()
    {
        return true;
    }
}