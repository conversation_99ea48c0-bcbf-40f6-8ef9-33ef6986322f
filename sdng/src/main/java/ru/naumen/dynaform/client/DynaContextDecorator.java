package ru.naumen.dynaform.client;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.google.gwt.event.shared.EventBus;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextDecorator;
import ru.naumen.core.client.content.ErrorAndAttentionMessageHandler;
import ru.naumen.core.client.content.HasParentContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Реализация контекста, позволяющая подменять {@link ReadyState} и {@link ErrorAndAttentionMessageHandler}
 *
 * <AUTHOR>
 *
 */
public class DynaContextDecorator implements ChildDynaContext, ContextDecorator, HasParentContext
{
    /**
     * Cравнивает контексты Context,
     * извлекая их из оберток DynaContextDecorator, если требуется
     */
    public static boolean compareDecorated(Context ctx1, Context ctx2)
    {
        //@formatter:off
        return ObjectUtils.equals(
                ctx1 instanceof DynaContextDecorator ? ((DynaContextDecorator)ctx1).getAdaptee() : ctx1,
                ctx2 instanceof DynaContextDecorator ? ((DynaContextDecorator)ctx2).getAdaptee() : ctx2);
        //@formatter:on
    }

    private final ReadyState readyState;
    @CheckForNull
    private final ErrorAndAttentionMessageHandler errorAndAttentionMsgHandler;

    protected final DynaContext adaptee;

    public DynaContextDecorator(DynaContext adaptee, ErrorAndAttentionMessageHandler errorAndAttentionMsgHandler)
    {
        this(adaptee, adaptee.getReadyState(), errorAndAttentionMsgHandler);
    }

    public DynaContextDecorator(DynaContext adaptee, ReadyState readyState)
    {
        this(adaptee, readyState, adaptee.getErrorAndAttentionMsgHandler());
    }

    public DynaContextDecorator(DynaContext adaptee, ReadyState readyState,
            @Nullable ErrorAndAttentionMessageHandler errorAndAttentionMsgHandler)
    {
        this.adaptee = adaptee;
        this.readyState = readyState;
        this.errorAndAttentionMsgHandler = errorAndAttentionMsgHandler;
    }

    @Override
    public void destroy()
    {
        adaptee.destroy();
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends Context> T getAdaptee()
    {
        return (T)adaptee;
    }

    @Override
    public MapProperties getContextProperties()
    {
        return adaptee.getContextProperties();
    }

    @Override
    public <T> T getContextProperty(String name)
    {
        return adaptee.getContextProperty(name);
    }

    @Override
    public List<Long> getEditingSessionIds()
    {
        return adaptee.getEditingSessionIds();
    }

    @CheckForNull
    @Override
    public ErrorAndAttentionMessageHandler getErrorAndAttentionMsgHandler()
    {
        return errorAndAttentionMsgHandler;
    }

    @Override
    public EventBus getEventBus()
    {
        return adaptee.getEventBus();
    }

    @Override
    public String getFormCode()
    {
        return adaptee.getFormCode();
    }

    @Override
    public List<ClassFqn> getMetaClassesFqn()
    {
        return adaptee.getMetaClassesFqn();
    }

    @Override
    public MetaClass getMetainfo()
    {
        return adaptee.getMetainfo();
    }

    @Override
    public DtObject getObject()
    {
        return adaptee.getObject();
    }

    @Override
    public Set<DtObject> getObjects()
    {
        return adaptee.getObjects();
    }

    @Nullable
    @Override
    public <R> R getPermissionMetaData(String key)
    {
        return adaptee.getPermissionMetaData(key);
    }

    @Override
    public DynaContext getParentContext()
    {
        if (adaptee instanceof ChildDynaContext)
        {
            return ((ChildDynaContext)adaptee).getParentContext();
        }
        return null;
    }

    @Override
    public PermissionHolder getPermissions()
    {
        return adaptee.getPermissions();
    }

    @Override
    public boolean hasPermission(String subjectUuid, String permissionKey)
    {
        return adaptee.hasPermission(subjectUuid, permissionKey);
    }

    @Override
    public ReadyState getReadyState()
    {
        return readyState;
    }

    @Override
    public boolean hasContentPermission(Content content, String permissionKey)
    {
        return adaptee.hasContentPermission(content, permissionKey);
    }

    @Override
    public boolean hasContentPermission(String contentUuid, String permissionKey)
    {
        return adaptee.hasContentPermission(contentUuid, permissionKey);
    }

    @Override
    public boolean hasContentPermission(String contentUuid, String permissionKey, boolean defaultValue)
    {
        return adaptee.hasContentPermission(contentUuid, permissionKey, defaultValue);
    }

    @Override
    public boolean isContentVisible(String contentUuid)
    {
        return adaptee.isContentVisible(contentUuid);
    }

    @Override
    public boolean isContentPermissionDefined(String contentUuid, String permissionKey)
    {
        return adaptee.isContentPermissionDefined(contentUuid, permissionKey);
    }

    @Override
    public void ready(IReadyCallback callback)
    {
        readyState.ready(callback);
    }

    @Override
    public void registerChildContext(Context child)
    {
        adaptee.registerChildContext(child);
    }

    @Override
    public SynchronizationCallbackRegistration registerSynchronization(SynchronizationCallback callback)
    {
        return readyState.registerSynchronization(callback);
    }

    @Override
    public void setContentVisible(String contentUuid, boolean visible)
    {
        adaptee.setContentVisible(contentUuid, visible);
    }

    @Override
    public void setContextProperties(MapProperties contextProperties)
    {
        adaptee.setContextProperties(contextProperties);
    }

    @Override
    public void setContextProperty(String name, @Nullable Object value)
    {
        adaptee.setContextProperty(name, value);
    }

    @Override
    public void setObjects(Collection<DtObject> objects)
    {
        adaptee.setObjects(objects);
    }

    @Override
    public void unregisterChildContext(Context child)
    {
        adaptee.unregisterChildContext(child);
    }
}
