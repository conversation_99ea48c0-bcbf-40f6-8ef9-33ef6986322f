package ru.naumen.dynaform.client;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.ADMINISTRATOR_INTERFACE;
import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.OPERATOR_INTERFACE;
import static ru.naumen.core.shared.permission.PermissionType.ALL;

import com.google.gwt.dom.client.Style.Position;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.user.client.ui.RootPanel;

import jakarta.inject.Inject;
import ru.naumen.core.client.AbstractMainDisplayImpl;
import ru.naumen.core.client.CoreGinjector.UncaughtExceptionHandlerFactory;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.permission.AdminPermissionCheckServiceAsync;
import ru.naumen.core.client.widgets.AttentionWidget;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.permission.dispatch.CheckAdminPermissionActionResponse;
import ru.naumen.core.shared.utils.ReadyState;

/**
 * Реализация OperatorDisplay
 * <AUTHOR>
 * @since 03.09.2010
 */
public class OperatorDisplayImpl extends AbstractMainDisplayImpl implements OperatorDisplay
{
    private final AttentionWidget attentionWidget;
    private final AdminPermissionCheckServiceAsync adminPermissionCheckServiceAsync;
    private final SecurityHelper securityHelper;

    @Inject
    public OperatorDisplayImpl(UncaughtExceptionHandlerFactory handlerFactory,
            AttentionWidget attentionWidget,
            AdminPermissionCheckServiceAsync adminPermissionCheckServiceAsync,
            SecurityHelper securityHelper)
    {
        super(handlerFactory);
        this.attentionWidget = attentionWidget;
        this.adminPermissionCheckServiceAsync = adminPermissionCheckServiceAsync;
        this.securityHelper = securityHelper;
        headerPanel.getHeaderTools().setModeSwitchLink("../" + Constants.ADMIN_ALIAS + "/");

        WidgetResources.INSTANCE.all().ensureInjected();
        getHeaderPanel().addStyleName(WidgetResources.INSTANCE.all().flexGenerous());
    }

    @Override
    public void setAttentionBox(String warning)
    {
        attentionWidget.setText(warning);
        attentionWidget.getElement().getStyle().setZIndex(100000);
        attentionWidget.getElement().getStyle().setWidth(340.0, Unit.PX);
        attentionWidget.getElement().getStyle().setRight(1, Unit.PX);
        attentionWidget.getElement().getStyle().setBottom(2, Unit.PX);
        attentionWidget.getElement().getStyle().setPosition(Position.FIXED);
        attentionWidget.setVisible(true);
        RootPanel.get().add(attentionWidget);
    }

    @Override
    protected void onLoad()
    {
        ReadyState readyState = new ReadyState(this);
        adminPermissionCheckServiceAsync.hasPermission(OPERATOR_INTERFACE, ALL,
                new BasicCallback<CheckAdminPermissionActionResponse>(readyState)
                {
                    @Override
                    protected void handleSuccess(CheckAdminPermissionActionResponse response)
                    {
                        if (Boolean.FALSE.equals(response.hasPermission()))
                        {
                            headerPanel.getSearchPanel().setVisible(false);
                            headerPanel.getHeaderTools().showPermCheckModuleButton(false);
                        }
                    }
                });

        adminPermissionCheckServiceAsync.hasPermission(ADMINISTRATOR_INTERFACE, ALL,
                new BasicCallback<CheckAdminPermissionActionResponse>(readyState)
                {
                    @Override
                    protected void handleSuccess(CheckAdminPermissionActionResponse response)
                    {
                        if (Boolean.FALSE.equals(response.hasPermission())
                            || Boolean.FALSE.equals(securityHelper.isAdminInterfaceAllowed()))
                        {
                            headerPanel.getHeaderTools().hideModeSwitchLink();
                        }
                    }
                });

        readyState.onReady(super::onLoad);
    }

    @Override
    public void startProcessing()
    {
        headerPanel.getSearchPanel().addStyleName(WidgetResources.INSTANCE.all().disabledFontIconContainer());
    }

    @Override
    public void stopProcessing()
    {
        headerPanel.getSearchPanel().removeStyleName(WidgetResources.INSTANCE.all().disabledFontIconContainer());
    }
}
