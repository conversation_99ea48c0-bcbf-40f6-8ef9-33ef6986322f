package ru.naumen.dynaform.client.attributes;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.ModalFormDetector;
import ru.naumen.core.client.content.Context;
import ru.naumen.dynaform.client.DynaContextDecorator;
import ru.naumen.dynaform.client.actioncommand.FormContext;
import ru.naumen.metainfo.shared.ui.Form;

/**
 *
 * <AUTHOR>
 * @since 19 окт. 2015 г.
 */
public class ModalFormDetectorImpl implements ModalFormDetector
{
    @Override
    public boolean isOnModalForm(PresentationContext context)
    {
        Context parentContext = context.getParentContext();
        if (parentContext instanceof DynaContextDecorator)
        {
            parentContext = ((DynaContextDecorator)parentContext).getAdaptee();
        }
        if (parentContext instanceof FormContext)
        {
            Form form = ((FormContext)parentContext).getForm();
            return form == null || !form.isInline();
        }
        return true;
    }
}
