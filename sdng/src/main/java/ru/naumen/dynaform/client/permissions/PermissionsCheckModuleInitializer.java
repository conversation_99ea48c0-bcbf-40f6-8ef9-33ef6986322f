package ru.naumen.dynaform.client.permissions;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.inject.client.AsyncProvider;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.dynaform.client.permissions.events.ShowPermissionModuleEvent;
import ru.naumen.dynaform.client.permissions.events.ShowPermissionModuleEventHandler;

/**
 * Объект, инициальзирующий интерфейс модуля проверки прав
 *
 * <AUTHOR>
 * @since 8 июня 2016 г.
 */
public class PermissionsCheckModuleInitializer implements ShowPermissionModuleEventHandler
{
    @Inject
    private AsyncProvider<PermissionsCheckModulePresenter> provider;

    @Inject
    public PermissionsCheckModuleInitializer(EventBus eventBus)
    {
        eventBus.addHandler(ShowPermissionModuleEvent.getType(), this);
    }

    @Override
    public void onShowModule(ShowPermissionModuleEvent event)
    {
        if (!event.isShow())
        {
            return;
        }
        provider.get(new BasicCallback<PermissionsCheckModulePresenter>()
        {
            @Override
            protected void handleSuccess(PermissionsCheckModulePresenter presenter)
            {
                if (presenter.isBound())
                {
                    return;
                }
                presenter.bind();
            }
        });
    }
}
