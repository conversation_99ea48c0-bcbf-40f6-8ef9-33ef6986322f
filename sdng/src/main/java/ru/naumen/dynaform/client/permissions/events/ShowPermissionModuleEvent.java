package ru.naumen.dynaform.client.permissions.events;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие отображения интерфейса модуля проверки прав
 *
 * <AUTHOR>
 * @since 8 июня 2016 г.
 */
public class ShowPermissionModuleEvent extends GwtEvent<ShowPermissionModuleEventHandler>
{
    private static Type<ShowPermissionModuleEventHandler> TYPE = new Type<ShowPermissionModuleEventHandler>();

    public static Type<ShowPermissionModuleEventHandler> getType()
    {
        return TYPE;
    }

    private final boolean show;

    public ShowPermissionModuleEvent(boolean show)
    {
        this.show = show;
    }

    @Override
    public Type<ShowPermissionModuleEventHandler> getAssociatedType()
    {
        return TYPE;
    }

    /**
     * Если true - отобразить модуль
     * @return
     */
    public boolean isShow()
    {
        return show;
    }

    @Override
    protected void dispatch(ShowPermissionModuleEventHandler handler)
    {
        handler.onShowModule(this);
    }
}
