package ru.naumen.dynaform.client.push.log.table;

import java.util.Date;

import jakarta.inject.Inject;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.cell.client.ValueUpdater;
import com.google.gwt.dom.client.AnchorElement;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.shared.Constants.Push;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.push.log.NotificationLogResources;
import ru.naumen.metainfo.shared.Constants.Presentations;

/**
 * Ячейка, содержащая текст уведомления.
 * <AUTHOR>
 * @since Sep 25, 2019
 */
public class NotificationTextCell extends AbstractCell<NotificationLogItem>
{
    private static final String DATE_FORMAT = "dd.MM.yyyy HH:mm";

    private final CommonHtmlTemplates templates;
    private final NotificationLogResources resources;
    private final Formatters formatters;
    private final PresentationFactories presentationFactories;

    @Inject
    public NotificationTextCell(CommonHtmlTemplates templates, NotificationLogResources resources,
            Formatters formatters, PresentationFactories presentationFactories)
    {
        super(BrowserEvents.CLICK);
        this.templates = templates;
        this.resources = resources;
        this.formatters = formatters;
        this.presentationFactories = presentationFactories;
    }

    @Override
    public void onBrowserEvent(Context context, Element parent, NotificationLogItem value, NativeEvent event,
            ValueUpdater<NotificationLogItem> valueUpdater)
    {
        if (BrowserEvents.CLICK.equals(event.getType()) && AnchorElement.is(event.getEventTarget())
            && !StringUtilities.isEmpty(event.getEventTarget().<AnchorElement> cast().getHref()))
        {
            valueUpdater.update(value);
        }
        super.onBrowserEvent(context, parent, value, event, valueUpdater);
    }

    @Override
    public void render(Context context, NotificationLogItem value, SafeHtmlBuilder sb)
    {
        DtObject itemDto = value.getItem();
        if (itemDto.hasProperty(NotificationLogDataModel.GROUP_FIELD))
        {
            String icon = itemDto.getProperty(NotificationLogDataModel.GROUP_ICON);
            String style = resources.css().groupTitle();
            if (null != icon)
            {
                style += ' ' + icon;
            }
            sb.append(templates.div(style, itemDto.getTitle()));
            return;
        }
        String text = itemDto.getProperty(Push.TEXT);
        if (null != text)
        {
            String style = value.isExpanded()
                    ? resources.cellTableStyle().expandedNotification()
                    : resources.cellTableStyle().collapsedNotification();
            SafeHtmlBuilder contentBuilder = new SafeHtmlBuilder();
            SafeHtml html;
            if (isWrapPushInIframe())
            {
                PresentationContext prsContext = new PresentationContext(null, null, null);
                html = presentationFactories.getViewPresentationFactory(Presentations.RICH_TEXT_VIEW).createHtml(
                        prsContext, text);
            }
            else
            {
                html = SafeHtmlUtils.fromTrustedString(text);
            }
            contentBuilder
                    .append(templates.div(resources.cellTableStyle().notificationMask(), StringUtilities.EMPTY))
                    .append(templates.div(StringUtilities.EMPTY, html,
                            SafeHtmlUtils.EMPTY_SAFE_HTML));
            sb.append(templates.div(style, contentBuilder.toSafeHtml(), SafeHtmlUtils.EMPTY_SAFE_HTML));
        }
        Date date = itemDto.getProperty(Push.GENERATION_DATE);
        if (null != date)
        {
            sb.append(templates.div(resources.cellTableStyle().notificationDate(),
                    formatters.formatDateTime(date, null, DATE_FORMAT)));
        }
    }

    private native boolean isWrapPushInIframe()
        /*-{
            return $wnd.wrapPushInIframe;
        }-*/;
}
