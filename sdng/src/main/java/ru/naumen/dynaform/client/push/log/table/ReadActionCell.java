package ru.naumen.dynaform.client.push.log.table;

import jakarta.inject.Inject;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.cell.client.ValueUpdater;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.EventTarget;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.dynaform.client.push.log.NotificationLogMessages;
import ru.naumen.dynaform.client.push.log.NotificationLogResources;
import ru.naumen.dynaform.client.widgets.DynaformWidgetResources;

/**
 * Ячейка с действием пометки уведомления прочитанным/непрочитанным.
 * <AUTHOR>
 * @since Sep 25, 2019
 */
public class ReadActionCell extends AbstractCell<Boolean>
{
    private final NotificationLogResources resources;
    private final CommonHtmlTemplates templates;
    private final NotificationLogMessages messages;

    @Inject
    public ReadActionCell(NotificationLogResources resources, CommonHtmlTemplates templates,
            NotificationLogMessages messages)
    {
        super(BrowserEvents.CLICK);
        this.resources = resources;
        this.templates = templates;
        this.messages = messages;
    }

    @Override
    public void onBrowserEvent(Context context, Element parent, Boolean value, NativeEvent event,
            ValueUpdater<Boolean> valueUpdater)
    {
        super.onBrowserEvent(context, parent, value, event, valueUpdater);
        if (null != value && null != valueUpdater && isClickOnReadAction(event))
        {
            valueUpdater.update(!value);
            event.preventDefault();
        }
    }

    @Override
    public void render(Context context, Boolean value, SafeHtmlBuilder sb)
    {
        if (null != value)
        {
            String hint = value ? messages.markAsRead() : messages.markAsUnread();
            String styleName = resources.cellTableStyle().unreadNotification()
                               + ' ' + DynaformWidgetResources.INSTANCE.all().actionsForceEnabled()
                               + ' ' + IconCodes.DOT_MARK;
            sb.append(templates.divWithTitle(styleName, hint, StringUtilities.EMPTY));
        }
    }

    private boolean isClickOnReadAction(NativeEvent event)
    {
        EventTarget target = event.getEventTarget();
        return BrowserEvents.CLICK.equals(event.getType()) && Element.is(target)
               && Element.as(target).hasClassName(resources.cellTableStyle().unreadNotification());
    }
}
