<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
        xmlns:g="urn:import:com.google.gwt.user.client.ui"
        xmlns:toolbar="urn:import:ru.naumen.core.client.content.toolbar.display">
	<ui:with field="res" type="ru.naumen.objectlist.client.extended.advlist.AdvlistResources"/>
	<ui:with field="filterRes" type="ru.naumen.objectlist.client.mode.active.extended.advlist.filter.ListFilterResources"/>
	<ui:with field="widgetRes" type="ru.naumen.core.client.widgets.WidgetResources"/>

	<g:FlowPanel ui:field="panel" styleName="{res.advlist.bSmartList} {filterRes.style.bSmartListContent}">
		<g:FlowPanel ui:field="container"/>
		<g:FlowPanel ui:field="toolContainer"/>
		<g:FlowPanel ui:field="clearer" styleName="{widgetRes.main.clearer}"/>
	</g:FlowPanel>
</ui:UiBinder>
