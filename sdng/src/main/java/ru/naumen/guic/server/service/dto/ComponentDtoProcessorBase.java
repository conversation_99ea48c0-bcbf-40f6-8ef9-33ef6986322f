package ru.naumen.guic.server.service.dto;

import static ru.naumen.metainfo.shared.embeddedapplication.Constants.Application.ADMIN_PERMISSIONS;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.guic.shared.components.ComponentBase;
import ru.naumen.guic.shared.environment.IUIEnvironment;

/**
 * <AUTHOR>
 * @since Apr 21, 2015
 */
@Component("ComponentDtoProcessorBase")
public class ComponentDtoProcessorBase implements IComponentDtoProcessor
{
    private static final char MESSAGE_START_END_CHAR = ':';

    @Inject
    protected MessageFacade messages;

    @Override
    public void process(ComponentBase component, IUIEnvironment env)
    {
        processMessages(component);
        processPermissionVisible(component, env);
    }

    private void processPermissionVisible(ComponentBase component, IUIEnvironment env)
    {
        List<PermissionType> permissions = env.getProperty(ADMIN_PERMISSIONS);
        if (Boolean.TRUE.equals(component.isVisible()) && component.getPermissionVisibleCode() != null)
        {
            component.setVisible(permissions == null
                                 || permissions.contains(PermissionType.valueOf(component.getPermissionVisibleCode())));
        }
    }

    private boolean isMessageCode(String message)
    {
        //@formatter:off
        return message != null && message.length() >= 2 && 
                message.charAt(0) == MESSAGE_START_END_CHAR && 
                message.charAt(message.length() - 1) == MESSAGE_START_END_CHAR;
        //@formatter:on
    }

    private String processMessage(String message)
    {
        //@formatter:off
        return isMessageCode(message) 
                ? messages.getMessage(message.substring(1, message.length() - 1))
                : message;
        //@formatter:on
    }

    private void processMessages(ComponentBase component)
    {
        HashMap<String, String> messages = component.getMessages();
        for (Map.Entry<String, String> entry : messages.entrySet())
        {
            String value = processMessage(entry.getValue());
            entry.setValue(value);
        }
        component.setMessages(messages);

        for (ComponentBase child : component.getChilds())
        {
            processMessages(child);
        }
    }

}
