package ru.naumen.mailreader.server.receiver;

import jakarta.annotation.Nullable;

/**
 * Результат обработки письма (сохранения разобранного письма в базу данных и добавления письма в очередь на обработку)
 * @param succeed результат сохранения ({@code true}, если успешно. Иначе {@code false})
 * @param messageId идентификатор письма, однозначно определяющий письмо в
 *                  очереди писем на обработку. {@code null}, если письмо обработано неуспешно
 */
public record DecodingResult(boolean succeed, @Nullable String messageId)
{
}
