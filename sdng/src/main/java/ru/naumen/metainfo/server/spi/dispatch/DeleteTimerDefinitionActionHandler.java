package ru.naumen.metainfo.server.spi.dispatch;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TIMERS;
import static ru.naumen.core.shared.permission.PermissionType.DELETE;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.core.server.script.storage.modification.utils.TimerScriptModificationUtils;
import ru.naumen.core.server.sets.usage.BeforeDeleteMetaInfoElementSettingsSetEvent;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.timer.DeleteTimerDefinitionAction;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.sec.server.admin.log.ProcessSettingsLogService;
import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.sec.server.admin.log.ScriptLogService;

/**
 * Обработчик действия по удалению счётчика времени.
 *
 * <AUTHOR>
 * @since 17.04.2012
 *
 */
@Component
public class DeleteTimerDefinitionActionHandler extends
        TransactionalActionHandler<DeleteTimerDefinitionAction, EmptyResult>
{
    private final MetainfoServicePersister persister;
    private final MetainfoServiceBean metainfoService;
    private final HandlerUtils handlerUtils;
    private final MetainfoModification metainfoModification;
    private final ProcessSettingsLogService logService;
    private final ScriptLogService scriptLogService;
    private final TimerScriptModificationUtils timerScriptModificationUtils;
    private final AdminPermissionCheckService adminPermissionCheckService;
    private final ApplicationEventPublisher eventPublisher;

    @Inject
    public DeleteTimerDefinitionActionHandler(MetainfoServicePersister persister,
            MetainfoServiceBean metainfoService,
            HandlerUtils handlerUtils,
            MetainfoModification metainfoModification,
            ProcessSettingsLogService logService,
            ScriptLogService scriptLogService,
            TimerScriptModificationUtils timerScriptModificationUtils,
            AdminPermissionCheckService adminPermissionCheckService,
            ApplicationEventPublisher eventPublisher)
    {
        this.persister = persister;
        this.metainfoService = metainfoService;
        this.handlerUtils = handlerUtils;
        this.metainfoModification = metainfoModification;
        this.logService = logService;
        this.scriptLogService = scriptLogService;
        this.timerScriptModificationUtils = timerScriptModificationUtils;
        this.adminPermissionCheckService = adminPermissionCheckService;
        this.eventPublisher = eventPublisher;
    }

    @Override
    public EmptyResult executeInTransaction(DeleteTimerDefinitionAction action, ExecutionContext context)
            throws DispatchException
    {
        metainfoModification.modify(MetainfoRegion.TIMERS);

        String timerDefinitionCode = action.getTimerDefinitionCode();
        TimerDefinition timerDefinition = metainfoService.getTimerDefinition(timerDefinitionCode);
        adminPermissionCheckService.checkPermission(timerDefinition, TIMERS, DELETE);
        handlerUtils.checkTimerUsageInAttr(timerDefinitionCode, "DeleteTimerDefinitionActionHandler.error");

        List<ScriptAdminLogInfo> scriptsLog = new ArrayList<>(
                timerScriptModificationUtils.processDeleteScript(timerDefinition));
        eventPublisher.publishEvent(new BeforeDeleteMetaInfoElementSettingsSetEvent(timerDefinition,
                timerDefinition.getSettingsSet(), null));
        persister.deleteTimerDefinition(timerDefinitionCode);
        logService.timerDefinitionDelete(timerDefinition);
        metainfoService.deleteTimerDefinitionFromCache(timerDefinitionCode);
        scriptLogService.makeLogs(scriptsLog);
        return EmptyResult.instance;
    }
}
