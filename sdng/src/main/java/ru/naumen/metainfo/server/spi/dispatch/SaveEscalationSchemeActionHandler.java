package ru.naumen.metainfo.server.spi.dispatch;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.ESCALATIONS;
import static ru.naumen.core.shared.permission.PermissionType.CREATE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.catalog.valuemap.ValueMapCatalogItemDao;
import ru.naumen.core.server.catalog.valuemap.ValueMapCatalogItemImpl;
import ru.naumen.core.server.escalation.EscalationSchemeLevelValue;
import ru.naumen.core.server.escalation.EscalationSchemeValue;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.sets.usage.BeforeEditMetaInfoElementSettingsSetEvent;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.core.shared.escalation.EscalationScheme.StateCode;
import ru.naumen.core.shared.escalation.EscalationSchemeClient;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.SaveEscalationSchemeAction;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.sec.server.admin.log.EscalationLogService;

/**
 * <AUTHOR>
 * @since 20.07.2012
 *
 */
@Component
public class SaveEscalationSchemeActionHandler
        extends TransactionalActionHandler<SaveEscalationSchemeAction, SimpleResult<DtoContainer<EscalationScheme>>>
{
    //Оставляет те действия, типы которых не входят в заданный набор типов (с учетом вложения в класс)
    private static class EventActionFqnFilter implements Predicate<EventAction>
    {
        private final Collection<ClassFqn> permittedTypes;

        public EventActionFqnFilter(Collection<ClassFqn> permittedTypes)
        {
            this.permittedTypes = permittedTypes;
        }

        @Override
        public boolean test(EventAction input)
        {
            if (input == null)
            {
                return false;
            }
            for (ClassFqn fqn : input.getLinkedClasses())
            {
                if (!permittedTypes.contains(fqn) && !permittedTypes.contains(fqn.fqnOfClass()))
                {
                    return true;
                }
            }
            return false;
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(SaveEscalationSchemeActionHandler.class);

    private final Function<String, EventAction> EVENT_ACTION_EXTRACTOR = new Function<String, EventAction>()
    {

        @Override
        public EventAction apply(String input)
        {
            if (input == null)
            {
                return null;
            }
            return eventActionService.getEventAction(input);
        }
    };

    private final EventActionService eventActionService;
    private final MetaStorageService metastorageService;
    private final MetainfoService metainfoService;
    private final MappingService mappingService;
    private final MessageFacade messages;
    private final ValueMapCatalogItemDao<ValueMapCatalogItemImpl> vmapDao;
    private final I18nUtil i18nUtil;
    private final MetainfoModification metainfoModification;
    private final EscalationLogService logService;
    private final ApplicationEventPublisher eventPublisher;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public SaveEscalationSchemeActionHandler(EventActionService eventActionService,
            MetaStorageService metastorageService,
            MetainfoService metainfoService,
            MappingService mappingService,
            MessageFacade messages,
            ValueMapCatalogItemDao<ValueMapCatalogItemImpl> vmapDao,
            I18nUtil i18nUtil,
            MetainfoModification metainfoModification,
            EscalationLogService logService,
            ApplicationEventPublisher eventPublisher,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.eventActionService = eventActionService;
        this.metastorageService = metastorageService;
        this.metainfoService = metainfoService;
        this.mappingService = mappingService;
        this.messages = messages;
        this.vmapDao = vmapDao;
        this.i18nUtil = i18nUtil;
        this.metainfoModification = metainfoModification;
        this.logService = logService;
        this.eventPublisher = eventPublisher;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public SimpleResult<DtoContainer<EscalationScheme>> executeInTransaction(SaveEscalationSchemeAction action,
            ExecutionContext context) throws DispatchException
    {
        metainfoModification.modify(MetainfoRegion.ESCALATION);

        EscalationSchemeClient scheme = action.getEscalationScheme();
        EscalationSchemeValue oldValue = getEscalationSchemeValue(action.isNewEscalationScheme(), scheme.getCode());
        String oldSettingsSet = oldValue.getSettingsSet();

        boolean isNewScheme = action.isNewEscalationScheme();
        if (isNewScheme)
        {
            adminPermissionCheckService.checkPermission(scheme, ESCALATIONS, CREATE);
        }
        else
        {
            adminPermissionCheckService.checkPermission(oldValue, ESCALATIONS, EDIT);
        }

        validateEscalationScheme(scheme, action.isNewEscalationScheme());
        MapProperties oldProperties = logService.getEscalationSchemeInfo(oldValue);
        EscalationSchemeValue value = mappingService.transform(scheme, oldValue);
        I18nUtil.copyI18nTitles(getEscalationSchemeValue(isNewScheme, scheme.getCode()), value);
        I18nUtil.copyI18nDescr(getEscalationSchemeValue(isNewScheme, scheme.getCode()), value);
        i18nUtil.updateI18nObjectTitle(value, i18nUtil.getLocalizedTitle(scheme));
        i18nUtil.updateI18nObjectDescription(value, i18nUtil.getLocalizedDescription(scheme));
        metainfoService.addEscalationScheme(value);
        metastorageService.save(value, Constants.ESCALATION_SCHEME, value.getCode());

        if (isNewScheme)
        {
            logService.escalationSchemeAdd(value);
            eventPublisher.publishEvent(new BeforeEditMetaInfoElementSettingsSetEvent(value, null,
                    value.getSettingsSet()));
        }
        else
        {
            logService.escalationSchemeEdit(value, oldProperties);
            eventPublisher.publishEvent(new BeforeEditMetaInfoElementSettingsSetEvent(value, oldSettingsSet,
                    value.getSettingsSet()));
        }

        EscalationScheme result = mappingService.transform(value, new EscalationScheme());
        DtoContainer<EscalationScheme> dtoContainer = new DtoContainer<>(result);
        dtoContainer.setProperty(SettingsSet.ADMIN_PERMISSIONS,
                adminPermissionCheckService.getPermissions(result));
        return new SimpleResult<>(dtoContainer);
    }

    private EscalationSchemeValue getEscalationSchemeValue(boolean isNewEscalationScheme, String code)
    {
        if (isNewEscalationScheme)
        {
            return new EscalationSchemeValue(code);
        }

        return metastorageService.get(Constants.ESCALATION_SCHEME, code);
    }

    private void validateActions(EscalationSchemeClient scheme)
    {
        EscalationSchemeValue oldScheme;
        try
        {
            oldScheme = metainfoService.getEscalationScheme(scheme.getCode());
        }
        catch (FxException e)
        {
            //Такой схемы эскалации еще нет => проблем с действиями в ней не будет, т.к. их еще нет.
            logger.debug(e.getMessage(), e);
            return;
        }
        Set<EventAction> actions = new HashSet<>();
        for (EscalationSchemeLevelValue level : oldScheme.getLevels())
        {
            actions.addAll(level.getActions()
                    .stream()
                    .map(EVENT_ACTION_EXTRACTOR)
                    .toList());
        }
        Collection<EventAction> actionsWithBadFqns = actions.stream()
                .filter(new EventActionFqnFilter(scheme.getTargetTypes()))
                .toList();
        if (actionsWithBadFqns.isEmpty())
        {
            return;
        }
        throw new FxException(messages.getMessage("EscalationScheme.eventActionsBadFqn",
                actionsWithBadFqns.stream().map(i18nUtil.getTitleExtractor()).toList()));
    }

    private void validateEscalationScheme(EscalationSchemeClient scheme, boolean isNewEscalationScheme)
    {
        if (isNewEscalationScheme)
        {
            if (metainfoService.getEscalationSchemes().stream()
                    .anyMatch(escalationScheme -> escalationScheme.getCode().equalsIgnoreCase(scheme.getCode())))
            {
                throw new FxException(messages.getMessage("EscalationScheme.codeMustBeUnic", scheme.getCode()));
            }
        }
        else
        {
            if (StateCode.OFF.equals(scheme.getState()))
            {
                validateState(scheme);
            }
            validateActions(scheme);
        }
    }

    private void validateState(EscalationSchemeClient scheme)
    {
        DtoCriteria criteria = new DtoCriteria(ru.naumen.core.shared.Constants.ValueMapCatalogItem.FQN);
        criteria.addFilters(Filters.eq(ru.naumen.core.shared.Constants.ValueMapCatalogItem.TYPE,
                ru.naumen.core.shared.Constants.ValueMapCatalogItem.ESCALATION_TYPE));
        criteria.addFilters(
                Filters.eq(ru.naumen.core.shared.Constants.ValueMapCatalogItem.DEFAULT_OBJECT, scheme.getCode()));
        List<ValueMapCatalogItemImpl> vmapsWithSchemeAsDefaultObject = vmapDao.list(criteria);
        if (vmapsWithSchemeAsDefaultObject.isEmpty())
        {
            return;
        }
        throw new FxException(messages.getMessage("EscalationScheme.usedAsDefaultValue",
                Lists.transform(vmapsWithSchemeAsDefaultObject, CommonUtils.TitleExtractor.INSTANCE)));
    }
}