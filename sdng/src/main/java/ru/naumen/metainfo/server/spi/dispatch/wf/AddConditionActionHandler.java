package ru.naumen.metainfo.server.spi.dispatch.wf;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.WORKFLOW;
import static ru.naumen.core.shared.permission.PermissionType.CREATE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.List;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;

import java.util.ArrayList;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyContext;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyRegistry;
import ru.naumen.core.server.script.storage.modification.usage.WfActionConditionScriptModifyProcess;
import ru.naumen.core.server.sets.usage.BeforeEditMetaInfoElementSettingsSetEvent;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.core.shared.script.places.WorkflowCategory;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.elements.wf.ConditionImpl;
import ru.naumen.metainfo.server.spi.elements.wf.StateDeclarationImpl;
import ru.naumen.metainfo.server.spi.elements.wf.WorkflowImpl;
import ru.naumen.metainfo.shared.dispatch2.wf.AddConditionAction;
import ru.naumen.metainfo.shared.dispatch2.wf.GetConditionResult;
import ru.naumen.metainfo.shared.dispatch2.wf.WfConstants;
import ru.naumen.metainfo.shared.elements.wf.Condition;
import ru.naumen.metainfo.shared.elements.wf.Condition.ConditionType;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.sec.server.admin.log.MetaClassLogService;
import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.sec.server.admin.log.ScriptLogService;

/**
 * Обработчик {@link AddConditionAction команды} добавления {@link Condition условия} входа/выхода из состояния
 *
 * <AUTHOR>
 *
 */
@Component
public class AddConditionActionHandler extends TransactionalActionHandler<AddConditionAction, GetConditionResult>
{
    private final MetainfoServiceBean metainfoService;
    private final SnapshotService snapshotService;
    private final MetainfoServicePersister persister;
    private final MetainfoModification metainfoModification;
    private final MetaClassLogService logService;
    private final ScriptLogService scriptLogService;
    private final ScriptModifyRegistry scriptModifyRegistry;
    private final AdminPermissionCheckService adminPermissionCheckService;
    private final ApplicationEventPublisher eventPublisher;

    @Inject
    public AddConditionActionHandler(MetainfoServiceBean metainfoService,
            SnapshotService snapshotService,
            MetainfoServicePersister persister,
            MetainfoModification metainfoModification,
            MetaClassLogService logService,
            ScriptLogService scriptLogService,
            ScriptModifyRegistry scriptModifyRegistry,
            AdminPermissionCheckService adminPermissionCheckService,
            ApplicationEventPublisher eventPublisher)
    {
        this.metainfoService = metainfoService;
        this.snapshotService = snapshotService;
        this.persister = persister;
        this.metainfoModification = metainfoModification;
        this.logService = logService;
        this.scriptLogService = scriptLogService;
        this.scriptModifyRegistry = scriptModifyRegistry;
        this.adminPermissionCheckService = adminPermissionCheckService;
        this.eventPublisher = eventPublisher;
    }

    @Override
    public GetConditionResult executeInTransaction(AddConditionAction action, ExecutionContext context)
            throws DispatchException
    {
        adminPermissionCheckService.checkPermission(WORKFLOW, EDIT);

        metainfoModification.modify(MetainfoRegion.METACLASS_DECLARATIONS);

        MetaClassImpl metaClass = metainfoService.getMetaClass(action.getMetaClass());
        StateDeclarationImpl declaration = getDeclaration(action, metaClass);
        List<ScriptAdminLogInfo> scriptsLog = new ArrayList<>();
        ConditionImpl condition = buildCondition(action, declaration, scriptsLog);

        adminPermissionCheckService.checkPermission(condition, CREATE);

        WorkflowImpl wf = declaration.getWorkflow();
        logService.stateConditionAdd(condition, wf.getState(action.getState()), metaClass);

        eventPublisher.publishEvent(
                new BeforeEditMetaInfoElementSettingsSetEvent(condition, null, condition.getSettingsSet()));
        persister.persist(metaClass);
        scriptLogService.makeLogs(scriptsLog);
        Condition snapshot = snapshotService.prepare(condition, Condition.class);
        return new GetConditionResult(snapshot);
    }

    private ConditionImpl buildCondition(AddConditionAction action, StateDeclarationImpl declaration,
            List<ScriptAdminLogInfo> scriptsLog)
    {
        WorkflowImpl wf = declaration.getWorkflow();
        String conditionCode = UUIDGenerator.get().nextUUID();
        ConditionImpl condition;
        String state = action.getState();
        if (action.isPreCondition())
        {
            condition = wf.addDeclaredPreCondition(state, conditionCode);
        }
        else
        {
            condition = wf.addDeclaredPostCondition(state, conditionCode);
        }
        condition.setPreCondition(action.isPreCondition());
        condition.setType(action.getType());
        condition.addTitle(LocaleContextHolder.getLocale().getLanguage(), action.getTitle());

        scriptsLog.addAll(processSaveScripts(action, condition));

        IProperties properties = new MapProperties(condition.getProperties());
        for (String name : action.getProperties().propertyNames())
        {
            if (WfConstants.SCRIPT_PROPERTY.equals(name) || WfConstants.SCRIPT_DTO_PROPERTY.equals(name))
            {
                continue;
            }
            Object property = action.getProperties().getProperty(name);
            properties.setProperty(name, property);
        }
        condition.setProperties(properties);
        return condition;
    }

    private StateDeclarationImpl getDeclaration(AddConditionAction condition, MetaClassImpl metaClass)
    {
        WorkflowImpl workflow = metaClass.getWorkflow();
        StateDeclarationImpl declaration = workflow.getStateDeclaration(condition.getState());
        if (null == declaration || declaration.isHardcoded())
        {
            declaration = workflow.getStateOverride(condition.getState());
            if (null == declaration)
            {
                declaration = workflow.addStateOverride(condition.getState());
            }
        }
        return declaration;
    }

    private List<ScriptAdminLogInfo> processSaveScripts(AddConditionAction action, ConditionImpl holder)
    {
        if (holder.getType() != ConditionType.SCRIPT)
        {
            return new ArrayList<>();
        }

        ScriptDto scriptDto = action.getProperties().getProperty(WfConstants.SCRIPT_DTO_PROPERTY);
        Preconditions.checkNotNull(scriptDto, "AddConditionAction must have scriptDto property!");

        ScriptModifyProcess<ConditionImpl> process = scriptModifyRegistry.getProcess(holder);
        ScriptModifyContext context = new ScriptModifyContext(WorkflowCategory.WF_SCRIPT_CONDITION,
                ScriptHolders.WORKFLOW);
        context.setProperty(WfActionConditionScriptModifyProcess.META_CLASS_FQN, action.getMetaClass().asString());
        context.setProperty(WfActionConditionScriptModifyProcess.STATE_CODE, action.getState());
        process.save(null, holder, scriptDto, context);
        return context.getScriptsLogInfo();
    }
}
