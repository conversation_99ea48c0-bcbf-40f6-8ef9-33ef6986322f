package ru.naumen.metainfo.server.spi.store;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.IHasI18nTitle;
import ru.naumen.metainfo.shared.Constants.ElementTypes;
import ru.naumen.metainfo.shared.elements.HasElementId;
import ru.naumen.metainfo.shared.elements.wf.StateResponsible;
import ru.naumen.metainfo.shared.segment.MetainfoSegment;
import ru.naumen.metainfo.shared.segment.MetainfoSegmentNames.WorkflowLevelSegments;
import ru.naumen.metainfo.shared.sets.HasSettingsSet;
import ru.naumen.metainfo.shared.tags.HasTags;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * <p>Java class for State complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="State">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="title" type="{http://www.naumen.ru/metaclass-srv}LocalizedString" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="description" type="{http://www.naumen.ru/metaclass-srv}LocalizedString" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="responsible" type="{http://www.naumen.ru/metaclass-srv}StateResponsible" minOccurs="0" form="unqualified"/>
 *         &lt;element name="preAction" type="{http://www.naumen.ru/metaclass-srv}WfAction" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="postAction" type="{http://www.naumen.ru/metaclass-srv}WfAction" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="preActionsOrder" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="postActionsOrder" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="preCondition" type="{http://www.naumen.ru/metaclass-srv}WfCondition" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="postCondition" type="{http://www.naumen.ru/metaclass-srv}WfCondition" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="stateSetting" type="{http://www.naumen.ru/metaclass-srv}StateSetting" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *       &lt;attribute name="code" use="required" type="{http://www.naumen.ru/metaclass-srv}MetaId" />
 *       &lt;attribute name="enabled" type="{http://www.w3.org/2001/XMLSchema}boolean" />
 *       &lt;attribute name="hardcoded" type="{http://www.w3.org/2001/XMLSchema}boolean" />
 *       &lt;attribute name="color" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="responsibleType" type="{http://www.w3.org/2001/XMLSchema}string" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "State", propOrder = { "title", "description", "responsible", "changeResponsibleButtonVisible",
        "stateSetting", "preAction", "postAction", "preActionsOrder", "postActionsOrder", "preCondition",
        "postCondition", "showAttributesDescription", "tags", "endState", "disableEditableEndState", "settingsSet" })
public class State extends MetainfoSegment implements HasCode, HasTags, IHasI18nTitle, HasSettingsSet, HasElementId
{
    protected ArrayList<LocalizedString> title;
    protected ArrayList<LocalizedString> description;
    @XmlElement()
    protected StateResponsible responsible;
    @XmlElement()
    protected Boolean changeResponsibleButtonVisible;
    @XmlElement()
    protected Boolean showAttributesDescription;
    protected ArrayList<WfAction> preAction;
    protected ArrayList<WfAction> postAction;
    protected ArrayList<String> preActionsOrder;
    protected ArrayList<String> postActionsOrder;
    protected ArrayList<WfCondition> preCondition;
    protected ArrayList<WfCondition> postCondition;
    protected ArrayList<StateSetting> stateSetting;
    @XmlAttribute(required = true)
    protected String code;
    @XmlAttribute
    protected Boolean enabled;
    @XmlAttribute
    protected Boolean hardcoded;
    @XmlAttribute
    protected String color;
    @XmlAttribute
    protected String responsibleType;
    protected ArrayList<String> tags;
    /**
     * Комплект настроек
     */
    @XmlElement(name = "set", required = false)
    protected String settingsSet;
    @XmlElement()
    protected Boolean endState;
    @XmlElement()
    protected Boolean disableEditableEndState;

    @Override
    public boolean equals(Object obj)
    {
        if (!(obj instanceof State))
        {
            return false;
        }
        if (this == obj)
        {
            return true;
        }
        State other = (State)obj;

        List<StateSetting> copyStateSetting = Lists.newArrayList(getStateSetting());
        Collections.sort(getStateSetting());

        List<StateSetting> copyOtherStateSetting = Lists.newArrayList(other.getStateSetting());
        Collections.sort(other.getStateSetting());

        List<String> copyPreActionsOrder = Lists.newArrayList(getPreActionsOrder());
        Collections.sort(getPreActionsOrder());

        List<String> copyOtherPreActionsOrder = Lists.newArrayList(other.getPreActionsOrder());
        Collections.sort(other.getPreActionsOrder());

        List<String> copyPostActionsOrder = Lists.newArrayList(getPostActionsOrder());
        Collections.sort(getPostActionsOrder());

        List<String> copyOtherPostActionsOrder = Lists.newArrayList(other.getPostActionsOrder());
        Collections.sort(other.getPostActionsOrder());

        Comparator<WfAction> wfActionComparator = Comparator.comparing(WfAction::getCode);

        List<WfAction> copyPreAction = Lists.newArrayList(getPreAction());
        copyPreAction.sort(wfActionComparator);

        List<WfAction> copyOtherPreAction = Lists.newArrayList(other.getPreAction());
        copyOtherPreAction.sort(wfActionComparator);

        List<WfAction> copyPostAction = Lists.newArrayList(getPostAction());
        copyPostAction.sort(wfActionComparator);

        List<WfAction> copyOtherPostAction = Lists.newArrayList(other.getPostAction());
        copyOtherPostAction.sort(wfActionComparator);

        Comparator<WfCondition> wfConditionComparator = Comparator.comparing(WfCondition::getCode);

        List<WfCondition> copyPreCondition = Lists.newArrayList(getPreCondition());
        copyPreCondition.sort(wfConditionComparator);

        List<WfCondition> copyOtherPreCondition = Lists.newArrayList(other.getPreCondition());
        copyOtherPreCondition.sort(wfConditionComparator);

        List<WfCondition> copyPostCondition = Lists.newArrayList(getPostCondition());
        copyPostCondition.sort(wfConditionComparator);

        List<WfCondition> copyOtherPostCondition = Lists.newArrayList(other.getPostCondition());
        copyOtherPostCondition.sort(wfConditionComparator);

        List<String> copyTags = Lists.newArrayList(getTags());
        Collections.sort(copyTags);

        List<String> copyOtherTags = Lists.newArrayList(other.getTags());
        Collections.sort(copyOtherTags);

        boolean result = EqualsBuilder.reflectionEquals(this, other);

        getStateSetting().clear();
        getStateSetting().addAll(copyStateSetting);

        other.getStateSetting().clear();
        other.getStateSetting().addAll(copyOtherStateSetting);

        getPreActionsOrder().clear();
        getPreActionsOrder().addAll(copyPreActionsOrder);

        other.getPreActionsOrder().clear();
        other.getPreActionsOrder().addAll(copyOtherPreActionsOrder);

        getPostActionsOrder().clear();
        getPostActionsOrder().addAll(copyPostActionsOrder);

        other.getPostActionsOrder().clear();
        other.getPostActionsOrder().addAll(copyOtherPostActionsOrder);

        getPreAction().clear();
        getPreAction().addAll(copyPreAction);

        other.getPreAction().clear();
        other.getPreAction().addAll(copyOtherPreAction);

        getPostAction().clear();
        getPostAction().addAll(copyPostAction);

        other.getPostAction().clear();
        other.getPostAction().addAll(copyOtherPostAction);

        getPreCondition().clear();
        getPreCondition().addAll(copyPreCondition);

        other.getPreCondition().clear();
        other.getPreCondition().addAll(copyOtherPreCondition);

        getPostCondition().clear();
        getPostCondition().addAll(copyPostCondition);

        other.getPostCondition().clear();
        other.getPostCondition().addAll(copyOtherPostCondition);

        getTags().clear();
        getTags().addAll(copyTags);

        other.getTags().clear();
        other.getTags().addAll(copyOtherTags);

        return result;
    }

    /**
     * Gets the value of the changeResponsibleButtonVisible property.
     *
     * @return
     *     possible object is
     *     {@link Boolean }
     *
     */
    public Boolean getChangeResponsibleButtonVisible()
    {
        return changeResponsibleButtonVisible;
    }

    /**
     * Gets the value of the endState property.
     *
     * @return possible object is {@link Boolean }
     *
     */
    public Boolean isEndState()
    {
        return endState;
    }

    /**
     * Gets the value of the disableEditableEndState property.
     *
     * @return possible object is {@link Boolean }
     *
     */
    public Boolean isDisableEditableEndState()
    {
        return disableEditableEndState;
    }

    /**
     * Gets the value of the code property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    @Override
    public String getCode()
    {
        return code;
    }

    /**
     * Gets the value of the color property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getColor()
    {
        return color;
    }

    /**
     * Gets the value of the description property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the description property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDescription().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link LocalizedString }
     *
     *
     */
    public List<LocalizedString> getDescription()
    {
        if (description == null)
        {
            description = new ArrayList<>();
        }
        return this.description;
    }

    /**
     * Gets the value of the postAction property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the postAction property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPostAction().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WfAction }
     *
     *
     */
    public List<WfAction> getPostAction()
    {
        if (postAction == null)
        {
            postAction = new ArrayList<>();
        }
        return this.postAction;
    }

    /**
     * Gets the value of the postActionsOrder property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the postActionsOrder property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPostActionsOrder().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     *
     *
     */
    public List<String> getPostActionsOrder()
    {
        if (postActionsOrder == null)
        {
            postActionsOrder = new ArrayList<>();
        }
        return this.postActionsOrder;
    }

    /**
     * Gets the value of the postCondition property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the postCondition property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPostCondition().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WfCondition }
     *
     *
     */
    public List<WfCondition> getPostCondition()
    {
        if (postCondition == null)
        {
            postCondition = new ArrayList<>();
        }
        return this.postCondition;
    }

    /**
     * Gets the value of the preAction property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the preAction property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPreAction().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WfAction }
     *
     *
     */
    public List<WfAction> getPreAction()
    {
        if (preAction == null)
        {
            preAction = new ArrayList<>();
        }
        return this.preAction;
    }

    /**
     * Gets the value of the preActionsOrder property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the preActionsOrder property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPreActionsOrder().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     *
     *
     */
    public List<String> getPreActionsOrder()
    {
        if (preActionsOrder == null)
        {
            preActionsOrder = new ArrayList<>();
        }
        return this.preActionsOrder;
    }

    /**
     * Gets the value of the preCondition property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the preCondition property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPreCondition().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WfCondition }
     *
     *
     */
    public List<WfCondition> getPreCondition()
    {
        if (preCondition == null)
        {
            preCondition = new ArrayList<>();
        }
        return this.preCondition;
    }

    /**
     * Gets the value of the responsible property.
     *
     * @return
     *     possible object is
     *     {@link StateResponsible }
     *
     */
    public StateResponsible getResponsible()
    {
        return responsible;
    }

    /**
     * Gets the value of the responsibleType property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getResponsibleType()
    {
        return responsibleType;
    }

    /**
     * Gets the value of the tags property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the tags property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTags().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     *
     *
     */
    @Override
    public List<String> getTags()
    {
        if (null == tags)
        {
            tags = new ArrayList<>();
        }
        return tags;
    }

    @Override
    public String getSegmentID()
    {
        return getCode();
    }

    @Override
    public String getSegmentType()
    {
        return WorkflowLevelSegments.STATE;
    }

    @Override
    public boolean isDetachableSegment()
    {
        return true;
    }

    /**
     * Gets the value of the showAttributesDescription property.
     *
     * @return
     *     possible object is
     *     {@link Boolean }
     *
     */
    public Boolean isShowAttributesDescription()
    {
        return showAttributesDescription;
    }

    /**
     * Gets the value of the stateSetting property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the stateSetting property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getStateSetting().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link StateSetting }
     *
     *
     */
    public List<StateSetting> getStateSetting()
    {
        if (stateSetting == null)
        {
            stateSetting = new ArrayList<>();
        }
        return this.stateSetting;
    }

    /**
     * Gets the value of the title property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the title property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTitle().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link LocalizedString }
     *
     *
     */
    @Override
    public List<LocalizedString> getTitle()
    {
        if (title == null)
        {
            title = new ArrayList<>();
        }
        return this.title;
    }

    @Override
    public int hashCode()
    {
        return HashCodeBuilder.reflectionHashCode(this);
    }

    /**
     * Gets the value of the enabled property.
     *
     * @return
     *     possible object is
     *     {@link Boolean }
     *
     */
    public Boolean isEnabled()
    {
        return enabled;
    }

    /**
     * Gets the value of the hardcoded property.
     *
     * @return
     *     possible object is
     *     {@link Boolean }
     *
     */
    public Boolean isHardcoded()
    {
        return hardcoded;
    }

    /**
     * Sets the value of the changeResponsibleButtonVisible property.
     *
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *
     */
    public void setChangeResponsibleButtonVisible(Boolean value)
    {
        this.changeResponsibleButtonVisible = value;
    }

    /**
     * Sets the value of the code property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCode(String value)
    {
        this.code = value;
    }

    /**
     * Sets the value of the color property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setColor(String value)
    {
        this.color = value;
    }

    /**
     * Sets the value of the enabled property.
     *
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *
     */
    public void setEnabled(Boolean value)
    {
        this.enabled = value;
    }

    /**
     * Sets the value of the hardcoded property.
     *
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *
     */
    public void setHardcoded(Boolean value)
    {
        this.hardcoded = value;
    }

    /**
     * Sets the value of the responsible property.
     *
     * @param value
     *     allowed object is
     *     {@link StateResponsible }
     *
     */
    public void setResponsible(StateResponsible value)
    {
        this.responsible = value;
    }

    /**
     * Sets the value of the responsibleType property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setResponsibleType(String value)
    {
        this.responsibleType = value;
    }

    /**
     * Sets the value of the showAttributesDescription property.
     *
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *
     */
    public void setShowAttributeDescription(Boolean value)
    {
        this.showAttributesDescription = value;
    }

    /**
     * Sets the value of the endState property.
     * @param value allowed object is {@link Boolean }
     */
    public void setEndState(Boolean value)
    {
        this.endState = value;
    }

    /**
     * Sets the value of the disableEditableEndState property.
     * @param value allowed object is {@link Boolean }
     */
    public void setDisableEditableEndState(Boolean value)
    {
        this.disableEditableEndState = value;
    }

    @Override
    public @Nullable String getSettingsSet()
    {
        return settingsSet;
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet)
    {
        this.settingsSet = settingsSet;
    }

    @XmlTransient
    @Override
    public String getElementType()
    {
        return ElementTypes.STATE;
    }

    @XmlTransient
    @Override
    public String getElementCode()
    {
        return getCode();
    }
}
