package ru.naumen.metainfo.server.spi.store;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Пользовательский справочник. 
 *
 * <p>Java class for UserCatalog complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="UserCatalog">
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.naumen.ru/metaclass-srv}Catalog">
 *       &lt;sequence>
 *         &lt;element name="itemMetaClassFqn" type="{http://www.naumen.ru/metaclass-srv}ClassFqn"/>
 *       &lt;/sequence>
 *       &lt;attribute name="flat" type="{http://www.w3.org/2001/XMLSchema}boolean" default="true" />
 *       &lt;attribute name="withFolders" type="{http://www.w3.org/2001/XMLSchema}boolean" default="false" />
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UserCatalog", propOrder = { "itemMetaClassFqn" })
public class UserCatalog extends Catalog
{
    @XmlElement(required = true)
    protected ClassFqn itemMetaClassFqn;
    @XmlAttribute
    protected Boolean flat;
    @XmlAttribute
    protected Boolean withFolders;

    @Override
    public boolean equals(Object obj)
    {
        if (!(obj instanceof UserCatalog))
        {
            return false;
        }
        if (this == obj)
        {
            return true;
        }
        UserCatalog other = (UserCatalog)obj;
        return EqualsBuilder.reflectionEquals(this, other);
    }

    /**
     * Gets the value of the itemMetaClassFqn property.
     *
     * @return
     *     possible object is
     *     {@link ClassFqn }
     *
     */
    public ClassFqn getItemMetaClassFqn()
    {
        return itemMetaClassFqn;
    }

    @Override
    public int hashCode()
    {
        return HashCodeBuilder.reflectionHashCode(this);
    }

    /**
     * Gets the value of the flat property.
     *
     * @return
     *     possible object is
     *     {@link Boolean }
     *
     */
    public boolean isFlat()
    {
        return null == flat || flat;
    }

    /**
     * Gets the value of the withFolders property.
     *
     * @return
     *     possible object is
     *     {@link Boolean }
     *
     */
    public boolean isWithFolders()
    {
        if (withFolders == null)
        {
            return false;
        }
        else
        {
            return withFolders;
        }
    }

    /**
     * Sets the value of the flat property.
     *
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *
     */
    public void setFlat(Boolean value)
    {
        this.flat = value;
    }

    /**
     * Sets the value of the itemMetaClassFqn property.
     *
     * @param value
     *     allowed object is
     *     {@link ClassFqn }
     *
     */
    public void setItemMetaClassFqn(ClassFqn value)
    {
        this.itemMetaClassFqn = value;
    }

    /**
     * Sets the value of the withFolders property.
     *
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *
     */
    public void setWithFolders(Boolean value)
    {
        this.withFolders = value;
    }

}
