/**
 *
 */
package ru.naumen.metainfo.server.spi.store.sec;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 15.01.2013
 *
 */
public interface HasProfile
{
    Function<HasProfile, String> PROFILE_EXTRACTOR = new Function<HasProfile, String>()
    {
        @Override
        public String apply(HasProfile input)
        {
            if (input == null)
            {
                return "";
            }
            return input.getProfile();
        }
    };

    String getProfile();
}