package ru.naumen.metainfo.server.spi.store.sec;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.commons.shared.utils.ComparatorUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.IHasI18nTitle;
import ru.naumen.core.shared.SecConstants.MarkerGroups;
import ru.naumen.metainfo.shared.Constants.ElementTypes;
import ru.naumen.metainfo.shared.elements.HasElementId;
import ru.naumen.metainfo.shared.segment.MetainfoSegment;
import ru.naumen.metainfo.shared.sets.HasSettingsSet;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Элемент для сериализации {@link ru.naumen.metainfo.shared.elements.sec.Marker маркера прав доступа}.
 *
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "marker")
@XmlSeeAlso({ MarkerDeclaration.class, MarkerOverride.class, SecAction.class })
public abstract class Marker extends MetainfoSegment implements HasCode, IHasI18nTitle, HasSettingsSet, HasElementId
{
    @XmlTransient
    public static final Comparator<Marker> EXPORT_COMPARATOR = new Comparator<>()
    {
        @Override
        public int compare(Marker m1, Marker m2)
        {
            int i = getType(m1) - getType(m2);
            if (i != 0)
            {
                return i;
            }
            i = m1 instanceof SecAction ? StringUtilities.compareToIgnoreCase(m1.getGroup(), m2.getGroup())
                    : getGroup(m1) - getGroup(m2);
            return i != 0 ? i : HasCode.COMPARATOR.compare(m1, m2);
        }

        private int getGroup(Marker m)
        {
            //@formatter:off
            return MarkerGroups.VIEW_ATTRS.equals(m.getGroup()) ? 0 :
                MarkerGroups.EDIT_ATTRS.equals(m.getGroup()) ? 1 :
                2;
            //@formatter:on
        }

        private int getType(Marker m)
        {
            return m instanceof MarkerDeclaration ? 0
                    : m instanceof MarkerOverride ? 1 : m instanceof SecAction ? 2 : 3;
        }
    };

    @XmlAttribute(required = true)
    private String code;
    ;

    @XmlAttribute
    private boolean enabled = true;

    @XmlAttribute(name = "group")
    private String group;

    @XmlAttribute
    private Boolean granted = null;

    @XmlAttribute(name = "unlicense-allowed")
    private Boolean unlicensedAllowed = null;

    @XmlAttribute(name = "handler")
    private String handler;

    @XmlElement
    private List<LocalizedString> title;

    @XmlElement(name = "attributes")
    private Set<String> addedAttributes;

    @XmlElement(name = "removed-attributes")
    private Set<String> removedAttributes;

    /**
     * Комплект настроек
     */
    @XmlElement(name = "set")
    private String settingsSet;

    @Override
    public boolean equals(Object obj)
    {
        if (!(obj instanceof Marker))
        {
            return false;
        }
        if (this == obj)
        {
            return true;
        }
        Marker other = (Marker)obj;
        return EqualsBuilder.reflectionEquals(this, other);
    }

    public Set<String> getAddedAttributes()
    {
        if (null == addedAttributes)
        {
            addedAttributes = Sets.newTreeSet(ComparatorUtils.ignoreCaseComparator());
        }
        return addedAttributes;
    }

    @Override
    public String getCode()
    {
        return code;
    }

    public String getGroup()
    {
        return group;
    }

    public String getHandler()
    {
        return handler;
    }

    public Set<String> getRemovedAttributes()
    {
        if (null == removedAttributes)
        {
            removedAttributes = Sets.newTreeSet(ComparatorUtils.ignoreCaseComparator());
        }
        return removedAttributes;
    }

    @Override
    public List<LocalizedString> getTitle()
    {
        if (null == title)
        {
            title = new ArrayList<>();
        }
        return title;
    }

    @Override
    public int hashCode()
    {
        return HashCodeBuilder.reflectionHashCode(this);
    }

    public boolean isAttributesMarker()
    {
        return MarkerGroups.VIEW_ATTRS.equals(getGroup()) || MarkerGroups.EDIT_ATTRS.equals(getGroup())
               || MarkerGroups.UNLICENSED.equals(getGroup());
    }

    public boolean isChangeStateMarker()
    {
        return MarkerGroups.EDIT_STATE.equals(getGroup());
    }

    public boolean isCommentAttrMarker()
    {
        return MarkerGroups.VIEW_COMMENT_ATTRS.equals(getGroup()) || MarkerGroups.EDIT_COMMENT_ATTRS.equals(getGroup());
    }

    public boolean isEnabled()
    {
        return enabled;
    }

    public boolean isGranted()
    {
        return Boolean.TRUE.equals(granted);
    }

    public boolean isUnlicensedAllowed()
    {
        return Boolean.TRUE.equals(unlicensedAllowed);
    }

    /**
     * Возвращает true если маркер является настройкой прав на пользовательские события
     * @return
     */
    public boolean isUserEventMarker()
    {
        return MarkerGroups.USER_EVENT.equals(getGroup());
    }

    public void setAddedAttributes(Set<String> addedAttributes)
    {
        this.addedAttributes = Sets.newTreeSet(addedAttributes);
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }

    public void setGranted(boolean granted)
    {
        this.granted = granted;
    }

    public void setGroup(String group)
    {
        this.group = group;
    }

    public void setHandler(String handler)
    {
        this.handler = handler;
    }

    public void setRemovedAttributes(Set<String> removedAttributes)
    {
        this.removedAttributes = Sets.newTreeSet(removedAttributes);
    }

    public void setUnlicensedAllowed(boolean unlicensedAllowed)
    {
        this.unlicensedAllowed = unlicensedAllowed;
    }

    @Override
    public String getSegmentID()
    {
        return getCode();
    }

    @Override
    public boolean isDetachableSegment()
    {
        return true;
    }

    @Override
    public @Nullable String getSettingsSet()
    {
        return settingsSet;
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet)
    {
        this.settingsSet = settingsSet;
    }

    @XmlTransient
    @Override
    public String getElementCode()
    {
        return getCode();
    }

    @XmlTransient
    @Override
    public String getElementType()
    {
        return ElementTypes.SECURITY_MARKER;
    }
}
