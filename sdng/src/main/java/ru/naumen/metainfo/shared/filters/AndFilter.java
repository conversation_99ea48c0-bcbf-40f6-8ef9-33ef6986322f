package ru.naumen.metainfo.shared.filters;

import java.util.ArrayList;
import java.util.Iterator;

import com.google.common.base.Joiner;
import com.google.common.base.Predicate;
import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.IsSerializable;

/**
 *
 * <AUTHOR>
 *
 * @param <T>
 */
public class AndFilter<T> implements Predicate<T>, IsSerializable
{
    private static boolean iterableElementsEqual(Iterable<?> iterable1, Iterable<?> iterable2)
    {
        Iterator<?> iterator1 = iterable1.iterator();
        Iterator<?> iterator2 = iterable2.iterator();
        while (iterator1.hasNext())
        {
            if (!iterator2.hasNext())
            {
                return false;
            }
            if (!iterator1.next().equals(iterator2.next()))
            {
                return false;
            }
        }
        return !iterator2.hasNext();
    }

    private ArrayList<? extends Predicate<? super T>> components;

    public AndFilter()
    {
    }

    public AndFilter(Iterable<? extends Predicate<? super T>> components)
    {
        this.components = Lists.newArrayList(components);
    }

    @Override
    public boolean apply(T t)
    {
        for (Predicate<? super T> predicate : components)
        {
            if (!predicate.apply(t))
            {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (obj instanceof AndFilter<?>)
        {
            AndFilter<?> that = (AndFilter<?>)obj;
            return iterableElementsEqual(components, that.components);
        }
        return false;
    }

    @Override
    public int hashCode()
    {
        int result = -1; /* Start with all bits on. */
        for (Predicate<? super T> predicate : components)
        {
            result &= predicate.hashCode();
        }
        return result;
    }

    @Override
    public String toString()
    {
        return "And(" + Joiner.on(",").join(components) + ")";
    }
}
