package ru.naumen.metainfo.shared.mobile;

import java.io.Serializable;
import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.core.shared.HasAttributeFqn;
import ru.naumen.core.shared.HasClone;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.segment.MetainfoSegment;
import ru.naumen.metainfo.shared.segment.MetainfoSegmentNames.MobileObjectCardSegments;

/**
 * Представление настроек атрибута для мобильного клиента в метаинформации
 *
 * <AUTHOR>
 * @since May 6, 2014
 */
@XmlType(name = "attribute")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class MobileAttribute extends MetainfoSegment implements HasCode, HasAttributeFqn, Serializable, HasClone
{
    private static final long serialVersionUID = 8999058308083733961L;

    public static MobileAttribute withUuid(String uuid)
    {
        return new MobileAttribute(uuid);
    }

    /**
     * По требованию apavlov для мобильного списка/карточки может быть 
     * настроено более одного атрибута, при этом все манипуляции с этими атрибутами
     * должны происходить независимо друг от друга
     * Для того чтобы приложение соответствовало этому требованию для каждого
     * настроенного атрибута добавлен уникальный идентификатор
     */
    private String uuid = UUIDGenerator.get().nextUUID();

    /**
     * {@link AttributeFqn} в строковом виде
     * Например: serviceCall$incident@clientDescription
     */
    private String attributeFqn;

    private PresentationType presentation = PresentationType.WITH_TITLE;

    private Boolean showAsSeparateList;

    private String mobileListUuid;

    public MobileAttribute()
    {
    }

    public MobileAttribute(AttributeFqn attributeFqn)
    {
        this.attributeFqn = attributeFqn.toString();
    }

    public MobileAttribute(String uuid)
    {
        this.uuid = uuid;
    }

    public MobileAttribute(String uuid, String attributeFqn, @NotNull PresentationType presentation)
    {
        this(uuid);
        setPresentation(presentation);
        this.attributeFqn = attributeFqn;
    }

    @Override
    public MobileAttribute clone()
    {
        MobileAttribute clone = new MobileAttribute();
        clone.setAttributeFqn(getAttributeFqn());
        clone.setMobileListUuid(getMobileListUuid());
        clone.setPresentation(getPresentation());
        clone.setShowAsSeparateList(getShowAsSeparateList());
        clone.setUuid(getUuid());
        return clone;
    }

    @Override
    public boolean equals(Object object)
    {
        if (this == object)
        {
            return true;
        }
        if (!(object instanceof MobileAttribute))
        {
            return false;
        }
        return ObjectUtils.equals(getUuid(), ((MobileAttribute)object).getUuid());
    }

    @Override
    @XmlTransient
    public AttributeFqn getAttributeFqn()
    {
        return AttributeFqn.parse(attributeFqn);
    }

    /**
     * Код в xml - это строковое значение {@link AttributeFqn}.
     */
    @XmlElement(name = "code", required = true)
    public String getAttributeFqnString()
    {
        return attributeFqn;
    }

    /**
     * Конкретный код атрибута
     */
    @Override
    public String getCode()
    {
        return getAttributeFqn().getCode();
    }

    public String getMobileListUuid()
    {
        return mobileListUuid;
    }

    @XmlElement(name = "presentation")
    public PresentationType getPresentation()
    {
        return presentation;
    }

    @Override
    public String getSegmentID()
    {
        return AttributeFqn.getCode(getAttributeFqnString());
    }

    @Override
    public String getSegmentType()
    {
        return MobileObjectCardSegments.ATTRIBUTES;
    }

    @Override
    public boolean isDetachableSegment()
    {
        return true;
    }

    public Boolean getShowAsSeparateList()
    {
        return showAsSeparateList;
    }

    public String getUuid()
    {
        return uuid;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(getUuid());
    }

    public void setAttributeFqn(AttributeFqn fqn)
    {
        this.attributeFqn = fqn.toString();
    }

    public void setAttributeFqnString(String attributeFqn)
    {
        this.attributeFqn = attributeFqn;
    }

    public void setMobileListUuid(@Nullable String mobileListUuid)
    {
        this.mobileListUuid = mobileListUuid;
    }

    public void setPresentation(@NotNull PresentationType presentation)
    {
        this.presentation = Objects.requireNonNull(
                presentation, "Peresentation type must be not null");
    }

    public void setShowAsSeparateList(Boolean showAsSeparateList)
    {
        this.showAsSeparateList = showAsSeparateList;
    }

    public void setUuid(String uuid)
    {
        this.uuid = uuid;
    }

    @Override
    public String toString()
    {
        return "MobileAttribute [uuid=" + uuid + ", code=" + attributeFqn + ", presentation=" + presentation + "]";
    }
}
