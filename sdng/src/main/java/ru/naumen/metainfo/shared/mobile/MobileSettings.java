package ru.naumen.metainfo.shared.mobile;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import java.util.ArrayList;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlElements;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.core.shared.HasClone;
import ru.naumen.metainfo.shared.Constants.ElementTypes;
import ru.naumen.metainfo.shared.elements.HasElementId;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.editforms.EditForm;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.lists.MobileObjectList;
import ru.naumen.metainfo.shared.mobile.lists.MobileRelObjectsList;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.metainfo.shared.mobile.other.BarcodeScannerSettings;
import ru.naumen.metainfo.shared.mobile.other.GeoHistorySettings;
import ru.naumen.metainfo.shared.mobile.other.OtherSettings;
import ru.naumen.metainfo.shared.segment.MetainfoSegment;
import ru.naumen.metainfo.shared.segment.MetainfoSegmentNames.TopLevelSegments;

/**
 * Настройки мобильного клиента
 *
 * <AUTHOR>
 * @since Apr 24, 2014
 */
@XmlRootElement(name = "mobile-settings")
@XmlType(propOrder = { "lists", "objectCards", "addForms", "editForms",
        "navigationSettings", "otherSettings", "geoHistorySettings",
        "barcodeScannerSettings" }, name = "mobile-settings")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class MobileSettings extends MetainfoSegment implements Serializable, HasClone, HasElementId
{
    public static final MobileSettings EMPTY_SETTINGS = new MobileSettings();
    private static final long serialVersionUID = 0L;
    private List<MobileList> mainLists;
    private List<ObjectCard> objectCards;
    private List<AddForm> addForms;
    private List<EditForm> editForms;
    private List<MobileMenuItemValue> navigationSettings;
    private OtherSettings otherSettings;
    private GeoHistorySettings geoHistorySettings;
    private BarcodeScannerSettings barcodeScannerSettings;

    @Override
    public Object clone()
    {
        MobileSettings clone = new MobileSettings();
        clone.setObjectCards(cloneItems(getObjectCards()));
        clone.setLists(cloneItems(getLists()));
        clone.setAddForms(cloneItems(getAddForms()));
        clone.setEditForms(cloneItems(getEditForms()));
        clone.setNavigationSettings(cloneItems(getNavigationSettings()));
        clone.setOtherSettings(getOtherSettings());
        clone.setGeoHistorySettings(getGeoHistorySettings());
        clone.setBarcodeScannerSettings(getBarcodeScannerSettings());
        MobileContentUtil.fillAddFormParents(clone);
        return clone;
    }

    @XmlElementWrapper(name = "add-forms")
    @XmlElement(name = "add-form", required = true)
    public List<AddForm> getAddForms()
    {
        if (null == addForms)
        {
            addForms = new ArrayList<>();
        }
        return addForms;
    }

    @XmlTransient
    public List<AddForm> getAllAddForms()
    {
        return getAllAddFormsStream().collect(Collectors.toList());
    }

    @XmlTransient
    public Stream<AddForm> getAllAddFormsStream()
    {
        if (null == addForms)
        {
            return Stream.empty();
        }
        return addForms.stream().flatMap(MobileSettings::flattenAddForm);
    }

    @XmlElement(name = "barcode-scanner-settings")
    public BarcodeScannerSettings getBarcodeScannerSettings()
    {
        if (null == barcodeScannerSettings)
        {
            barcodeScannerSettings = new BarcodeScannerSettings();
        }
        return barcodeScannerSettings;
    }

    @XmlElementWrapper(name = "edit-forms")
    @XmlElement(name = "edit-form", required = true)
    public List<EditForm> getEditForms()
    {
        if (null == editForms)
        {
            editForms = new ArrayList<>();
        }
        return editForms;
    }

    @XmlElement(name = "geo-history-settings")
    public GeoHistorySettings getGeoHistorySettings()
    {
        if (null == geoHistorySettings)
        {
            geoHistorySettings = new GeoHistorySettings();
        }
        return geoHistorySettings;
    }

    @Override
    public String getSegmentID()
    {
        return "mobile-settings";
    }

    @Override
    public String getSegmentType()
    {
        return TopLevelSegments.TRANSFER_VALUES;
    }

    @Override
    public boolean isDetachableSegment()
    {
        return true;
    }

    @XmlElementWrapper(name = "lists")
    @XmlElements({ @XmlElement(name = "related-objects", type = MobileRelObjectsList.class),
            @XmlElement(name = "objects", type = MobileObjectList.class), })
    public List<MobileList> getLists()
    {
        if (null == mainLists)
        {
            mainLists = new ArrayList<>();
        }
        return mainLists;
    }

    @XmlElementWrapper(name = "navigationSettings")
    @XmlElement(name = "item")
    public List<MobileMenuItemValue> getNavigationSettings()
    {
        if (null == navigationSettings)
        {
            navigationSettings = new ArrayList<>();
        }
        return navigationSettings;
    }

    @XmlElementWrapper(name = "object-cards")
    @XmlElement(name = "object-card", required = true)
    public List<ObjectCard> getObjectCards()
    {
        if (null == objectCards)
        {
            objectCards = new ArrayList<>();
        }
        return objectCards;
    }

    @XmlElement(name = "other-settings")
    public OtherSettings getOtherSettings()
    {
        return otherSettings;
    }

    public void setAddForms(List<AddForm> addForms)
    {
        this.addForms = addForms;
    }

    public void setBarcodeScannerSettings(@Nullable BarcodeScannerSettings barcodeScannerSettings)
    {
        this.barcodeScannerSettings = barcodeScannerSettings;
    }

    public void setEditForms(List<EditForm> editForms)
    {
        this.editForms = editForms;
    }

    public void setGeoHistorySettings(@Nullable GeoHistorySettings geoHistorySettings)
    {
        this.geoHistorySettings = geoHistorySettings;
    }

    public void setLists(List<MobileList> mainLists)
    {
        this.mainLists = mainLists;
    }

    public void setNavigationSettings(List<MobileMenuItemValue> navigationSettings)
    {
        this.navigationSettings = navigationSettings;
    }

    public void setObjectCards(List<ObjectCard> objectCards)
    {
        this.objectCards = objectCards;
    }

    public void setOtherSettings(@Nullable OtherSettings other)
    {
        this.otherSettings = other;
    }

    private <T extends HasClone> List<T> cloneItems(Collection<T> source)
    {
        return source.stream().map(x -> ((T)x.clone())).collect(Collectors.toList());
    }

    private static Stream<AddForm> flattenAddForm(AddForm form)
    {
        return Stream.concat(
                form.getChildren().stream().flatMap(MobileSettings::flattenAddForm),
                Stream.of(form));
    }

    @XmlTransient
    @Override
    public String getElementType()
    {
        return ElementTypes.MOBILE_SETTINGS;
    }

    @XmlTransient
    @Override
    public String getElementCode()
    {
        return getElementType();
    }
}
