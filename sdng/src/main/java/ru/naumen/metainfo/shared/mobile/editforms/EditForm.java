package ru.naumen.metainfo.shared.mobile.editforms;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;

import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.segment.MetainfoSegmentNames.MobileSettingsSegments;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Настройки формы редактирования объекта в мобильном клиенте
 *
 * Позволяет добавлять атрибут только один раз.
 *
 * <AUTHOR>
 * @since 31.10.2016
 */
@XmlType(name = "edit-form")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class EditForm extends CommonMobileView implements HasCode
{
    private static final long serialVersionUID = -3114891629126685710L;

    @Override
    public void addAttribute(MobileAttribute attribute)
    {
        ensureAttributes();

        if (!getAttrFqns().contains(attribute.getAttributeFqn()))
        {
            getAttributes().add(attribute);
        }
    }

    @Override
    public String getCode()
    {
        return getUuid();
    }

    @Override
    public void setAttributes(List<MobileAttribute> attributes)
    {
        Iterable<MobileAttribute> filtered = Iterables.filter(attributes,
                new com.google.common.base.Predicate<MobileAttribute>()
                {
                    private Set<String> attributeFqns = new HashSet<>();

                    @Override
                    public boolean apply(MobileAttribute input)
                    {
                        return attributeFqns.add(input.getAttributeFqnString());
                    }
                });

        super.setAttributes(Lists.newArrayList(filtered));
    }

    @Override
    public String toString()
    {
        return "EditForm [getUuid()=" + getUuid() + ", getClazz()=" + getClazz() + ", getCases()=" + getCases() + "]";
    }

    @Override
    protected Content newInstance()
    {
        return new EditForm();
    }

    @Override
    public String getSegmentType()
    {
        return MobileSettingsSegments.EDIT_FORMS;
    }
}
