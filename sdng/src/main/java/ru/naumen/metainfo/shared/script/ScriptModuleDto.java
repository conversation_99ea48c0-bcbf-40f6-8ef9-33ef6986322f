package ru.naumen.metainfo.shared.script;

import java.io.Serializable;

import com.google.gwt.user.client.rpc.IsSerializable;

import jakarta.annotation.Nullable;
import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.metainfo.shared.sets.HasSettingsSet;

/**
 * Класс для передачи необходимого содержимого скриптовых модулей между клиентской и серверной частью приложения.
 *
 */
public class ScriptModuleDto implements IsSerializable, Serializable, Result, HasSettingsSet
{
    private static final long serialVersionUID = -9164865718582672939L;

    /**
     * Код модуля. Аналог namespace
     *
     * @see ru.naumen.core.server.script.ScriptService.Constants#MODULES_PARAM
     */
    private String code;

    /**
     * Пользовательское описание модуля, методов и классов (документация)
     */
    private String description;

    /**
     * Версия модуля - число, возможно в формате XX.YY, где YY - багофиксовая версия модуля
     */
    private String moduleVersion;

    /**
     * Признак доступности для просмотра суперпользователями.
     */
    private Boolean superUserReadable;

    /**
     * Признак доступности для редактирования суперпользователями.
     */
    private Boolean superUserWritable;

    /**
     * Тело скрипта.
     */
    private String script;

    /**
     * Автор модуля.
     */
    private String author;

    /**
     * Является ли модуль скрытым
     */
    private boolean hidden = false;

    /**
     * Признак доступности выполнения через REST
     */
    private boolean restAllowed;

    /**
     * Комплект настроек
     */
    private String settingsSet;

    public ScriptModuleDto()
    {
    }

    public String getAuthor()
    {
        return author;
    }

    public String getCode()
    {
        return code;
    }

    public String getDescription()
    {
        return description;
    }

    public String getModuleVersion()
    {
        return moduleVersion;
    }

    public String getScript()
    {
        return script;
    }

    public Boolean getSuperUserReadable()
    {
        return superUserReadable;
    }

    public Boolean getSuperUserWritable()
    {
        return superUserWritable;
    }

    public boolean isHidden()
    {
        return hidden;
    }

    public boolean isRestAllowed()
    {
        return restAllowed;
    }

    public void setAuthor(String author)
    {
        this.author = author;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setDescription(String description)
    {
        this.description = description;
    }

    public void setHidden(boolean hidden)
    {
        this.hidden = hidden;
    }

    public void setModuleVersion(String moduleVersion)
    {
        this.moduleVersion = moduleVersion;
    }

    public void setScript(String script)
    {
        this.script = script;
    }

    public void setSuperUserReadable(Boolean superUserReadable)
    {
        this.superUserReadable = superUserReadable;
    }

    public void setSuperUserWritable(Boolean superUserWritable)
    {
        this.superUserWritable = superUserWritable;
    }

    public void setRestAllowed(boolean restAllowed)
    {
        this.restAllowed = restAllowed;
    }

    @Override
    public String toString()
    {
        return "ScriptModuleDto{" +
               "code='" + code + '\'' +
               ", moduleVersion='" + moduleVersion + '\'' +
               '}';
    }

    @Override
    public @Nullable String getSettingsSet()
    {
        return settingsSet;
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet)
    {
        this.settingsSet = settingsSet;
    }
}
