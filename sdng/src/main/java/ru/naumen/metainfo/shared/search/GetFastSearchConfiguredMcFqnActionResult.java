package ru.naumen.metainfo.shared.search;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * {@link Result} проверки наличия настроенных атрибутов для быстрого поиска в метаклассах
 *
 * <AUTHOR>
 * @since 6 июл. 2018 г.
 */
public class GetFastSearchConfiguredMcFqnActionResult implements Result, Serializable
{
    private static final long serialVersionUID = 3494665621363399119L;

    private List<ClassFqn> classesWithFastSearch = new ArrayList<>();

    public GetFastSearchConfiguredMcFqnActionResult(List<ClassFqn> classesWithFastSearch)
    {
        this.classesWithFastSearch = classesWithFastSearch;
    }

    public List<ClassFqn> getClassesWithFastSearch()
    {
        return classesWithFastSearch;
    }

    public void setClassesWithFastSearch(List<ClassFqn> classesWithFastSearch)
    {
        this.classesWithFastSearch = classesWithFastSearch;
    }
}
