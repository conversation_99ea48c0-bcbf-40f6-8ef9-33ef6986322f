package ru.naumen.metainfoadmin.client.common.content.commands;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeLayoutModeEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ContentUtils;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Позволяет выйти из режима редактирования разметки
 * <AUTHOR>
 * @since 25.01.2017
 *
 */
public class DisableLayoutModeCommand extends BaseCommandImpl<FlowContent, Void>
{
    @Inject
    private ContentUtils contentUtils;

    @Inject
    public DisableLayoutModeCommand(@Assisted TabContentCommandParam<FlowContent, Void> param)
    {
        super(param);
    }

    @Override
    public void execute(final CommandParam<FlowContent, Void> param)
    {
        if (!(param instanceof TabContentCommandParam))
        {
            return;
        }

        TabContentCommandParam<FlowContent, Void> tabParam = (TabContentCommandParam<FlowContent, Void>)param;
        Layout parent = tabParam.getLayout();

        getContext().getEventBus().fireEvent(new ChangeLayoutModeEvent(parent, false));
    }

    @SuppressWarnings("rawtypes")
    public UIContext getContext()
    {
        return ((TabContentCommandParam)param).getContext();
    }

    @SuppressWarnings("rawtypes")
    public Layout getLayout()
    {
        return ((TabContentCommandParam)param).getLayout();
    }

    @Override
    public boolean isPossible(Object input)
    {
        return contentUtils.isContentInEditMode(input, this.getContext());
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

}
