package ru.naumen.metainfoadmin.client.common.content.commands;

import jakarta.annotation.Nullable;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.objectlist.client.gin.ListDefaultPrsFormProvider;

/**
 * <AUTHOR>
 * @since 29 февр. 2016 г.
 *
 */
public class EditAdvlistDefaultPrsCommandParam extends CommandParam<FlowContent, Void>
{
    private final ObjectListUIContext context;
    private final ListDefaultPrsFormProvider formProvider;

    /*
     * Передаем провайдер формы, т.к. он должен создаваться из того же инжектора, что и список объектов, чтобы
     * правильно отображать фильтрацию\сортировку
     */
    public EditAdvlistDefaultPrsCommandParam(@Nullable FlowContent value, @Nullable AsyncCallback<Void> callback,
            ObjectListUIContext context, ListDefaultPrsFormProvider formProvider)
    {
        super(value, callback);
        this.context = context;
        this.formProvider = formProvider;
    }

    public ObjectListUIContext getContext()
    {
        return context;
    }

    public ListDefaultPrsFormProvider getFormProvider()
    {
        return formProvider;
    }
}