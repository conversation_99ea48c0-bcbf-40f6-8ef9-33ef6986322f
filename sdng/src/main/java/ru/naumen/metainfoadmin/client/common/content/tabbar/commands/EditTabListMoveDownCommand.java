package ru.naumen.metainfoadmin.client.common.content.tabbar.commands;

import java.util.List;

import com.google.gwt.view.client.CellPreviewEvent;
import com.google.inject.assistedinject.Assisted;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.listeditor.commands.ListEditorMoveDownCommand;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.ui.ListEditor;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfoadmin.client.common.content.tabbar.CurrentTabsExtractor;
import ru.naumen.metainfoadmin.client.common.content.tabbar.EditTabListUtils;
import ru.naumen.metainfoadmin.client.common.content.tabbar.SafeTabBarModifier;
import ru.naumen.metainfoadmin.client.common.content.tabbar.SafeTabBarModifier.ModificationBlock;

/**
 * <AUTHOR>
 * @since 23.11.2011
 *
 */
public class EditTabListMoveDownCommand extends ListEditorMoveDownCommand
{
    @Inject
    private EditTabListUtils editTabListUtils;
    @Inject
    private CurrentTabsExtractor extractor;
    @Inject
    private SafeTabBarModifier modifier;

    private final TabCommandParam initParam;

    @SuppressWarnings("rawtypes")
    @Inject
    public EditTabListMoveDownCommand(@Assisted CommandParam<ListEditor<?>, Void> param)
    {
        super(param);
        initParam = (TabCommandParam)(CommandParam)param;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void execute(final CommandParam<CellPreviewEvent<String>, Void> param)
    {
        if (editTabListUtils.getEditValueKey(model) != null || !isPossible(param.getValue().getValue()))
        {
            return;
        }
        modifier.modify((ListEditor<Tab>)model, null, initParam.getContext(), new ModificationBlock()
        {

            @Override
            public void apply(final ListEditor<Tab> model, final TabBar tabBar)
            {
                doSwap(param, model, new BasicCallback<Void>()
                {
                    @Override
                    protected void handleSuccess(Void result)
                    {
                        List<Tab> tabs = extractor.get(model);
                        tabBar.getTab().clear();
                        tabBar.getTab().addAll(tabs);
                    }
                });
            }
        }, new BasicCallback<Void>()
        {
            @Override
            public void onFailure(Throwable e)
            {
            }

            @Override
            protected void handleSuccess(Void result)
            {
                param.getCallback().onSuccess(result);
            }
        });
        param.getCallback().onSuccess(null);
    }

    @SuppressWarnings("unchecked")
    private Tab getTab(@Nullable Object tabId)
    {
        return ((ListEditor<Tab>)model).getDomain().get(StringUtilities.toString(tabId));
    }

    @Override
    public boolean isPossible(Object input)
    {
        Tab tab = getTab(input);
        return !(model.getCurrent().getOrder().indexOf(input) == model.getCurrent().getOrder().size() - 1)
               && initParam.hasPermission(tab, PermissionType.EDIT);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DOWN;
    }
}
