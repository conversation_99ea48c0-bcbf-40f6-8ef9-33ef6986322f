/**
 *
 */
package ru.naumen.metainfoadmin.client.common.objectcommands.delete;

import ru.naumen.core.shared.IHasTitle;
import ru.naumen.core.shared.dispatch.SimpleResult;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Callback после выполнения действия удаления
 * <AUTHOR>
 * @since 04.02.2013
 *
 */
public interface ObjectDeleteCallback<V extends IHasTitle, T extends ObjectDeleteCallbackType> extends
        AsyncCallback<SimpleResult<String>>
{
}