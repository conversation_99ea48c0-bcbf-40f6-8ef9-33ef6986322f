/**
 *
 */
package ru.naumen.metainfoadmin.client.common.objectcommands.form;

import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptorFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;

/**
 * <AUTHOR>
 * @since 29.01.2013
 *
 */
public class ObjectFormGinModule<T extends ObjectForm, V> extends AbstractGinModule
{
    private final Class<T> objectFormClass;
    private final Class<V> valueClass;
    private final PropertyControllerGinModule<V, T> pcGinModule;

    TypeLiteral<? extends ObjectFormMessages<T, V>> messages;
    TypeLiteral<? extends ObjectFormConstants<T, V>> constants;
    TypeLiteral<? extends PropertyControllerFactory<V, T>> propertyControllerFactory;
    TypeLiteral<? extends PropertyParametersDescriptorFactory<V, T>> propertyParametersDescriptorFactory;
    TypeLiteral<? extends ObjectFormPropertiesInitializerImpl<T, V>> propertyInitializer;
    TypeLiteral<? extends ObjectFormAfterBindHandlerImpl<T, V>> afterBindHandler;
    TypeLiteral<? extends ObjectFormApplyCallback<T, V>> applyCallback;
    TypeLiteral<? extends ObjectFormActionFactory<T, V>> actionFactory;

    public ObjectFormGinModule(Class<T> objectFormClass, Class<V> valueClass)
    {
        this.objectFormClass = objectFormClass;
        this.valueClass = valueClass;
        pcGinModule = PropertyControllerGinModule.create(valueClass, objectFormClass);
    }

    public ObjectFormGinModule<T, V> setActionFactory(Class<? extends ObjectFormActionFactory<T, V>> actionFactory)
    {
        this.actionFactory = TypeLiteral.get(actionFactory);
        return this;
    }

    public ObjectFormGinModule<T, V> setAfterBindHandler(
            Class<? extends ObjectFormAfterBindHandlerImpl<T, V>> afterBindHandler)
    {
        this.afterBindHandler = TypeLiteral.get(afterBindHandler);
        return this;
    }

    public ObjectFormGinModule<T, V> setConstants(Class<? extends ObjectFormConstants<T, V>> constants)
    {
        this.constants = TypeLiteral.get(constants);
        return this;
    }

    public ObjectFormGinModule<T, V> setMessages(Class<? extends ObjectFormMessages<T, V>> messages)
    {
        this.messages = TypeLiteral.get(messages);
        return this;
    }

    public ObjectFormGinModule<T, V> setPropertyControllerFactory(
            Class<? extends PropertyControllerFactory<V, T>> propertyControllerFactory)
    {
        this.propertyControllerFactory = TypeLiteral.get(propertyControllerFactory);
        return this;
    }

    public ObjectFormGinModule<T, V> setPropertyInitializer(
            Class<? extends ObjectFormPropertiesInitializerImpl<T, V>> propertyInitializer)
    {
        this.propertyInitializer = TypeLiteral.get(propertyInitializer);
        return this;
    }

    public ObjectFormGinModule<T, V> setPropertyParametersDescriptorFactory(
            Class<? extends PropertyParametersDescriptorFactory<V, T>> propertyParametersDescriptorFactory)
    {
        this.propertyParametersDescriptorFactory = TypeLiteral.get(propertyParametersDescriptorFactory);
        return this;
    }

    @Override
    protected void configure()
    {
        //@formatter:off
        bind(Gin.parameterizedTypeLiteral(ObjectFormMessages.class, objectFormClass, valueClass)).to(messages).in(Singleton.class);
        bind(Gin.parameterizedTypeLiteral(ObjectFormConstants.class, objectFormClass, valueClass)).to(constants).in(Singleton.class);
        bind(Gin.parameterizedTypeLiteral(ObjectFormPropertiesInitializer.class, objectFormClass, valueClass))
            .to(propertyInitializer).in(Singleton.class);
        bind(Gin.parameterizedTypeLiteral(ObjectFormAfterBindHandler.class, objectFormClass, valueClass))
            .to(afterBindHandler).in(Singleton.class);
        bind(Gin.parameterizedTypeLiteral(ObjectFormActionFactory.class, objectFormClass, valueClass))
            .to(actionFactory).in(Singleton.class);
        
        install(pcGinModule
                .setPropertyControllerFactory(propertyControllerFactory)
                .setPropertyParametersDescriptorFactory(propertyParametersDescriptorFactory));
        
        install(new GinFactoryModuleBuilder()
            .implement(Gin.parameterizedTypeLiteral(ObjectFormPresenter.class, objectFormClass, valueClass), 
                    Gin.parameterizedTypeLiteral(ObjectFormPresenter.class, objectFormClass, valueClass))
            .build(Gin.parameterizedTypeLiteral(ObjectFormPresenterFactory.class, objectFormClass, valueClass)));
        install(new GinFactoryModuleBuilder()
            .implement(Gin.parameterizedTypeLiteral(ObjectFormApplyCallback.class, objectFormClass, valueClass), applyCallback)
            .build(Gin.parameterizedTypeLiteral(ObjectFormApplyCallbackFactory.class, objectFormClass, valueClass)));
        //@formatter:on
    }
}