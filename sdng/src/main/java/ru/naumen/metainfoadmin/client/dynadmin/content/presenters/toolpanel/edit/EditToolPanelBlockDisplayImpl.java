package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.FlowPanel;

import ru.naumen.core.client.components.block.TitledBlockDisplayImpl;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;

/**
 * Реализация {@link EditToolPanelBlockDisplay}
 * <AUTHOR>
 *
 */
public class EditToolPanelBlockDisplayImpl extends TitledBlockDisplayImpl implements EditToolPanelBlockDisplay
{
    private static final String USE_SYSTEM_OBJECT_ACTIONS = "use-system-object-actions";

    private BooleanCheckBoxProperty useSystemSettings;
    private BooleanCheckBoxProperty panelDisabled;
    private FlowPanel container;

    @Inject
    public EditToolPanelBlockDisplayImpl(BooleanCheckBoxProperty useSystemSettings,
            BooleanCheckBoxProperty panelDisabled)
    {
        container = new FlowPanel();
        container.add(this.panelDisabled = panelDisabled);
        container.add(this.useSystemSettings = useSystemSettings);
        useSystemSettings.ensureDebugId(USE_SYSTEM_OBJECT_ACTIONS);
        setControlledWidget(container);
    }

    @Override
    public BooleanCheckBoxProperty getUseSystemSettings()
    {
        return useSystemSettings;
    }

    @Override
    public BooleanCheckBoxProperty getPanelDisabled()
    {
        return panelDisabled;
    }

    @Override
    public void addWidget(Display display)
    {
        container.add(display);
    }
}
