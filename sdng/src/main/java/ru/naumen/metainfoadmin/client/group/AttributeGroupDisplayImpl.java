package ru.naumen.metainfoadmin.client.group;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.google.common.collect.Collections2;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NodeList;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.components.block.TitledBlockWithToolDisplayImpl;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.group.ListEditorDnDGroupControllerBase;
import ru.naumen.core.client.widgets.NauGrid;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * @see AttributeGroupDisplay
 *
 * <AUTHOR>
 * @since 28.07.2010
 *
 */
public class AttributeGroupDisplayImpl extends TitledBlockWithToolDisplayImpl implements AttributeGroupDisplay
{
    private class AttributeGroupListDnDController extends ListEditorDnDGroupControllerBase
    {
        private List<String> attributeCodes = new ArrayList<>();
        private Set<String> editableAttributes = new HashSet<>();
        private ReadyState readyState = null;

        public AttributeGroupListDnDController()
        {
            super(tableContainer.getElement());
        }

        @Override
        public void move(int oldPosition, int newPosition, ReadyState readyState)
        {
            this.readyState = readyState;
            readyState.notReady();
            int diff = newPosition - oldPosition;
            AttributeGroupDisplayImpl.this.fireEvent(new MoveAttrEvent(attributeCodes.get(oldPosition), diff));
        }

        public void releaseDropLock()
        {
            if (null != readyState)
            {
                readyState.ready();
                readyState = null;
            }
        }

        public void setAttributeCodes(Collection<String> codes)
        {
            attributeCodes.clear();
            attributeCodes.addAll(codes);
        }

        public void setEditableAttributes(Set<String> editableAttributes)
        {
            this.editableAttributes = editableAttributes;
        }

        @Override
        protected List<Element> retrieveChildren()
        {
            List<Element> elements = new ArrayList<>();
            NodeList<Element> trs = getRootElement().getElementsByTagName("tr");
            for (int i = 0; i < trs.getLength(); ++i)
            {
                elements.add(trs.getItem(i));
            }
            return elements;
        }

        @Override
        public boolean canDragStart(Element element)
        {
            int index = indexOf(element);
            if (index < 0 || index >= attributeCodes.size())
            {
                return false;
            }
            return editableAttributes.contains(attributeCodes.get(index));
        }
    }

    private enum Columns
    {
        UP(0), DOWN(1), TITLE(2), CODE(3);

        private int position;

        Columns(int position)
        {
            setPosition(position);
        }

        public int getPosition()
        {
            return position;
        }

        public void setPosition(int position)
        {
            this.position = position;
        }
    }

    @Inject
    private FontIconFactory<String> iconFactory;

    private FlowPanel tableContainer;
    private NauGrid table;
    private Anchor showUsageIcon;

    private ListEditorDnDController dndController;
    private AttributeGroupListDnDController listDnDController;

    @Inject
    public AttributeGroupDisplayImpl(ListEditorDnDControllerFactory dndControllerFactory, CommonMessages messages)
    {
        table = new NauGrid();
        table.addStyleName(AdminWidgetResources.INSTANCE.tables().tableElems());
        table.resize(1, Columns.values().length);
        table.setText(0, Columns.TITLE.getPosition(), messages.title());
        table.setText(0, Columns.CODE.getPosition(), messages.code());
        addImageStyle(0, Columns.UP.getPosition());
        addImageStyle(0, Columns.DOWN.getPosition());
        table.getCellFormatter().addStyleName(0, Columns.TITLE.getPosition(),
                AdminWidgetResources.INSTANCE.tables().tableElemsTDLeft());
        table.getRowFormatter().addStyleName(0, AdminWidgetResources.INSTANCE.tables().head());

        tableContainer = new FlowPanel();
        tableContainer.add(table);
        addControlledWidget(tableContainer);

        showUsageIcon = new Anchor();
        showUsageIcon.addStyleName(WidgetResources.INSTANCE.all().showUsageAttrGroupIcon());
        showUsageIcon.setTitle(messages.useInSettings());
        showUsageIcon.ensureDebugId("showUsageAttrGroup");
        addToCaptionPanel(showUsageIcon);

        addStyleName(WidgetResources.INSTANCE.all().attributeGroup());
        captionPanel.addStyleName(WidgetResources.INSTANCE.all().attributeGroupCaption());

        setOpened(false);

        listDnDController = new AttributeGroupListDnDController();
        dndController = dndControllerFactory.create(listDnDController);
    }

    /**
     * В Группах атрибутов кнопки видны всегда
     */
    @Override
    public void setOpened(boolean open)
    {
        super.setOpened(open);
        getToolBar().asWidget().setVisible(true);
    }

    public void addImageStyle(int row, int column)
    {
        table.getCellFormatter().addStyleName(row, column, WidgetResources.INSTANCE.tables().tableRowIcons());
        table.getCellFormatter().addStyleName(row, column, WidgetResources.INSTANCE.all().alignCenter());
    }

    @Override
    public HandlerRegistration addMoveAttrHandler(MoveAttrHandler handler)
    {
        return addHandler(handler, MoveAttrEvent.getType());
    }

    @Override
    public void destroy()
    {
        removeFromParent();
    }

    /**
     * Добавляет колонку с кнопками сортировки: "вверх" "вниз".
     * @param row
     * @param column
     * @param direction направление перемещения строки
     * @param attrCode код атрибута
     * @param attrCount количество атрибутов
     */
    public void addMoveColumn(int row, int column, FontIconDisplay<String> icon, final Move direction,
            final String attrCode,
            int attrCount)
    {
        if (1 == row && direction.equals(Move.UP) || attrCount == row && direction.equals(Move.DOWN))
        {
            table.setText(row, column, "");
        }
        else
        {
            icon.addClickHandler(new ClickHandler()
            {
                @Override
                public void onClick(ClickEvent event)
                {
                    AttributeGroupDisplayImpl.this
                            .fireEvent(new MoveAttrEvent(attrCode, direction == Move.UP ? -1 : 1));
                }
            });
            table.setWidget(row, column, icon);
            DebugIdBuilder.ensureDebugId(icon, attrCode, direction.name());
        }
        addImageStyle(row, column);
    }

    @Override
    public HandlerRegistration addShowUsageAttrGroupClickHandler(ClickHandler handler)
    {
        return showUsageIcon.addClickHandler(handler);
    }

    @Override
    public void scrollIntoView()
    {
        getElement().scrollIntoView();
    }

    @Override
    public void setAttributes(List<Attribute> attributes, @Nullable Set<String> disabledAttributes,
            Set<String> editableAttributes)
    {
        table.resize(attributes.size() + 1, Columns.values().length);
        for (int i = 0; i < attributes.size(); i++)
        {
            final Attribute attr = attributes.get(i);
            int row = i + 1;
            if (editableAttributes.contains(attr.getCode()))
            {
                addMoveColumn(row, Columns.UP.getPosition(), iconFactory.create(IconCodes.UP), Move.UP,
                        attr.getCode(), attributes.size());
                addMoveColumn(row, Columns.DOWN.getPosition(), iconFactory.create(IconCodes.DOWN), Move.DOWN,
                        attr.getCode(), attributes.size());
            }
            table.setText(row, Columns.TITLE.getPosition(), attr.getTitle());
            table.getCellFormatter().addStyleName(row, Columns.TITLE.getPosition(),
                    WidgetResources.INSTANCE.all().attributeGroupsTitle());
            table.setText(row, Columns.CODE.getPosition(), attr.getCode());
            if (attr.isHiddenAttrCaption())
            {
                table.getCellFormatter().addStyleName(row, Columns.TITLE.getPosition(),
                        WidgetResources.INSTANCE.form().hiddenAttrCaption());
            }
            if (null != disabledAttributes && disabledAttributes.contains(attr.getCode()))
            {
                table.getRowFormatter().addStyleName(row, AdminWidgetResources.INSTANCE.tables().tableRowYellow());
            }
            else
            {
                table.getRowFormatter().removeStyleName(row, AdminWidgetResources.INSTANCE.tables().tableRowYellow());
            }
            DebugIdBuilder.ensureDebugId(table.getWidget(row, Columns.TITLE.getPosition()), attr.getCode(),
                    Columns.TITLE.name());
            DebugIdBuilder.ensureDebugId(table.getWidget(row, Columns.CODE.getPosition()), attr.getCode(),
                    Columns.CODE.name());
        }

        listDnDController.setAttributeCodes(Collections2.transform(attributes, Attribute::getCode));
        listDnDController.setEditableAttributes(editableAttributes);

        dndController.update();
    }

    @Override
    public void setEditable(boolean editable)
    {
    }

    @Override
    public void stopProcessing()
    {
        super.stopProcessing();
        listDnDController.releaseDropLock();
    }
}
