package ru.naumen.metainfoadmin.client.tags;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TAGS;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.activity.PrevLinkContainer;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.tags.data.TagServiceAsync;

/**
 * Представление карточки метки в интерфейсе администратора.
 * <AUTHOR>
 * @since Sep 23, 2017
 */
public class TagCardPresenter extends AdminTabPresenter<TagPlace>
{
    private final TagServiceAsync tagService;
    private final TagInfoPresenter infoPresenter;
    private final TagUsagePlacesPresenter usagePlacesPresenter;
    private final Dialogs dialogs;
    private final CommonMessages commonMessages;
    private final TagsMessages tagsMessages;
    private final PrevLinkContainer prevLinkContainer;
    private final TagsPresenterSettings settings;

    private OnStartCallback<DtObject> refreshCallback = new SafeOnStartBasicCallback<DtObject>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            infoPresenter.setTag(value);
            usagePlacesPresenter.setTag(value);
            refreshDisplay();
        }
    };

    @Inject
    public TagCardPresenter(AdminTabDisplay display,
            EventBus eventBus,
            TagServiceAsync tagService,
            TagInfoPresenter infoPresenter,
            TagUsagePlacesPresenter usagePlacesPresenter,
            Dialogs dialogs,
            CommonMessages commonMessages,
            TagsMessages tagsMessages,
            PrevLinkContainer prevLinkContainer,
            TagsPresenterSettings settings)
    {
        super(display, eventBus);
        this.tagService = tagService;
        this.infoPresenter = infoPresenter;
        this.usagePlacesPresenter = usagePlacesPresenter;
        this.dialogs = dialogs;
        this.commonMessages = commonMessages;
        this.tagsMessages = tagsMessages;
        this.prevLinkContainer = prevLinkContainer;
        this.settings = settings;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        infoPresenter.refreshDisplay();
        usagePlacesPresenter.refreshDisplay();
    }

    protected void afterTagLoaded(DtObject tagDto)
    {
        Place previousPlace = prevLinkContainer.getPreviousPlace();
        String backLinkCaption = null == previousPlace || previousPlace instanceof TagsPlace
                ? tagsMessages.backToTags()
                : commonMessages.back();
        prevPageLinkPresenter.bind(backLinkCaption, TagsPlace.INSTANCE);

        getDisplay().setTitle(tagDto.getTitle());
        infoPresenter.setTag(tagDto);
        usagePlacesPresenter.setTag(tagDto);

        addContent(infoPresenter, "info");
        addContent(usagePlacesPresenter, "usage");
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        if (!settings.isTagCardEnabled())
        {
            dialogs.error(commonMessages.resourceNotFoundUserMessage());
            return;
        }

        infoPresenter.init(refreshCallback);

        tagService.getTag(getPlace().getTagCode(), new BasicCallback<DtObject>(getDisplay())
        {
            @Override
            protected void handleSuccess(@Nullable DtObject value)
            {
                if (value == null)
                {
                    dialogs.error(commonMessages.resourceNotFoundUserMessage());
                }
                else
                {
                    afterTagLoaded(value);
                }
            }
        });
    }

    @Override
    protected void onUnbind()
    {
        infoPresenter.unbind();
        usagePlacesPresenter.unbind();
        super.onUnbind();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return TAGS;
    }
}
