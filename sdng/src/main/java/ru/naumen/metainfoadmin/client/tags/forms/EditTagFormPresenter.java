package ru.naumen.metainfoadmin.client.tags.forms;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Tag;

/**
 * Представление формы редактирования метки.
 * <AUTHOR>
 * @since Sep 22, 2017
 */
public class EditTagFormPresenter extends TagFormPresenter
{
    @Inject
    public EditTagFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void bindProperties()
    {
        super.bindProperties();
        code.setDisable();
    }

    @Override
    protected String getFormCaption()
    {
        return messages.editingTag();
    }

    @Override
    protected void initValues()
    {
        super.initValues();
        if (null == tag)
        {
            return;
        }

        title.setValue(tag.getTitle());
        code.setValue(tag.getProperty(Constants.Tag.CODE));
        description.setValue(tag.getProperty(Constants.Tag.DESCRIPTION));
    }

    @Override
    protected String getInitSettingsSetValue()
    {
        return tag.getProperty(Tag.SETTINGS_SET);
    }
}
