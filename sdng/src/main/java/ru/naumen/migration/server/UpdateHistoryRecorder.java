package ru.naumen.migration.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.Date;

import jakarta.inject.Inject;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.Version;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.hibernate.column.ColumnDescription;
import ru.naumen.core.server.hibernate.column.ColumnDescriptions;
import ru.naumen.core.server.hibernate.constraint.PrimaryKeyConstraint;
import ru.naumen.core.server.hibernate.table.TableDescription;

/**
 * Фиксирует историю обновлений приложения в таблице update_history
 * При каждом старте проверяет наличие таблицы update_history, если её нет - создает и записывает текущую версию
 * При наличии этой таблицы сравнивает текущую версию приложения и версию в таблице:
 * Если текущая версия новее - записывает её в таблицу
 *
 * Работа компонента в двух фазах - на старте запуска приложения ({@link #start()}) и
 * по окончании запуска приложения ({@link #finish()}).
 *
 * Первая фаза должна вызываться до начала обновлений, которые могут вызвать ошибки запуска, чтобы зафиксировать
 * попытку обновления на версию. (Вызывается из ru.naumen.migration.server.EmptySchemaInitializer, пока никакие миграции
 * еще не начались)
 *
 * Вторая фаза по окончании старта, когда все обновления на новую версию завершились удачно, чтобы зафиксировать
 * успешность обновления. (Вызывается из ru.naumen.migration.server.OnStartMigrator, по окончании прохождения всех
 * миграций)
 */
@Component
public class UpdateHistoryRecorder
{
    private static String TABLE_NAME = "update_history";
    private static String ID_COLUMN_NAME = "id";
    private static String PREV_VERSION_COLUMN_NAME = "prev_version";
    private static String CURRENT_VERSION_COLUMN_NAME = "current_version";
    private static String UPDATE_DATETIME_COLUMN_NAME = "update_datetime";

    private static final Logger LOG = LoggerFactory.getLogger(UpdateHistoryRecorder.class);

    @Inject
    private DataBaseInfo dataBaseInfo;

    @Inject
    private DataSource dataSource;

    /**
     * Метод предназначен для фиксации момента успешного окончания обновления версии
     * приложения (окончания запуска приложения)
     */
    public void finish()
    {
        if (AppContext.isReadOnly())
        {
            LOG.info("Read only enabled. UpdateHistoryRecorder finish skipped.");
            return;
        }

        try (Connection connection = dataSource.getConnection())
        {
            Long lastId = getLastIdValue(connection);
            if (lastId != null)
            {
                recordFinish(connection, lastId);
            }
        }
        catch (SQLException exc)
        {
            // Далее не прокидываем, чтобы не останавливать запуск приложения
            LOG.error(exc.getMessage(), exc);
        }
    }

    /**
     * Метод предназначен для фиксации момента начала обновления приложения на новую версию
     * (старт запуска приложения)
     */
    public void start()
    {
        if (AppContext.isReadOnly())
        {
            LOG.info("Read only enabled. UpdateHistoryRecorder start skipped.");
            return;
        }
        init();
        try (Connection connection = dataSource.getConnection())
        {
            createTableIfNeeded(connection);
            Long lastId = getLastIdValue(connection);
            String prevVersion = null == lastId ? null : getVersion(connection, lastId);
            long nextId = null == lastId ? 0L : lastId + 1L;
            recordStart(connection, nextId, prevVersion);
        }
        catch (SQLException exc)
        {
            // Далее не прокидываем, чтобы не останавливать запуск приложения
            LOG.error(exc.getMessage(), exc);
        }
    }

    private void createTable(DDLTool ddlTool) throws SQLException
    {
        ColumnDescription idColumn = ColumnDescriptions.bigint(ID_COLUMN_NAME);
        idColumn.addConstraint(new PrimaryKeyConstraint(ID_COLUMN_NAME));

        ColumnDescription prevVersionColumn = ColumnDescriptions.string(PREV_VERSION_COLUMN_NAME);
        ColumnDescription currentVersionColumn = ColumnDescriptions.string(CURRENT_VERSION_COLUMN_NAME);
        ColumnDescription updateDatetimeColumn = ColumnDescriptions.dateTime(UPDATE_DATETIME_COLUMN_NAME);
        TableDescription tableDescription = new TableDescription(TABLE_NAME, idColumn, prevVersionColumn,
                currentVersionColumn, updateDatetimeColumn);
        ddlTool.createTable(tableDescription);
    }

    private boolean createTableIfNeeded(Connection connection) throws SQLException
    {
        DDLTool ddlTool = new DDLTool(connection);
        boolean created = !ddlTool.tableExists(dataBaseInfo.getSchema(), TABLE_NAME);
        if (created)
        {
            createTable(ddlTool);
        }
        return created;
    }

    private Long getLastIdValue(Connection connection) throws SQLException
    {
        try (Statement statement = connection.createStatement();
             ResultSet rs = statement.executeQuery(
                     "SELECT MAX(" + ID_COLUMN_NAME + ") FROM " + dataBaseInfo.getSchema() + "." + TABLE_NAME))
        {
            Object value = null;
            if (rs.next())
            {
                value = rs.getObject(1);
            }
            return value == null ? null : ((Number)value).longValue();
        }
    }

    private String getVersion(Connection connection, long id) throws SQLException
    {
        try (PreparedStatement statement = connection.prepareStatement("SELECT " + CURRENT_VERSION_COLUMN_NAME
                                                                       + " FROM " + dataBaseInfo.getSchema() + "."
                                                                       + TABLE_NAME + " WHERE " + ID_COLUMN_NAME
                                                                       + " = ?"))
        {
            statement.setLong(1, id);
            try (ResultSet rs = statement.executeQuery())
            {
                if (rs.next())
                {
                    return rs.getString(1);
                }
                return null;
            }
        }
    }

    private void init()
    {
        TABLE_NAME = DDLTool.getCanonicalIdentifier(TABLE_NAME);
        ID_COLUMN_NAME = DDLTool.getCanonicalIdentifier(ID_COLUMN_NAME);
        PREV_VERSION_COLUMN_NAME = DDLTool.getCanonicalIdentifier(PREV_VERSION_COLUMN_NAME);
        CURRENT_VERSION_COLUMN_NAME = DDLTool.getCanonicalIdentifier(CURRENT_VERSION_COLUMN_NAME);
        UPDATE_DATETIME_COLUMN_NAME = DDLTool.getCanonicalIdentifier(UPDATE_DATETIME_COLUMN_NAME);
    }

    private void recordFinish(Connection connection, long id) throws SQLException
    {
        try (PreparedStatement statement = connection.prepareStatement(
                "UPDATE " + dataBaseInfo.getSchema() + "." + TABLE_NAME + " SET "
                + UPDATE_DATETIME_COLUMN_NAME + " = ? WHERE " + ID_COLUMN_NAME + " = ? AND "
                + UPDATE_DATETIME_COLUMN_NAME + " IS NULL"))
        {
            statement.setTimestamp(1, new Timestamp(new Date().getTime()));
            statement.setLong(2, id);
            statement.executeUpdate();
        }
    }

    private void recordStart(Connection connection, long id, String prevVersion) throws SQLException
    {
        if (Version.getFullVersion().equals(prevVersion))
        {
            return;
        }
        try (PreparedStatement statement = connection.prepareStatement(
                "INSERT INTO " + dataBaseInfo.getSchema() + "." + TABLE_NAME
                + " VALUES(?, ?, ?, ?)"))
        {
            statement.setLong(1, id);
            statement.setString(2, prevVersion == null ? "old" : prevVersion);
            statement.setString(3, Version.getFullVersion());
            statement.setTimestamp(4, null);
            statement.executeUpdate();
        }
    }
}