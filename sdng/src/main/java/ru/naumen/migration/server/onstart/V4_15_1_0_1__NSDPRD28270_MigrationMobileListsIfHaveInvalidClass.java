package ru.naumen.migration.server.onstart;

import static ru.naumen.metainfo.server.Constants.MOBILE_SETTINGS;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.shared.Constants.ServiceUsers;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfo.shared.mobile.contents.MobileListsGroupContent;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.lists.MobileRelObjectsList;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Миграция списков мобильного приложения при несоответствие класса
 *
 * <AUTHOR>
 * @since 28.04.2022
 */
public class V4_15_1_0_1__NSDPRD28270_MigrationMobileListsIfHaveInvalidClass extends JavaMigrationScript
{
    private static final Logger LOG = LoggerFactory.getLogger(
            V4_15_1_0_1__NSDPRD28270_MigrationMobileListsIfHaveInvalidClass.class);

    @Inject
    private MetainfoService metainfoService;
    @Inject
    private MetaStorageService metainfoStorage;
    @Inject
    private AuthorizationRunnerService authorizeRunner;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        LOG.info("Start migrate mobile lists if have invalid class");
        authorizeRunner.runAsSuperUser(ServiceUsers.MIGRATION_USER, () -> TransactionRunner.run(() ->
        {
            MobileSettings mobileSettings = metainfoService.getMobileSettings();
            if (null != mobileSettings)
            {
                List<String> cleanedItemsUuids = migrateMobileSettings(mobileSettings.getLists());
                deleteUsagePlaces(mobileSettings.getObjectCards(), cleanedItemsUuids);
                if (!cleanedItemsUuids.isEmpty())
                {
                    metainfoStorage.save(mobileSettings, MOBILE_SETTINGS, MOBILE_SETTINGS);
                }
            }
        }));
        LOG.info("Complete migration mobile lists if have invalid class");
    }

    private List<String> migrateMobileSettings(List<MobileList> mobileLists)
    {
        List<String> cleanedItemsUuids = new ArrayList<>();
        mobileLists.stream().filter(item -> item instanceof MobileRelObjectsList &&
                                            !((MobileRelObjectsList)item).getAttrsChain().isEmpty()).forEach(item ->
        {
            List<AttrReference> attributesChains = ((MobileRelObjectsList)item).getAttrsChain();
            AttrReference attrReference = attributesChains.get(attributesChains.size() - 1);
            ClassFqn metaClassFqn = metainfoService.getAttribute(AttributeFqn.parse(attrReference.toFqnString()))
                    .getType().<ObjectAttributeType> cast().getRelatedMetaClass();
            Collection<ClassFqn> metaClassDescendants = metainfoService.getMetaClassDescendants(metaClassFqn,
                    false);
            if (item.getCases().stream().anyMatch(c -> !metaClassDescendants.contains(c)))
            {
                item.getCases().clear();
                item.setClazz(metaClassFqn);
                item.getListFilter().getElements().clear();
                item.getOrders().clear();
                item.getAttributes().clear();
                cleanedItemsUuids.add(item.getUuid());
                LOG.info("Migrate to reset parameter for mobile list '{}'", item.getCode());
            }
        });
        return cleanedItemsUuids;
    }

    private static void deleteUsagePlaces(List<ObjectCard> objectCards, List<String> cleanedItemsUuids)
    {
        objectCards.stream().flatMap(objectCard -> objectCard.getContents().stream()).forEach(content ->
        {
            if (content instanceof MobileListContentBase && cleanedItemsUuids.contains(
                    ((MobileListContentBase)content).getLinkToMobileList()))
            {
                ((MobileListContentBase)content).setLinkToMobileList(null);
            }
            if (content instanceof MobileListsGroupContent && CollectionUtils.isNotEmpty(
                    ((MobileListsGroupContent)content).getListContents()))
            {
                ((MobileListsGroupContent)content).getListContents()
                        .stream()
                        .filter(l -> l instanceof MobileListContentBase && cleanedItemsUuids.contains(
                                ((MobileListContentBase)l).getLinkToMobileList()))
                        .forEach(l -> ((MobileListContentBase)l).setLinkToMobileList(null));
            }
            if (content instanceof MobilePropertiesListContent)
            {
                ((MobilePropertiesListContent)content).getAttributes()
                        .stream()
                        .filter(a -> cleanedItemsUuids.contains(a.getMobileListUuid()))
                        .forEach(a -> a.setMobileListUuid(null));
            }
        });
    }
}
