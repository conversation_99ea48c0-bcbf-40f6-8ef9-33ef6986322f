package ru.naumen.migration.server.onstart;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;

import jakarta.inject.Inject;

import org.springframework.beans.factory.annotation.Value;

import ru.naumen.commons.server.utils.LdapUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.configuration.FxBootstrapProperties;
import ru.naumen.core.server.ldap.LdapAuthenticationService;
import ru.naumen.migration.server.JavaMigrationScript;
import ru.naumen.sec.server.AuthenticatorType;

/**
 * Перенос настроек для аутентификации в LDAP или AD из bdaccess.propertie 
 * в отдельный файл ldap-settings.xml
 *
 * <AUTHOR>
 * @since 08 февр. 2016 г.
 */
public class V4_6_0__NSDPRD4790_LdapSettings extends JavaMigrationScript
{

    @Value("${ru.naumen.core.authentication.authenticators}")
    String authenticators;

    @Value("${ru.naumen.core.authentication.ldap-authenticator.connection-url}")
    String ldapUrl;

    @Value("${ru.naumen.core.authentication.ldap-authenticator.user-dn}")
    String ldapUserDn;

    @Value("${ru.naumen.core.authentication.ldap-authenticator.user-search-filter}")
    String ldapSearchFilter;

    @Value("${ru.naumen.core.authentication.ldap-authenticator.connection-timeout}")
    String ldapTimeout;

    @Value("${ru.naumen.core.authentication.ad-authenticator.connection-url}")
    String adUrl;

    @Value("${ru.naumen.core.authentication.ad-authenticator.user-upn}")
    String adUserDn;

    @Value("${ru.naumen.core.authentication.ad-authenticator.user-password}")
    String adUserPassword;

    @Value("${ru.naumen.core.authentication.ad-authenticator.user-password.enc}")
    String adUserPasswordEnc;

    @Value("${ru.naumen.core.authentication.ad-authenticator.users-dn}")
    String adBaseDn;

    @Value("${ru.naumen.core.authentication.ad-authenticator.user-search-filter}")
    String adSearchFilter;

    @Value("${ru.naumen.core.authentication.ad-authenticator.connection-timeout}")
    String adTimeout;

    String defaultSourceCode = "source1";

    @Inject
    LdapAuthenticationService service;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        if (!authenticators.contains(AuthenticatorType.LDAP.name())
            && !authenticators.contains(AuthenticatorType.AD.name()))
        {
            return;
        }

        String configFileName = FxBootstrapProperties.externalPropertiesDirectory() + File.separator
                                + "ldap-settings.xml";
        File file = new File(configFileName);
        if (!file.exists())
        {
            try
            {
                file.createNewFile();
            }
            catch (IOException e)
            {
                throw new FxException("Unable to create new configuration file 'ldap-settings.xml' in "
                                      + System.getProperty("ext.prop.dir") + ": " + e.getMessage(), e);
            }
        }
        else
        {
            LOG.warn("Ldap configuration file allready exists!");
            return;
        }

        LOG.info("Create Ldap configuration file...");
        String adSource = createAdSource();
        String ldapSource = createLdapSource();

        //@formatter:off
        String content = String.format("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<n:configuration xmlns:n=\"http://www.naumen.ru/ldapSettings\" " +
                "xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" " +
                "xsi:schemaLocation=\"http://www.naumen.ru/ldapSettings ldapSettings.xsd \">\n" +
                "\t<defaultSource>" + defaultSourceCode + "</defaultSource>\n" +
                "\t<sources>\n%s%s\t</sources>\n</n:configuration>", adSource, ldapSource);
        //@formatter:on

        FileWriter wr = new FileWriter(file);
        wr.write(content);
        wr.flush();
        wr.close();
        LOG.info("Ldap configuration file successfully created.");

        service.reload();
    }

    private String createAdSource()
    {
        if (!authenticators.contains(AuthenticatorType.AD.name()))
        {
            return "";
        }
        return String.format(getSourceTemplate(), defaultSourceCode, adUrl, LdapUtils.fixSlashes(adUserDn),
                LdapUtils.fixSlashes(adUserPassword), LdapUtils.fixSlashes(adUserPasswordEnc), adBaseDn,
                adSearchFilter, adTimeout);
    }

    private String createLdapSource()
    {
        if (!authenticators.contains(AuthenticatorType.LDAP.name()))
        {
            return "";
        }
        String sourceCode = authenticators.contains(AuthenticatorType.AD.name()) ? "source2" : defaultSourceCode;
        //Из url нужно убрать дерево отделов
        ldapUrl = ldapUrl.replace(ldapUserDn, "");
        return String.format(getSourceTemplate(), sourceCode, ldapUrl, "", "", "", LdapUtils.fixSlashes(ldapUserDn),
                ldapSearchFilter, ldapTimeout);
    }

    private String getSourceTemplate()
    {
        //@formatter:off
        return "\t\t<source>\n" +
                        "\t\t\t<code>%s</code>\n" +
                        "\t\t\t<url>%s</url>\n" +
                        "\t\t\t<login>%s</login>\n" +
                        "\t\t\t<password>%s</password>\n" +
                        "\t\t\t<encPassword>%s</encPassword>\n" +
                        "\t\t\t<baseDn>%s</baseDn>\n" +
                        "\t\t\t<domain></domain>\n" +
                        "\t\t\t<searchFilter>%s</searchFilter>\n" +
                        "\t\t\t<connectionTimeout>%s</connectionTimeout>\n" +
               "\t\t</source>\n";
       //@formatter:off
    }
}