package ru.naumen.migration.server.onstart;

import java.sql.Connection;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Queue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.AbstractSystemObject;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.AttributeGroupImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.ui.ClientInfo;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.EditablePropertyList;
import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;
import ru.naumen.metainfo.shared.ui.PropertyList;
import ru.naumen.metainfo.shared.ui.PropertyListBase;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.SelectCase;
import ru.naumen.metainfo.shared.ui.SelectContacts;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Изменяет названия для контентов "Встроенное приложение", "Параметры на форме" и "Параметры объекта" на видимое
 * пользователю
 * <AUTHOR>
 * @since 25.04.2017
 */
@SuppressWarnings("java:S3776")
public class V4_7_7_1__nsdprd6133_content extends JavaMigrationScript //NOSONAR
{
    private static final Logger LOG = LoggerFactory.getLogger(V4_7_7_1__nsdprd6133_content.class);

    @Inject
    private MetainfoServiceBean service;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private MetainfoServiceBean metainfoService;
    @Inject
    private EmbeddedApplicationService applicationService;
    @Inject
    private MessageFacade messages;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        Collection<ContentInfo> containers = service.getUiForms();
        List<Pair<ClassFqn, String>> handledContent = new ArrayList<>();
        for (ContentInfo container : containers)
        {
            ClassFqn fqn = container.getDeclaredMetaclass();
            if (AbstractSystemObject.FQN.equals(metainfoService.getMetaClass(fqn).getParent())
                || handledContent.contains(new Pair<ClassFqn, String>(fqn, container.getFormId())))
            {
                continue;
            }
            boolean needSave = false;
            Queue<Content> contents = new ArrayDeque<>();
            contents.add(container.getContent());

            for (Content currentContent; null != (currentContent = contents.poll()); )
            {
                if (currentContent instanceof EmbeddedApplicationContent embeddedApplicationContent)
                {
                    changeCaptionForEmbeddedApplication(embeddedApplicationContent, fqn);
                    needSave = true;
                }
                else if ((currentContent instanceof PropertyList
                          && !(currentContent instanceof ClientInfo || currentContent instanceof RelObjPropertyList))
                         || currentContent instanceof EditablePropertyList)
                {
                    changeCaptionForPropertyList((PropertyListBase)currentContent, fqn);
                    needSave = true;
                }
                else if (currentContent instanceof SelectCase || currentContent instanceof SelectContacts)
                {
                    changeCaptionForSelectCaseOrSelectContacts(currentContent, fqn);
                    needSave = true;
                }
                else if (currentContent instanceof ClientInfo clientInfo)
                {
                    changeClientInfoContent(clientInfo, fqn);
                    needSave = true;
                }

                contents.addAll(currentContent.getChilds());
            }

            if (needSave)
            {
                service.setUIForm(fqn, container.getFormId(), container.getContent(), true, false);
            }
            handledContent.add(new Pair<>(fqn, container.getFormId()));
        }
    }

    private void changeCaptionForEmbeddedApplication(final EmbeddedApplicationContent application, ClassFqn fqn)
    {
        EmbeddedApplication appl = applicationService.getApplication(application.getApplication());
        String applicationCaption = metainfoUtils.getLocalizedValue(appl.getTitle());
        LOG.info("Content with code {} change caption in metaclass {}: {} to {}", application.getUuid(), fqn,
                metainfoUtils.getLocalizedValue(application.getCaption()), applicationCaption);
        metainfoUtils.setCaption(application.getCaption(), applicationCaption);
    }

    private void changeCaptionForPropertyList(PropertyListBase propertyList, ClassFqn fqn)
    {
        AttributeGroupImpl attrGroup = metainfoService.getMetaClass(fqn)
                .getAttributeGroup(propertyList.getAttributeGroup());
        LOG.info("Content with code {} change caption in metaclass {}: {} to {}", propertyList.getUuid(), fqn,
                metainfoUtils.getLocalizedValue(propertyList.getCaption()), attrGroup.getTitle());
        metainfoUtils.setCaption(propertyList.getCaption(), attrGroup.getTitle());
    }

    private void changeCaptionForSelectCaseOrSelectContacts(Content content, ClassFqn fqn)
    {
        String title = content instanceof SelectCase ? messages.getMessage("selectCase")
                : messages.getMessage("selectContacts");
        LOG.info("Content with code {} change caption in metaclass {}: to {}", content.getUuid(), fqn, title);
        metainfoUtils.setCaption(content.getCaption(), title);
    }

    private void changeClientInfoContent(ClientInfo clientInfo, ClassFqn fqn)
    {
        clientInfo.setShowCaption(true);
        LOG.info("Content with code {} change caption in metaclass {}: to {}", clientInfo.getUuid(), fqn,
                metainfoUtils.getLocalizedValue(clientInfo.getCaption()));
    }
}
