/**
 *
 */
package ru.naumen.migration.server.scripts;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.DMLTool;
import ru.naumen.core.server.hibernate.column.ColumnDescriptions;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Изменяем тип колонки template в tbl_report_instance на blob(oracle), oid(postgres)  
 * <AUTHOR>
 * @since Nov 28, 2014
 */
public class V4_3_8_22_0__NSDPRD_3356 extends JavaMigrationScript
{
    private static final String TABLE = "tbl_report_instance";
    private static final String COLUMN = "template";
    private static final String NEW_COLUMN = "template_data";

    @Override
    @SuppressWarnings("PMD")
    public void migrate(Connection connection) throws Exception
    {
        DDLTool ddlTool = new DDLTool(connection);
        DMLTool dmlTool = new DMLTool(connection);

        if (!ddlTool.columnExists(TABLE, COLUMN) || ddlTool.columnExists(TABLE, NEW_COLUMN))
        {
            return;
        }

        ddlTool.createColumn(TABLE, ColumnDescriptions.blob(NEW_COLUMN));
        String sql = "select id, template from " + TABLE;
        try (Statement st = connection.createStatement())
        {
            ResultSet rs = st.executeQuery(sql);

            while (rs.next())
            {
                long id = rs.getLong("id");
                byte[] templateData = rs.getBytes("template");
                dmlTool.setBytesInBlob(TABLE, NEW_COLUMN, templateData, id);
            }
            rs.close();
        }
        ddlTool.dropColumn(null, TABLE, COLUMN, false);
    }
}
