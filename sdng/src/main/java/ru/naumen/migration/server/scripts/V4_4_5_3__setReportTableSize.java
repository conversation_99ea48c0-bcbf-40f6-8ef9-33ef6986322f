package ru.naumen.migration.server.scripts;

import java.sql.Connection;
import java.sql.PreparedStatement;

import jakarta.inject.Inject;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.column.ColumnDescriptions;
import ru.naumen.migration.server.JavaMigrationScript;
import ru.naumen.smp.report.specification.services.ReportWeightController;

/**
 * Миграция заполняет пустые колонки table_size таблицы tbl_report_instance
 *
 * <AUTHOR>
 * @since Mar 03, 2015
 */
public class V4_4_5_3__setReportTableSize extends JavaMigrationScript
{
    private String REPORT_INSTANCE_TABLE = "tbl_report_instance";
    private String TABLE_SIZE_COLUMN = "table_size";

    @Inject
    ReportWeightController weightController;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        DDLTool tool = new DDLTool(connection);

        if (tool.tableExists(REPORT_INSTANCE_TABLE) && !tool.columnExists(REPORT_INSTANCE_TABLE, TABLE_SIZE_COLUMN))
        {
            tool.createColumn(REPORT_INSTANCE_TABLE, ColumnDescriptions.bigint(TABLE_SIZE_COLUMN));
        }

        if (tool.tableExists(REPORT_INSTANCE_TABLE) && tool.columnExists(REPORT_INSTANCE_TABLE, TABLE_SIZE_COLUMN))
        {
            String query = String.format("update %1$s set %2$s = ? where %2$s is null", REPORT_INSTANCE_TABLE,
                    TABLE_SIZE_COLUMN);

            try (PreparedStatement statement = tool.getConnection().prepareStatement(query))
            {
                statement.setLong(1, weightController.getMaxSize());
                statement.executeUpdate();
            }
        }
    }
}
