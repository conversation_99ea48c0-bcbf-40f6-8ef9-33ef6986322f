/**
 *
 */
package ru.naumen.migration.server.scripts;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.UIContainer;
import ru.naumen.migration.server.JavaMigrationScript;
import ru.naumen.migration.server.metainfoscripts.MetainfoMigrationUtils;

import java.util.HashSet;

/**
 * Исправляет типы в цепочках атрибутов обратная ссылка во всех контентах типа "Список связанных объектов" на
 * "Карточке объекта"
 * которые возникли в результате копирования типов объектов
 * <AUTHOR>
 * @since 15 мая 2015 г.
 *
 */
public class V4_4_9_0__nsdprd3924 extends JavaMigrationScript
{
    @Inject
    private MetainfoMigrationUtils migrationUtils;
    @Inject
    private MetainfoServiceBean metainfoService;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        Collection<Pair<UIContainer, Content>> contents = migrationUtils.getContents();

        Set<Pair<UIContainer, Content>> modifiedPairs = new HashSet<>();
        for (Pair<UIContainer, Content> contentPair : contents)
        {
            ClassFqn fqn = contentPair.getLeft().getFqn();
            if (fqn.isClass())
            {
                continue;
            }
            MetaClassImpl metaClass = metainfoService.getMetaClass(fqn);
            if (contentPair.getRight() instanceof RelObjectList)
            {
                RelObjectList relObjectList = (RelObjectList)contentPair.getRight();
                List<AttrReference> attrChain = relObjectList.getAttributesChain();
                // Битая ссылка всегда идет первой в цепочке, остальные в норме
                AttrReference attrRef = attrChain.get(0);
                ArrayList<AttrReference> attrChainNew = new ArrayList<>();

                if (attrRef.getClassFqn() != null && !metaClass.getFqnHierarchy().contains(attrRef.getClassFqn()))
                {
                    attrChainNew.add(new AttrReference(fqn, attrRef.getAttrCode()));
                    attrChain.remove(0);
                    attrChainNew.addAll(attrChain);

                    relObjectList.getAttributesChain().clear();
                    relObjectList.getAttributesChain().addAll(attrChainNew);

                    modifiedPairs.add(contentPair);
                }
            }
        }
        for (Pair<UIContainer, Content> contentPair : modifiedPairs)
        {
            UIContainer modifiedUI = contentPair.getLeft();
            metainfoService.setUIForm(modifiedUI.getFqn(), modifiedUI.getCode(), modifiedUI.getContent(), true, false);
        }
    }
}