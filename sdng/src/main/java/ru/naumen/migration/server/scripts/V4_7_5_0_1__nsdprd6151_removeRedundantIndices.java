package ru.naumen.migration.server.scripts;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Set;

import jakarta.inject.Inject;

import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.migration.server.JavaMigrationScript;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Миграция для задачи
 * http://sd-jira.naumen.ru/browse/NSDPRD-6151
 *
 * Удаление избыточных индексов
 *
 * <AUTHOR>
 * @since 22.02.2017
 */
public class V4_7_5_0_1__nsdprd6151_removeRedundantIndices extends JavaMigrationScript
{
    @Inject
    private FlexHelper flexHelper;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        DDLTool tool = new DDLTool(connection);
        //Сначала удалим фиксированный набор индексов
        tool.dropIndex("qrtz_fired_triggers", "idx_qrtz_ft_trig_inst_name");
        tool.dropIndex("qrtz_triggers", "idx_qrtz_t_nft_misfire");
        tool.dropIndex("qrtz_triggers", "idx_qrtz_t_g");
        tool.dropIndex("qrtz_triggers", "idx_qrtz_t_state");
        tool.dropIndex("tbl_employee_teams", "idx_empl_teams_employee_id");
        tool.dropIndex("tbl_file", "idx_file_source");
        //Затем удалим индексы на первую колонку для таблиц связи many-to-many атрибутов-коллекций
        //"Набор ссылок на БО"
        for (Attribute attr : flexHelper.getBOLinksFlexAttributes())
        {
            dropIndexFromByAttr(attr, tool);
        }
        //"Набор элементов справочника"
        for (Attribute attr : flexHelper.getCatalogItemsFlexAttributes())
        {
            dropIndexFromByAttr(attr, tool);
        }
    }

    private void dropIndexFromByAttr(Attribute attr, DDLTool tool) throws SQLException
    {
        String tableName = DDLTool.getDialect().getCanonicalIdentifier(
                FlexHelper.getTableName(attr.getDeclaredMetaClass(), attr));
        String keyColumn = attr.getMetaClass().getFqn().getId() + "_id";
        //Удаляем индексы с первой колонки, кроме первичного ключа (берем список всех индексов колонки,
        //чтобы не обращать внимание на правила именования индекса в различные периоды развития приложения)
        Set<String> indices = tool.getExistingIndexNames(tableName, keyColumn);
        indices.remove(tool.getPrimaryKeyName(tableName));
        for (String index : indices)
        {
            tool.dropIndex(tableName, index);
        }
    }
}
