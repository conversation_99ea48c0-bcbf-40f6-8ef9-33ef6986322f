package ru.naumen.reports.shared;

import java.util.ArrayList;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @since 21.09.2012
 */
public interface Constants
{
    interface Buttons
    {
        String PARAMETERS = "reportParameters";
        String REBUILD = "refreshReport";
        String CREATE_NEW = "createNew";
        String CREATE_NEW_REPORT = "createNewReport";
        String SAVE = "saveReport";
        String PRINT_REPORT = "printReport";
        String EXPORT_EMAIL = "exportEmail";
    }

    interface CsvReportFormat
    {
        char SEPARATOR = ';';
    }

    interface MasterReport
    {
        String FUNCTIONS_METHOD = "getFunctions";
    }

    interface OnlineReport
    {
        String CLASS_ID = "onlineReport";
    }

    interface ParametersFeature
    {
        String REBUILD = "rebuildReport";
        String RESET_TO_DEFAULT_VALUES = "resetParameters";

        String TOOL_PANEL = "parametersToolPanel";
    }

    interface ReportFeature
    {
        String PARAMETERS = "parametersFeature";
    }

    interface ReportFormat
    {
        String PDF = "pdf";
        String HTML_ZIP = "htmlZip";
        String HTML = "html";
        String XLSX = "xlsx";
        String XLS = "xls";
        String CSV = "csv";
        String DOCX = "docx";
    }

    interface ReportInstancesCommandCode
    {
        String PRINT = "print";
        String EXPORT_XLSX = "exportXlsx";
        String EXPORT_DOCX = "exportDocx";
        String EXPORT_PDF = "exportPdf";
        String DELETE = "del";
        String EXPORT_EMAIL = "exportEmail";

        ArrayList<String> ALL_COMMANDS = Lists.newArrayList(PRINT, EXPORT_XLSX, EXPORT_DOCX, EXPORT_PDF, DELETE,
                EXPORT_EMAIL);
    }

    interface ReportParameters
    {
        String SUBJECT_CASE_PARAM = "subject_case";
        String SUBJECT_ID_PARAM = "subject_id";
        String SUBJECT_TABLE_PARAM = "subject_table";
        String POSSIBLE_TO_SEND = "possibleToSend";

        /**
         * Дополнительные параметры в параметрах отчета
         * Способ округления атрибута типа "Дата и время". {@see DateTextBoxWithPickerWidgetBase}
         */
        String DATE_TIME_ROUNDING_MODE = "dateTimeRoundingMode";

        /**
         * Дополнительные параметры в параметрах отчета
         * группа атрибутов для отражения в ComplexRelationForm в виджете добавления сложной связи,
         * если параметр задан то появится иконка виджета сложной формы добавления
         */
        String ATTR_GROUP_CODE = "attrGroupCode";

        /**
         * Дополнительные параметры в параметрах отчета
         * код представления (0,1,2,3 ... - порядковый номер представления соответствующего типа атрибутов)
         */
        String PRESENTATION_CODE = "presentationCode";
    }

    interface ReportTemplate
    {
        String PARAMETERS_METHOD = "getParameters";
        String GET_ACTUAL_PARAMETERS_METHOD = "getActualParameters";
        String SETTINGS_METHOD = "getSettings";
        String STREAM_MODE_DIRECTIVE = "/*&streamMode*/";
        String PARAMETERS_SCRIPTS = "getParametersScripts";
    }

    final class ReportTemplateCard
    {
        private ReportTemplateCard()
        {
        }

        public static final String TITLE = "title";
        public static final String DESCRIPTION = "description";
        public static final String FILE = "reportFile";
        public static final String SCRIPT = "script";
        public static final String CODE = "code";
        public static final String DELETE = "delete";
        public static final String BACK_TITLE = "ReportTemplateMessages.backTitle";
        public static final String SETTINGS_SET = "settingsSet";
        public static final String SETTINGS_SET_ELEMENTS = "settingsSet#elements";
    }

    interface SaveReportServlet
    {
        String URL = "saveReport";
        String UUID = "uuid";
        String FORMAT = "format";
        String FILE_NAME = "report";
        String MODE = "mode";

        String EXPORT_MODE = "export";
        String PRINT_MODE = "print";
    }

    interface SQLReportDataFactoryImpl
    {
        String TABLE_PARAM = "table";
    }

    String REPORT_TEMPLATES_STORAGE_TYPE_OLD = "report-templates-container";
    String REPORT_TEMPLATES_STORAGE_TYPE = "report-templates-container-2";
    String REPORT_PARAMETERS_STORAGE_TYPE = "report-parameters-container";
    String PARAMETERS_SERIALIZER = "parametersSerializer";
    String SETTINGS_STORAGE_TYPE = "report-settings";

    String SINGLE_KEY = ru.naumen.metainfo.server.Constants.SINGLE_KEY;

    String REPORT_DATA_STORAGE_TYPE = "report-instance-data";
}