package ru.naumen.reports.shared.dispatch;

import java.util.Collection;
import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;

/**
 * Действие удаление шаблона отчета или печатной формы.
 *
 * <AUTHOR>
 */
public class DeleteReportTemplateAction implements Action<EmptyResult>, HasActionDebugTokens
{
    private Collection<String> templateCodes;

    public DeleteReportTemplateAction(Collection<String> templateCodes)
    {
        this.templateCodes = templateCodes;
    }

    DeleteReportTemplateAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getTemplateCodes());
    }

    public Collection<String> getTemplateCodes()
    {
        return templateCodes;
    }
}
