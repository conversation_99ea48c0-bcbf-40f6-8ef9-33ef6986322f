package ru.naumen.reports.shared.dispatch;

import java.io.Serial;
import java.util.Collection;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.metainfo.shared.dispatch2.script.ScriptParameterized;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.reports.shared.ReportTemplate;

/**
 * <AUTHOR>
 * @since 09.08.2012
 */
public class GetReportTemplatesAction extends ScriptParameterized
        implements Action<SimpleScriptedResult<Collection<ReportTemplate>>>
{
    @Serial
    private static final long serialVersionUID = -5774405816295831107L;

    @CheckForNull
    private Collection<String> codes;

    /**
     * @param codes коллекция кодов шаблонов отчетов, которые необходимо получить, если null, то необходимо получить
     *              все шаблоны отчетов
     */
    public GetReportTemplatesAction(@Nullable Iterable<String> codes)
    {
        this.codes = CollectionUtils.asArrayList(codes);
    }

    protected GetReportTemplatesAction()
    {
    }

    @CheckForNull
    public Collection<String> getCodes()
    {
        return codes;
    }
}
