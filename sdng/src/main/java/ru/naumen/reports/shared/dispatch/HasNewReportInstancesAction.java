/**
 *
 */
package ru.naumen.reports.shared.dispatch;

import java.util.List;
import java.util.Set;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.core.shared.dispatch.SimpleResult;

import com.google.common.collect.Lists;

/**
 * Проверяет появились ли новые построенные отчёты. (Для списка отчётов) 
 * <AUTHOR>
 * @since Nov 7, 2014
 */
public class HasNewReportInstancesAction implements Action<SimpleResult<Boolean>>, HasActionDebugTokens
{
    private String templateCode;
    private Set<Long> oldNotReadyReports; // id-шники неготовых отчетов

    public HasNewReportInstancesAction()
    {

    }

    public HasNewReportInstancesAction(String templateCode, Set<Long> oldNotReadyReports)
    {
        this.templateCode = templateCode;
        this.oldNotReadyReports = oldNotReadyReports;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.newArrayList(getTemplateCode());
    }

    public Set<Long> getOldNotReadyReports()
    {
        return oldNotReadyReports;
    }

    public String getTemplateCode()
    {
        return templateCode;
    }
}
