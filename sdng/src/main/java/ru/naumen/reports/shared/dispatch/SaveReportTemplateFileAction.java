package ru.naumen.reports.shared.dispatch;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.reports.shared.ReportTemplate;

import com.google.common.collect.Lists;

/**
 * Действие сохранения файла шаблона отчёта.<br>
 * Используется при импорте метаинформации.<br>
 * Снача мы обновляем саму метаинформацию, а затем только загружаем файлы с шаблонами отчётов.<br>
 * Oracle требует, чтобы изменение таблиц и загрузка данных была в разных транзакциях.
 *
 * <AUTHOR>
 */
public class SaveReportTemplateFileAction implements Action<SimpleScriptedResult<ReportTemplate>>, HasActionDebugTokens
{
    private ReportTemplate reportTemplate;

    public SaveReportTemplateFileAction(ReportTemplate template)
    {
        this.reportTemplate = template;
    }

    protected SaveReportTemplateFileAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(null == getReportTemplate() ? null : getReportTemplate().getCode());
    }

    public ReportTemplate getReportTemplate()
    {
        return reportTemplate;
    }
}