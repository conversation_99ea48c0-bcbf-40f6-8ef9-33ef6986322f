package ru.naumen.reports.shared.dispatch;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.reports.shared.ReportSettings;

/**
 * <AUTHOR>
 * @since 14.11.2012
 */
public class SaveSettingsAction implements Action<SimpleResult<Void>>
{
    private ReportSettings settings;

    public SaveSettingsAction(ReportSettings settings)
    {
        this.settings = settings;
    }

    protected SaveSettingsAction()
    {
    }

    public ReportSettings getSettings()
    {
        return settings;
    }
}