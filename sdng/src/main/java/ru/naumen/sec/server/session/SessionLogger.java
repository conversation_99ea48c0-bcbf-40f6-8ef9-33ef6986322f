package ru.naumen.sec.server.session;

import jakarta.annotation.Nullable;

import org.springframework.security.core.session.SessionInformation;

/**
 * Сервис логирования событий, связанных с сессиями работы пользователей.
 * <AUTHOR>
 * @since May 07, 2019
 */
public interface SessionLogger
{
    /**
     * Логирует событие входа в приложение.
     *
     * @param principal пользователь
     * @param eventSource тип события для логирования сессии пользователя
     */
    void login(Object principal, SessionEventType eventSource);

    /**
     * Логирует событие выхода из приложения.
     *
     * @param session сессия пользователя
     * @param reason причина разлогина
     */
    void logout(SessionInformation session, @Nullable LogoutReason reason);

    /**
     * Логирует событие выхода из приложения.
     *
     * @param principal пользователь
     * @param eventSource тип события для логирования сессии пользователя
     * @param reason причина разлогина пользователя
     */
    void logout(Object principal, SessionEventType eventSource, @Nullable LogoutReason reason);
}
