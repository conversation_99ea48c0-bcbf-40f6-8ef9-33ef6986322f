package ru.naumen.sec.server.session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.session.SessionInformation;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.server.utils.HibernateUtil;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.events.Constants.Categories;
import ru.naumen.core.server.events.EventService;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.sec.server.users.employee.EmployeeUser;

/**
 * Реализация сервиса логирования событий, связанных с сессиями работы пользователей.
 * <AUTHOR>
 * @since May 07, 2019
 */
@Component
public class SessionLoggerImpl implements SessionLogger
{
    private static final Logger LOG = LoggerFactory.getLogger(SessionLoggerImpl.class);

    private final EventService eventService;
    private final MessageFacade messages;
    private final IPrefixObjectLoaderService objectLoader;

    @Inject
    public SessionLoggerImpl(EventService eventService, MessageFacade messages, IPrefixObjectLoaderService objectLoader)
    {
        this.eventService = eventService;
        this.messages = messages;
        this.objectLoader = objectLoader;
    }

    @Override
    public void login(Object principal, SessionEventType eventSource)
    {
        if (!SessionEventType.MOBILE.equals(eventSource))
        {
            authEvent(principal, null, Categories.LOGIN_SUCCESSFUL);
        }
        else
        {
            authEvent(principal, null, Categories.LOGIN_SUCCESSFUL_FROM_MOBILE);
        }
    }

    @Override
    public void logout(SessionInformation session, @Nullable LogoutReason reason)
    {
        if (session instanceof SessionInfoBase sessionInfo && sessionInfo.isExpirationLoggingAllowed())
        {
            logout(sessionInfo.getPrincipal(), sessionInfo.getEventSource(), reason);
            sessionInfo.preventExpirationLogging();
        }
    }

    @Override
    public void logout(Object principal, SessionEventType eventSource, @Nullable LogoutReason reason)
    {
        switch (eventSource)
        {
            case COMMON -> authEvent(principal, reason, Categories.LOGOUT);
            case COMMON_LINKED_WITH_MOBILE ->
                    authEvent(principal, reason, Categories.LOGOUT, Categories.LOGOUT_FROM_MOBILE);
            case MOBILE -> authEvent(principal, reason, Categories.LOGOUT_FROM_MOBILE);
            default -> LOG.warn("Unknown event source: {}", eventSource);
        }
    }

    private void authEvent(Object principal, @Nullable LogoutReason reason, String... categories)
    {
        String username = principal instanceof UserDetails details
                ? details.getUsername()
                : null;

        if (null == username)
        {
            return;
        }
        Employee employee = null;
        if (principal instanceof EmployeeUser employeeUser)
        {
            employee = loadEmployee(employeeUser);
            if (employee == null)
            {
                return;
            }
        }
        for (String category : categories)
        {
            eventService.txEvent(category, employee, null, username);
            LOG.info(messages.getMessage("eventService." + category, username));
            if (reason != null)
            {
                LOG.info("Logout reason of user {} is {}", username, reason.getCode());
            }
        }
    }

    @Nullable
    private Employee loadEmployee(EmployeeUser employeeUser)
    {
        String employeeUuid = employeeUser.getUUID();
        if (employeeUuid == null)
        {
            return null;
        }
        // Логирование не должно влиять на текущую транзакцию.
        return TransactionRunner.call(TransactionType.NEW_READ_ONLY, () ->
        {
            Employee employee = objectLoader.getSafe(employeeUuid);
            return null == employee ? null : HibernateUtil.unproxy(employee);
        });
    }
}
