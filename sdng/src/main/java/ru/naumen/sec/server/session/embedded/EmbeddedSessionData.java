package ru.naumen.sec.server.session.embedded;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.google.common.collect.ImmutableSet;

/**
 * Непосредственно данные сессии:
 * <ul>
 * <li>Атрибуты.</li>
 * <li>Временные характеристики.</li>
 * <li>Идентификатор.</li>
 * </ul>
 * Передаются между узлами кластера
 * <AUTHOR>
 * @since 24.12.2019
 *
 * @see EmbeddedSessionRepository
 * @see EmbeddedSession
 */
class EmbeddedSessionData implements Serializable
{
    private static final long serialVersionUID = 4512963818682088930L;
    private String sessionId;
    private long createdAt = System.currentTimeMillis();
    private long lastAccessedTime = this.createdAt;
    private int maxInactiveInterval;

    public EmbeddedSessionData()
    {
    }

    /**
     * Время последнего изменения данных сессий.
     * Меняется при добавлении/удалении атрибутов и при изменении интервала бездействия
     * На основе этой метки в кластере выбирается более свежая версия данных для обновления
     */
    private long lastModified = createdAt;

    private final Map<String, Object> attributes = new HashMap<>();

    public EmbeddedSessionData(EmbeddedSessionData other)
    {
        this.lastModified = other.getLastModified();
        this.sessionId = other.getSessionId();
        this.maxInactiveInterval = other.getMaxInactiveInterval();
        this.createdAt = other.getCreatedAt();
        this.lastAccessedTime = other.getLastAccessedTime();

        for (String attributeName : other.getAttributeNames())
        {
            this.attributes.put(attributeName, other.getAttribute(attributeName));
        }
    }

    public Object getAttribute(String attribute)
    {
        return attributes.get(attribute);
    }

    public Set<String> getAttributeNames()
    {
        return ImmutableSet.copyOf(attributes.keySet());
    }

    public long getCreatedAt()
    {
        return createdAt;
    }

    public long getLastAccessedTime()
    {
        return lastAccessedTime;
    }

    public long getLastModified()
    {
        return lastModified;
    }

    public int getMaxInactiveInterval()
    {
        return maxInactiveInterval;
    }

    public String getSessionId()
    {
        return sessionId;
    }

    public void setLastModified(long lastModified)
    {
        this.lastModified = lastModified;
    }

    void removeAttribute(String attribute)
    {
        attributes.remove(attribute);
    }

    void setAttribute(String attribute, Object value)
    {
        this.attributes.put(attribute, value);
    }

    void setLastAccessedTime(long lastAccessedTime)
    {
        this.lastAccessedTime = lastAccessedTime;
    }

    void setMaxInactiveInterval(int maxInactiveInterval)
    {
        this.maxInactiveInterval = maxInactiveInterval;
    }

    void setSessionId(String sessionId)
    {
        this.sessionId = sessionId;
    }
}
