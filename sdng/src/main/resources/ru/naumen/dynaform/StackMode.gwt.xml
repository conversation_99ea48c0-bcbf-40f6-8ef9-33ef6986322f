<?xml version="1.0" encoding="UTF-8"?>
<module>
	<!--Эмуляция стека выполнения 'emulated'/'strip' переключается через 
        методы EmployeeApi для каждого пользователя в отдельности, 
        Значение для этого параметра генерируется мавен-реплейс-плагином
        в зависимости от выбранного профиля: stripped-stack или emulated-stack.
        По умолчанию включен профиль stripped-stack (value = "strip"),
        чтобы изменить активный профиль на emulated-stack (value = "strip,emulated"),
        нужно при сборке добавить ключ -DemulateStack -->
	<set-property name="compiler.stackMode" value="strip"/>
</module>