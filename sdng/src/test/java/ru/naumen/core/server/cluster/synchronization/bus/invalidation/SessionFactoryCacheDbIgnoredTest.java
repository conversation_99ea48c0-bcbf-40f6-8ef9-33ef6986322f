package ru.naumen.core.server.cluster.synchronization.bus.invalidation;

import jakarta.inject.Inject;

import org.hibernate.internal.SessionFactoryImpl;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import ru.naumen.common.CreatedListener;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.cluster.external.NodeRole;
import ru.naumen.core.server.cluster.synchronization.bus.invalidation.events.ClusterStateEvent;
import ru.naumen.core.server.cluster.synchronization.bus.invalidation.events.ClusterStateEvent.ClusterState;
import ru.naumen.core.server.cluster.synchronization.bus.invalidation.events.ClusterStateEvent.ClusterMembership;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.flex.spi.ReloadableSessionFactoryBean;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.ClassFqn;

@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
@Rollback
@Transactional("txManager")
public class SessionFactoryCacheDbIgnoredTest
{
    @Inject
    private ApplicationEventPublisher eventPublisher;

    @Inject
    private ReloadableSessionFactoryBean sessionFactoryBean;

    @Inject
    private ObjectTestUtils objectTestUtils;

    @Inject
    private SecurityTestUtils securityTestUtils;

    @Inject
    private CreatedListener createdListener;

    @Before
    public void setUp()
    {
        createdListener.setUp();
    }

    @After
    public void tearDown()
    {
        createdListener.tearDown();
    }

    /**
     * Тестирование того, что приложение выключит L2 cache hibernate при получении события выхода из кластера
     * ноды-инвалидатора
     * @throws Exception
     */
    public void testSecondLevelCacheDroppedOnInvalidatorNodeLeftEvent() throws Exception
    {
        securityTestUtils.autenticateAsSuperUser();
        ClassFqn ouCase = TransactionRunner.call(TransactionRunner.TransactionType.NEW,
                () -> objectTestUtils.createCase(Constants.OU.FQN));
        OU ou = TransactionRunner.call(TransactionRunner.TransactionType.NEW, () -> objectTestUtils.createOU(ouCase));
        objectTestUtils.get(ou.getUUID());

        SessionFactoryImpl currentSessionFactory = sessionFactoryBean.getCurrentSessionFactory();
        boolean ouCached = currentSessionFactory.getCache().containsEntity(ou.getClass(), ou.getId());
        Assert.assertTrue("Entity : " + ou.getUUID() + "is not cached!", ouCached);
        eventPublisher.publishEvent(ClusterStateEvent.builder().withRole(NodeRole.FRONTEND)
                .withClusterState(ClusterState.PARTIAL)
                .withClusterMembership(ClusterMembership.IN_CLUSTER)
                .withPreviousClusterMembership(ClusterMembership.NOT_IN_CLUSTER)
                .build());
        boolean containsEntity = currentSessionFactory.getCache().containsEntity(ou.getClass(), ou.getId());
        Assert.assertFalse("Entity : " + ou.getUUID() + "is cached!", containsEntity);

        TransactionRunner.run(TransactionRunner.TransactionType.NEW, () -> objectTestUtils.get(ou.getUUID()));
        boolean containsEntityAfterTransaction = currentSessionFactory.getCache()
                .containsEntity(ou.getClass(), ou.getId());
        Assert.assertFalse("Entity : " + ou.getUUID() + "is cached!", containsEntityAfterTransaction);
    }

    /**
     * Тестирование того, что приложение включит L2 cache hibernate при получении события входа в кластер
     * ноды-инвалидатора
     * @throws Exception
     */
    public void testSecondLevelCacheEnabledOnInvalidatorNodeJoinedEvent() throws Exception
    {
        securityTestUtils.autenticateAsSuperUser();
        ClassFqn ouCase = TransactionRunner.call(TransactionRunner.TransactionType.NEW,
                () -> objectTestUtils.createCase(Constants.OU.FQN));
        OU ou = TransactionRunner.call(TransactionRunner.TransactionType.NEW, () -> objectTestUtils.createOU(ouCase));
        eventPublisher.publishEvent(ClusterStateEvent.builder().withRole(NodeRole.FRONTEND)
                .withClusterState(ClusterState.PARTIAL)
                .withClusterMembership(ClusterMembership.IN_CLUSTER)
                .withPreviousClusterMembership(ClusterMembership.NOT_IN_CLUSTER)
                .build());
        objectTestUtils.get(ou.getUUID());

        SessionFactoryImpl currentSessionFactory = sessionFactoryBean.getCurrentSessionFactory();
        boolean ouCached = currentSessionFactory.getCache().containsEntity(ou.getClass(), ou.getId());
        Assert.assertFalse("Entity : " + ou.getUUID() + "is cached!", ouCached);

        eventPublisher.publishEvent(ClusterStateEvent.builder().withRole(NodeRole.FRONTEND)
                .withClusterState(ClusterState.COMPLETE)
                .withClusterMembership(ClusterMembership.IN_CLUSTER)
                .withPreviousClusterMembership(ClusterMembership.NOT_IN_CLUSTER)
                .build());
        TransactionRunner.run(TransactionRunner.TransactionType.NEW, () -> objectTestUtils.get(ou.getUUID()));
        boolean containsEntityAfterTransaction = currentSessionFactory.getCache()
                .containsEntity(ou.getClass(), ou.getId());
        Assert.assertTrue("Entity : " + ou.getUUID() + "is not cached!", containsEntityAfterTransaction);
    }

}
