package ru.naumen.core.server.cluster.synchronization.bus.invalidation.events;

import static org.mockito.Mockito.*;
import static ru.naumen.core.server.cluster.external.NodeRole.BACKEND;
import static ru.naumen.core.server.cluster.external.NodeRole.FRONTEND;
import static ru.naumen.core.server.cluster.external.NodeRole.UNIVERSAL;
import static ru.naumen.core.server.cluster.synchronization.bus.invalidation.events.ClusterStateEvent.ClusterMembership.IN_CLUSTER;
import static ru.naumen.core.server.cluster.synchronization.bus.invalidation.events.ClusterStateEvent.ClusterMembership.NOT_IN_CLUSTER;
import static ru.naumen.core.server.cluster.synchronization.bus.invalidation.events.ClusterStateEvent.ClusterState.COMPLETE;
import static ru.naumen.core.server.cluster.synchronization.bus.invalidation.events.ClusterStateEvent.ClusterState.PARTIAL;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.core.server.background.BackgroundManager;
import ru.naumen.core.server.cluster.session.ClusterSessionLoader;
import ru.naumen.core.server.cluster.external.NodeRole;
import ru.naumen.core.server.cluster.synchronization.bus.invalidation.HibernateCacheInvalidator;
import ru.naumen.core.server.cluster.synchronization.bus.invalidation.events.ClusterStateEvent.ClusterMembership;
import ru.naumen.core.server.cluster.synchronization.bus.invalidation.events.ClusterStateEvent.ClusterState;
import ru.naumen.core.server.flex.spi.ReloadableSessionFactory;

@RunWith(MockitoJUnitRunner.class)
public class ClusterStateEventListenerJdkTest
{
    @Mock
    private ReloadableSessionFactory reloadableSessionFactoryBean;
    @Mock
    private BackgroundManager backgroundManager;
    @Mock
    private HibernateCacheInvalidator hibernateCacheInvalidator;
    @Mock
    private ClusterSessionLoader clusterSessionLoader;
    private ClusterStateEventListener newClusterStateEventListener;
    private ClusterRetrieveSessionsEventListener clusterRetrieveSessionsEventListener;

    private AutoCloseable closeable;

    @Before
    public void setUp()
    {
        closeable = MockitoAnnotations.openMocks(this);
        clusterRetrieveSessionsEventListener = new ClusterRetrieveSessionsEventListener(clusterSessionLoader);
        newClusterStateEventListener = new ClusterStateEventListener(
                hibernateCacheInvalidator, reloadableSessionFactoryBean, backgroundManager);
    }

    @After
    public void releaseMocks() throws Exception
    {
        closeable.close();
    }

    @Test
    public void testFetchClusterSessionsIfMain()
    {
        clusterRetrieveSessionsEventListener.onApplicationEvent(getClusterStateEvent(FRONTEND, COMPLETE));
        verify(clusterSessionLoader, only()).loadSessionsFromCluster();
    }

    @Test
    public void testFetchClusterSessionsIfNotMainOrUnversal()
    {
        newClusterStateEventListener.onApplicationEvent(getClusterStateEvent(BACKEND, COMPLETE));
        verify(clusterSessionLoader, never()).loadSessionsFromCluster();
    }

    @Test
    public void testFetchClusterSessionsIfUnversal()
    {
        clusterRetrieveSessionsEventListener.onApplicationEvent(getClusterStateEvent(UNIVERSAL, COMPLETE));
        verify(clusterSessionLoader, only()).loadSessionsFromCluster();
    }

    @Test
    public void testL2CacheDisabledOnPartialState()
    {
        newClusterStateEventListener.onApplicationEvent(getClusterStateEvent(BACKEND, PARTIAL));
        verify(reloadableSessionFactoryBean).setL2CacheEnabled(false);
    }

    private static ClusterChangeStateEvent getClusterStateEvent(NodeRole nodeRole, ClusterState clusterState)
    {
        return getClusterStateEvent(nodeRole, clusterState, NOT_IN_CLUSTER);
    }

    private static ClusterChangeStateEvent getClusterStateEvent(NodeRole nodeRole, ClusterState clusterState,
            ClusterMembership previousClusterMembership)
    {
        return (ClusterChangeStateEvent)ClusterStateEvent.builder().withRole(nodeRole)
                .withClusterState(clusterState)
                .withClusterMembership(ClusterMembership.IN_CLUSTER)
                .withPreviousClusterMembership(previousClusterMembership)
                .build();
    }

    @Test
    public void testL2CacheDroppedOnNewEvent()
    {
        newClusterStateEventListener.onApplicationEvent(getClusterStateEvent(BACKEND, PARTIAL));
        verify(hibernateCacheInvalidator).invalidateAll();
    }

    @Test
    public void testL2CacheEnabledOnCompleteState()
    {
        newClusterStateEventListener.onApplicationEvent(getClusterStateEvent(BACKEND, COMPLETE));
        verify(reloadableSessionFactoryBean).setL2CacheEnabled(true);
    }

    @Test
    public void testNoSessionFetchOnQuorumedBackendNode()
    {
        newClusterStateEventListener.onApplicationEvent(getClusterStateEvent(BACKEND, COMPLETE, IN_CLUSTER));
        verify(clusterSessionLoader, never()).loadSessionsFromCluster();
    }

    @Test
    public void testNoSessionFetchOnQuorumedFrontendNode()
    {
        newClusterStateEventListener.onApplicationEvent(getClusterStateEvent(FRONTEND, COMPLETE, IN_CLUSTER));
        verify(clusterSessionLoader, never()).loadSessionsFromCluster();
    }

    @Test
    public void testNoSessionFetchOnQuorumedUniversalNode()
    {
        newClusterStateEventListener.onApplicationEvent(getClusterStateEvent(UNIVERSAL, COMPLETE, IN_CLUSTER));
        verify(clusterSessionLoader, never()).loadSessionsFromCluster();
    }

    @Test
    public void testStartBackGroundServices()
    {
        newClusterStateEventListener.onApplicationEvent(getClusterStateEvent(BACKEND, COMPLETE));
        verify(backgroundManager).start();
    }

    @Test
    public void testStopBackGroundServices()
    {
        newClusterStateEventListener.onApplicationEvent(getClusterStateEvent(BACKEND, PARTIAL));
        verify(backgroundManager).stop(any(), anyString());
    }
}