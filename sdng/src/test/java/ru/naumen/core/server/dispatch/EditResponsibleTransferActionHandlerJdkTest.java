package ru.naumen.core.server.dispatch;

import static org.junit.Assert.assertEquals;

import java.util.Arrays;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.metainfo.server.spi.dispatch.EditResponsibleTransferActionHandler;
import ru.naumen.metainfo.shared.elements.ResponsibilityTransferItem;

/**
 * Тесты на работу {@link EditResponsibleTransferActionHandler}
 *
 * <AUTHOR>
 * @since 15.02.2021
 */
@RunWith(MockitoJUnitRunner.class)
public class EditResponsibleTransferActionHandlerJdkTest
{
    /**
     * Тест проверяет, что при вызове isMatrixChanged() в {@link EditResponsibleTransferActionHandler}  при разном
     * наборе
     * входных данных происходит корректное сравнение объектов и возвращается правильный результат сравнения.
     */
    @Test
    public void testIsMatrixChanged()
    {
        ResponsibilityTransferItem item1 = new ResponsibilityTransferItem("1", "2", true, true);
        ResponsibilityTransferItem item2 = new ResponsibilityTransferItem("1", "3", true, true);
        ResponsibilityTransferItem item3 = new ResponsibilityTransferItem("3", "2", false, false);
        ResponsibilityTransferItem item4 = new ResponsibilityTransferItem("4", "2", true, true);
        ResponsibilityTransferItem item5 = new ResponsibilityTransferItem("1", "2", true, true);
        ResponsibilityTransferItem item6 = new ResponsibilityTransferItem("1", "3", false, true);
        ResponsibilityTransferItem item7 = new ResponsibilityTransferItem("3", "2", true, false);
        ResponsibilityTransferItem item8 = new ResponsibilityTransferItem("4", "2", false, true);

        List<ResponsibilityTransferItem> dbMatrix = Arrays.asList(item1, item2);
        List<ResponsibilityTransferItem> clientMatrix = Arrays.asList(item1, item2);
        assertEquals(false, EditResponsibleTransferActionHandler.isMatrixChanged(dbMatrix, clientMatrix));

        dbMatrix = Arrays.asList(item1, item2);
        clientMatrix = Arrays.asList(item3, item4);
        assertEquals(true, EditResponsibleTransferActionHandler.isMatrixChanged(dbMatrix, clientMatrix));

        dbMatrix = Arrays.asList(item5, item6);
        clientMatrix = Arrays.asList(item7, item8);
        assertEquals(true, EditResponsibleTransferActionHandler.isMatrixChanged(dbMatrix, clientMatrix));

        dbMatrix = Arrays.asList(item3, item1);
        clientMatrix = Arrays.asList(item7, item5);
        assertEquals(true, EditResponsibleTransferActionHandler.isMatrixChanged(dbMatrix, clientMatrix));
    }
}
