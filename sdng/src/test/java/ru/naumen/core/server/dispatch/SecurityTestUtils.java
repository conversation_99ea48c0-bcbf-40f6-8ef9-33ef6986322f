package ru.naumen.core.server.dispatch;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.core.shared.SecConstants.MarkerGroups;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.core.server.bo.GroupMembersDao;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.license.LicensingServiceImpl;
import ru.naumen.core.server.license.conf.License;
import ru.naumen.core.server.license.conf.Modules;
import ru.naumen.core.server.license.conf.NamedLicenseGroup;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.SecurityService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.AddChangeStateMarkerAction;
import ru.naumen.metainfo.shared.dispatch2.AddSecurityGroupAction;
import ru.naumen.metainfo.shared.dispatch2.AddSecurityProfileAction;
import ru.naumen.metainfo.shared.dispatch2.AddSecurityRoleAction;
import ru.naumen.metainfo.shared.dispatch2.DelSecurityProfileAction;
import ru.naumen.metainfo.shared.dispatch2.DelSecurityRoleAction;
import ru.naumen.metainfo.shared.dispatch2.EditSecurityGroupMembersAction;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityDomainAction;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityGroupResponse;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityProfileResponse;
import ru.naumen.metainfo.shared.dispatch2.UpdateAccessMatrixAction;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix.Key;
import ru.naumen.metainfo.shared.elements.sec.Group;
import ru.naumen.metainfo.shared.elements.sec.Marker;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.elements.sec.Role.Type;
import ru.naumen.metainfo.shared.elements.sec.SecDomain;
import ru.naumen.metainfo.shared.elements.wf.TransitionLite;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.sec.server.users.employee.EmployeeUser;

/**
 *
 * <AUTHOR>
 *
 */
@Component
public class SecurityTestUtils
{
    public static final String LICENSE_CODE = "named";

    private static final Logger LOG = LoggerFactory.getLogger(SecurityTestUtils.class);

    private final ObjectTestUtils objectTestUtils;
    private final Dispatch dispatch;
    private final SecurityService securityService;
    private final MetainfoService metainfoService;
    private final CurrentEmployeeContext currentEmployeeContext;
    private final LicensingServiceImpl licensingService;
    private final AuthorizationRunnerService authorizeRunner;
    private final GroupMembersDao grpMembersDao;

    @Inject
    public SecurityTestUtils(ObjectTestUtils objectTestUtils, Dispatch dispatch,
            SecurityService securityService, MetainfoService metainfoService,
            CurrentEmployeeContext currentEmployeeContext, LicensingServiceImpl licensingService,
            AuthorizationRunnerService authorizeRunner, GroupMembersDao grpMembersDao)
    {
        this.objectTestUtils = objectTestUtils;
        this.dispatch = dispatch;
        this.securityService = securityService;
        this.metainfoService = metainfoService;
        this.currentEmployeeContext = currentEmployeeContext;
        this.licensingService = licensingService;
        this.authorizeRunner = authorizeRunner;
        this.grpMembersDao = grpMembersDao;
    }

    /**
     * Добавляет маркер прав для смены статуса
     * @param domain - домен относительно которого нужно добавить маркер
     * @param transitions - переходы маркера
     * @return созданный маркер
     * @throws DispatchException исключение
     */
    public Marker addChangeStateMarker(ClassFqn domain, List<TransitionLite> transitions) throws DispatchException
    {
        final AddChangeStateMarkerAction action = new AddChangeStateMarkerAction(domain, UUIDGenerator.get().nextUUID(),
                transitions, MarkerGroups.EDIT_STATE, null, null);
        return dispatch.execute(action).get();
    }

    public Group addSecurityGroup(String title) throws DispatchException
    {
        String code = StringUtilities.transliterate(title);
        final AddSecurityGroupAction action = new AddSecurityGroupAction(code, title);
        return dispatch.execute(action).get().getUserGroup();
    }

    public Group addSecurityGroup(String code, String title) throws DispatchException
    {
        final AddSecurityGroupAction action = new AddSecurityGroupAction(code, title);
        return dispatch.execute(action).get().getUserGroup();
    }

    public Role addSecurityRole() throws Exception
    {
        return addSecurityRole(objectTestUtils.createEmployee());
    }

    public Role addSecurityRole(Employee assignedEmployee) throws Exception
    {
        String title = UUIDGenerator.get().nextUUID();
        String code = UUIDGenerator.get().nextUUID();
        MapProperties properties = new MapProperties();
        properties.put(ru.naumen.metainfo.shared.Constants.Role.ASSIGNED_KEY, assignedEmployee.getUUID());
        AddSecurityRoleAction action = new AddSecurityRoleAction(title, code, null, Type.ASSIGNED, properties);
        return dispatch.execute(action).get().get();
    }

    public void deleteSecurityRole(Collection<String> codes) throws DispatchException
    {
        dispatch.execute(new DelSecurityRoleAction(codes));
    }

    public void autenticateAsSuperUser()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    public Employee autenticateAsUser() throws Exception
    {
        // требуется для возможности создать пользователя
        autenticateAsSuperUser();
        Employee employee = objectTestUtils.createEmployee();

        return authenticateAsUser(employee);
    }

    public Employee authenticateAsUser(Employee employee) throws DispatchException
    {
        LOG.debug("Autenticate as " + employee);

        SecurityContext securityContext = SecurityContextHolder.getContext();
        securityContext.setAuthentication(
                new TestingAuthenticationToken(new EmployeeUser(employee), "", "ROLE_ADMIN",
                        "ROLE_ADMIN_LITE", "ROLE_OPERATOR"));
        SecurityContextHolder.setContext(securityContext);

        grantAllAccess(Constants.AbstractSystemObject.FQN);

        return employee;
    }

    public <T> T callAsSuperUser(Callable<T> callable)
    {
        return authorizeRunner.callAs(SecurityTestHelper.SUPERUSER_TEST_AUTHENTICATION, callable);
    }

    public Profile createSecurityProfile(String code, String title, boolean isLicensed, List<String> roles,
            List<String> groupCodes, @Nullable String copyFrom, @Nullable ClassFqn fqn) throws DispatchException
    {
        GetSecurityProfileResponse profile = dispatch
                .execute(
                        new AddSecurityProfileAction(code, title, isLicensed, roles, groupCodes, copyFrom, fqn, false));
        return profile.get();
    }

    public void deleteSecurityProfile(String code) throws Exception
    {
        dispatch.execute(new DelSecurityProfileAction(code));
    }

    private Group getEmployeeGroup(Employee employee) throws DispatchException
    {
        for (Group grp : securityService.getGroups())
        {
            if (grpMembersDao.isEmployeeInGroup(grp.getCode(), employee.getId()))
            {
                return grp;
            }
        }

        String title = UUIDGenerator.get().nextUUID();
        String code = StringUtilities.transliterate(title);

        GetSecurityGroupResponse grp = dispatch.execute(new AddSecurityGroupAction(code, title));
        dispatch.execute(new EditSecurityGroupMembersAction(grp.get().getUserGroup().getCode(),
                Sets.newHashSet(employee.getUUID()),
                new HashSet<>()));
        return grp.get().getUserGroup();
    }

    public Profile getEmployeeProfile(Employee employee, final String role) throws DispatchException
    {
        return getEmployeeProfile(employee, role,
                !employee.getLicense().contains(Constants.Employee.NOT_LICENSED_USER));
    }

    private Profile getEmployeeProfile(Employee employee, final String role, boolean licensed) throws DispatchException
    {
        Group grp = getEmployeeGroup(employee);

        for (Profile profile : securityService.getProfiles())
        {
            if (profile.getRoles().stream().noneMatch(input -> role.equals(input.getCode())))
            {
                continue;
            }
            if (profile.getGroups().contains(grp) && (profile.isForLicensedUsers() == licensed))
            {
                return profile;
            }
        }

        String title = UUIDGenerator.get().nextUUID();
        return createSecurityProfile(title, title, licensed, Lists.newArrayList(role),
                Lists.newArrayList(grp.getCode()), null, null);
    }

    public Group getOuGroup(OU ou) throws DispatchException
    {
        final Collection<String> ouGroups = grpMembersDao.getOUGroups(ou.getUUID());
        if (ouGroups.isEmpty())
        {
            String title = UUIDGenerator.get().nextUUID();
            String code = StringUtilities.transliterate(title);

            GetSecurityGroupResponse grp = dispatch.execute(new AddSecurityGroupAction(code, title));
            dispatch.execute(new EditSecurityGroupMembersAction(grp.get().getUserGroup().getCode(),
                    Sets.newHashSet(ou.getUUID()), new HashSet<>()));
            return grp.get().getUserGroup();
        }
        final String first = ouGroups.iterator().next();
        return securityService.getGroup(first);
    }

    /**
     * Получить профиль для опредленного отдела и заданной роли
     *
     * @param ou нужный отдел
     * @param role нужная роль
     * @return профиль для отдела и роли
     * @throws DispatchException исключение
     */
    public Profile getOuProfile(OU ou, final String role) throws DispatchException
    {
        return getOuProfile(ou, role, true);
    }

    /**
     * Получить профиль для опредленного отдела и заданной роли
     *
     * @param ou нужный отдел
     * @param role нужная роль
     * @param licensed только для лицензированных профилей (true) или только не для лицензированных (false)
     * @return профиль для отдела и роли
     * @throws DispatchException исключение
     */
    private Profile getOuProfile(OU ou, final String role, boolean licensed) throws DispatchException
    {
        Group grp = getOuGroup(ou);
        for (Profile profile : securityService.getProfiles())
        {
            if (profile.getRoles().stream().noneMatch(input -> role.equals(input.getCode())))
            {
                continue;
            }
            if (profile.getGroups().contains(grp) && (profile.isForLicensedUsers() == licensed))
            {
                return profile;
            }
        }

        String title = UUIDGenerator.get().nextUUID();
        return createSecurityProfile(title, title, licensed, Lists.newArrayList(role),
                Lists.newArrayList(grp.getCode()), null, null);
    }

    public void grantAllAccess() throws Exception
    {
        Employee employee = currentEmployeeContext.getCurrentEmployee();
        Profile profile = getEmployeeProfile(employee, "employee");

        Collection<MetaClass> classes = Collections2.filter(metainfoService.getMetaClasses(),
                MetaClassFilters.isClass());
        for (MetaClass metaClass : classes)
        {
            grantAllAccess(profile, metaClass.getFqn());
        }
    }

    public void grantAllAccess(Employee currentEmployee, ClassFqn... fqns) throws DispatchException
    {
        Profile employee = getEmployeeProfile(currentEmployee, "employee");
        for (ClassFqn fqn : fqns)
        {
            grantAllAccess(employee, fqn);
        }
    }

    public void grantAllAccess(Profile profile, ClassFqn fqn) throws DispatchException
    {
        SecDomain domain = securityService.getDomain(fqn);
        Map<Key, Boolean> values = domain.getAccessMatrix().getData();
        Map<Key, String> scripts = domain.getAccessMatrix().getScripts();
        HashMap<Key, ScriptDto> scriptDtos = objectTestUtils.convertToMapScriptDtos(scripts);

        for (Marker marker : domain.getMarkers())
        {
            values.put(new Key(profile.getCode(), marker.getCode()), Boolean.TRUE);
        }
        dispatch.execute(new UpdateAccessMatrixAction(fqn, values, scriptDtos, new HashSet<>()));
    }

    public void initLicensing(String... modules)
    {
        NamedLicenseGroup named = new NamedLicenseGroup();
        named.setCount(Integer.MAX_VALUE);
        named.setId(LICENSE_CODE);
        named.setTitle("named");

        License license = new License();
        license.getGroups().add(named);
        if (ArrayUtils.isNotEmpty(modules))
        {
            Modules licModules = new Modules();
            licModules.setValue(String.join(",", modules));
            license.getModules().add(licModules);
        }

        licensingService.setLicense(license);
    }

    public void runAsSuperUser(Runnable runnable)
    {
        authorizeRunner.runAs(SecurityTestHelper.SUPERUSER_TEST_AUTHENTICATION, runnable);
    }

    public void setPermission(ClassFqn domain, String permission, boolean value) throws Exception
    {
        setPermission(domain, permission, value, "employee");
    }

    public void setPermission(ClassFqn domain, String permission, boolean value, Profile profile)
            throws DispatchException
    {
        SecDomain secDomain = dispatch.execute(new GetSecurityDomainAction(domain)).get();
        Map<Key, Boolean> values = secDomain.getAccessMatrix().getData();
        Map<Key, String> scripts = secDomain.getAccessMatrix().getScripts();
        HashMap<Key, ScriptDto> scriptDtos = objectTestUtils.convertToMapScriptDtos(scripts);

        values.put(new Key(profile.getCode(), permission), value);

        dispatch.execute(new UpdateAccessMatrixAction(domain, values, scriptDtos, new HashSet<>()));
    }

    /**
     * Устанавливает права для текущего пользователя
     *
     * @param domain домен в котором изменяются права
     * @param permission - marker
     * @param value устанавливаемое значение: true - разрешено, false - запрещено
     */
    public void setPermission(ClassFqn domain, String permission, boolean value, String role) throws Exception
    {
        Employee employee = currentEmployeeContext.getCurrentEmployee();
        setPermission(domain, permission, value, role, employee);
    }

    public void setPermission(ClassFqn domain, String permission, boolean value, String role, Employee employee)
            throws DispatchException
    {
        Profile profile = getEmployeeProfile(employee, role);
        setPermission(domain, permission, value, profile);
    }

    protected void grantAllAccess(ClassFqn fqn) throws DispatchException
    {
        Employee currentEmployee = currentEmployeeContext.getCurrentEmployee();
        grantAllAccess(currentEmployee, fqn);
    }
}
