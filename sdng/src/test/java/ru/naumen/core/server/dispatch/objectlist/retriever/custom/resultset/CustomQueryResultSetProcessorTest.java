package ru.naumen.core.server.dispatch.objectlist.retriever.custom.resultset;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Set;

import org.junit.Rule;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.dispatch.objectlist.retriever.custom.resultset.attributes.ResultSetAttributesExtractorProvider;
import ru.naumen.core.server.util.MessageFacade;

public class CustomQueryResultSetProcessorTest
{
    @Rule
    public MockitoRule mockitoRule = MockitoJUnit.rule();

    @Mock
    public ResultSet resultSet;
    @Mock
    private ResultSetAttributesExtractorProvider attributesExtractorProvider;
    @Mock
    private MessageFacade messages;

    /**
     * При наличии объекта неподходящего типа в результате, выбрасывается исключение.
     */
    @Test(expected = FxException.class)
    public void testShouldThrowExceptionOnWrongCaseResultRowObject() throws SQLException
    {
        when(messages.getLocalizedMessages(anyString())).thenReturn(ImmutableMap.of("asd", "asd"));

        //Список подходящих типов объектов
        final Set<String> caseIds = ImmutableSet.of("anotherCaseId", "oneMoreCaseId");

        @SuppressWarnings("ConstantConditions")
        final CustomQueryResultSetProcessor customQueryResultSetProcessor = new CustomQueryResultSetProcessor(
                messages,
                attributesExtractorProvider,
                null,
                null,
                null,
                20,
                "class",
                caseIds);

        when(resultSet.isBeforeFirst()).thenReturn(true);
        when(resultSet.next()).thenReturn(true);

        //Для строки результата запроса возвращаем неподходящий тип объекта.
        when(resultSet.getString(eq("case_id"))).thenReturn("caseid");

        //Выполнение проверяемого действия
        customQueryResultSetProcessor.extractData(resultSet);
    }
}