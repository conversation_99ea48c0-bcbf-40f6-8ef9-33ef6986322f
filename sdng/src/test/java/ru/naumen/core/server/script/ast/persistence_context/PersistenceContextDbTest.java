package ru.naumen.core.server.script.ast.persistence_context;

import static org.junit.Assert.assertTrue;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;

import jakarta.inject.Inject;

import org.junit.After;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.script.ScriptActionExecutor;
import ru.naumen.core.server.script.ScriptExecutionParameters;
import ru.naumen.core.server.script.ScriptServiceException;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.eventaction.DeleteEventAction;
import ru.naumen.metainfo.shared.dispatch2.eventaction.SaveEventAction;
import ru.naumen.metainfo.shared.dispatch2.script.AddScriptModuleAction;
import ru.naumen.metainfo.shared.dispatch2.script.DeleteScriptModuleAction;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventAction.TxType;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.PlannedEventRule;
import ru.naumen.metainfo.shared.eventaction.ScriptEventAction;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfoadmin.shared.Constants;

/**
 * Тестирование работы AST трансформации {@link PersistenceContextASTTransformation}
 *
 * <AUTHOR>
 * @since Dec 05, 2019
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class PersistenceContextDbTest
{
    private static final String ERROR_MESSAGE_PREFIX = "PersistenceContext limit has been exceeded";

    private static final String EVENT_ACTION_CODE = "eventActionCode";

    private static final String PATH_PREFIX = "ru/naumen/core/server/script/ast.persistence_context/";

    private static final String TEST_SCRIPT_PATH = PATH_PREFIX + "testScript.txt";
    private static final String TEST_SCRIPT_CODE = "testScript";
    private static String testScript;

    private static final String TEST_MODULE_PATH = PATH_PREFIX + "testModule.txt";
    private static final String TEST_MODULE_CODE = "testModule";
    private static String testModuleScript;

    private static final String TRAITS_SCRIPT_PATH = PATH_PREFIX + "traits.txt";
    private static String traitsScript;

    private static final String INTERFACES_SCRIPT_PATH = PATH_PREFIX + "interfaces.txt";
    private static String interfacesScript;

    private ClassFqn userClass;
    private ClassFqn userCase;

    private ClassFqn fakeClass;
    private ClassFqn fakeCase;

    @Inject
    private ObjectTestUtils objectTestUtils;
    @Inject
    private ConfigurationProperties configurationProperties;
    @Inject
    private SecurityTestUtils securityTestUtils;
    @Inject
    private Dispatch dispatch;
    @Inject
    private ScriptActionExecutor scriptActionExecutor;

    @BeforeClass
    public static void beforeClass() throws IOException, URISyntaxException
    {
        final ClassLoader classLoader = PersistenceContextDbTest.class.getClassLoader();

        final Path pathToModule = Paths.get(classLoader.getResource(TEST_MODULE_PATH).toURI());
        testModuleScript = new String(Files.readAllBytes(pathToModule));

        final Path pathToScript = Paths.get(classLoader.getResource(TEST_SCRIPT_PATH).toURI());
        testScript = new String(Files.readAllBytes(pathToScript));

        final Path pathToTraits = Paths.get(classLoader.getResource(TRAITS_SCRIPT_PATH).toURI());
        traitsScript = new String(Files.readAllBytes(pathToTraits));

        final Path pathToInterfaces = Paths.get(classLoader.getResource(INTERFACES_SCRIPT_PATH).toURI());
        interfacesScript = new String(Files.readAllBytes(pathToInterfaces));
    }

    @Before
    public void before() throws Exception
    {
        securityTestUtils.autenticateAsSuperUser();

        userClass = objectTestUtils.createMetaClass(false, false);
        userCase = objectTestUtils.createCase(userClass);

        fakeClass = objectTestUtils.createMetaClass(false, false);
        fakeCase = objectTestUtils.createCase(fakeClass);

        addTestModule(testModuleScript);
    }

    @After
    public void after() throws DispatchException
    {
        objectTestUtils.deleteObjects(userClass);
        objectTestUtils.deleteMetaClass(userClass);

        objectTestUtils.deleteObjects(fakeClass);
        objectTestUtils.deleteMetaClass(fakeClass);

        dispatch.execute(new DeleteScriptModuleAction(Collections.singletonList(TEST_MODULE_CODE)));
    }

    /**
     * Выполнить в консоли скрипт, который во время выполнения превысит ограничение на кол-во поднятых объектов
     * Проверить, что выполнение скрипта было прервано с соответстующей ошибкой
     */
    @Test
    public void testScriptFromConsoleInterrupted() throws Exception
    {
        configurationProperties.setPersistenceContextLimit(10);

        try
        {
            scriptActionExecutor.execute(new ScriptExecutionParameters(
                    String.format(testScript, 100, userCase.asString())));
        }
        catch (final ScriptServiceException e)
        {
            assertTrue(e.getMessage().contains(ERROR_MESSAGE_PREFIX));
            return;
        }

        throw new Exception("RuntimeException was expected");
    }

    /**
     * Выполнить в консоли скрипт, который не превышает ограничения по кол-ву поднятых объектов
     * Все должно быть ок
     */
    @Test
    public void testScriptFromConsoleNotInterrupted() throws DispatchException
    {
        configurationProperties.setPersistenceContextLimit(10_000);

        scriptActionExecutor.execute(
                new ScriptExecutionParameters(String.format(testScript, 100, userCase.asString())));
    }

    /**
     * Проверить, что скрипт ДПС прерывается, если в нем был превышен лимит на кол-во поднятых объектов
     */
    @Test(expected = OperationException.class)
    public void testSyncEventActionScriptInterrupted() throws Exception
    {
        configurationProperties.setPersistenceContextLimit(10);
        final EventAction eventAction = addEventAction(10, fakeCase.asString());

        final IProperties properties = new MapProperties();
        properties.setProperty(ru.naumen.core.shared.Constants.AbstractBO.TITLE, "some title");

        try
        {
            objectTestUtils.create(userCase, properties);
        }
        finally
        {
            dispatch.execute(new DeleteEventAction(eventAction));
            objectTestUtils.deleteUnusedScript(TEST_SCRIPT_CODE);
        }
    }

    /**
     * Проверить, что скрипт ДПС не прерывается, если в нем не был превышен лимит на кол-во поднятых объектов
     */
    @Test
    public void testSyncEventActionScriptNotInterrupted() throws DispatchException
    {
        configurationProperties.setPersistenceContextLimit(10_000);
        final EventAction eventAction = addEventAction(100, fakeCase.asString());

        final IProperties properties = new MapProperties();
        properties.setProperty(ru.naumen.core.shared.Constants.AbstractBO.TITLE, "some title");

        objectTestUtils.create(userCase, properties);

        dispatch.execute(new DeleteEventAction(eventAction));
        objectTestUtils.deleteUnusedScript(TEST_SCRIPT_CODE);
    }

    /**
     * Проверить, что PersistenceContextAST трансформация не применилась к трейтам
     */
    @Test
    public void testPersistenceContextASTNotAppliedToTraits() throws DispatchException
    {
        configurationProperties.setPersistenceContextLimit(10_000);

        scriptActionExecutor.execute(new ScriptExecutionParameters(traitsScript));
    }

    /**
     * Проверить, что PersistenceContextAST трансформация не применилась к интерфейсам
     */
    @Test
    public void testPersistenceContextASTNotAppliedToInterfaces() throws DispatchException
    {
        configurationProperties.setPersistenceContextLimit(10_000);

        scriptActionExecutor.execute(new ScriptExecutionParameters(interfacesScript));
    }

    private EventAction addEventAction(final int usersCount, final String fqn) throws DispatchException
    {
        final PlannedEventRule event = new PlannedEventRule();
        event.setEventType(EventType.add);

        final ScriptDto scriptDto = objectTestUtils.createScriptDto(String.format(testScript, usersCount, fqn));
        scriptDto.setCode(TEST_SCRIPT_CODE);

        final ScriptEventAction scriptEventAction = new ScriptEventAction();
        scriptEventAction.setActionType(ActionType.ScriptEventAction);
        scriptEventAction.setScript(scriptDto.getBody());

        final EventAction eventAction = new EventAction(event, scriptEventAction);
        eventAction.getTitle().add(new LocalizedString("ru", TEST_SCRIPT_CODE));
        eventAction.setOn(true);
        eventAction.setAction(scriptEventAction);
        eventAction.setId(EVENT_ACTION_CODE);
        eventAction.setLinkedClasses(Lists.newArrayList(userClass));
        eventAction.setTxType(TxType.CURRENT_TX);

        final SaveEventAction saveEventAction = new SaveEventAction(eventAction, true);
        saveEventAction.setScript(scriptDto);
        dispatch.execute(saveEventAction);

        return eventAction;
    }

    private void addTestModule(final String moduleScript) throws DispatchException
    {
        final IProperties props = new MapProperties();
        props.setProperty(Constants.ScriptModule.CODE, TEST_MODULE_CODE);
        props.setProperty(Constants.ScriptModule.SCRIPT, moduleScript);

        dispatch.execute(new AddScriptModuleAction(props));
    }
}
