package ru.naumen.core.server.script.spi;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import jakarta.inject.Inject;

import javax.management.ObjectName;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.scriptlet4docx.docx.TemplateFileManager;
import ru.naumen.core.server.upload.NestedFileItem;
import ru.naumen.core.server.utils.LeakTestUtils;

@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class ScriptUtilsLeakDbTest
{
    private static final int LOADED_CLASSES_THRESHOLD = 1000;
    private static final String PATH_TO_TEST_TEMPLATE = "ru/naumen/core/server/script/spi/template.docx";

    private static final String TEMPLATES_DIR_FIELD_NAME = "templatesDir";

    @Rule
    public TemporaryFolder temporaryFolder = new TemporaryFolder();

    @Inject
    private IScriptUtils scripts;

    @Inject
    private SecurityTestUtils securityTestUtils;

    @Before
    public void setUp()
    {
        securityTestUtils.autenticateAsSuperUser();
    }

    /**
     * Тестирование того, что при использовании utils.processDocx
     * нет утечки metaspace. Кол-во загруженных классов после прогона метода 1000 раз не должно превысить
     * {@value #LOADED_CLASSES_THRESHOLD}
     * Запуск 1000 раз происходит после первого запуска метода, чтобы скомпиленный класс шаблона был загружен
     * @throws Exception
     */
    @Test
    @Transactional
    public void testProcessDocxLeak() throws Exception
    {
        final TemplateFileManager fileManager = TemplateFileManager.getInstance();
        final Object originalTemplatesDir = ReflectionTestUtils.getField(fileManager, TEMPLATES_DIR_FIELD_NAME);
        final File file = temporaryFolder.newFolder();
        ReflectionTestUtils.setField(fileManager, TEMPLATES_DIR_FIELD_NAME, file);
        try
        {
            Map<String, Object> bindings = new HashMap<>();
            bindings.put("test", "it works!");

            String fileUUID = addFile(PATH_TO_TEST_TEMPLATE);

            ObjectName classLoading = new ObjectName("java.lang:type=ClassLoading");

            //Выполняем первый раз для начальной загрузки скомпилированного класса шаблона
            scripts.processTemplate(scripts.get(fileUUID), bindings);
            int before = LeakTestUtils.getLoadedClassesCount(classLoading);
            for (int i = 0; i < 1000; i++)
            {
                scripts.processTemplate(scripts.get(fileUUID), bindings);
            }
            int after = LeakTestUtils.getLoadedClassesCount(classLoading);
            int result = after - before;
            Assert.assertTrue(
                    String.format("Number of loaded classes exceeded threshold. Expected not more than %s, actual %s",
                            LOADED_CLASSES_THRESHOLD, result),
                    result <= LOADED_CLASSES_THRESHOLD);
        }
        finally
        {
            ReflectionTestUtils.setField(fileManager, TEMPLATES_DIR_FIELD_NAME, originalTemplatesDir);
        }
    }

    /**
     * тестирование того, что при использовании utils.processTemplate 
     * нет неконтролируемого роста загруженнных классов, которой приводит к утечке metaspace
     * кол-во загруженных классов после прогона метода 1000 раз не должно привысить {@value #LOADED_CLASSES_THRESHOLD}
     * Запуск 1000 раз происходит после первого запуска метода, чтобы скомпиленный класс шаблона был загружен
     * @throws Exception
     */
    @Test
    public void testProcessTemplateLeak() throws Exception
    {
        HashMap<String, Object> params = new HashMap<>();
        params.put("test", "test");

        ObjectName classLoading = new ObjectName("java.lang:type=ClassLoading");
        //Выполненяем первый раз, для начальной загрузки скомпилированного класса шаблона
        scripts.processTemplate("${test}", params);
        int before = LeakTestUtils.getLoadedClassesCount(classLoading);
        for (int i = 0; i < 1000; i++)
        {
            scripts.processTemplate("${test}", params);
        }
        int after = LeakTestUtils.getLoadedClassesCount(classLoading);
        int result = after - before;
        Assert.assertTrue(
                String.format("Number of loaded classes exceeded threshold. Expected not more than %s, actual %s",
                        LOADED_CLASSES_THRESHOLD, result),
                result <= LOADED_CLASSES_THRESHOLD);

    }

    /**
     * Добавить файл на карточку компании
     * @return БО файл
     */
    private String addFile(final String path)
    {
        final String fileName = "test-template.docx";
        return TransactionRunner.call(TransactionType.NEW, () ->
        {
            try (InputStream ignored = getClass().getResourceAsStream(path))
            {
                MapProperties properties = new MapProperties();
                properties.setProperty(ru.naumen.core.shared.Constants.File.CREATION_DATE, new Date());
                properties.setProperty(ru.naumen.core.shared.Constants.File.SOURCE, "root");
                properties.setProperty(ru.naumen.core.shared.Constants.File.RELATION, null);
                properties.setProperty(ru.naumen.core.shared.Constants.File.CONTENT,
                        createNestedFileItem());
                properties.setProperty(ru.naumen.core.shared.Constants.File.TITLE, fileName);
                properties.setProperty(ru.naumen.core.shared.Constants.File.DESCRIPTION, "test storage");

                return scripts.create(ru.naumen.core.shared.Constants.File.FQN, properties).getUUID();
            }
            catch (IOException e)
            {
                throw new RuntimeException(e);
            }
        });
    }

    private NestedFileItem createNestedFileItem() throws IOException
    {
        return new NestedFileItem(new File("src/test/resources/ru/naumen/core/server/script/spi/template.docx"));
    }
}
