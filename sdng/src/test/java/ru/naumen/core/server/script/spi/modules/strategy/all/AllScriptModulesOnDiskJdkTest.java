package ru.naumen.core.server.script.spi.modules.strategy.all;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.script.spi.modules.strategy.BaseScriptModulesCompilationServiceJdkTest;

/**
 * Проверка компиляции модуляции за раз
 *
 * <AUTHOR>
 * @since Oct 12, 2020
 */
public class AllScriptModulesOnDiskJdkTest extends BaseAllScriptModulesCompilationServiceTest
{
    private static final Path DIRECTORY_CLASSES = BaseScriptModulesCompilationServiceJdkTest.dataDir.resolve(
            "modules/classes");
    private static final String SIMPLE_MODULE = """
            def asd() {
              return 'asd'
            }
            
            def qwe() {
              retrun 'qwe'
            }""";
    private static final String CLASS_WITH_SIMPLE_METHODS = """
            enum NUMBERS
            {
              ONE, TWO, THREE, FOUR
            }
            
            def getOne() {
              return NUMBERS.ONE
            }
            
            def getString() {
              return 'NUMBERS.ONE'
            }""";
    private static final String CLASS_AAA = """
            class AAA
            {
              def aaa() {
                return "is's class A - " + this.getClass().toString()
              }
            }""";
    private static final String CLASS_BBB = """
            class BBB
            {
              def bbb() {
                return "it's class BBB - " + this.getClass().toString()
              }
            }""";
    private static final String CLASS_WITH_PACKAGE = """
            package ru.naumen.box
            
            class Cat
            {
              def say() {
                return 'meow'
              }
            }""";
    private static final Map<String, String> SCRIPTS = Map.of(
            "simpleModule", SIMPLE_MODULE,
            "classWithSimpleMethods", CLASS_WITH_SIMPLE_METHODS,
            "classAAA", CLASS_AAA,
            "classBBB", CLASS_BBB,
            "classWithPackage", CLASS_WITH_PACKAGE
    );

    @Before
    public void setUpEachTest()
    {
        BaseAllScriptModulesCompilationServiceTest.dropAllModules();
        BaseAllScriptModulesCompilationServiceTest.createModules(SCRIPTS);
    }

    @Test
    public void checkFilesOnDisk()
    {
        var paths = List.of(
                DIRECTORY_CLASSES.resolve("simple'Module-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("class'With'Simple'Methods-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("'N'U'M'B'E'R'S-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("'A'A'A-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("'B'B'B-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("ru/naumen/box/'Cat-MAIN$%.class")
        );
        checkPaths(paths);
    }

    @Test
    public void checkDeleteModule()
    {
        BaseAllScriptModulesCompilationServiceTest.dropModule("classWithSimpleMethods");
        var paths = List.of(
                DIRECTORY_CLASSES.resolve("simple'Module-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("'A'A'A-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("'B'B'B-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("ru/naumen/box/'Cat-MAIN$%.class")
        );
        checkPaths(paths);
    }

    @Test
    public void checkDeleteModuleWithPackage()
    {
        BaseAllScriptModulesCompilationServiceTest.dropModule("classWithPackage");
        var paths = List.of(
                DIRECTORY_CLASSES.resolve("simple'Module-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("class'With'Simple'Methods-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("'N'U'M'B'E'R'S-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("'A'A'A-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("'B'B'B-MAIN$%.class")
        );
        checkPaths(paths);
    }

    @Test
    public void testRemoveClassFromModuleWithClass()
    {
        BaseAllScriptModulesCompilationServiceTest.createModules(
                Map.of("classWithSimpleMethods", SIMPLE_MODULE));
        var paths = List.of(
                DIRECTORY_CLASSES.resolve("simple'Module-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("class'With'Simple'Methods-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("'A'A'A-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("'B'B'B-MAIN$%.class"),
                DIRECTORY_CLASSES.resolve("ru/naumen/box/'Cat-MAIN$%.class")
        );
        checkPaths(paths);
    }

    private static void checkPaths(Collection<Path> paths, Path directoryFrom)
    {
        if (Files.notExists(directoryFrom))
        {
            Assert.fail("Directory " + directoryFrom + " does not exist");
        }
        if (!Files.isDirectory(directoryFrom))
        {
            Assert.fail("В качестве второго аргумента должна быть директория. path: " + directoryFrom);
        }
        String possiblesPath = paths.stream()
                .map(Path::toString)
                .collect(Collectors.joining(", \n"));
        try
        {
            try (Stream<Path> list = Files.list(directoryFrom))
            {
                list.forEach(path ->
                {
                    if (Files.isDirectory(path))
                    {
                        checkPaths(paths, path);
                    }
                    else
                    {
                        Assert.assertTrue(String.format("Найден лишний файл. Могли быть:\n %s. \n Нашелся файл:\n %s",
                                possiblesPath, path), paths.contains(path));
                    }
                });
            }
        }
        catch (IOException e)
        {
            throw new FxException(e);
        }
    }

    private static void checkPaths(Collection<Path> paths)
    {
        checkPaths(paths, DIRECTORY_CLASSES);
    }
}
