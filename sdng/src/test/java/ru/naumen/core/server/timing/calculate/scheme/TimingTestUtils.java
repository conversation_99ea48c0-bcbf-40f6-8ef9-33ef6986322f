package ru.naumen.core.server.timing.calculate.scheme;

import static org.apache.commons.lang3.time.DateUtils.MILLIS_PER_MINUTE;

import java.util.Calendar;
import java.util.Date;
import java.util.SortedMap;
import java.util.SortedSet;
import java.util.TreeMap;
import java.util.TreeSet;

import org.apache.commons.lang3.time.DateUtils;

import ru.naumen.core.server.timing.Timesheet;
import ru.naumen.core.server.timing.TimesheetImpl;
import ru.naumen.core.server.timing.period.DateTiming;
import ru.naumen.core.server.timing.period.DayPeriod;
import ru.naumen.core.server.timing.period.DayPeriodImpl;
import ru.naumen.core.server.timing.period.WeekDayTiming;
import ru.naumen.core.server.timing.period.WeekDayTimingImpl;

public class TimingTestUtils
{

    //@formatter:off
    private static final Integer[] days = new Integer[] { 
            Calendar.MONDAY, 
            Calendar.TUESDAY, 
            Calendar.WEDNESDAY,
            Calendar.THURSDAY, 
            Calendar.FRIDAY, 
            Calendar.SATURDAY, 
            Calendar.SUNDAY };
    
    private static final Integer[][][] periods = new Integer[][][]{
        new Integer[][]{
                new Integer[]{800, 1000},
                new Integer[]{1200,1230}
        },
        new Integer[][]{
                new Integer[]{900, 1400}
        },
        new Integer[][]{
                new Integer[]{1000, 1330},
                new Integer[]{1400, 1700}
        },
        new Integer[][]{
                new Integer[]{1100, 1545}
        },
        new Integer[][]{
        },
        new Integer[][]{
                new Integer[]{1200,2200}
        },
        new Integer[][]{}
    };
    //@formatter:on

    public static long convertTimeToMillis(int time)
    {
        int hours = time / 100;
        int minutes = time % 100;
        return hours * DateUtils.MILLIS_PER_HOUR + minutes * MILLIS_PER_MINUTE;
    }

    public static SortedSet<DayPeriod> setUpDayPeriods(int index)
    {
        SortedSet<DayPeriod> result = new TreeSet<DayPeriod>();
        for (int i = 0; i < periods[index].length; i++)
        {
            DayPeriod period = new DayPeriodImpl();
            period.setStart(convertTimeToMillis(periods[index][i][0]));
            period.setEnd(convertTimeToMillis(periods[index][i][1]));
            result.add(period);
        }
        return result;
    }

    public static SortedMap<Date, DateTiming> setUpExcludedTimings()
    {
        return new TreeMap<Date, DateTiming>();
    }

    public static Timesheet setUpTimesheet(long resolutionTime, Date deadLineDate,
            SortedMap<Date, DateTiming> excludedTimings, SortedMap<Integer, WeekDayTiming> weekDayTimings)
    {
        Timesheet result = new TimesheetImpl();
        result.setDeadLineDate(deadLineDate);
        result.setResolutionTime(resolutionTime);
        result.setExcludedTimings(excludedTimings);
        result.setWeekDayTimings(weekDayTimings);
        return result;
    }

    public static WeekDayTiming setUpWeekDayTiming(Integer day, int index)
    {
        WeekDayTiming result = new WeekDayTimingImpl();
        result.setDayOfWeek(day);
        result.setPeriods(setUpDayPeriods(index));
        return result;
    }

    public static SortedMap<Integer, WeekDayTiming> setUpWeekDayTimings()
    {
        SortedMap<Integer, WeekDayTiming> result = new TreeMap<Integer, WeekDayTiming>();
        for (int i = 0; i < days.length; i++)
        {
            result.put(days[i], setUpWeekDayTiming(days[i], i));
        }
        return result;
    }
}
