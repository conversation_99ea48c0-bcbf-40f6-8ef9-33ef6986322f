package ru.naumen.ui;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * Конфигурация мигратора настроек старого интерфейса в настройки нового интерфейса
 */
@Configuration
public class UIMigratorConfiguration
{
    /** Идентификатор приложения, создаваемого при миграции */
    @Value("${ru.naumen.ui2.applicationId:demo}")
    private String applicationId;

    /** Имя приложения в адресной строке браузера, создаваемого при миграции */
    @Value("${ru.naumen.ui2.applicationPath:demo}")
    private String applicationPath;

    /** Запускать или нет миграцию настроек при запуске приложения */
    @Value("${ru.naumen.ui2.runMigration:true}")
    private boolean needToMigrateOnStart;

    /** Страница, на которой располагается профиль пользователя */
    @Value("${ru.naumen.ui2.userProfile}")
    private String userProfile;

    /**
     * Получить идентификатор приложения, создаваемого при миграции
     */
    public String getApplicationId()
    {
        return this.applicationId;
    }

    /**
     * Получить имя приложения для адресной строки браузера
     */
    public String getApplicationPath()
    {
        return this.applicationPath;
    }

    /**
     * Нужно ли запускать миграцию при старте приложения
     */
    public boolean isNeedToMigrateOnStart()
    {
        return this.needToMigrateOnStart;
    }

    public String getUserProfile()
    {
        return userProfile;
    }
}
