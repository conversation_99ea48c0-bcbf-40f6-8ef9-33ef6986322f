package ru.naumen.ui;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import ru.naumen.config.UIConfiguration;
import ru.naumen.core.server.cluster.external.ClusterServiceManager;

/**
 * Лисенер, запускающий миграцию настроек при старте приложения
 */
@Component
public class UIMigratorListener
{
    private final UIMigrationService uiMigrationService;
    private final UIMigratorConfiguration uiMigratorConfiguration;
    private final ClusterServiceManager clusterServiceManager;
    private final UIConfiguration uiConfiguration;

    private static final Logger LOG = LoggerFactory.getLogger(UIMigratorListener.class);

    public UIMigratorListener(final UIMigrationService uiMigrationService,
            UIMigratorConfiguration uiMigratorConfiguration,
            ClusterServiceManager clusterServiceManager,
            UIConfiguration uiConfiguration)
    {
        this.uiMigrationService = uiMigrationService;
        this.uiMigratorConfiguration = uiMigratorConfiguration;
        this.clusterServiceManager = clusterServiceManager;
        this.uiConfiguration = uiConfiguration;
    }

    /**
     * Запуск миграции настроек при старте приложения
     */
    @EventListener
    public void onContextStart(ContextRefreshedEvent event)
    {
        if (!uiMigratorConfiguration.isNeedToMigrateOnStart())
        {
            if (LOG.isInfoEnabled())
            {
                LOG.info(
                        ("Migration has not started. UI settings migration from old settings has not started: no need "
                         + "to run migration. Application: %s, applicationPath: %s").formatted(
                                uiMigratorConfiguration.getApplicationId(),
                                uiMigratorConfiguration.getApplicationPath()));
            }
            return;
        }

        // Если настройки хранятся не в памяти и мы не на первой ноде кластера - пропускаем миграцию. Ее уже сделала
        // первая нода, остальные подтянут изменения из репозитория. Если настройки хранятся в памяти - нужно
        // мигрировать их на каждой ноде, иначе другие ноды не смогут подтянуть их с той ноды, где они промигрировались
        if (!uiConfiguration.isInMemorySettingsStorage() && !clusterServiceManager.isFirstClusterNode())
        {
            LOG.info(
                    "Migration has not started. No need to migrate. UI settings already has been migrated on other "
                    + "node.");
            return;
        }

        try
        {
            uiMigrationService.migrateApplicationSettings(uiMigratorConfiguration.getApplicationId(),
                    uiMigratorConfiguration.getApplicationPath());
            if (LOG.isInfoEnabled())
            {
                LOG.info(
                        ("Migration successful. UI settings migration from old settings on context start successful. "
                         + "Application: %s, applicationPath: %s")
                                .formatted(uiMigratorConfiguration.getApplicationId(),
                                        uiMigratorConfiguration.getApplicationPath()));
            }
        }
        catch (Exception e)
        {
            LOG.error("Migration failed. UI settings migration from old settings failed: %s".formatted(
                    e.getMessage()), e);
        }
    }
}
