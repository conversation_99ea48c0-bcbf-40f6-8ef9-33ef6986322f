package ru.naumen.ui.settings.entity.content.datasource.computable;

import java.util.List;

import ru.naumen.ui.settings.entity.common.UIAttributeReference;

/**
 * Источник данных, содержащий свойства вычисленного активного объекта
 *
 * <AUTHOR>
 * @since 04.12.2024
 * @doc_title Источник данных, содержащий свойства вычисленного активного объекта
 */
public interface HasComputableActiveObjectDSProperties
{
    /**
     * Объект, с которым работает приложение
     * (уид, либо другое обозначение, типа currentUser)
     **/
    String getActiveObject();

    /**
     * Цепочка связей объекта, с которым работает приложение,
     * относительно текущего пользователя
     **/
    List<UIAttributeReference> getActiveObjectAttrChain();
}
