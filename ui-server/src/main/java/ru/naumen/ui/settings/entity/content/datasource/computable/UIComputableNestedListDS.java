package ru.naumen.ui.settings.entity.content.datasource.computable;

import java.beans.ConstructorProperties;
import java.util.List;

import ru.naumen.authorization.rule.AuthRule;
import ru.naumen.ui.settings.entity.common.UIAttributeReference;
import ru.naumen.ui.settings.entity.common.UIClassFqn;
import ru.naumen.ui.settings.entity.common.UILocalizedString;
import ru.naumen.ui.settings.entity.content.datasource.UIDataSource;
import ru.naumen.ui.settings.entity.content.datasource.list.UINestedObjectListDS;

/**
 * Источник данных для списка объектов, вложенных в объект,
 * который необходимо вычислить относительно активного объекта
 *
 * <AUTHOR>
 * @since 04.12.2024
 * @doc_title Источник данных для списка объектов, вложенных в объект, который необходимо вычислить относительно
 * активного объекта
 */
public class UIComputableNestedListDS extends UINestedObjectListDS implements HasComputableActiveObjectDSProperties
{
    /**
     * Объект, с которым работает приложение
     */
    private final String activeObject;

    /**
     * Цепочка связей объекта, с которым работает приложение, относительно текущего пользователя
     */
    private List<UIAttributeReference> activeObjectAttrChain = List.of();

    @ConstructorProperties({ "id", "title", "authRule", "classFqn", "attrGroupCode", "activeObject" })
    public UIComputableNestedListDS(String id, List<UILocalizedString> title, AuthRule authRule, UIClassFqn classFqn,
            String attrGroupCode, String activeObject)
    {
        super(id, title, authRule, classFqn, attrGroupCode);
        this.activeObject = activeObject;
    }

    public UIComputableNestedListDS(UIComputableNestedListDS from)
    {
        super(from);
        this.activeObject = from.getActiveObject();
        this.activeObjectAttrChain = from.getActiveObjectAttrChain();
    }

    @Override
    public String getActiveObject()
    {
        return activeObject;
    }

    @Override
    public List<UIAttributeReference> getActiveObjectAttrChain()
    {
        return activeObjectAttrChain;
    }

    public void setActiveObjectAttrChain(
            List<UIAttributeReference> activeObjectAttrChain)
    {
        this.activeObjectAttrChain = activeObjectAttrChain;
    }

    @Override
    public UIDataSource doClone()
    {
        return new UIComputableNestedListDS(this);
    }
}
