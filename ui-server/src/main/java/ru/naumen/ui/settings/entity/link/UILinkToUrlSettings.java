package ru.naumen.ui.settings.entity.link;

import java.beans.ConstructorProperties;

/**
 * Настройка произвольной ссылки
 *
 * <AUTHOR>
 * @since 28.07.2023
 * @doc_title Настройка произвольной ссылки
 */
public class UILinkToUrlSettings extends UILinkSettings
{
    /** Адрес ссылки */
    private final String url;

    /** Начинается с URL системы */
    private final boolean startWithSystemUrl;

    /**
     * Конструктор с обязательными полями
     * @param applicationId Идентификатор приложения
     * @param url Адрес ссылки
     * @param startWithSystemUrl Начинается с URL системы
     */
    @ConstructorProperties({ "applicationId", "url", "startWithSystemUrl" })
    public UILinkToUrlSettings(String applicationId, String url, boolean startWithSystemUrl)
    {
        super(applicationId);
        this.url = url;
        this.startWithSystemUrl = startWithSystemUrl;
    }

    @Override
    public UILinkToUrlSettings doClone()
    {
        return new UILinkToUrlSettings(this);
    }

    private UILinkToUrlSettings(UILinkToUrlSettings linkSettings)
    {
        super(linkSettings);
        this.url = linkSettings.url;
        this.startWithSystemUrl = linkSettings.startWithSystemUrl;
    }

    public String getUrl()
    {
        return url;
    }

}
