package ru.naumen.ui.settings.entity.menu;

import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import jakarta.annotation.Nullable;
import ru.naumen.authorization.HasAuthRule;
import ru.naumen.authorization.rule.AuthRule;
import ru.naumen.core.shared.CoreHasClone;
import ru.naumen.core.shared.CoreHasCloneUtils;
import ru.naumen.ui.models.UIConstants;
import ru.naumen.ui.settings.entity.UIElementSettings;
import ru.naumen.ui.settings.entity.common.UILocalizedString;
import ru.naumen.ui.settings.entity.common.UILocalizedTitle;

/**
 * Настройки элемента меню
 *
 * <AUTHOR>
 * @since 24.07.2023
 * @doc_title настройки элемента меню
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = UIConstants.SERIALIZE_FIELD)
@JsonSubTypes(
        value = {
                @JsonSubTypes.Type(value = UIMenuSectionSettings.class, name = "UIMenuSectionSettings"),
                @JsonSubTypes.Type(value = UIMenuItemLinkSettings.class, name = "UIMenuItemLinkSettings"),
                @JsonSubTypes.Type(value = UIRelativePageMenuItemLinkSettings.class, name =
                        "UIRelativePageMenuItemLinkSettings"),
        })
public abstract class UIMenuItemSettings implements UIElementSettings,
        CoreHasClone<UIMenuItemSettings>, UILocalizedTitle, HasAuthRule
{
    /** Идентификатор элемента меню */
    private final String id;

    /** Идентификатор меню */
    private final String menuId;

    /** Идентификатор приложения */
    private final String applicationId;

    /** Локализованное название элемента меню */
    private List<UILocalizedString> title;

    /** Локализованный текст подсказки */
    private List<UILocalizedString> hint;

    /** Код иконки */
    @Nullable
    private String iconCode;

    /** Аббревиатура */
    @Nullable
    private List<UILocalizedString> abbreviation;

    /** Идентификатор родительского пункта меню (раздела). Null - нет родительского раздела */
    @Nullable
    private String parentMenuItemId;

    /** Правило проверки прав на элемент меню */
    private final AuthRule authRule;

    /** Глобальный признак доступности элемента */
    private boolean enabled = true;

    /**
     * Конструктор с обязательными полями
     * @param id Идентификатор элемента меню
     * @param menuId Идентификатор меню
     * @param applicationId Идентификатор приложения
     * @param title Локализованное название элемента меню
     * @param authRule Правило проверки прав на пункт меню
     */
    protected UIMenuItemSettings(String id, String menuId, String applicationId, List<UILocalizedString> title,
            AuthRule authRule)
    {
        this.id = id;
        this.menuId = menuId;
        this.applicationId = applicationId;
        this.title = title;
        this.hint = title;
        this.authRule = authRule;
    }

    protected UIMenuItemSettings(UIMenuItemSettings settings)
    {
        this.id = settings.id;
        this.parentMenuItemId = settings.parentMenuItemId;
        this.menuId = settings.menuId;
        this.applicationId = settings.applicationId;
        this.title = CoreHasCloneUtils.cloneList(settings.title);
        this.hint = CoreHasCloneUtils.cloneList(settings.hint);
        this.enabled = settings.enabled;
        this.authRule = settings.authRule.doClone();
        this.abbreviation = CoreHasCloneUtils.cloneList(settings.abbreviation);
        this.iconCode = settings.iconCode;
    }

    public String getId()
    {
        return id;
    }

    @Nullable
    public String getParentMenuItemId()
    {
        return parentMenuItemId;
    }

    public void setParentMenuItemId(@Nullable String parentMenuItemId)
    {
        this.parentMenuItemId = parentMenuItemId;
    }

    public String getMenuId()
    {
        return menuId;
    }

    public String getApplicationId()
    {
        return applicationId;
    }

    public List<UILocalizedString> getHint()
    {
        return hint;
    }

    public UIMenuItemSettings setHint(List<UILocalizedString> hint)
    {
        this.hint = hint;
        return this;
    }

    @Override
    public List<UILocalizedString> getTitle()
    {
        return title;
    }

    @Nullable
    public String getIconCode()
    {
        return iconCode;
    }

    public UIMenuItemSettings setIconCode(@Nullable String iconCode)
    {
        this.iconCode = iconCode;
        return this;
    }

    @Nullable
    public List<UILocalizedString> getAbbreviation()
    {
        return abbreviation;
    }

    public UIMenuItemSettings setAbbreviation(@Nullable List<UILocalizedString> abbreviation)
    {
        this.abbreviation = abbreviation;
        return this;
    }

    @Override
    public AuthRule getAuthRule()
    {
        return authRule;
    }

    public boolean isEnabled()
    {
        return enabled;
    }

    public UIMenuItemSettings setEnabled(boolean enabled)
    {
        this.enabled = enabled;
        return this;
    }

    public void setTitle(List<UILocalizedString> title)
    {
        this.title = title;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        UIMenuItemSettings that = (UIMenuItemSettings)o;
        return Objects.equals(id, that.id)
               && Objects.equals(parentMenuItemId, that.parentMenuItemId)
               && Objects.equals(menuId, that.menuId)
               && Objects.equals(applicationId, that.applicationId)
               && Objects.equals(authRule, that.authRule);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(id, parentMenuItemId, menuId, applicationId, authRule);
    }
}
