package ru.naumen.ui.settings.entity.toolbar;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import ru.naumen.core.shared.CoreHasClone;
import ru.naumen.ui.models.UIConstants;
import ru.naumen.ui.settings.entity.UIHasIdElementSettings;

/**
 * Базовый класс инструмента (из панели инструментов)
 *
 * <AUTHOR>
 * @since 09.04.2024
 * @doc_title Базовый класс инструмента
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = UIConstants.SERIALIZE_FIELD)
@JsonSubTypes(
        {
                @JsonSubTypes.Type(value = UIButtonSettings.class, name = "UIButtonSettings"),
                @JsonSubTypes.Type(value = UIFilterToggleControlSettings.class, name = "UIFilterToggleControlSettings"),
                @JsonSubTypes.Type(value = UISearchQueryFieldSettings.class, name = "UISearchQueryFieldSettings"),
                @JsonSubTypes.Type(value = UIListViewSelectorSettings.class, name = "UIListViewSelectorSettings"),
        })
public abstract class UIToolSettings extends UIHasIdElementSettings
        implements CoreHasClone<UIToolSettings>
{
    protected UIToolSettings(String id)
    {
        super(id);
    }

    protected UIToolSettings(UIHasIdElementSettings settings)
    {
        super(settings);
    }
}
