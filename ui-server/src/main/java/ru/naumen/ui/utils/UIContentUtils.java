package ru.naumen.ui.utils;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Deque;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import ru.naumen.ui.models.content.UICard;
import ru.naumen.ui.models.content.UIContainer;
import ru.naumen.ui.models.content.UIContent;
import ru.naumen.ui.models.content.UIForm;
import ru.naumen.ui.models.content.UITab;
import ru.naumen.ui.models.content.UITabBar;
import ru.naumen.ui.settings.entity.content.UIContentSettings;
import ru.naumen.ui.settings.entity.content.UIHasChildrenContentSettings;
import ru.naumen.ui.settings.entity.content.UIHasContentSettings;
import ru.naumen.ui.settings.entity.content.UITabBarSettings;
import ru.naumen.ui.settings.entity.content.datasource.HasUIDataSource;
import ru.naumen.ui.settings.entity.layout.UIGrid;
import ru.naumen.ui.settings.entity.layout.UIGridArea;
import ru.naumen.ui.settings.entity.layout.UIGridLayout;
import ru.naumen.ui.settings.entity.layout.UILayout;

/**
 * Утилитарные методы для работы с контентами
 *
 * <AUTHOR>
 * @since 16.07.2024
 */
public final class UIContentUtils
{
    public static UILayout createSimpleLayout(String... contentIds)
    {
        List<UIGridArea> areas = Arrays.stream(contentIds)
                .map(UIGridArea::new)
                .toList();
        return new UIGridLayout(List.of(new UIGrid().setAreas(areas)));
    }

    /**
     * Пустой UIGridLayout
     */
    public static UILayout emptyLayout()
    {
        return new UIGridLayout(List.of());
    }

    /**
     * Пустой контейнер
     * @param id идентификатор контейнера
     */
    public static UIContainer emptyContainer(String id)
    {
        return new UIContainer(id, List.of(), emptyLayout());
    }

    /**
     * Пустой контейнер
     */
    public static UIContainer emptyContainer()
    {
        return emptyContainer("emptyContainer");
    }

    /**
     * Получить коллекцию вложенных контентов (только прямых потомков)
     * @param content корневой контент
     */
    public static Collection<UIContent> getDirectChildren(UIContent content)
    {
        return switch (content)
        {
            case UIContainer container -> container.getContents();
            case UITabBar tabBar -> tabBar.getTabs().stream()
                    .map(UITab::getContent)
                    .toList();
            case UIForm form -> List.of(form.getBody().getContent());
            case UICard card -> List.of(card.getContent());
            default -> List.of();
        };
    }

    /**
     * Получить все вложенные (рекурсивно) контенты
     * @param content исходный контент
     */
    public static Set<UIContent> getAllNestedContents(UIContent content)
    {
        Set<UIContent> result = new HashSet<>();
        List<UIContent> contents = new ArrayList<>();
        contents.add(content);
        while (!contents.isEmpty())
        {
            UIContent uiContent = contents.removeFirst();
            result.add(uiContent);
            contents.addAll(getDirectChildren(uiContent));
        }

        return result;
    }

    /**
     * Получить коллекцию вложенных контентов (только прямых потомков)
     * @param content корневой контент
     */
    public static Collection<UIContentSettings> getDirectChildren(UIContentSettings content)
    {
        return switch (content)
        {
            case UIHasContentSettings hasContent -> List.of(hasContent.getContent());
            case UIHasChildrenContentSettings hasChildren -> hasChildren.getChildren();
            default -> List.of();
        };
    }

    /**
     * Получить все вложенные (рекурсивно) контенты
     * @param content исходный контент
     */
    public static Set<UIContentSettings> getAllNestedContents(UIContentSettings content)
    {
        Set<UIContentSettings> result = new HashSet<>();
        List<UIContentSettings> contents = new ArrayList<>();
        contents.add(content);
        while (!contents.isEmpty())
        {
            UIContentSettings cs = contents.removeFirst();
            result.add(cs);
            contents.addAll(getDirectChildren(cs));
        }

        return result;
    }

    /**
     * Получить список идентификаторов вложенных источников данных
     * @param content исходный контент
     */
    public static Set<String> getAllNestedDataSources(UIContentSettings content)
    {
        Set<UIContentSettings> allContents = getAllNestedContents(content);
        return allContents.stream()
                .filter(HasUIDataSource.class::isInstance)
                .map(element -> ((HasUIDataSource)element).getDataSourceId())
                .collect(Collectors.toSet());
    }

    /**
     * Получить контент самой глубоко вложенной вкладки
     * @param content контент, внутри которого искать
     * @param tabMap ассоциативный массив панель вкладок -> вкладка
     */
    public static UIContentSettings getContentOfDeepestTab(UIContentSettings content, Map<String, String> tabMap)
    {
        if (tabMap.isEmpty())
        {
            // Если список вкладок пусть, вернуть переданный контент
            return content;
        }

        List<UIContentSettings> children = new ArrayList<>();
        children.add(content);
        Map<String, String> tabs = new HashMap<>(tabMap);

        // Найти контент самой глубокой вкладки
        UIContentSettings result = null;
        while (!tabs.isEmpty())
        {
            if (children.isEmpty())
            {
                // Если вкладки определены, а контенты уже закончились, вернуть null
                return null;
            }

            UIContentSettings child = children.removeFirst();
            if (child instanceof UITabBarSettings tabBarSettings)
            {
                // Если таббар, то из мапы извлечь идентификатор вкладки и ее контент
                String tabId = tabs.remove(tabBarSettings.getCode());
                result = tabId == null
                        ? null
                        : tabBarSettings.getTabContent(tabId);

                if (result != null)
                {
                    // Если контент вкладки найден, то дальнейший поиск только внутри этого контента
                    children.clear();
                    children.add(result);
                }
            }
            else
            {
                // Если не таббар, то добавить извлечь идентификатор вкладки и ее контент
                children.addAll(getDirectChildren(child));
            }
        }
        return result;
    }

    /**
     * Проверка, содержится ли переданный контент в контенте для поиска (или является им)
     * @param contentToSearch проверяемый контент
     * @param containerForSearch контент, в котором нужно проверить,
     *                           содержится ли проверяемый контент (или является этим контентом)
     */
    public static boolean isContentExists(UIContentSettings contentToSearch, UIContentSettings containerForSearch)
    {
        Deque<UIContentSettings> contents = new ArrayDeque<>();
        contents.add(containerForSearch);
        while (!contents.isEmpty())
        {
            UIContentSettings currentContent = contents.pop();
            if (contentToSearch.getId().equals(currentContent.getId()))
            {
                return true;
            }
            if (currentContent instanceof UIHasChildrenContentSettings hasChildrenContentSettings)
            {
                contents.addAll(hasChildrenContentSettings.getChildren());
            }
            if (currentContent instanceof UIHasContentSettings hasContentSettings)
            {
                contents.add(hasContentSettings.getContent());
            }
        }

        return false;
    }

    /**
     * Создает одно-колоночный контейнер и добавляет в него контенты из переданного списка
     * @param contents список контентов
     * @return контейнер
     */
    public static UIContainer createOneColumnContainer(List<UIContent> contents)
    {
        return createOneColumnContainer("default-container", contents);
    }

    /**
     * Создает одно-колоночный контейнер и добавляет в него контенты из переданного списка
     * @param id идентификатор контейнера
     * @param contents список контентов
     * @return контейнер
     */
    public static UIContainer createOneColumnContainer(String id, List<UIContent> contents)
    {
        UIGrid grid = new UIGrid(List.of("1fr"));

        int row = 1;
        List<UIGridArea> areas = new ArrayList<>();
        for (UIContent content : contents)
        {
            UIGridArea area = new UIGridArea(content.getId(), 1, 2, row, ++row);
            areas.add(area);
        }
        grid.setAreas(areas);
        UIGridLayout layout = new UIGridLayout(List.of(grid));

        return new UIContainer(id, contents, layout);
    }

    private UIContentUtils()
    {
    }
}