package ru.naumen.ui.web.rules;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.csrf.CsrfException;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.sec.AccessDeniedHandlerRule;
import ru.naumen.generated.model.ErrorItemDto;
import ru.naumen.ui.api.exceptions.ExceptionsResolver;
import ru.naumen.ui.api.mapper.UIErrorMapperGroup.UIErrorMapper;
import ru.naumen.ui.models.error.ErrorItem;
import ru.naumen.ui.web.Paths;

/**
 * Правило обработки некорректного csrf-токена для запросов интерфейса 2.0.
 * Возвращается 401 код ответа
 *
 * <AUTHOR>
 * @since 03.04.2024
 */
@Component
public class UICsrfHandlerRule implements AccessDeniedHandlerRule
{
    private static final Logger LOG = LoggerFactory.getLogger(UICsrfHandlerRule.class);

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ExceptionsResolver exceptionsResolver;
    private final UIErrorMapper mapper;

    @Inject
    public UICsrfHandlerRule(ExceptionsResolver exceptionsResolver, UIErrorMapper mapper)
    {
        this.exceptionsResolver = exceptionsResolver;
        this.mapper = mapper;
    }

    @Override
    public int getRulePriority()
    {
        return AccessDeniedHandlerRule.MAX_PRIORITY;
    }

    @Override
    public boolean doCheck(HttpServletRequest request, HttpServletResponse response,
            AccessDeniedException exception)
    {
        // Обработать только те запросы, которые пришли по api интерфейса 2.0 и упали из-за csrf-токена
        if (exception instanceof CsrfException && request.getRequestURI().contains(Paths.UI_API_PATH))
        {
            try
            {
                // NSDPRD-30987 Решение ниже нужно проверить с асинхронными сервлетами
                ErrorItem errorItem = exceptionsResolver.resolve(exception);
                ErrorItemDto errorItemDto = mapper.convert(errorItem);
                response.setCharacterEncoding(StandardCharsets.UTF_8.name());
                response.setContentType(MediaType.APPLICATION_JSON_VALUE);
                response.getWriter().print(objectMapper.writeValueAsString(errorItemDto));
                response.setStatus(HttpStatus.CONFLICT.value());
                return true;
            }
            catch (IOException e)
            {
                LOG.error("Error write response", e);
                return false;
            }
        }

        return false;
    }
}
