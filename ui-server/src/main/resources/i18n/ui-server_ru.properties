ui.common.canNotBeNull={0} не может быть null
ui.common.notFound=Oбъект не найден: uuid={0}

ui.content.employee.alias=Сотрудник

ui.error.api.badRequest.userMessage=Некорректный запрос
ui.error.api.field.tooManyItems.single.userMessage=Произошла ошибка при открытии формы из-за большого количества выбранных элементов в атрибуте {0}. Обратитесь к администратору для решения данной ситуации
ui.error.api.field.tooManyItems.multiple.userMessage=Произошла ошибка при открытии формы из-за большого количества выбранных элементов в атрибутах: {0}. Обратитесь к администратору для решения данной ситуации
ui.error.api.field.tooManyItems.hint.single.userMessage=Количество выбранных элементов в атрибуте "{0}" превышает допустимый максимум. Пожалуйста, уменьшите количество выбранных элементов до {1}
ui.error.api.field.tooManyItems.hint.multiple.userMessage=Количество выбранных элементов в атрибутах "{0}" превышает допустимый максимум. Пожалуйста, уменьшите количество выбранных элементов до {1}
ui.error.api.field.tooManyItems.fullSubtree.hint.userMessage=Невозможно выбрать данный элемент, так как он содержит более {0} вложенных элементов
ui.error.api.forbidden.userMessage=Доступ запрещен
ui.error.api.internalServerError.userMessage=Внутренняя ошибка сервера
ui.error.api.methodNotAllowed.userMessage=Метод не поддерживается
ui.error.api.notFound.userMessage=Ресурс не найден
ui.error.api.unauthorized.userMessage=Ошибка авторизации
ui.error.api.unsupportedMediaType.userMessage=Неподдерживаемый тип данных

ui.error.api.badRequest.userDetails=Возникла проблема с запросом. Обновите страницу. Если проблема не решится, обратитесь в техподдержку.
ui.error.api.unauthorized.userDetails=Требуется авторизация. Обновите страницу и введите данные для входа.
ui.error.api.forbidden.userDetails=Доступ закрыт. Обратитесь к администратору.
ui.error.api.notFound.page.userDetails=Не можем найти страницу. Возможно, она была удалена или еще не создана.
ui.error.api.notFound.content.userDetails=Содержимое не найдено. Возможно, оно было удалено или еще не создано.
ui.error.api.notFound.element.userDetails=Не можем найти элемент. Возможно, он был удален или еще не создан.
ui.error.api.methodNotAllowed.userDetails=Произошла неизвестная ошибка. Обновите страницу позже. Если проблема не решится, обратитесь в техподдержку.
ui.error.api.unsupportedMediaType.userDetails=Произошла неизвестная ошибка. Обновите страницу позже. Если проблема не решится, обратитесь в техподдержку.
ui.error.api.internalServerError.userDetails=Произошла внутренняя ошибка сервера. Обновите страницу позже. Если проблема не решится, обратитесь в техподдержку.

ui.error.defaultMessage=Внутренняя ошибка сервера. Обратитесь к администратору системы.
ui.error.file.upload.antivirus=Файл не был загружен. Файл является вредоносным.
ui.error.file.upload.antivirusWithDetails=Файл не был загружен. Файл является вредоносным: {0}.
ui.error.file.upload.anyTroubles=Файл не был загружен. Возможные причины: размер файла больше допустимого, либо файловая система сервера недоступна для записи, либо файл является вредоносным.
ui.error.file.upload.emptyFile=Файл не был загружен. Нельзя загрузить пустой файл.
ui.error.file.upload.filesystem=Файл не был загружен. Файловая система сервера недоступна, обратитесь к администратору.
ui.error.file.upload.overflowMaxSize=Файл не был загружен. Размер файла превышает ограничение, заданное администратором системы.
ui.error.file.upload.unexpectedExtension=Файл не был загружен. Недопустимый тип файла.
ui.error.content.list.standalone.wrongParams=Параметры отображения списка некорректны (не найден объект связи). Для решения проблемы обратитесь к администратору.


ui.form.addObject=Добавление объекта
ui.form.deleteObject=Подтверждение удаления
ui.form.deleteMessage=Вы действительно хотите удалить объект?
ui.form.editObject=Редактирование объекта
ui.form.addComment=Добавления комментария
ui.form.editComment=Редактирование комментария

ui.page.search.caption=Результаты поиска "{0}"
ui.page.search.class.caption={0}: результаты поиска "{1}" по всем полям"
ui.page.search.class.manyObjectsAttention=Найдено более {0} объектов. В результатах поиска отображаются первые {0}. Уточните параметры поиска.
ui.page.module.licenseExpired=Истекла лицензия на встроенное приложение  "{0}". Обратитесь к администратору системы.

ui.menu.item.hiddenTitle=Название скрыто

ui.eventaction.execute.title.success=Информация
ui.eventaction.execute.title.error=Ошибка
ui.eventaction.execute.async.executing=Действие "'{{buttonTitle}}'" запускается
ui.eventaction.execute.async.success=Действие "'{{buttonTitle}}'" запущено
ui.eventaction.execute.sync.executing=Действие "'{{buttonTitle}}'" выполняется
ui.eventaction.execute.sync.success=Действие "'{{buttonTitle}}'" выполнено
ui.eventaction.execute.sync.error=При выполнении действия "'{{buttonTitle}}'" произошла ошибка:

ui.unknown=??? ???
