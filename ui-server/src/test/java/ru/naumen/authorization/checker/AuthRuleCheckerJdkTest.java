package ru.naumen.authorization.checker;

import static ru.naumen.ui.utils.UITestUtils.randomString;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

import ru.naumen.authorization.AuthRuleService;
import ru.naumen.authorization.AuthorizationService;
import ru.naumen.authorization.rule.AndAuthRule;
import ru.naumen.authorization.rule.AuthRule;
import ru.naumen.authorization.rule.EverybodyAuthRule;
import ru.naumen.authorization.rule.NobodyRule;
import ru.naumen.authorization.rule.OrAuthRule;
import ru.naumen.authorization.rule.ProfileAuthRule;
import ru.naumen.authorization.rule.TagAuthRule;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.ui.InitMocksBeforeEachBaseJdkTest;
import ru.naumen.ui.utils.BoTestUtils;

/**
 * Тестирование классов проверки доступа на основе правил {@link AuthRuleChecker}
 *
 * <AUTHOR>
 * @since 05.12.2023
 */
public class AuthRuleCheckerJdkTest extends InitMocksBeforeEachBaseJdkTest
{
    @Mock
    private AuthorizationService authService;
    @Mock
    private AuthRuleService authRuleService;

    private CoreBusinessObject bo;

    @Before
    public void setUp()
    {
        bo = BoTestUtils.mockBO();
    }

    /**
     * Тестирование {@link NobodyAuthRuleChecker}
     */
    @Test
    public void testNobodyAuthRuleChecker()
    {
        NobodyAuthRuleChecker nobodyAuthRuleChecker = new NobodyAuthRuleChecker();
        NobodyRule rule = new NobodyRule();

        Assert.assertFalse("Доступ должен быть запрещен", nobodyAuthRuleChecker.hasPermission(rule, bo.getMetaClass()
                , bo));
    }

    /**
     * Тестирование {@link EverybodyAuthRuleChecker}
     */
    @Test
    public void testEverybodyAuthRuleChecker()
    {
        EverybodyAuthRuleChecker nobodyRuleChecker = new EverybodyAuthRuleChecker();
        EverybodyAuthRule rule = new EverybodyAuthRule();

        Assert.assertTrue("Доступ должен быть разрешен", nobodyRuleChecker.hasPermission(rule, bo.getMetaClass(), bo));
    }

    /**
     * Тестирование {@link ProfileAuthRuleChecker}
     */
    @Test
    public void testProfileAuthRuleChecker()
    {
        ProfileAuthRuleChecker profileAuthRuleChecker = new ProfileAuthRuleChecker(authService);

        String profileCode = randomString();
        ProfileAuthRule profileAuthRule = new ProfileAuthRule(profileCode);

        Mockito.when(authService.hasProfile(bo, profileCode)).thenReturn(true);
        Mockito.when(authService.hasProfile(bo.getMetaClass(), profileCode)).thenReturn(true);

        Assert.assertTrue("Доступ должен быть разрешен",
                profileAuthRuleChecker.hasPermission(profileAuthRule, bo.getMetaClass(), bo));

        Mockito.when(authService.hasProfile(bo, profileCode)).thenReturn(false);
        Mockito.when(authService.hasProfile(bo.getMetaClass(), profileCode)).thenReturn(false);

        Assert.assertFalse("Доступ должен быть запрещен",
                profileAuthRuleChecker.hasPermission(profileAuthRule, bo.getMetaClass(), bo));
    }

    /**
     * Тестирование {@link TagAuthRuleChecker}
     */
    @Test
    public void testTagAuthRuleChecker()
    {
        TagAuthRuleChecker tagAuthRuleChecker = new TagAuthRuleChecker(authService);

        String tagCode = randomString();
        TagAuthRule tagAuthRule = new TagAuthRule(List.of(tagCode));

        Mockito.when(authService.isElementEnabled(tagAuthRule)).thenReturn(true);

        Assert.assertTrue("Доступ должен быть запрещен",
                tagAuthRuleChecker.hasPermission(tagAuthRule, bo.getMetaClass(), bo));

        Mockito.when(authService.isElementEnabled(tagAuthRule)).thenReturn(false);

        Assert.assertFalse("Доступ должен быть разрешен",
                tagAuthRuleChecker.hasPermission(tagAuthRule, bo.getMetaClass(), bo));
    }

    /**
     * Тестирование {@link AndAuthRuleChecker}
     */
    @Test
    public void testAndAuthRuleChecker()
    {
        AndAuthRuleChecker andAuthRuleChecker = new AndAuthRuleChecker(authRuleService);

        AuthRule authRule1 = Mockito.mock(AuthRule.class);
        AuthRule authRule2 = Mockito.mock(AuthRule.class);

        AndAuthRule andAuthRule = new AndAuthRule(List.of(authRule1, authRule2));

        CoreClassFqn metaClass = bo.getMetaClass();

        Mockito.when(authRuleService.hasPermission(authRule1, metaClass, bo)).thenReturn(true);
        Mockito.when(authRuleService.hasPermission(authRule2, metaClass, bo)).thenReturn(true);

        Assert.assertTrue("Доступ должен быть разрешен", andAuthRuleChecker.hasPermission(andAuthRule, metaClass, bo));

        Mockito.when(authRuleService.hasPermission(authRule1, metaClass, bo)).thenReturn(false);
        Assert.assertFalse("Доступ должен быть запрещен", andAuthRuleChecker.hasPermission(andAuthRule, metaClass, bo));

        Mockito.when(authRuleService.hasPermission(authRule2, metaClass, bo)).thenReturn(false);
        Assert.assertFalse("Доступ должен быть запрещен", andAuthRuleChecker.hasPermission(andAuthRule, metaClass, bo));
    }

    /**
     * Тестирование {@link OrAuthRuleChecker}
     */
    @Test
    public void testOrAuthRuleChecker()
    {
        OrAuthRuleChecker andAuthRuleChecker = new OrAuthRuleChecker(authRuleService);

        AuthRule authRule1 = Mockito.mock(AuthRule.class);
        AuthRule authRule2 = Mockito.mock(AuthRule.class);

        OrAuthRule orAuthRule = new OrAuthRule(List.of(authRule1, authRule2));

        CoreClassFqn metaClass = bo.getMetaClass();

        Mockito.when(authRuleService.hasPermission(authRule1, metaClass, bo)).thenReturn(true);
        Mockito.when(authRuleService.hasPermission(authRule2, metaClass, bo)).thenReturn(true);

        Assert.assertTrue("Доступ должен быть разрешен", andAuthRuleChecker.hasPermission(orAuthRule, metaClass, bo));

        Mockito.when(authRuleService.hasPermission(authRule1, metaClass, bo)).thenReturn(false);
        Assert.assertTrue("Доступ должен быть разрешен", andAuthRuleChecker.hasPermission(orAuthRule, metaClass, bo));

        Mockito.when(authRuleService.hasPermission(authRule2, metaClass, bo)).thenReturn(false);
        Assert.assertFalse("Доступ должен быть запрещен", andAuthRuleChecker.hasPermission(orAuthRule, metaClass, bo));
    }
}