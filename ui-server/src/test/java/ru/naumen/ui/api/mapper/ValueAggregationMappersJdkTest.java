package ru.naumen.ui.api.mapper;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.AGGREGATE_TYPE_CODE;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.RESPONSIBLE_TYPE_CODE;
import static ru.naumen.ui.utils.UITestUtils.randomLong;
import static ru.naumen.ui.utils.UITestUtils.randomString;

import java.util.List;

import org.junit.Test;
import org.mockito.Mock;

import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.server.attrdescription.resolvers.CoreAggregateResolver;
import ru.naumen.core.shared.CoreAggregateContainer;
import ru.naumen.ui.api.utils.UuidUtils;
import ru.naumen.ui.services.value.adapter.corevalue.AttrValueAggregateAdapter;
import ru.naumen.ui.services.value.adapter.corevalue.AttrValueBOAdapter;
import ru.naumen.ui.services.value.adapter.corevalue.AttrValueResponsibleAdapter;
import ru.naumen.ui.services.value.adapter.corevalue.AttributeValueAdapter;
import ru.naumen.ui.services.value.adapter.summary.ValueAggregateSummaryAdapter;
import ru.naumen.ui.services.value.adapter.summary.ValueSummaryAdapter;
import ru.naumen.ui.services.value.delegate.BOValueModelDelegate;
import ru.naumen.ui.settings.entity.bo.value.Value.ResolvedValue;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueAggregated;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueBOUuidList;
import ru.naumen.ui.settings.entity.common.UIClassFqn;
import ru.naumen.ui.utils.BoTestUtils;

/**
 * Тесты для проверки конвертации значений атрибутов/полей типов "Агрегирующий" и "Ответственный" в
 * {@link ResolvedValue} и обратно
 *
 * <AUTHOR>
 * @since 19.09.2024
 */
public class ValueAggregationMappersJdkTest extends AbstractValueMappersTest
{
    @Mock
    private CoreAggregateResolver aggregateResolver;

    private final long employeeId = randomLong();
    private final String employeeFqn = "employee" + UuidUtils.UUID_DELIMITER + randomString();
    private final String employeeTitle = "employee_" + randomString();

    private final long oulId = randomLong();
    private final String ouFqn = "ou" + UuidUtils.UUID_DELIMITER + randomString();
    private final String ouTitle = "ou_" + randomString();

    private final long teamId = randomLong();
    private final String teamFqn = "team" + UuidUtils.UUID_DELIMITER + randomString();
    private final String teamTitle = "team_" + randomString();

    @Override
    protected List<AttributeValueAdapter<?, ?>> prepareAttributeValueAdapter()
    {
        List<BOValueModelDelegate<?>> delegates = getBOValueModelDelegates();
        AttrValueBOAdapter boAdapter = new AttrValueBOAdapter(delegates, systemNameService);
        return List.of(
                new AttrValueAggregateAdapter(boAdapter),
                new AttrValueResponsibleAdapter(boAdapter));
    }

    @Override
    protected List<ValueSummaryAdapter<?, ?>> prepareSummaryAdapters()
    {
        return List.of(new ValueAggregateSummaryAdapter(aggregateResolver));
    }

    @Override
    protected List<TestValueEntry> getTestValueEntries()
    {
        return List.of(
                //Агрегирующий
                createAggregationEntry(),
                //Ответственный
                createResponsibleEntry());
    }

    /**
     * Тестирование маппинга core-значения в значение поля {@link ResolvedValue}.
     */
    @Test
    public void testMapValueToFieldValue()
    {
        checkCoreValueMappingToFieldValue();
    }

    /**
     * @return тестовые данные для проверки атрибута типа "Агрегирующий"
     */
    private TestValueEntry createAggregationEntry()
    {
        CoreAggregateContainer value = mock(CoreAggregateContainer.class);
        CoreBusinessObject employee = BoTestUtils.mockBO(new UIClassFqn(employeeFqn), employeeId, employeeTitle);
        CoreBusinessObject ou = BoTestUtils.mockBO(new UIClassFqn(ouFqn), oulId, ouTitle);
        CoreBusinessObject team = BoTestUtils.mockBO(new UIClassFqn(teamFqn), teamId, teamTitle);
        when(value.getEmployee()).thenReturn(employee);
        when(value.getOu()).thenReturn(ou);
        when(value.getTeam()).thenReturn(team);
        when(value.getMain()).thenReturn(employee);
        mockBoMetaClass(employee, null);
        mockBoMetaClass(ou, null);
        mockBoMetaClass(team, null);

        ValueBOUuidList uuids =
                new ValueBOUuidList(List.of(ou.getUUID(), team.getUUID(), employee.getUUID()));
        ResolvedValue fieldValue =
                new ValueAggregated(employee.getUUID(), false, employee.getTitle(), uuids);

        return new TestValueEntry()
                .value(value)
                .resolvedValue(fieldValue)
                .attribute(mockAttribute("aggrAttr", AGGREGATE_TYPE_CODE))
                .mappingFieldMode(MappingFieldMode.TO_FIELD_VALUE)
                .asSummaryValue(true);
    }

    /**
     * @return тестовые данные для проверки атрибута типа "Ответственный"
     */
    private TestValueEntry createResponsibleEntry()
    {
        CoreAggregateContainer value = mock(CoreAggregateContainer.class);
        CoreBusinessObject employee = BoTestUtils.mockBO(new UIClassFqn(employeeFqn), employeeId, employeeTitle);
        mockBoMetaClass(employee, null);
        when(value.getEmployee()).thenReturn(employee);
        when(value.getOu()).thenReturn(null);
        when(value.getTeam()).thenReturn(null);
        when(value.getMain()).thenReturn(employee);

        ValueBOUuidList uuids = new ValueBOUuidList(List.of(employee.getUUID()));
        ResolvedValue fieldValue =
                new ValueAggregated(employee.getUUID(), false, employee.getTitle(), uuids);

        return new TestValueEntry()
                .value(value)
                .resolvedValue(fieldValue)
                .attribute(mockAttribute("respAttr", RESPONSIBLE_TYPE_CODE))
                .mappingFieldMode(MappingFieldMode.TO_FIELD_VALUE)
                .asSummaryValue(true);
    }
}
