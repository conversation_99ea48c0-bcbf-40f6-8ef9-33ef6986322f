package ru.naumen.ui.services.filter.condition.contains;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BACK_LINK_TYPE_CODE;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BO_LINKS_TYPE_CODE;
import static ru.naumen.ui.utils.BoTestUtils.mockBO;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.ui.services.filter.condition.ConditionControllerJdkTestBase;
import ru.naumen.ui.services.datapart.UIObjectDataPart;
import ru.naumen.ui.settings.entity.bo.value.Value.VoidValue;

/**
 * Тестирование контроллера для условия "Содержит текущий объект"
 * {@link ContainsSubjectConditionController}
 *
 * <AUTHOR>
 * @since 10.04.2024
 */
@RunWith(Parameterized.class)
public class ContainsSubjectConditionControllerJdkTest extends ConditionControllerJdkTestBase
{
    private CoreBusinessObject currentObject;

    @Parameters
    public static Collection<Object[]> data()
    {
        CoreBusinessObject currentObject = mockBO();
        return Arrays.asList(new Object[][] {
                { BO_LINKS_TYPE_CODE, null, currentObject, false },
                { BO_LINKS_TYPE_CODE, List.of(), currentObject, false },
                { BO_LINKS_TYPE_CODE, List.of(currentObject, mockBO()), currentObject, true },
                { BO_LINKS_TYPE_CODE, List.of(mockBO()), currentObject, false },
                { BO_LINKS_TYPE_CODE, null, null, false },
                { BO_LINKS_TYPE_CODE, List.of(mockBO()), null, false },

                { BACK_LINK_TYPE_CODE, null, currentObject, false },
                { BACK_LINK_TYPE_CODE, List.of(), currentObject, false },
                { BACK_LINK_TYPE_CODE, List.of(currentObject, mockBO()), currentObject, true },
                { BACK_LINK_TYPE_CODE, List.of(mockBO()), currentObject, false },
                { BO_LINKS_TYPE_CODE, null, null, false },
                { BO_LINKS_TYPE_CODE, List.of(mockBO()), null, false },
        });
    }

    @Before
    public void setUp()
    {
        super.setUp();
    }

    public ContainsSubjectConditionControllerJdkTest(String attrType, Object attrValue,
            CoreBusinessObject currentObject,
            boolean expected)
    {
        super(attrType, attrValue, new VoidValue(), expected);
        this.currentObject = currentObject;
    }

    @Test
    public void testChecker()
    {
        dataParts.add(new UIObjectDataPart(currentObject));
        testChecker(new ContainsSubjectConditionController());
    }
}
