package ru.naumen.ui.services.filter.condition.contains;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BACK_LINK_TYPE_CODE;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BO_LINKS_TYPE_CODE;
import static ru.naumen.ui.utils.BoTestUtils.mockEmployee;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;
import org.mockito.Mockito;

import ru.naumen.core.bo.CoreEmployee;
import ru.naumen.core.services.CoreAuthenticationService;
import ru.naumen.ui.services.filter.condition.ConditionControllerJdkTestBase;
import ru.naumen.ui.settings.entity.bo.value.Value.VoidValue;

/**
 * Тестирование контроллера для условия "Не содержит текущего пользователя"
 * {@link NotContainsUserConditionController}
 *
 * <AUTHOR>
 * @since 10.04.2024
 */
@RunWith(Parameterized.class)
public class NotContainsUserConditionControllerJdkTest extends ConditionControllerJdkTestBase
{
    private CoreAuthenticationService authenticationService;

    @Parameters
    public static Collection<Object[]> data()
    {
        CoreEmployee currentUser = mockEmployee();
        return Arrays.asList(new Object[][] {
                { BO_LINKS_TYPE_CODE, null, currentUser, true },
                { BO_LINKS_TYPE_CODE, List.of(), currentUser, true },
                { BO_LINKS_TYPE_CODE, List.of(currentUser, mockEmployee()), currentUser, false },
                { BO_LINKS_TYPE_CODE, List.of(mockEmployee()), currentUser, true },
                // проверяем случай когда текущий пользовать - суперпользователь (null)
                { BO_LINKS_TYPE_CODE, null, null, true },
                { BO_LINKS_TYPE_CODE, List.of(mockEmployee()), null, true },

                { BACK_LINK_TYPE_CODE, null, currentUser, true },
                { BACK_LINK_TYPE_CODE, List.of(), currentUser, true },
                { BACK_LINK_TYPE_CODE, List.of(currentUser, mockEmployee()), currentUser, false },
                { BACK_LINK_TYPE_CODE, List.of(mockEmployee()), currentUser, true },
                // проверяем случай когда текущий пользовать - суперпользователь (null)
                { BO_LINKS_TYPE_CODE, null, null, true },
                { BO_LINKS_TYPE_CODE, List.of(mockEmployee()), null, true },
        });
    }

    public NotContainsUserConditionControllerJdkTest(String attrType, Object attrValue, CoreEmployee currentUser,
            boolean expected)
    {
        super(attrType, attrValue, new VoidValue(), expected);
        authenticationService = Mockito.mock(CoreAuthenticationService.class);
        Mockito.when(authenticationService.getCurrentEmployee()).thenReturn(currentUser);
    }

    @Test
    public void testChecker()
    {
        testChecker(new NotContainsUserConditionController(authenticationService));
    }
}
