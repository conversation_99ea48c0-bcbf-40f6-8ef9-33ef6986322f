package ru.naumen.ui.services.filter.condition.contains;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BACK_LINK_TYPE_CODE;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BO_LINKS_TYPE_CODE;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BO_LINK_TYPE_CODE;
import static ru.naumen.ui.utils.BoTestUtils.mockBO;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.ui.services.filter.condition.ConditionControllerJdkTestBase;
import ru.naumen.ui.settings.entity.bo.value.Value.ConditionValue;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueBOUuid;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueBOUuidList;

/**
 * Тестирование контроллера для условия "Не содержит (включая архивные)"
 * {@link NotContainsWithRemovedConditionController}
 *
 * <AUTHOR>
 * @since 17.05.2024
 */
@RunWith(Parameterized.class)
public class NotContainsWithRemovedConditionControllerJdkTest extends ConditionControllerJdkTestBase
{
    @Parameters
    public static Collection<Object[]> data()
    {
        CoreBusinessObject bo1 = mockBO();
        CoreBusinessObject bo2 = mockBO();

        return Arrays.asList(new Object[][] {

                { BO_LINK_TYPE_CODE, null, new ValueBOUuid(bo1.getUUID()), true },
                { BO_LINK_TYPE_CODE, bo1, new ValueBOUuid(bo1.getUUID()), false },
                { BO_LINK_TYPE_CODE, bo2, new ValueBOUuid(bo1.getUUID()), true },

                { BO_LINKS_TYPE_CODE, null, new ValueBOUuidList(bo1.getUUID(), bo2.getUUID()), true },
                { BO_LINKS_TYPE_CODE, List.of(), new ValueBOUuidList(bo1.getUUID(), bo2.getUUID()),
                        true },
                { BO_LINKS_TYPE_CODE, List.of(bo1), new ValueBOUuidList(bo1.getUUID(), bo2.getUUID()),
                        false },
                { BO_LINKS_TYPE_CODE, List.of(bo2), new ValueBOUuidList(bo1.getUUID()), true },

                { BACK_LINK_TYPE_CODE, null, new ValueBOUuidList(bo1.getUUID(), bo2.getUUID()), true },
                { BACK_LINK_TYPE_CODE, List.of(), new ValueBOUuidList(bo1.getUUID(), bo2.getUUID()),
                        true },
                { BACK_LINK_TYPE_CODE, List.of(bo1), new ValueBOUuidList(bo1.getUUID(), bo2.getUUID()),
                        false },
                { BACK_LINK_TYPE_CODE, List.of(bo2), new ValueBOUuidList(bo1.getUUID()), true },
        });
    }

    public NotContainsWithRemovedConditionControllerJdkTest(String attrType, Object attrValue,
            ConditionValue conditionValue, boolean expected)
    {
        super(attrType, attrValue, conditionValue, expected);
    }

    @Test
    public void testChecker()
    {
        testChecker(new NotContainsWithRemovedConditionController(coreFilterFactory, attributeChainHelper));
    }
}
