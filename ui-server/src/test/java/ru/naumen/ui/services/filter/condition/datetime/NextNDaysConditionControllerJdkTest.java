package ru.naumen.ui.services.filter.condition.datetime;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.DATE_TIME_TYPE_CODE;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.DATE_TYPE_CODE;

import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collection;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.ui.settings.entity.bo.value.Value.ConditionValue;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueInteger;

/**
 * Тестирование контроллера для условия "В ближайшие n дней" {@link NextNDaysConditionController}
 *
 * <AUTHOR>
 * @since 01.05.2024
 */
@RunWith(Parameterized.class)
public class NextNDaysConditionControllerJdkTest extends DateTimeConditionControllerJdkTestBase
{
    @Parameters
    public static Collection<Object[]> data()
    {
        return Arrays.asList(new Object[][] {

                // Атрибут типа "Дата" не учитывает клиентскую ТЗ. Поэтому могут быть случаи когда в клиентской ТЗ
                // сегодняшняя дата уже сменилась, а дата в атрибуте не учитывает клиентскую ТЗ и оказывается в прошлом
                { DATE_TYPE_CODE, null, "2024-05-04T00:00:00.000+03:00", EKAT_TZ, new ValueInteger(2), false },
                { DATE_TYPE_CODE, "2024-05-07T00:00:00.000+03:00", "2024-05-04T00:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(2), false }, // разница больше 2х дней
                { DATE_TYPE_CODE, "2024-05-07T00:00:00.000+03:00", "2024-05-04T23:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(2), true }, // разница 2 дня (в Екб уже 05)
                { DATE_TYPE_CODE, "2024-05-07T00:00:00.000+03:00", "2024-05-04T23:00:00.000+03:00", LISBON_TZ,
                        new ValueInteger(2), false }, // разница 2 дня (в Лиссабоне еще 04)
                { DATE_TYPE_CODE, "2024-05-07T00:00:00.000+03:00", "2024-05-05T00:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(2), true }, // разница 2 дня
                { DATE_TYPE_CODE, "2024-05-07T00:00:00.000+03:00", "2024-05-06T00:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(2), true }, // разница 1 день
                { DATE_TYPE_CODE, "2024-05-07T00:00:00.000+03:00", "2024-05-07T00:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(2), true }, // разница 0 дней
                { DATE_TYPE_CODE, "2024-05-07T00:00:00.000+03:00", "2024-05-07T23:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(2), false }, // дата в прошлом (в Екб уже 08)
                { DATE_TYPE_CODE, "2024-05-07T00:00:00.000+03:00", "2024-05-07T23:00:00.000+03:00", LISBON_TZ,
                        new ValueInteger(2), true }, // разница 0 дней (в Лиссабоне еще 07)
                { DATE_TYPE_CODE, "2024-05-07T00:00:00.000+03:00", "2024-05-08T00:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(2), false }, // дата в прошлом
                { DATE_TYPE_CODE, "2024-05-07T00:00:00.000+03:00", "2024-05-08T00:00:00.000+03:00", LISBON_TZ,
                        new ValueInteger(2), true }, // разница 0 дней (в Лиссабоне еще 07)
                { DATE_TYPE_CODE, "2024-05-07T00:00:00.000+03:00", "2024-05-08T04:00:00.000+03:00", LISBON_TZ,
                        new ValueInteger(2), false }, // дата в прошлом

                // Атрибут типа "Дата/время" учитывает клиентскую ТЗ. Поэтому обе даты (сегодня и в значении
                // атрибута) приводятся к единой ТЗ и между ними считается кол-во дней
                { DATE_TIME_TYPE_CODE, null, "2024-05-05T11:59:59.000+03:00", EKAT_TZ, new ValueInteger(2),
                        false },
                { DATE_TIME_TYPE_CODE, "2024-05-07T12:00:00.000+03:00", "2024-05-05T11:59:59.000+03:00", EKAT_TZ,
                        new ValueInteger(2), false }, // разница больше 2х дней
                { DATE_TIME_TYPE_CODE, "2024-05-07T12:00:00.000+03:00", "2024-05-05T11:59:59.000+03:00", LISBON_TZ,
                        new ValueInteger(2), false }, // разница больше 2х дней
                { DATE_TIME_TYPE_CODE, "2024-05-07T12:00:00.000+03:00", "2024-05-05T12:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(2), true }, // разница 2 дня
                { DATE_TIME_TYPE_CODE, "2024-05-07T12:00:00.000+03:00", "2024-05-05T12:00:00.000+03:00", LISBON_TZ,
                        new ValueInteger(2), true }, // разница 2 дня
                { DATE_TIME_TYPE_CODE, "2024-05-07T12:00:00.000+03:00", "2024-05-06T12:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(2), true }, // разница 1 день
                { DATE_TIME_TYPE_CODE, "2024-05-07T12:00:00.000+03:00", "2024-05-07T12:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(2), true }, // разница 0 дней
                { DATE_TIME_TYPE_CODE, "2024-05-07T12:00:00.000+03:00", "2024-05-07T12:00:00.000+03:00", LISBON_TZ,
                        new ValueInteger(2), true }, // разница 0 дней
                { DATE_TIME_TYPE_CODE, "2024-05-07T12:00:00.000+03:00", "2024-05-07T12:00:01.000+03:00", EKAT_TZ,
                        new ValueInteger(2), false }, // дата в прошлом
                { DATE_TIME_TYPE_CODE, "2024-05-07T12:00:00.000+03:00", "2024-05-07T12:00:01.000+03:00", LISBON_TZ,
                        new ValueInteger(2), false }, //  дата в прошлом
                { DATE_TIME_TYPE_CODE, "2024-05-07T12:00:00.000+03:00", "2024-05-17T12:00:01.000+03:00", EKAT_TZ,
                        new ValueInteger(2), false }, // дата в прошлом
        });
    }

    public NextNDaysConditionControllerJdkTest(String attrType, String attrValue, String currentTime,
            ZoneId userTZ, ConditionValue conditionValue, boolean expected)
    {
        super(attrType, attrValue, currentTime, userTZ, conditionValue, expected);
    }

    @Test
    public void testChecker()
    {
        testChecker(new NextNDaysConditionController(coreFilterFactory));
    }
}