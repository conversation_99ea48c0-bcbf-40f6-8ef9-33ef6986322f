package ru.naumen.ui.services.filter.condition.empty;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.*;
import static ru.naumen.ui.utils.BoTestUtils.mockAggregContainer;
import static ru.naumen.ui.utils.BoTestUtils.mockBO;
import static ru.naumen.ui.utils.CatalogTestUtils.mockCatalogItem;
import static ru.naumen.ui.utils.DateTimeIntervalUtils.mockDateTimeInterval;
import static ru.naumen.ui.utils.HyperlinkTestUtils.mockHyperLink;
import static ru.naumen.ui.utils.MetaClassesTestUtils.mockClassFqn;
import static ru.naumen.ui.utils.SourceCodeTestUtils.mockSourceCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.ui.services.filter.condition.ConditionControllerJdkTestBase;
import ru.naumen.ui.settings.entity.bo.value.Value.VoidValue;

/**
 * Тестирование контроллера для условия "Пусто" {@link EmptyConditionController}
 *
 * <AUTHOR>
 * @since 10.04.2024
 */
@RunWith(Parameterized.class)
public class EmptyConditionControllerJdkTest extends ConditionControllerJdkTestBase
{
    @Parameters
    public static Collection<Object[]> data()
    {
        List<Object> listWithNullElement = new ArrayList<>();
        listWithNullElement.add(null);

        return Arrays.asList(new Object[][] {
                { STRING_TYPE_CODE, "", true },
                { STRING_TYPE_CODE, null, true },
                { STRING_TYPE_CODE, "Значение", false },

                { TEXT_TYPE_CODE, "", true },
                { TEXT_TYPE_CODE, null, true },
                { TEXT_TYPE_CODE, "Значение", false },

                { RICHTEXT_TYPE_CODE, "", true },
                { RICHTEXT_TYPE_CODE, null, true },
                { RICHTEXT_TYPE_CODE, "<b>Значение</b>", false },

                { BO_LINKS_TYPE_CODE, null, true },
                { BO_LINKS_TYPE_CODE, List.of(), true },
                { BO_LINKS_TYPE_CODE, listWithNullElement, true },
                { BO_LINKS_TYPE_CODE, List.of(mockBO()), false },

                { BACK_LINK_TYPE_CODE, null, true },
                { BACK_LINK_TYPE_CODE, List.of(), true },
                { BACK_LINK_TYPE_CODE, listWithNullElement, true },
                { BACK_LINK_TYPE_CODE, List.of(mockBO()), false },

                { CATALOG_ITEMS_SET_TYPE_CODE, null, true },
                { CATALOG_ITEMS_SET_TYPE_CODE, List.of(), true },
                { CATALOG_ITEMS_SET_TYPE_CODE, listWithNullElement, true },
                { CATALOG_ITEMS_SET_TYPE_CODE, List.of(mockCatalogItem()), false },

                { CASE_LIST_TYPE_CODE, null, true },
                { CASE_LIST_TYPE_CODE, List.of(), true },
                { CASE_LIST_TYPE_CODE, listWithNullElement, true },
                { CASE_LIST_TYPE_CODE, List.of(mockClassFqn()), false },

                { SOURCECODE_TYPE_CODE, null, true },
                { SOURCECODE_TYPE_CODE, mockSourceCode(null, null), true },
                { SOURCECODE_TYPE_CODE, mockSourceCode("", ""), true },
                { SOURCECODE_TYPE_CODE, mockSourceCode(null, "java"), true },
                { SOURCECODE_TYPE_CODE, mockSourceCode("", "java"), true },
                { SOURCECODE_TYPE_CODE, mockSourceCode("return", null), false },
                { SOURCECODE_TYPE_CODE, mockSourceCode("return", ""), false },

                { HYPERLINK_TYPE_CODE, null, true },
                { HYPERLINK_TYPE_CODE, mockHyperLink(null, null), true },
                { HYPERLINK_TYPE_CODE, mockHyperLink("", ""), true },
                { HYPERLINK_TYPE_CODE, mockHyperLink(null, "text"), false },
                { HYPERLINK_TYPE_CODE, mockHyperLink("text", null), false },
                { HYPERLINK_TYPE_CODE, mockHyperLink("text", "text"), false },

                { AGGREGATE_TYPE_CODE, null, true },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(null, null, null), true },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(mockBO(), mockBO(), null), false },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(mockBO(), null, mockBO()), false },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(null, mockBO(), null), false },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(null, null, mockBO()), false },

                { RESPONSIBLE_TYPE_CODE, null, true },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(null, null, null), true },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(mockBO(), mockBO(), null), false },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(mockBO(), null, mockBO()), false },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(null, mockBO(), null), false },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(null, null, mockBO()), false },

                { BO_LINK_TYPE_CODE, null, true },
                { BO_LINK_TYPE_CODE, mockBO(), false },

                { CATALOG_ITEM_TYPE_CODE, null, true },
                { CATALOG_ITEM_TYPE_CODE, mockCatalogItem(), false },

                { INTEGER_TYPE_CODE, null, true },
                { INTEGER_TYPE_CODE, 0, false },
                { INTEGER_TYPE_CODE, 22, false },

                { DOUBLE_TYPE_CODE, null, true },
                { DOUBLE_TYPE_CODE, 0.0, false },
                { DOUBLE_TYPE_CODE, 1.2, false },

                { DATE_TYPE_CODE, null, true },
                { DATE_TYPE_CODE, new Date(), false },

                { DATE_TIME_TYPE_CODE, null, true },
                { DATE_TIME_TYPE_CODE, new Date(), false },

                { TIME_INTERVAL_TYPE_CODE, null, true },
                { TIME_INTERVAL_TYPE_CODE, mockDateTimeInterval(100, 1), false }
        });
    }

    public EmptyConditionControllerJdkTest(String attrType, Object attrValue, boolean expected)
    {
        super(attrType, attrValue, new VoidValue(), expected);
    }

    @Test
    public void testChecker()
    {
        testChecker(new EmptyConditionController(coreFilterFactory, attributeChainHelper));
    }
}
