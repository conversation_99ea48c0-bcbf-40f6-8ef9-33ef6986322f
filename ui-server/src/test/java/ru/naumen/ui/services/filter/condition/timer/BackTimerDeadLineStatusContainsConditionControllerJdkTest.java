package ru.naumen.ui.services.filter.condition.timer;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BACK_TIMER_TYPE_CODE;
import static ru.naumen.ui.utils.TimerTestUtils.mockBackTimer;

import java.util.Arrays;
import java.util.Collection;
import java.util.Date;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.ui.models.value.TimerStatusModel;
import ru.naumen.ui.services.filter.condition.ConditionControllerJdkTestBase;
import ru.naumen.ui.settings.entity.bo.value.Value.ConditionValue;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueBoolean;

/**
 * Тестирование контроллера для условия "Просроченность содержит"
 * {@link BackTimerDeadLineStatusContainsConditionController}
 *
 * <AUTHOR>
 * @since 19.05.2024
 */
@RunWith(Parameterized.class)
public class BackTimerDeadLineStatusContainsConditionControllerJdkTest extends ConditionControllerJdkTestBase
{
    @Parameters
    public static Collection<Object[]> data()
    {
        Date deadLine = createDate("3024-05-07T00:00:00.000+03:00");
        return Arrays.asList(new Object[][] {
                { null, new ValueBoolean(true), false },
                { null, new ValueBoolean(false), false },

                { mockBackTimer("state1", deadLine), new ValueBoolean(true), false },
                { mockBackTimer("state1", deadLine), new ValueBoolean(false), true },

                { mockBackTimer(TimerStatusModel.EXCEED.getCode(), deadLine), new ValueBoolean(true), true },
                { mockBackTimer(TimerStatusModel.EXCEED.getCode(), deadLine), new ValueBoolean(false), false },
        });
    }

    public BackTimerDeadLineStatusContainsConditionControllerJdkTest(Object attrValue, ConditionValue conditionValue,
            boolean expected)
    {
        super(BACK_TIMER_TYPE_CODE, attrValue, conditionValue, expected);
    }

    @Test
    public void testChecker()
    {
        testChecker(new BackTimerDeadLineStatusContainsConditionController());
    }
}
