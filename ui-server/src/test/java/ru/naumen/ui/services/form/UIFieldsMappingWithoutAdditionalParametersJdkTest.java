package ru.naumen.ui.services.form;

import java.util.List;

import org.junit.Test;

import ru.naumen.core.shared.CoreConstants.AttributeTypes;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.models.form.field.UIField;
import ru.naumen.ui.models.form.field.UIFieldCatalogItemTree;
import ru.naumen.ui.models.form.field.UIFieldCatalogItemsTree;
import ru.naumen.ui.models.form.field.UIFieldCheckbox;
import ru.naumen.ui.models.form.field.UIFieldHyperLink;
import ru.naumen.ui.models.form.field.UIFieldSwitch;
import ru.naumen.ui.settings.entity.form.field.UIFieldCatalogItemTreeSettings;
import ru.naumen.ui.settings.entity.form.field.UIFieldCatalogItemsTreeSettings;
import ru.naumen.ui.settings.entity.form.field.UIFieldCheckboxSettings;
import ru.naumen.ui.settings.entity.form.field.UIFieldHyperLinkSettings;
import ru.naumen.ui.settings.entity.form.field.UIFieldSettings;
import ru.naumen.ui.settings.entity.form.field.UIFieldSwitchSettings;

/**
 * Тестирование маппинга атрибутов со специальными настройками в поля, которые не имеют никаких дополнительных
 * данных, кроме общих для всех полей
 *
 * <AUTHOR>
 * @since 20.09.2024
 */
public class UIFieldsMappingWithoutAdditionalParametersJdkTest extends AbstractUIFieldMappingFromAttribute
{
    @Override
    protected List<TestFieldEntry> getTestFieldEntries()
    {
        return List.of(
                createSimpleUIFieldEntry(UIFieldCatalogItemTreeSettings.class, UIFieldCatalogItemTree.class,
                        AttributeTypes.CATALOG_ITEM_TYPE_CODE),
                createSimpleUIFieldEntry(UIFieldCatalogItemsTreeSettings.class, UIFieldCatalogItemsTree.class,
                        AttributeTypes.CATALOG_ITEMS_SET_TYPE_CODE),
                createSimpleUIFieldEntry(UIFieldCheckboxSettings.class, UIFieldCheckbox.class,
                        AttributeTypes.BOOLEAN_TYPE_CODE),
                createSimpleUIFieldEntry(UIFieldSwitchSettings.class, UIFieldSwitch.class,
                        AttributeTypes.BOOLEAN_TYPE_CODE),
                createSimpleUIFieldEntry(UIFieldHyperLinkSettings.class, UIFieldHyperLink.class,
                        AttributeTypes.HYPERLINK_TYPE_CODE)
        );
    }

    /**
     * Тестирование маппинга атрибутов в соответствующие им поля
     */
    @Test
    public void testFieldByAttributeEditPresentation()
    {
        checkFieldEntries();
    }

    private static TestFieldEntry createSimpleUIFieldEntry(Class<? extends UIFieldSettings> fieldSettingsType,
            Class<? extends UIField> expectedFieldType, String attributeTypeCode)
    {
        CoreAttribute attribute = mockAttribute(attributeTypeCode);
        UIFieldSettings settings = mockSettings(fieldSettingsType, attribute);

        return new TestFieldEntry()
                .attribute(attribute)
                .fieldSettings(settings)
                .checking(expectedFieldType, (actual, asserter) ->
                        checkBaseFieldProperties(asserter, actual, attribute, settings));
    }
}
