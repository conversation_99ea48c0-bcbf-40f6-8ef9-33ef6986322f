package ru.naumen.ui.services.processor.content;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static ru.naumen.ui.utils.UITestUtils.randomString;

import java.util.List;
import java.util.Objects;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

import ru.naumen.ui.models.content.UIContainer;
import ru.naumen.ui.models.content.UIContent;
import ru.naumen.ui.models.content.UIInformer;
import ru.naumen.ui.models.content.UIInformer.UIMessageType;
import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.ui.settings.entity.content.UIContainerSettings;
import ru.naumen.ui.settings.entity.content.UIContentSettings;
import ru.naumen.ui.settings.entity.content.UIInformerSettings;
import ru.naumen.ui.settings.entity.layout.UIGridLayout;
import ru.naumen.ui.utils.L10nTestUtils;
import ru.naumen.ui.utils.UIContentTestUtils;

/**
 * Тестирование процессора контента "Информационное сообщение" {@link UIInformerProcessor}
 *
 * <AUTHOR>
 * @since 21.03.2024
 */
public class UIInformerProcessorJdkTest extends AbstractUIContentProcessorJdkTest
{
    private UIContainerProcessor processor;
    @Mock
    protected UISettingsService settingsService;

    @Before
    @Override
    public void setUp()
    {
        super.setUp();

        processor = new UIContainerProcessor(settingsService, l10nService);
        processor.init(processingService, authRuleService, dataFilterService);
    }

    /**
     * Тестирование построения контента "Информационное сообщение" с разными типами
     */
    @Test
    public void testContainerProcessingWithCaption()
    {
        //создаем настройки контентов
        UIContentSettings informer1 = UIContentTestUtils.createInformerSettings(
                appId,
                pageId,
                randomString(),
                L10nTestUtils.createLocalizedString(),
                UIMessageType.SUCCESS,
                false);
        UIContentSettings informer2 = UIContentTestUtils.createInformerSettings(
                appId,
                pageId,
                randomString(),
                L10nTestUtils.createLocalizedString(),
                UIMessageType.INFO,
                true);
        UIContentSettings informer3 = UIContentTestUtils.createInformerSettings(
                appId,
                pageId,
                randomString(),
                L10nTestUtils.createLocalizedString(),
                UIMessageType.WARNING,
                false);
        UIContentSettings informer4 = UIContentTestUtils.createInformerSettings(
                appId,
                pageId,
                randomString(),
                L10nTestUtils.createLocalizedString(),
                UIMessageType.ERROR,
                true);

        UIGridLayout layout = UIContentTestUtils.createSimpleLayout(informer1, informer2, informer3, informer4);

        UIContainerSettings settings = new UIContainerSettings(contentId, pageId, appId, layout);
        List<UIContentSettings> contentSettings = List.of(informer1, informer2, informer3, informer4);
        settings.setContents(contentSettings);
        contentSettings.forEach(informer -> mockChildProcessing((UIInformerSettings)informer));

        //пропускаем проверку прав
        when(processingService.shouldBeVisible(any(UIInformerSettings.class), any())).thenReturn(true);

        //строим контент
        UIContainer container = processor.process(settings, context);
        Objects.requireNonNull(container);

        List<UIContent> contents = container.getContents();
        assertEquals("Количество контентов не совпало с ожидаемым", contentSettings.size(), contents.size());
        for (int ind = 0; ind < contentSettings.size(); ind++)
        {
            UIInformerSettings contentSettingInd = (UIInformerSettings)contentSettings.get(ind);
            UIInformer contentInd = (UIInformer)contents.get(ind);

            assertEquals("Сообщение не совпало с ожидаемым",
                    contentSettingInd.getMessage().getFirst().getValue(),
                    contentInd.getMessage());

            assertEquals("Тип сообщения не совпал с ожидаемым",
                    contentSettingInd.getType(),
                    contentInd.getType());

            assertEquals("Признак наличия иконки закрытия не совпал с ожидаемым",
                    contentSettingInd.isClosable(),
                    contentInd.isClosable());
        }
    }

    private void mockChildProcessing(UIInformerSettings contentSettings)
    {
        UIInformer content = Mockito.mock(UIInformer.class);
        when(content.getMessage()).thenReturn(contentSettings.getMessage().getFirst().getValue());
        when(content.getType()).thenReturn(contentSettings.getType());
        when(content.getId()).thenReturn(contentSettings.getId());
        when(content.isClosable()).thenReturn(contentSettings.isClosable());
        when(processingService.process(eq(contentSettings), any())).thenReturn(content);
        when(processingService.processContent(eq(contentSettings), any())).thenReturn(content);
        when(content.hasData()).thenReturn(true);
    }
}
