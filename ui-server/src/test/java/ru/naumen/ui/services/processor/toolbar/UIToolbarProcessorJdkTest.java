package ru.naumen.ui.services.processor.toolbar;

import static ru.naumen.ui.utils.UITestUtils.randomString;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

import ru.naumen.ui.models.toolbar.UIButton;
import ru.naumen.ui.models.toolbar.UIToolGroup;
import ru.naumen.ui.models.toolbar.UIToolbar;
import ru.naumen.ui.services.filter.DataFilterService;
import ru.naumen.ui.services.processor.AbstractUIProcessorJdkTest;
import ru.naumen.ui.settings.entity.toolbar.UIToolGroupSettings;
import ru.naumen.ui.settings.entity.toolbar.UIToolSettings;
import ru.naumen.ui.settings.entity.toolbar.UIToolbarSettings;
import ru.naumen.ui.utils.UIContentTestUtils;

/**
 * Тесты на процессор тулбаров
 *
 * <AUTHOR>
 * @since 27.04.2024
 */
public class UIToolbarProcessorJdkTest extends AbstractUIProcessorJdkTest
{
    private final UIToolBarProcessor toolbarProcessor = new UIToolBarProcessor();

    private final String tb1 = randomString();
    private final String tg1 = randomString();
    private final String tg2 = randomString();
    private final String tg3 = randomString();
    private final String caption11 = randomString();
    private final String caption12 = randomString();
    private final String caption13 = randomString();
    private final String caption31 = randomString();

    @Mock
    protected DataFilterService dataFilterService;

    @Before
    public void setUp()
    {
        super.setUp();
        toolbarProcessor.init(processingService, authRuleService, dataFilterService);
    }

    /**
     * Тест процессинга панели инструментов
     * <p>
     * Проверка количества групп инструментов, их порядок
     * Проверка количества и порядка инструментов в группах
     */
    @Test
    public void testProcessToolBar()
    {
        UIToolbarSettings toolbarSettings = new UIToolbarSettings(tb1);
        UIToolGroupSettings toolGroupSettings1 = new UIToolGroupSettings(tg1);
        UIToolGroupSettings toolGroupSettings2 = new UIToolGroupSettings(tg2);
        UIToolGroupSettings toolGroupSettings3 = new UIToolGroupSettings(tg3);

        toolbarSettings.addToolGroup(toolGroupSettings1);
        toolbarSettings.addToolGroup(toolGroupSettings2);
        toolbarSettings.addToolGroup(toolGroupSettings3);

        UIToolSettings toolSettings11 = UIContentTestUtils.createDefaultButtonSettings(caption11);
        toolGroupSettings1.addTool(toolSettings11);
        UIButton button11 = UIContentTestUtils.createDefaultButton(caption11);
        Mockito.when(processingService.process(toolSettings11, context)).thenReturn(button11);

        UIToolSettings toolSettings12 = UIContentTestUtils.createDefaultButtonSettings(caption12);
        toolGroupSettings1.addTool(toolSettings12);
        UIButton button12 = UIContentTestUtils.createDefaultButton(caption12);
        Mockito.when(processingService.process(toolSettings12, context)).thenReturn(button12);

        UIToolSettings toolSettings13 = UIContentTestUtils.createDefaultButtonSettings(caption13);
        toolGroupSettings1.addTool(toolSettings13);
        UIButton button13 = UIContentTestUtils.createDefaultButton(caption13);
        Mockito.when(processingService.process(toolSettings13, context)).thenReturn(button13);

        UIToolSettings toolSettings31 = UIContentTestUtils.createDefaultButtonSettings(caption31);
        toolGroupSettings3.addTool(toolSettings31);
        UIButton button31 = UIContentTestUtils.createDefaultButton(caption31);
        Mockito.when(processingService.process(toolSettings31, context)).thenReturn(button31);

        UIToolbar toolbar = toolbarProcessor.process(toolbarSettings, context);

        Assert.assertNotNull(toolbar);
        Assert.assertEquals(tb1, toolbar.getId());
        Assert.assertEquals(2, toolbar.getToolGroups().size());
        UIToolGroup toolGroup1 = toolbar.getToolGroups().getFirst();
        Assert.assertEquals(tg1, toolGroup1.getId());
        Assert.assertEquals(3, toolGroup1.getTools().size());
        Assert.assertEquals(caption11, ((UIButton)toolGroup1.getTools().getFirst()).getText());
        Assert.assertEquals(caption12, ((UIButton)toolGroup1.getTools().get(1)).getText());
        Assert.assertEquals(caption13, ((UIButton)toolGroup1.getTools().get(2)).getText());
        UIToolGroup toolGroup3 = toolbar.getToolGroups().get(1);
        Assert.assertEquals(tg3, toolGroup3.getId());
        Assert.assertEquals(1, toolGroup3.getTools().size());
        Assert.assertEquals(caption31, ((UIButton)toolGroup3.getTools().getFirst()).getText());
    }
}