package ru.naumen.ui.utils;

import static ru.naumen.metainfo.shared.CoreClassFqn.DELIMITER;
import static ru.naumen.ui.utils.UITestUtils.randomString;

import org.mockito.Mockito;

import ru.naumen.core.bo.CoreCatalogItem;

/**
 * Утилитарный класс для работы со справочниками
 * <AUTHOR>
 * @since 11.04.2024
 */
public class CatalogTestUtils
{
    /**
     * Получить mock-объект элемента справочника
     *
     * @return mock-объект
     */
    public static CoreCatalogItem mockCatalogItem()
    {
        return mockCatalogItem(randomString(), randomString(), randomString());
    }

    /**
     * Получить mock-объект элемента справочника
     *
     * @param catalogCode код справочника
     * @param code        код элемента
     * @param title       название элемента
     * @return mock-объект
     */
    public static CoreCatalogItem mockCatalogItem(String catalogCode, String code, String title)
    {
        CoreCatalogItem catalogItem = Mockito.mock(CoreCatalogItem.class);
        Mockito.when(catalogItem.getCode()).thenReturn(code);
        Mockito.when(catalogItem.getTitle()).thenReturn(title);
        Mockito.when(catalogItem.getUUID()).thenReturn(title);

        String uuid = catalogCode + DELIMITER + code;
        Mockito.when(catalogItem.getUUID()).thenReturn(uuid);
        return catalogItem;
    }
}
