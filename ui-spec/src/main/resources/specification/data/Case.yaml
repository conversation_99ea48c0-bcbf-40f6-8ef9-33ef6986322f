openapi: '3.0.3'

info:
  version: 1.0.0
  title: 'Типы объекта'
  description: 'Типы объекта'

components:
  schemas:
    Case:
      description: 'Идентификатор типа с названием типа'
      required:
        - 'id'
        - 'title'
      properties:
        id:
          description: 'Идентификатор типа'
          type: 'string'
          example: 'serviceCall$ABsc'
        title:
          description: 'Название типа'
          type: 'string'
          example: 'AB_sc'