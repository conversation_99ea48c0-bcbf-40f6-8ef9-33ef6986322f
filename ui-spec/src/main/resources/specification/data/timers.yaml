openapi: '3.0.3'

info:
  version: '1.0.0'
  title: 'Счетчик времени'
  description: 'Счетчик времени'

components:
  schemas:
    BaseTimer:
      type: 'object'
      description: 'Объект счетчик времени'
      required:
        - 'status'
      properties:
        status:
          type: 'string'
          description: 'Статус счетчика времени. Возможные значения:
          * `ACTIVE` - Активен
          * `EXCEED` - Кончился запас времени
          * `NOT_STARTED` - Ожидает начала
          * `PAUSED` - Приостановлен
          * `STOPPED` - Остановлен'
          enum:
            - 'ACTIVE'
            - 'EXCEED'
            - 'NOT_STARTED'
            - 'PAUSED'
            - 'STOPPED'
          example: 'ACTIVE'

    Timer:
      allOf:
        - $ref: '#/components/schemas/BaseTimer'
      description: 'Объект счетчик времени'
      required:
        - 'elapsed'
        - 'startTime'
      properties:
        elapsed:
          type: 'integer'
          description: 'Прошедшее время'
          format: 'int64'
          example: 1234
        startTime:
          type: 'string'
          description: 'Время начала отсчета в формате RFC 3339'
          format: 'date-time'
          example: '2023-04-28T15:05:00Z'

    BackTimer:
      allOf:
        - $ref: '#/components/schemas/BaseTimer'
      description: 'Объект обратный счетчик времени'
      properties:
        allowance:
          type: 'integer'
          format: 'int64'
          nullable: true
          description: 'Остаток времени'
          example: 12
        allowanceStart:
          type: 'string'
          nullable: true
          description: 'Время начала отсчета в формате RFC 3339'
          format: 'date-time'
          example: '2023-04-28T15:05:00Z'
        deadlineTime:
          type: 'string'
          nullable: true
          description: 'Время конца отсчета в формате RFC 3339'
          format: 'date-time'
          example: '2023-04-29T15:05:00Z'
        elapsedFromOverdue:
          type: 'integer'
          format: 'int64'
          nullable: true
          description: 'Время истекшее с момента просрочки'
          example: 24

