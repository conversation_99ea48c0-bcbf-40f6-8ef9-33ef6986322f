openapi: '3.0.3'

info:
  version: '1.0.0'
  title: 'Спецификация переиспользуемых параметров'
  description: | 
    Спецификация параметров, не относящихся к конкретному домену и используемых в разных частях  системы. Параметры 
    для передачи через query

components:
  parameters:
    applicationId:
      name: 'applicationId'
      in: 'query'
      required: true
      schema:
        type: 'string'
      description: 'Идентификатор приложения'
      example: 'demo'

    changedFieldCode:
      name: 'changedFieldCode'
      in: 'query'
      required: false
      schema:
        type: 'string'
      description: 'Код поля, изменение которого вызвало перестроение формы, либо код последнего изменяемого поля'
      example: 'metaclass'

    contentId:
      name: 'contentId'
      in: 'query'
      required: true
      schema:
        type: 'string'
      description: 'Идентификатор контента'
      example: 'testContent'

    queryPageId:
      name: 'pageId'
      in: 'query'
      required: true
      schema:
        type: 'string'
      description: 'Идентификатор страницы'
      example: 'employee'

    timeZone:
      name: 'timeZone'
      in: 'query'
      required: false
      schema:
        type: 'string'
      description: 'Часовой пояс'
      example: 'Asia/Yekaterinburg'

    fieldCode:
      name: 'fieldCode'
      in: 'query'
      required: true
      schema:
        type: 'string'
      description: "Код поля, для которого выполняется запрос"
      example: 'metaClass'

    formSessionId:
      name: 'formSessionId'
      in: 'query'
      required: true
      schema:
        type: 'string'
      description: 'Идентификатор сеанса формы'
      example: '2121021c-7777-412b-a0d0-7a720ba201fb'

    targetStepId:
      name: 'targetStepId'
      in: 'query'
      required: true
      schema:
        type: 'string'
      description: 'Идентификатор целевого шага для перехода на пошаговой форме'
      example: '2121021c-7777-412b-a0d0-7a720ba201fb'

    querySurfaceId:
      name: 'surfaceId'
      in: 'query'
      required: true
      schema:
        type: 'string'
      description: 'Идентификатор поверхности'

    objectUUID:
      name: 'objectUUID'
      in: 'query'
      required: false
      schema:
        type: 'string'
      example: 'ou$123'
      description: 'Уникальный идентификатор объекта'

    cardObjectUUID:
      name: 'cardObjectUUID'
      in: 'query'
      required: false
      schema:
        type: 'string'
      example: 'ou$123'
      description: 'Уникальный идентификатор объекта карточки'

    searchQuery:
      name: 'query'
      in: 'query'
      required: true
      description: 'Поисковый запрос'
      schema:
        type: 'string'
      example: 'Запрос на покупку ноутбука'

    offset:
      name: 'offset'
      in: 'query'
      schema:
        type: 'integer'
        format: 'int32'
        minimum: 0
      description: 'Сдвиг относительно первого элемента в списке'
      required: true
      example: 0
