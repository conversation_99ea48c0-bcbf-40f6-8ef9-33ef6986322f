openapi: '3.0.3'

info:
  version: '1.0.0'
  title: 'Тема оформления'
  description: 'Тема оформления'

components:
  schemas:
    UITheme:
      type: 'object'
      description: 'Тема оформления'
      required:
        - 'id'
        - 'logoId'
        - 'properties'
      properties:
        id:
          type: 'string'
          description: 'Идентификатор темы'
          example: 'schema1'
        properties:
          type: 'object'
          additionalProperties:
            type: 'string'