openapi: '3.0.3'

info:
  version: '1.0.0'
  title: 'Шапка'
  description: 'Контент шапки страницы'

components:
  schemas:
    UIPageHeader:
      description: |
        Контент шапки страницы.
        Содержит логотип, информацию о пользователе, кнопку "Выход".
      allOf:
        - $ref: '../../specification/ui-contents/UIContent.yaml#/components/schemas/UIContent'
      required:
        - 'showLogo'
        - 'showMenu'
        - 'showSearch'
        - 'userMenuId'
      properties:
        menuId:
          type: 'string'
          nullable: true
          description: 'Идентификатор верхнего меню'
          example: '123'
        showLogo:
          description: 'Признак отображения логотипа'
          allOf:
            - $ref: '../../specification/ui-contents/properties.yaml#/components/schemas/showLogo'
        showMenu:
          type: 'boolean'
          description: 'Необходимо ли отображать меню'
        showSearch:
          type: 'boolean'
          description: 'Необходимо ли отображать поле поиска'
          default: true
        userMenuId:
          type: 'string'
          description: 'Идентификатор меню пользователя'
          example: '123'
