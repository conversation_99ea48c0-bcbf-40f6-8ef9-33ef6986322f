openapi: '3.0.3'

info:
  version: '1.0.0'
  title: 'Спецификация методов получения информации, связанной с пользователем'
  description: 'Спецификация методов получения информации, связанной с пользователем'

paths:
  /user/info:
    get:
      tags:
        - 'UserSettings'
      summary: 'Метод получения информации о текущем пользователе'
      operationId: 'getUserInfo'
      parameters:
        - $ref: '../../specification/shared/headers.yaml#/components/parameters/traceId'
      responses:
        '200':
          description: 'Информация о текущем пользователе'
          content:
            application/json:
              schema:
                $ref: '../../specification/user-settings/responses.yaml#/components/schemas/UserInfo'
        '400':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error400'
        '401':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error401'
        '403':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error403'
        '500':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error500'

  /user/settings:
    get:
      tags:
        - 'UserSettings'
      summary: 'Метод получения настроек текущего пользователя'
      operationId: 'getUserSettings'
      parameters:
        - $ref: '../../specification/shared/queryParameters.yaml#/components/parameters/applicationId'
        - $ref: '../../specification/shared/headers.yaml#/components/parameters/traceId'
      responses:
        '200':
          description: 'Настройки текущего пользователя'
          content:
            application/json:
              schema:
                $ref: '../../specification/user-settings/responses.yaml#/components/schemas/UserSettings'
        '400':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error400'
        '401':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error401'
        '403':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error403'
        '404':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error404'
        '500':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error500'
    put:
      tags:
        - 'UserSettings'
      description: 'Метод сохраняет персональные настройки пользователя в БД'
      summary: 'Метод сохранения персональных настроек'
      operationId: 'saveUserSettings'
      parameters:
        - $ref: '../../specification/shared/queryParameters.yaml#/components/parameters/applicationId'
        - $ref: '../../specification/shared/headers.yaml#/components/parameters/traceId'
      requestBody:
        description: 'Настройки пользователя'
        required: true
        content:
          application/json:
            schema:
              $ref: '../../specification/user-settings/UserSettingsUpdate.yaml#/components/schemas/UserSettingsUpdate'
      responses:
        '200':
          description: 'Настройки сохранены'
          content:
            application/json:
              schema:
                $ref: '../../specification/user-settings/responses.yaml#/components/schemas/UserSettings'
        '400':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error400'
        '401':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error401'
        '403':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error403'
        '409':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error409'
