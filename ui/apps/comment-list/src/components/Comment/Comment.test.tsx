import {mapComments} from '../../store/helpers';
import {Comment} from './Comment';
import {render, screen, within} from '@testing-library/react';
import {authorNames, createComment} from '@ui/mocks';
import {InteractionErrors} from '@ui/types';
import {BrowserRouter} from 'react-router';

describe('Компонент <Comment />', () => {
	/**
	 * <p>Тестирование отображения ФИО пользователя - автора комментария как ссылки</p>
	 *
	 * <p><b>Ссылки на задачи:</b></p>
	 *
	 * <ol>
	 *   <li>
	 *     <a href="https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$240098301" target="_blank">
	 *       NSDPRD-30874 [2.0][1.3] [front-test] Unit-тесты на контент "Список комментариев"
	 *     </a>
	 *   </li>
	 * </ol>
	 */
	it('выводит ФИО пользователя как ссылку', () => {
		const commentWithUser = mapComments([{...createComment(1, 1), text: 'text'}]);
		const author = authorNames[0];

		render(
			<BrowserRouter>
				<Comment
					{...commentWithUser[0]}
					additionalData={{
						attributes: [],
						errorCode: null,
						errors: null,
						errorsReset: (_errors: InteractionErrors) => undefined,
						isLoading: false,
						values: {},
					}}
					onDelete={null}
					onDetailsOpen={() => {}}
					privateBadgeTitle=""
					themeMode={null}
					timeZone=""
					uuid=""
				/>
			</BrowserRouter>,
		);

		const comment = screen.getByTestId('Comment-author');

		expect(comment).toBeVisible();

		expect(within(comment).getByRole('link')).toHaveTextContent(author);
	});

	/**
	 * <p>Тестирование отображения имени суперпользователя - автора комментария без ссылки</p>
	 *
	 * <p><b>Ссылки на задачи:</b></p>
	 *
	 * <ol>
	 *   <li>
	 *     <a href="https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$240098301" target="_blank">
	 *       NSDPRD-30874 [2.0][1.3] [front-test] Unit-тесты на контент "Список комментариев"
	 *     </a>
	 *   </li>
	 * </ol>
	 */
	it('выводит имя суперпользователя без ссылки', () => {
		const commentWithUser = mapComments([{...createComment(1, 4), text: 'text'}]);
		const author = authorNames[3];

		render(
			<BrowserRouter>
				<Comment
					{...commentWithUser[0]}
					additionalData={{
						attributes: [],
						errorCode: null,
						errors: null,
						errorsReset: (_errors: InteractionErrors) => undefined,
						isLoading: false,
						values: {},
					}}
					onDelete={null}
					onDetailsOpen={() => {}}
					privateBadgeTitle=""
					themeMode={null}
					timeZone=""
					uuid=""
				/>
			</BrowserRouter>,
		);

		const comment = screen.getByTestId('Comment-author');

		expect(comment).toBeVisible();

		const link = within(comment).queryByRole('link');

		expect(link).not.toBeInTheDocument();

		expect(within(comment).getByRole('heading')).toHaveTextContent(author);
	});
});
