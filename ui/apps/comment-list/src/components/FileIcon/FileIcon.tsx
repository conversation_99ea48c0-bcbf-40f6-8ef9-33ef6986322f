import {FileIconProps} from './FileIcon.types';
import {getIconByMimeType} from './helpers';
import {theme} from '@nau-ant/components';
import {Icon} from '@ui/component-kit';

const {useToken} = theme;

export const FileIcon = (props: FileIconProps) => {
	const {mimeType} = props;
	const {
		colorTextDescription, // rgba(0, 0, 0, 0.45) / rgba(255, 255, 255, 0.45)
	} = useToken().token;
	const iconUrl = getIconByMimeType(mimeType);

	return <Icon color={colorTextDescription} fileSrc={iconUrl} height={22} width={22} />;
};
