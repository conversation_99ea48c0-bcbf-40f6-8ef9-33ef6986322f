import {AuthorLinkProps} from './styled.types';
import {theme, Typography} from '@nau-ant/components';
import {Link} from 'react-router';
import styled, {css, RuleSet} from 'styled-components';

const {Text, Title} = Typography;
const {useToken} = theme;

export const AntTitleStyled = styled(Title)<{
	$isLink: boolean,
	$isRemoved : boolean,
}>(({
	$isLink,
	$isRemoved,
}) => {
	const {colorLinkActive, colorLinkHover, colorTextDisabled, motionDurationSlow} = useToken().token;

	const linkStyle = $isLink
		? `
			transition: color ${motionDurationSlow};

			&:hover {
				color: ${colorLinkHover};
				cursor: pointer;
			}

			&:hover:active {
				color: ${colorLinkActive};
			}
		`
		: 'cursor: auto'
	;

	const removedUserStyle = $isRemoved
		? `color: ${colorTextDisabled} !important;`
		: '';

	return css`
		${linkStyle};
		${removedUserStyle};
		margin: 0 !important;
  `;
});

export const Author = styled(Text)`
	cursor: default;
	display: inline-block;
	margin-right: 16px;

	a {
		display: inline-block;
		position: relative;
	}

	& > span {
		display: inline-block;
		position: relative;
	}
`;

export const AuthorLink = styled(Link)<AuthorLinkProps>(({$avatarOnly}): RuleSet => {
	const avatarOnlyStyles = $avatarOnly
		? `
			&:hover,
			&:focus,
			&:active {
				text-decoration: none;
			}
		`
		: '';

	return css`
		position: relative;
		${avatarOnlyStyles}
	`;
});
