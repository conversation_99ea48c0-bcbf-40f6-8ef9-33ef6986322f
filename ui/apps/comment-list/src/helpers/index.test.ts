import {AdditionalValuesState} from '../store';
import {getCommentAdditionalData} from '.';
import {attributeListMock11, valueString} from '@ui/mocks';
import {Attribute} from '@ui/rest-api';
import {InteractionErrorType} from '@ui/store';
import {InteractionErrors, ValueMap} from '@ui/types';

const uuid = 'comment$1';
const attributes: Attribute[] = [attributeListMock11[0]];
const valueMap: ValueMap = {
	'attrString': valueString,
};

describe('Функция "getCommentAdditionalData"', () => {
	it('правильно формирует значение свойства, пока значения атрибутов загружаются', () => {
		const values: AdditionalValuesState = {
			detailsFor: uuid,
			errorCode: null,
			isLoading: false,
			values: {},
		};

		const errors: InteractionErrors = {api: null, infrastructure: null};
		const errorsReset = (_errors: InteractionErrors) => undefined;

		const expectedResult = {
			attributes,
			errorCode: null,
			errors: {
				api: null,
				infrastructure: null,
			},
			errorsReset,
			isLoading: true,
			values: {},
		};

		const result = getCommentAdditionalData(uuid, attributes, values, errors, errorsReset);

		expect(result).toEqual(expectedResult);
	});

	it('правильно формирует значение свойства, когда значения атрибутов загружены', () => {
		const values: AdditionalValuesState = {
			detailsFor: null,
			errorCode: null,
			isLoading: true,
			values: {
				'comment$1': valueMap,
			},
		};

		const errors: InteractionErrors = {api: null, infrastructure: null};
		const errorsReset = (_errors: InteractionErrors) => undefined;

		const expectedResult = {
			attributes,
			errorCode: null,
			errors: null,
			errorsReset,
			isLoading: false,
			values: values.values[uuid],
		};

		const result = getCommentAdditionalData(uuid, attributes, values, errors, errorsReset);

		expect(result).toEqual(expectedResult);
	});

	it('правильно формирует значение свойства, если в ходе загрузки значений атрибутов произошла ошибка', () => {
		const values: AdditionalValuesState = {
			detailsFor: uuid,
			errorCode: 1,
			isLoading: false,
			values: {},
		};

		const errors: InteractionErrors = {
			api: {
				error: {
					message: 'message',
					traceId: 'traceId',
					userDetails: 'userDetails',
					userMessage: 'userMessage',
				},
				status: 500,
				type: InteractionErrorType.API,
			},
			infrastructure: null,
		};
		const errorsReset = (_errors: InteractionErrors) => undefined;

		const expectedResult = {
			attributes,
			errorCode: 1,
			errors,
			errorsReset,
			isLoading: true,
			values: {},
		};

		const result = getCommentAdditionalData(uuid, attributes, values, errors, errorsReset);

		expect(result).toEqual(expectedResult);
	});
});
