import {useStore, UseStoreReturn} from '../store';
import {useAdditionalValues} from './useAdditionalValues';
import {renderHook} from '@testing-library/react';
import {getAppId} from '@ui/utils';

jest.mock('store', () => ({
	useStore: jest.fn(),
}));

const mockedUseStore = useStore as jest.Mock<Partial<UseStoreReturn>>;

const mockedGetAdditionalValues = jest.fn();

describe('Хук "useAdditionalValues"', () => {
	const applicationId = getAppId();
	const commentId = '1';
	const dataSourceId = 'dataSourceId';
	const objectUUID = 'root$801';
	const getObjectUUID = () => objectUUID;

	beforeEach(() => {
		mockedUseStore.mockReturnValue({
			getAdditionalValues: mockedGetAdditionalValues,
		});
	});

	it('вызывает функцию "getAdditionalValues" с правильными параметрами', () => {
		renderHook(() => useAdditionalValues(commentId, dataSourceId, getObjectUUID));

		expect(mockedGetAdditionalValues).toHaveBeenCalledWith({
			applicationId,
			contextObjectUUID: getObjectUUID(),
			dataSourceId,
			objectUUID: commentId,
		});
	});
});
