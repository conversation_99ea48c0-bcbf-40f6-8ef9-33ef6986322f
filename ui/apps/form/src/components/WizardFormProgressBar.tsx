import {useWizardFormStore} from '../store/features/wizardForm';
import {EMPTY_WIZARD_FORM} from './constants';
import {Steps, StepsProps} from '@nau-ant/components';
import {useCommonProps} from '@ui/hooks';
import {prepareFormRequestValueMap} from '@ui/utils';

/**
 * Прогресс бар для пошаговой формы.
 */
export const WizardFormProgressBar = () => {
	const {currentStepId, steps, takeStep, uiForm, values} = useWizardFormStore();
	const [{userSettings: {timeZone}}] = useCommonProps();

	const handleChange: StepsProps['onChange'] = current => {
		const {formSessionId, progressBar: {steps}} = uiForm || EMPTY_WIZARD_FORM;

		const stepId = steps[current].id;

		takeStep({
			formSessionId,
			requestBody: prepareFormRequestValueMap(values, timeZone),
			targetStepId: stepId,
		});
	};

	return <Steps current={currentStepId} items={steps} onChange={handleChange} size="small" />;
};
