import {UIFormActionTypeEnum} from '@ui/rest-api';
import {FormActionHandler} from '@ui/types';

/**
 * Формирует функцию в зависимости от типа действия кнопки.
 * @param {Record<UIFormActionTypeEnum, VoidFunction>} callbackMap - объект функций для каждого действия.
 * @returns {FormActionHandler} функция, которая вызывается в зависимости от типа действия кнопки.
 */
export const useFormActionHandler = (
	callbackMap: Record<UIFormActionTypeEnum, (actionType: UIFormActionTypeEnum) => void>,
): FormActionHandler => (actionType, id) => {
	const callback = callbackMap[actionType];

	if (callback) {
		callback(actionType);
		return;
	}

	console.warn(`Кнопка c идентификатором ${id} не имеет обработчика`);
};
