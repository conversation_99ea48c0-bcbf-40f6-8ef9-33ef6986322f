import {ContentState} from '@ui/types';

/**
 * Возвращает ссылку на контейнер атрибута, который выводится первым на форме.
 * Это нужно для вычисления правильного расстояния между полем ввода и возможным
 * сообщением об ошибке валидации на форме.
 * @param {ContentState} container - контент формы.
 * @returns {ContentState} - возвращает ссылку на контейнер атрибута, который
 * выводится первым на форме.
 */
export const getFirstContent = (container: ContentState): ContentState => {
	let result = container;

	while (result?.contents?.length) {
		result = result.contents[0];
	}

	return result;
};
