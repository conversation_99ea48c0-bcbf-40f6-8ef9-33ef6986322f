import {getRecalcFormThunk, getSubmitFormThunk, useBaseFormStore, useTypedSelector} from '../baseForm';
import {takeStep, TakeStepParams, wizardFormSlice} from './wizardFormSlice';
import {combineReducers, configureStore} from '@reduxjs/toolkit';
import {UIWizardForm} from '@ui/rest-api';
import {createServerInteractionSlice} from '@ui/store';
import {WizardFormState} from '@ui/types';
import {setupDevTools} from '@ui/utils';
import {useCallback} from 'react';
import {useDispatch} from 'react-redux';

export type FormDispatch = ReturnType<typeof createWizardFormStore>['dispatch'];

const recalcForm = getRecalcFormThunk(wizardFormSlice);
const submitForm = getSubmitFormThunk(wizardFormSlice);

/**
 * Слайс для обработки серверных ошибок пошаговой формы
 */
export const serverInteractionWizardFormSlice = createServerInteractionSlice(
	'serverInteractionWizardForm',
	[
		recalcForm,
		submitForm,
		takeStep,
	],
);

const storeMap: Record<string, ReturnType<typeof createWizardFormStore>> = {};

/**
 * Создает хранилище полностраничной пошаговой формы.
 * @param {string} id - идентификатор формы.
 */
export const createWizardFormStore = (
	id: string,
) => configureStore({
	...setupDevTools('WizardForm', id),
	middleware: getDefaultMiddleware => getDefaultMiddleware({
		serializableCheck: false,
	}),
	reducer: combineReducers({
		form: wizardFormSlice.reducer,
		serverInteraction: serverInteractionWizardFormSlice.reducer,
	}),
});

/**
 * Возвращает `singleton` объект хранилища полностраничной пошаговой формы.
 * @param {string} id - идентификатор формы.
 */
export const getWizardFormStoreInstance = (
	id: string,
) => {
	if (!storeMap[id]) {
		storeMap[id] = createWizardFormStore(id);
	}

	return storeMap[id];
};

export const useWizardFormTypedDispatch = () => useDispatch<FormDispatch>();

/**
 * Хук с подключением к хранилищу полностраничной формы.
 */
export const useWizardFormStore = () => {
	const dispatch = useWizardFormTypedDispatch();

	const useSelector = useTypedSelector<UIWizardForm, WizardFormState>();

	return {
		...useBaseFormStore<
			UIWizardForm,
			WizardFormState,
			'wizardForm',
			'serverInteractionWizardForm'
		>(
			wizardFormSlice,
			serverInteractionWizardFormSlice,
			dispatch,
		),
		currentStepId: useSelector(state => state.form.currentStepId),
		steps: useSelector(state => state.form.steps),
		takeStep: useCallback((params: TakeStepParams) => dispatch(takeStep(params)), [dispatch]),
	};
};
