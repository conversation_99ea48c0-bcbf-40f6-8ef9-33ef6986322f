// Настройки Code Mirror
const CODE_MIRROR_OPTIONS: object = {
	indentWithTabs: true,
	lineNumbers: false,
	lineWrapping: true,
	mode: 'text/html',
	tabMode: 'indent',
	tabSize: 2,
};

/* eslint-disable */
// Наборы шрифтов и подписи к ним для выпадающего списка в панели инструментов (сохранение порядка)
const FONT_FAMILY = {
	'Arial,Helvetica,sans-serif': 'Arial',
	'Georgia,serif': 'Georgia',
	'Impact,Charcoal,sans-serif': 'Impact',
	'Tahoma,Geneva,sans-serif': 'Tahoma',
	'Times New Roman,Times,serif,-webkit-standard': 'Times New Roman',
	'Verdana,Geneva,sans-serif': 'Verdana',
	'Courier New,Courier,monospace': 'Courier New',
};
/* eslint-enable */

// Список HTML-атрибутов, которые допустимо использовать в HTML-тегах
const HTML_ALLOWED_ATTRS = [
	'accept',
	'accept-charset',
	'accesskey',
	'action',
	'align',
	'allowfullscreen',
	'allowtransparency',
	'alt',
	'aria-.*',
	'async',
	'autocomplete',
	'autofocus',
	'autoplay',
	'autosave',
	'background',
	'bgcolor',
	'border',
	'charset',
	'cellpadding',
	'cellspacing',
	'checked',
	'cite',
	'class',
	'color',
	'cols',
	'colspan',
	'content',
	'contenteditable',
	'contextmenu',
	'controls',
	'coords',
	'data',
	'data-.*',
	'datetime',
	'default',
	'defer',
	'dir',
	'dirname',
	'disabled',
	'download',
	'draggable',
	'dropzone',
	'enctype',
	'for',
	'form',
	'formaction',
	'frameborder',
	'headers',
	'height',
	'hidden',
	'high',
	'href',
	'hreflang',
	'http-equiv',
	'icon',
	'id',
	'ismap',
	'itemprop',
	'keytype',
	'kind',
	'label',
	'lang',
	'language',
	'list',
	'loop',
	'low',
	'max',
	'maxlength',
	'media',
	'method',
	'min',
	'mozallowfullscreen',
	'multiple',
	'muted',
	'name',
	'novalidate',
	'onclick',
	'open',
	'optimum',
	'pattern',
	'ping',
	'placeholder',
	'playsinline',
	'poster',
	'preload',
	'pubdate',
	'radiogroup',
	'readonly',
	'rel',
	'required',
	'reversed',
	'rows',
	'rowspan',
	'sandbox',
	'scope',
	'scoped',
	'scrolling',
	'seamless',
	'selected',
	'shape',
	'size',
	'sizes',
	'span',
	'src',
	'srcdoc',
	'srclang',
	'srcset',
	'start',
	'step',
	'summary',
	'spellcheck',
	'style',
	'tabindex',
	'target',
	'title',
	'type',
	'translate',
	'usemap',
	'value',
	'valign',
	'webkitallowfullscreen',
	'width',
	'wrap',
];

// Список допустимых HTML-тегов для использования в редакторе
const HTML_ALLOWED_TAGS = [
	'a',
	'abbr',
	'address',
	'area',
	'article',
	'aside',
	'audio',
	'b',
	'base',
	'bdi',
	'bdo',
	'blockquote',
	'br',
	'button',
	'canvas',
	'caption',
	'cite',
	'code',
	'col',
	'colgroup',
	'datalist',
	'dd',
	'del',
	'details',
	'dfn',
	'dialog',
	'div',
	'dl',
	'dt',
	'em',
	'embed',
	'fieldset',
	'figcaption',
	'figure',
	'font',
	'footer',
	'form',
	'h1',
	'h2',
	'h3',
	'h4',
	'h5',
	'h6',
	'header',
	'hgroup',
	'hr',
	'i',
	'iframe',
	'img',
	'input',
	'ins',
	'kbd',
	'keygen',
	'label',
	'legend',
	'li',
	'link',
	'main',
	'map',
	'mark',
	'menu',
	'menuitem',
	'meter',
	'nav',
	'noscript',
	'object',
	'ol',
	'optgroup',
	'option',
	'output',
	'p',
	'param',
	'pre',
	'progress',
	'queue',
	'rp',
	'rt',
	'ruby',
	's',
	'samp',
	'style',
	'section',
	'select',
	'small',
	'source',
	'span',
	'strike',
	'strong',
	'sub',
	'summary',
	'sup',
	'table',
	'tbody',
	'td',
	'textarea',
	'tfoot',
	'th',
	'thead',
	'time',
	'title',
	'tr',
	'track',
	'u',
	'ul',
	'var',
	'video',
	'wbr',
];

// Удаляемые атрибуты при вставке HTML в область редактирования
const PASTE_DENIED_ATTRS = [
	'id',
	'class',
	'onblur',
	'onchange',
	'onclick,',
	'ondblclick',
	'onfocus,',
	'onerror',
	'onkeydown',
	'onkeypress',
	'onkeyup',
	'onload',
	'onmousedown',
	'onmousemove',
	'onmouseout',
	'onmouseover',
	'onmouseup',
	'onreset',
	'onselect',
	'onsubmit',
	'onunload',
];

// Настраиваемые кнопка и плагин эмодзи.
// Настройка, регулирующая использование кнопки и плагина приходит
// с сервера, поэтому необходимо настраивать по месту.
export const EMOTICON_BUTTON_NAME = 'emoticons';
export const EMOTICON_PLUGIN_NAME = 'emoticons';

// Включенные дополнения
export const PLUGINS_ENABLED = [
	'align',
	'codeBeautifier',
	'codeView',
	'colors',
	'draggable',
	EMOTICON_PLUGIN_NAME,
	'entities',
	'fontFamily',
	'fontSize',
	'fullscreen',
	'help',
	'image',
	'lineHeight',
	'link',
	'lists',
	'paragraphFormat',
	'quote',
	'table',
	'url',
	'video',
	'wordPaste',
];

// Действия, для которых разрешены клавиатурные сокращения
const SHORTCUTS_ENABLED = [
	'bold',
	'help',
	'indent',
	'insertImage',
	'insertLink',
	'insertMention',
	'italic',
	'outdent',
	'quote',
	'redo',
	'show',
	'strikeThrough',
	'underline',
	'undo',
];

const TABLE_EDIT_BUTTONS = [
	'tableHeader',
	'tableRemove',
	'|',
	'tableRows',
	'tableColumns',
	'tableCells',
	'tableStyle',
	'tableCellBackground',
	'tableCellVerticalAlign',
	'tableCellHorizontalAlign',
];

// Список кнопок панели инструментов редактора
export const BUTTONS = [[
	'bold',
	'italic',
	'underline',
	'strikeThrough',
	'subscript',
	'superscript',
	'|',
	'paragraphFormat',
	'quote',
	'align',
	'formatOL',
	'formatUL',
	'outdent',
	'indent',
	'|',
	'fontFamily',
	'fontSize',
	'colors',
	'textColor',
	'backgroundColor',
	'clearFormatting',
	'|',
	'insertLink',
	'insertImage',
	'insertVideo',
	'insertTable',
	'insertHR',
	'insertMention',
	'|',
	'html',
	'help',
	'fullscreen',
	EMOTICON_BUTTON_NAME,
]];

// Список разрешенных правил CSS при вставке из Word
const WORD_ALLOWED_STYLE_PROPS = [
	'background',
	'background-color',
	'color',
	'font-family',
	'font-size',
	'font-style',
	'font-weight',
	'height',
	'margin',
	'margin-bottom',
	'margin-left',
	'margin-right',
	'margin-top',
	'padding',
	'text-align',
	'text-decoration',
	'text-indent',
	'vertical-align',
	'width',
];

// Конфигурация Froala по умолчанию
export const DEFAULT_CONFIG: object = {
	// Вывод шильдика "powered by Froala"
	attribution: false,
	// Вывод счетчика символов
	charCounterCount: false,
	// Настройки CodeMirror
	codeMirrorOptions: CODE_MIRROR_OPTIONS,
	// Использование svg-иконок EmojiOne вместо Unicode-символов
	emoticonsUseImage: false,
	// Тег, вставляемый по нажатию на Enter (0 - p, 1 - div, 2 - br)
	enter: 1, // для админки должно быть 2
	// Наборы шрифтов
	fontFamily: FONT_FAMILY,
	// Размеры шрифтов в выпадающем списке в панели инструментов
	fontSize: [8, 9, 10, 11, 12, 13, 14, 16, 18, 20, 24, 28, 32, 36, 48, 60, 72, 96],
	// Текст выбранного значения, если размер шрифта не был явно выбран пользователем
	fontSizeDefaultSelection: '14',
	// Максимальная высота области редактирования
	heightMax: null,
	// Минимальная высота области редактирования
	heightMin: null,
	// Список HTML-атрибутов, которые допустимо использовать в HTML-тегах
	htmlAllowedAttrs: HTML_ALLOWED_ATTRS,
	// Список допустимых HTML-тегов для использования в редакторе
	htmlAllowedTags: HTML_ALLOWED_TAGS,
	// Разрешение запускать скрипты, введенные в режиме просмотра кода
	htmlExecuteScripts: false,
	// Список тегов, которые удаляются со всем их содержимым
	htmlRemoveTags: ['script'],
	// Не изменять HTML-код в редакторе, кроме очистки
	htmlUntouched: true,
	// Помещать область редактирования в iframe
	iframe: false,
	// Стили для использования в iframe-е области редактирования
	iframeStyle: '',
	// Допустимые к загрузке типы изображений
	imageAllowedTypes: ['bmp', 'gif', 'ico', 'jpg', 'jpeg', 'nbmp', 'png', 'svg', 'svgz'],
	// Режим вывода изображения при вставке через редактор ('inline' или 'block')
	imageDefaultDisplay: 'inline',
	// Размер по умолчанию для изображений, вставляемых через редактор. 0 - атрибут width не устанавливается
	imageDefaultWidth: 0,
	// Список кнопок редактирования изображения во всплывающей панели
	imageEditButtons: ['imageAlign', 'imageDisplay', 'imageCaption', 'imageRemove', 'imageSize'],
	// Список кнопок вставки изображения во всплывающей панели
	imageInsertButtons: ['imageBack', '|', 'imageUpload', 'imageByURL'],
	// Максимальный размер загружаемого изображения в байтах
	imageMaxSize: 1024 * 1024 * 10,
	// Устанавливать атрибуты height и width в теги изображений
	imageOutputSize: false,
	// Возможность менять размер изображения
	imageResize: false,
	// Список кнопок изменения размера изображения во всплывающей панели
	imageSizeButtons: [],
	// Загрузка изображений вставленных через URL на сервер, вместо того, чтобы ссылаться на них
	imageUploadRemoteUrls: false,
	// URL, по которому на сервер загружаются вставляемые изображения
	imageUploadURL: null,
	// Инициализации редактора по щелчку в области редактирования
	initOnClick: false,
	// Локализация
	language: null,
	// Префикс по умолчанию для URL-ов ссылок
	linkAutoPrefix: '',
	// Список кнопок изменения ссылки во всплывающей панели
	linkEditButtons: [],
	// Список кнопок вставки ссылки во всплывающей панели
	linkInsertButtons: ['linkBack', '|', 'linkRemove'],
	// Список предопределенных ссылок для выбора при вставке или редактировании ссылки
	linkList: [],
	// Расширенные маркеры в списках
	listAdvancedTypes: false,
	// Удаляемые атрибуты при вставке HTML в область редактирования
	pasteDeniedAttrs: PASTE_DENIED_ATTRS,
	// Текст-приглашение когда область редактирования пуста
	placeholderText: '',
	// Включенные дополнения
	pluginsEnabled: PLUGINS_ENABLED,
	// Список тегов, для которых будут выведены кнопки быстрой вставки если область редактирования пуста
	quickInsertTags: [],
	// Селектор контейнера, выступающего родительским для всех всплывающих панелей редактора
	scrollableContainer: 'body',
	// Действия, для которых разрешены клавиатурные сокращения
	shortcutsEnabled: SHORTCUTS_ENABLED,
	// Список кнопок редактирования таблицы во всплывающей панели
	tableEditButtons: TABLE_EDIT_BUTTONS,
	// Использование инструмента, облегчающего вставку строк и колонок
	tableInsertHelper: false,
	// Названия CSS-классов и подписи к ним для стилизации таблиц во всплывающей панели
	tableStyles: {},
	// Тема оформления (соответствует названию файла темы)
	theme: 'gray',
	// Список кнопок панели инструментов для ширин экрана >= 1200px
	toolbarButtons: BUTTONS,
	// Список кнопок панели инструментов для ширин экрана >= 992px
	toolbarButtonsMD: BUTTONS,
	// Список кнопок панели инструментов для ширин экрана >= 768px
	toolbarButtonsSM: BUTTONS,
	// Список кнопок панели инструментов для ширин экрана < 768px
	toolbarButtonsXS: BUTTONS,
	// Фиксация панели инструментов при прокрутке
	toolbarSticky: false,
	// Преобразование внешних стилей в inline-стили (отключено, т.е. используются названия CSS-классов как есть)
	useClasses: false,
	// Режим вывода видео при вставке через редактор ('inline' или 'block')
	videoDefaultDisplay: 'inline',
	// Список кнопок редактирования видео во всплывающей панели
	videoEditButtons: ['videoRemove', 'videoDisplay', 'videoAlign', 'videoSize'],
	// Список кнопок вставки видео во всплывающей панели
	videoInsertButtons: ['videoBack', '|', 'videoByURL'],
	// Изменение расположения видео перетаскиванием (отключено)
	videoMove: false,
	// Изменения размеров видео в редакторе
	videoResize: false,
	// Список кнопок изменения размера видео во всплывающей панели
	videoSizeButtons: [],
	// Список разрешенных правил CSS при вставке из Word
	wordAllowedStyleProps: WORD_ALLOWED_STYLE_PROPS,
	// Показывать или нет модальное окно с вопросом сохранения или удаления стилей из Word
	wordPasteModal: false,
	// zIndex для панелей инструментов и всплывающих панелей редактора
	zIndex: 5,
};

export const KEYS = {
	BACKSPACE: 'BACKSPACE',
	DELETE: 'DELETE',
	ENTER: 'ENTER',
	ESCAPE: 'ESCAPE',
	TAB: 'TAB',
};

/**
 * Специальный класс для обертки области редактирования в полноэкранном режиме.
 * Используется для корректировки рассчета высоты области редактирования и
 * для определения фона панели инструментов в полноэкранном режиме.
 */
export const FR_FULLSCREEN_WRAPPER_FIX_CLASS = 'fr-fullscreen-wrapper-fix';
