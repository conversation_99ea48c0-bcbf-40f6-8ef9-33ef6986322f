import {UINestedTable} from '@ui/rest-api';

export type NestedTablesProps = {
	/**
	 * Идентификатор контента
	 */
	contentId: string,

	/**
	 * Идентификатор источника данных
	 */
	dataSourceId: string,

	/**
	 * Уровень вложенности
	 */
	level: number,

	/**
	 * Список вложенных таблиц
	 */
	nestedTables: UINestedTable[],

	/**
	 * Функция обратного вызова при загрузке всех дочерних таблиц
	 */
	onAllChildrenLoaded?: () => void,

	/**
	 * Варианты значений для переключателя количества элементов списка на странице
	 */
	pagingSteps: number[],

	/**
	 * Идентификатор родительской таблицы
	 */
	parentTableId?: string,

	/**
	 * UUID родительского объекта
	 */
	parentUUID?: string,
};
