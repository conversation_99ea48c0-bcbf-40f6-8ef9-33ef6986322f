import colorIcon from '../../icons/colorIcon.svg';
import colorIconChecked from '../../icons/colorIconChecked.svg';
import {IconWrapper} from '../../styled';
import {Icon} from '@ui/component-kit';
import {UITheme} from '@ui/rest-api';
import {useMemo} from 'react';

type SystemThemePanelProps = {
	onChange : (color: UITheme) => void,
	themeList : Array<UITheme>,
	themeUuid : string,
	useCustomTheme? : boolean | null,
};

export const SystemThemePanel = (props: SystemThemePanelProps) => {
	const {onChange, themeList, themeUuid, useCustomTheme} = props;

	return useMemo(() => themeList.map((color: UITheme) => (
		<IconWrapper
			$colorPrimary={color.properties.colorPrimary}
			data-testid={`system-theme-${color.id}`}
			key={color.id}
			onClick={() => onChange(color)}
		>
			<Icon
				fileSrc={useCustomTheme ? colorIcon : color.id === themeUuid ? colorIconChecked : colorIcon}
				height={32}
				width={32}
			/>
		</IconWrapper>
	)), [onChange, themeList, themeUuid, useCustomTheme]);
};
