import {ColorsWrapper, FormItemStyled} from '../../styled';
import {SystemThemePanel} from '../SystemThemePanel';
import {ColorPicker, ColorPickerProps} from '@nau-ant/components';
import {UITheme} from '@ui/rest-api';
import {useTranslation} from 'react-i18next';

type ThemeProps = {
	customPrimaryColor?: string,
	onCustomThemeChange : ColorPickerProps['onChange'],
	onOpenCustomTheme : ColorPickerProps['onOpenChange'],
	onSystemThemeChange : (color: UITheme) => void,
	showCustomColor?: boolean,
	showThemeField: boolean,
	themeList : Array<UITheme>,
	themeUuid : string,
	useCustomTheme? : boolean,
};

export const ThemeField = (props: ThemeProps) => {
	const {t} = useTranslation(['personalSettings']);
	const {
		customPrimaryColor,
		onCustomThemeChange,
		onOpenCustomTheme,
		onSystemThemeChange,
		showCustomColor,
		showThemeField,
		themeList,
		themeUuid,
		useCustomTheme,
	} = props;

	return (
		<FormItemStyled
			label={t('personalSettings:theme')}
			name="color-picker"
		>
			<ColorsWrapper>
				{showThemeField && (
					<SystemThemePanel
						onChange={onSystemThemeChange}
						themeList={themeList}
						themeUuid={themeUuid}
						useCustomTheme={useCustomTheme}
					/>
				)}
				{showCustomColor && (
					<ColorPicker
						data-testid="color-picker"
						defaultValue={customPrimaryColor}
						disabledAlpha
						onChange={onCustomThemeChange}
						onOpenChange={onOpenCustomTheme}
						showText
						style={useCustomTheme ? {borderColor: customPrimaryColor} : {}}
					/>
				)}
			</ColorsWrapper>
		</FormItemStyled>
	);
};
