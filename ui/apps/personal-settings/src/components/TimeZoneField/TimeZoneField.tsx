import {FormItemStyled} from '../../styled';
import {FieldType} from '../App/App.types';
import {NauSelectOption} from '@nau-ant/components';
import {NauSelectCustom} from '@ui/component-kit';
import {CatalogItem} from '@ui/rest-api';
import {useMemo} from 'react';
import {useTranslation} from 'react-i18next';

type TimeZoneFieldProps = {
	onChange : (type: FieldType, value: boolean | string) => void,
	timeZone: string | null,
	timeZoneList: Array<CatalogItem>,
};

export const TimeZoneField = (props: TimeZoneFieldProps) => {
	const {onChange: handleChange, timeZone, timeZoneList} = props;

	const {t} = useTranslation(['personalSettings', 'fields']);

	const notSpecifiedValue: NauSelectOption = useMemo(() => ({
		disabled: false,
		label: t('fields:select.notSpecified'),
		last: false,
		title: t('fields:select.notSpecified'),
		value: '',
	}), [t]);

	const options = useMemo(() => [
		notSpecifiedValue,
		...timeZoneList.map((timeZone: CatalogItem) => ({
			disabled: false,
			label: timeZone.title,
			last: false,
			title: timeZone.title,
			value: timeZone.uuid,
		})),
	], [notSpecifiedValue, timeZoneList]);

	return (
		<FormItemStyled label={t('personalSettings:timeZone')}>
			<NauSelectCustom
				additionalOption={notSpecifiedValue}
				data-testid="time-zone-select"
				moreItemsAreLoading={false}
				onChange={value => handleChange('timeZone', value)}
				options={options}
				value={timeZone ?? notSpecifiedValue.value}
			/>
		</FormItemStyled>
	);
};
