import {createApi} from '@reduxjs/toolkit/query/react';
import {getUserSettingsApi} from '@ui/api-provider';
import {SaveUserSettingsRequest} from '@ui/rest-api';
import {baseQuery} from '@ui/store';
import {ModifiedUserSettings} from '@ui/types';
import {getCsrfHeader} from '@ui/utils/src/security';

export const personalSettingsApi = createApi({
	baseQuery,
	endpoints: builder => ({
		saveUserSettings: builder.mutation<ModifiedUserSettings, SaveUserSettingsRequest>({
			query: async params => await getUserSettingsApi().saveUserSettings(params, {headers: getCsrfHeader()}),
		}),
	}),
	keepUnusedDataFor: 0,
	reducerPath: 'personal-settings',
	tagTypes: ['PersonalSettings'],
});

export const {
	useSaveUserSettingsMutation,
} = personalSettingsApi;
