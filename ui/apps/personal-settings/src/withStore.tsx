import {PersonalSettingsProps} from './components/App/App.types';
import {getStoreInstance} from './store';
import {ComponentType} from 'react';
import {Provider} from 'react-redux';

export const withStore = (AppComponent: ComponentType<PersonalSettingsProps>) => {
	const WithStoreAppComponent = (props: PersonalSettingsProps) => {
		const {content} = props;

		return (
			<Provider store={getStoreInstance(content)}>
				<AppComponent {...props} />
			</Provider>
		);
	};

	WithStoreAppComponent.displayName = 'withStore(personal-settings)';

	return WithStoreAppComponent;
};
