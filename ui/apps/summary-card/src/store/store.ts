import packageJson from '../../package.json';
import {
	summaryCardApi,
	useGetObjectCountQuery,
} from './features/summaryCardApi';
import {configureStore} from '@reduxjs/toolkit';
import {setupDevTools} from '@ui/utils';

/**
 * Создать экземпляр стора для компонента {@link SummaryCardApp}.
 * @param {string} [id=''] - идентификатор стора.
 */
const createStore = (id: string) => configureStore({
	...setupDevTools(packageJson.name, id),
	middleware: getDefaultMiddleware => getDefaultMiddleware().concat(summaryCardApi.middleware),
	reducer: {
		[summaryCardApi.reducerPath]: summaryCardApi.reducer,
	},
});

/**
 * Мапа {`идентификатор компонента`: `экземпляр стора`}.
 */
const storeMap: Record<string, ReturnType<typeof createStore>> = {};

/**
 * Получить экземпляр стора для компонента {@link SummaryCardApp} по идентификатору.
 * @param {string} id - идентификатор.
 * @returns экземпляр стора для компонента {@link SummaryCardApp}.
 */
export const getStoreInstance = (id: string) => {
	storeMap[id] = storeMap[id] || createStore(id);

	return storeMap[id];
};

/**
 * Хук для получения доступа к стору.
 */
export const useStore = () => ({
	useGetObjectCountQuery,
});
